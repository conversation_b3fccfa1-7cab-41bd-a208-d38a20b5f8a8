{"__meta": {"id": "X3e9aa1a0aa6948975f9f45f00f6c3804", "datetime": "2025-06-17 15:02:24", "utime": **********.897747, "method": "GET", "uri": "/customer/check/warehouse?customer_id=6&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.329684, "end": **********.897771, "duration": 0.5680868625640869, "duration_str": "568ms", "measures": [{"label": "Booting", "start": **********.329684, "relative_start": 0, "end": **********.811357, "relative_end": **********.811357, "duration": 0.481673002243042, "duration_str": "482ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.811371, "relative_start": 0.481687068939209, "end": **********.897774, "relative_end": 3.0994415283203125e-06, "duration": 0.08640289306640625, "duration_str": "86.4ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46234432, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02087, "accumulated_duration_str": "20.87ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.852106, "duration": 0.01862, "duration_str": "18.62ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 89.219}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.8824651, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 89.219, "width_percent": 6.325}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.88799, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 95.544, "width_percent": 4.456}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-1203531081 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1203531081\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1596945334 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1596945334\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-380010371 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-380010371\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-116677337 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImRuZ2JDc1Zhek9Gck5WU3ZPalVTWGc9PSIsInZhbHVlIjoiY0xQeDBWQzdMWmtvU2ZqbHBMdG1YRVJhNitmMEgrNmJ3ak1BNEpla1pabjNPb0E3RG10RWk0NlZXY2NjTDlvWGhJMG1UNGd5dVRDOEtuOHZHbTlKYXlKY2NYWXMyWFIzMHpEdmp4VGl1ekZEaGd1Y0ZHazZvN3kwVHZuUStvNWVzMW5rdUlKVXhMQ1hDNUNuNllsSGVQYUVXdEJydmdXa01zTHBuVW85ZTYydmVVY2hRREJFdncrRHJIa0pJTTdITGdHbiswSzBIT2VrVHBlb0tpQ3FEbFR5WUpLVTFGaHQwRTR2VUFQQysvNGpwZW9GS2RSeDFzc3RHUnErWWpRYThucWtYYUlhTDFBNTdiaGt0aHpuZ0EvWjlSQ253bXF1dFl1dXFDMHk1bTVTU0lhNitqSVhkdm1Bd2RXeWtDL0psV0lGUzdoU2EzVEI1RW93MnJzL3RML3Qxc2NFd1pPdFBJcGQ2aUlTZ2RsS21GUzVuUVhMcmllQ2tWazhuRm51VVd5eGtzRWJ5SmRZdnd3YVJNRFdVRkhzSE1abnAwU3h1K25rMXpFRHN2dFNGMGplc1QrTVBST0pLYzBncVJwbkwzWUcyZi9ybFRhVm91V2NSY3ZxRm5WblIvTkp1TjNTTk96bko1b2RMSm00bmlxanYyWXJnN2NwRUxhemIrUE8iLCJtYWMiOiI0Yjg3NzIyMGNhZmQxZmZhOTViMTNmZmYwNGQxNmMzODdiYzVlYmFjZDc5MWNmYWQxZWMzZDY4YTM4NWI4ZDI4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImdYRis1aGFHOVFVT3VieTZXNDNwbHc9PSIsInZhbHVlIjoiT1lMeHJ3M0pSTlVOV2JDVFpRSTBKNkhBSW4zUjZNQTIzSStzZTdiOTBnTzlFb2tuNmRCWEVnQ0lXNVhoZ0paNEx3dUxFTjFaZTJjNWdvVXFKRlNOc051N1ZJSUgxajNBSWZ0cm1UM0l0NytiNWo0elZUNG9FNVdINGpGc0Y4Qm5xbkd4R3V1M3JKU1FrcE15SDRHSnp5bnUvR0NSbitodFBmU2l4VXE2Z1lrcmdmSlNXTFoyQWNIL1lDRnBOMEVHUlpwNGZCalQxOGdWem1zM2x1aFdvbnNhTEU4ZktRZ2IrczZab0RSbkg3aDVzU1p0NUVycFNzTmY5YkFFa1BkWTFYNkR6cWpUQ0tZdzZxOVhoa1pZWkl1U1lZa2dzNE5tNFhPT1kyM0JybzZWcUZxYzQzYTFPcy84ckgrZ1AvVVVDU1BpcEhpb2pkTm54S3NPS3RwbldiYVgycDFvVWJJL2FZZXdyditqZWlhM3VoVUhvcTRqMExDZ3ZNT0hDVm9LWEFPSWpYT0V2RktoQnNWdlR5NmU4WjJVOFNmSVdtSnpUbXNaeVBSVENQSGlsREwyNWxXVXdKZ05FeWJ6YzJ0RXJGZ0ppZDhFcWY2QU82WTQ2dGNsQUYwVFpDM2ZaK2ZoVFJYUDU4eFRKYjlBRU1CK3FiKzdkYzNqU1gxaS9yTmoiLCJtYWMiOiJmYzFiZjU2NjI2Zjg0MGFhNTE4ODE3Nzg2MTFjNDgxY2E2MzhmZDdmYTZmMWExZTViNDA1MzI0M2I5YzUxYzQ3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-116677337\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1931838757 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1931838757\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1255746785 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:02:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkdhNTQzVTNYcmFOeU1Ldk1hUkRxOEE9PSIsInZhbHVlIjoib0FsSXJ5azliOENkcTZ0eHUzZ3JBeE1iaGlzQXpvQTNrbjdXZHN6OCsraHU0K2pZdXFhNDk0b3JMOThUbmR0aUNzRmZnOHp5WmptQmFBR3dZcmJ2UnM0VmkxWkVOYnQzN3Vna1pFUy8yMXVaVTREMnVwZjJZd0tZbnZ2OG9CUDlBUEMrS015Tjk1a0JFSUE2MFpPNTRYVVY3Y0dGdHEzNjZST3JvRnYrNU9HT3ViOUFnd04zMjJZVTYzY1Q2N0lraVhLWWZ6KzZvOFhSejBpTFIwWks5TjlnbUVMRlNBbk55ZXNYMUNPUlYvTENUMDdrQU9XVnNyQVZPcVpzbFhDQ0wzSVF2TWpRa21NRzNaUklENFFqU1RRMlp3SkV2d3R0cVNWS0hTQWJpblJvM2ZSSEdZbERsZkJ4ajNuVVJUNGNEOU9ucG4vb2phcGlaYUQ5MFc1Y3lwNTdrY3NiZ0swT003MnROcTdCTFUwRFFkUVhpTWFqNkhOZlIwbkpkZXJZclJkNHpCb0xSVkpidGFFd21Idis0V2RVQ3lQM2JiV2kwUUlCTUVIaFRxTUk0SGpKbk9aWmlIV0t1bEgwSmRHZDNWckRQUHdQRFZrSXFDcDZEcStBSUpUbjlmWW1Cem8vTGtSaGhZckJQSFR5b3FUZE9yYWVKK2JGOFdhcFdreG8iLCJtYWMiOiIwZGUzNGVjOTFkZDU2MTQ0YzEzZjE1NDRiYzAyYzU0YzA4NGNmYjNmOGQ0MTY0ZTBjOGZlN2RlZjJhMTQyZTQzIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:02:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InptLzJicUdkZTU5MTZwQVZPZHoyVGc9PSIsInZhbHVlIjoiNUszZE1tY2YvanJVRWEreWdYdlFLOHRjWlU4MmR4am5HeW9SbFIybVllQ3YzRndncmZNb0Q4WHoydHc0KzBTMUR5Q01nYW5XdHgvMEJxOUVCYW9MdGdCUllxc2NlaUZ4djJJMHRpZlAzWWhEWVdPZzRBMnl1RiswR1NxMXdRT3hTY0NlWVB0a1RROFB5NVdWQndsaWg2eDY2amhyVHo0aUkvb0tEdmNwdy95U3dzSmdiNVBoVmpWb1pTMk56dXhIZDN1N1picHdqTVVzd2lrUzd5akNiWFRlWGZrNzhEWkliRVl6Y3diS3FTb2hCUk1pb3VQMXh1UXg3dGJiL2U1cTUydDBBRDVRQU5NTHNncTEzVnl3VkJlTmFDem44cEpFa3EvZ2MxRGZCdTkycWZuVkRVOUNSekhZWGhIWmorV3hHL1NzZjNPNS9mckJDeXhjZkI4TmcwNE1BNkZhWmZNVmlTTFd5Z2pSdkRuUVhPMUYvaHAybkw4c1BiNWl0aEtCdTBxY1JCOXdaUVhiMFFHMzg2QjBpTUhoRis1aXB0ZG5mbml6T3NGbzA2bEpXS2xWWHZEcTgxS1h1Q0dWT3ZXYW1wNFg3VitNT042OWs5MEdzdUhyMjh6VWMybkJzUlRXM2c4VEluRlZNRi9EU1ZjZVZvYzljSVV3Ym5DbldSMVIiLCJtYWMiOiJiNzM5MzhhZWU1N2QyM2FlOGQ2NmUxYzY0NzRjODhlYTNiNjZlOWQ5OGJmMjAzMDdmM2RhNjdjMWNhYzNiMmU4IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:02:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkdhNTQzVTNYcmFOeU1Ldk1hUkRxOEE9PSIsInZhbHVlIjoib0FsSXJ5azliOENkcTZ0eHUzZ3JBeE1iaGlzQXpvQTNrbjdXZHN6OCsraHU0K2pZdXFhNDk0b3JMOThUbmR0aUNzRmZnOHp5WmptQmFBR3dZcmJ2UnM0VmkxWkVOYnQzN3Vna1pFUy8yMXVaVTREMnVwZjJZd0tZbnZ2OG9CUDlBUEMrS015Tjk1a0JFSUE2MFpPNTRYVVY3Y0dGdHEzNjZST3JvRnYrNU9HT3ViOUFnd04zMjJZVTYzY1Q2N0lraVhLWWZ6KzZvOFhSejBpTFIwWks5TjlnbUVMRlNBbk55ZXNYMUNPUlYvTENUMDdrQU9XVnNyQVZPcVpzbFhDQ0wzSVF2TWpRa21NRzNaUklENFFqU1RRMlp3SkV2d3R0cVNWS0hTQWJpblJvM2ZSSEdZbERsZkJ4ajNuVVJUNGNEOU9ucG4vb2phcGlaYUQ5MFc1Y3lwNTdrY3NiZ0swT003MnROcTdCTFUwRFFkUVhpTWFqNkhOZlIwbkpkZXJZclJkNHpCb0xSVkpidGFFd21Idis0V2RVQ3lQM2JiV2kwUUlCTUVIaFRxTUk0SGpKbk9aWmlIV0t1bEgwSmRHZDNWckRQUHdQRFZrSXFDcDZEcStBSUpUbjlmWW1Cem8vTGtSaGhZckJQSFR5b3FUZE9yYWVKK2JGOFdhcFdreG8iLCJtYWMiOiIwZGUzNGVjOTFkZDU2MTQ0YzEzZjE1NDRiYzAyYzU0YzA4NGNmYjNmOGQ0MTY0ZTBjOGZlN2RlZjJhMTQyZTQzIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:02:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InptLzJicUdkZTU5MTZwQVZPZHoyVGc9PSIsInZhbHVlIjoiNUszZE1tY2YvanJVRWEreWdYdlFLOHRjWlU4MmR4am5HeW9SbFIybVllQ3YzRndncmZNb0Q4WHoydHc0KzBTMUR5Q01nYW5XdHgvMEJxOUVCYW9MdGdCUllxc2NlaUZ4djJJMHRpZlAzWWhEWVdPZzRBMnl1RiswR1NxMXdRT3hTY0NlWVB0a1RROFB5NVdWQndsaWg2eDY2amhyVHo0aUkvb0tEdmNwdy95U3dzSmdiNVBoVmpWb1pTMk56dXhIZDN1N1picHdqTVVzd2lrUzd5akNiWFRlWGZrNzhEWkliRVl6Y3diS3FTb2hCUk1pb3VQMXh1UXg3dGJiL2U1cTUydDBBRDVRQU5NTHNncTEzVnl3VkJlTmFDem44cEpFa3EvZ2MxRGZCdTkycWZuVkRVOUNSekhZWGhIWmorV3hHL1NzZjNPNS9mckJDeXhjZkI4TmcwNE1BNkZhWmZNVmlTTFd5Z2pSdkRuUVhPMUYvaHAybkw4c1BiNWl0aEtCdTBxY1JCOXdaUVhiMFFHMzg2QjBpTUhoRis1aXB0ZG5mbml6T3NGbzA2bEpXS2xWWHZEcTgxS1h1Q0dWT3ZXYW1wNFg3VitNT042OWs5MEdzdUhyMjh6VWMybkJzUlRXM2c4VEluRlZNRi9EU1ZjZVZvYzljSVV3Ym5DbldSMVIiLCJtYWMiOiJiNzM5MzhhZWU1N2QyM2FlOGQ2NmUxYzY0NzRjODhlYTNiNjZlOWQ5OGJmMjAzMDdmM2RhNjdjMWNhYzNiMmU4IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:02:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1255746785\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-16980542 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-16980542\", {\"maxDepth\":0})</script>\n"}}