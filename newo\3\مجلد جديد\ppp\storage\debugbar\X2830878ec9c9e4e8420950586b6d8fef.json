{"__meta": {"id": "X2830878ec9c9e4e8420950586b6d8fef", "datetime": "2025-06-17 14:36:01", "utime": **********.416573, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750170960.819479, "end": **********.416598, "duration": 0.5971190929412842, "duration_str": "597ms", "measures": [{"label": "Booting", "start": 1750170960.819479, "relative_start": 0, "end": **********.302468, "relative_end": **********.302468, "duration": 0.4829890727996826, "duration_str": "483ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.30248, "relative_start": 0.4830009937286377, "end": **********.416601, "relative_end": 2.86102294921875e-06, "duration": 0.1141209602355957, "duration_str": "114ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49310352, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.01312, "accumulated_duration_str": "13.12ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.346606, "duration": 0.00431, "duration_str": "4.31ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 32.851}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.362028, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 32.851, "width_percent": 6.936}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.384861, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 39.787, "width_percent": 7.546}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.388496, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 47.332, "width_percent": 6.936}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.39565, "duration": 0.0038900000000000002, "duration_str": "3.89ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 54.268, "width_percent": 29.649}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.403747, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 83.918, "width_percent": 16.082}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1807981247 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1807981247\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.394259, "xdebug_link": null}]}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-973164139 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-973164139\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-2012696756 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2012696756\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2091729716 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2091729716\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2114310446 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImJ1aHlSNGFSM1BRQ29EclkwVzY0UGc9PSIsInZhbHVlIjoibklBTmt1ekRyb3VvV29ZdGRJaFpIcE9tclZJd01OZjl3Y3g5blBDSlhHSzNhSTRVVjJIRVF2dGlqcmx1a1ZKQ3JURDcvTlRYcFFIT01qTzBOREljQTRYSFdQZmtZZHRtbTJWSUd1aWxxZ1pIckhXM3A3NDdKcE5EcElid0hKRnpFVlhEc3lGSGM0Qks1T0ErTnA4M1EvcnlEOGphQ21kb2hVMlVYNjFTQmROMlVrY3pZWWUwR2RPTlNpNFc1cC9oSHh0cXBXL3RlT0N6ZUZWYlVhWWwzSldSZHZlTDV0RWxLeDhuUWtmNFkrYXFLTjBUeWlieGhvWE0ySFhwN1djelZVdFJ0Zm5wR2Nmd0tLS0k1dTBUQXA4NTlhL2srcXU2N3J2QlRYRU1LU1N1M1NnTlpUdlJoMjJ4ZS9IN1RtWThSbG9COVpjNEpZNDk2Q0FNRGczU2lvRHdsZHgwNTFKRDl2R1hhN29aTkhxZHQrNHN2TkJsWG5iQi9RTUIwVjNITUNqVWNLM2NpV21nS01IVTV6ZEViK1FoYllSOTBpcU9PWTcycU1RSHBwOEtqeG5ZQy9BUE51R05YelZnSXhCaW1nbW1KdHh4VVpVK3J4MThNcDRlYUx3dHVvZGJIZlA0bkxSRWgzbTZNeHE5Ui9YcnNqK20zVEQyMEtnVXZ2UGsiLCJtYWMiOiJhOTZkMzc3OWU3OGZiN2QyMGNlNjQ0NzYyNjIxMjI3Y2ZmZWRmZjBmZmMzMjBhNTllMTM0ZTQ3MjRmMTM5M2IzIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjhlaFpUZytDZis4MDFRYnRseEdoR0E9PSIsInZhbHVlIjoiRjFMNHJHM1Z1aWVOTXliajFwNXFHdDQ1RFB0ZG1xUjhvV21tWk10RlB6MlJaZGMzaGtzeHBPdVErd3dUdUtjNVNKUWhSYkFhZzBkZ055cW1ZanY5S2todWNKeGUzUFRHTzU0ZVYrRlBFem9YT0JIK0E3WWNGU0JQb29waW9HSHdXWklkNHRJL0JyRWY5d1pFU3pxMHJTMDhLM21DZTVad29QWSs4SFdqRC9EWERhOG1oLzFySjlaRzVQcnZ5Z3FxektWdEI2Z3NYUG1SRTdFakgvQ2tPZ1lUcXZhQURUVnBMbE52MXV3VXpVRlNSUmtKTnJvbGI2S0VZazMwSEZsRGR1WlJnbFRpaUZQY0w3YzRsSFJtRHpNT2xnQW8vUGU1MnZZNjNsM1NEK2toUUxnU0JlL0FiUTVkVGkzOTBEZ0Jab2YrSFFuTUxNcVMwbTZyN0xCdmY5cXdaM3VMM3dabStYbk1jTlRoU2xlVy90MHp4QXJ6a1ZTWDdHblRIS0FCWEdCM3JYVFpPUjd2T2Juak5JVVBwVU1HV1hrVGk5Z2E2NWptRERVcUpXbldDTkdYQnZsZkg5S3ZFVUZZSWptdHozS0dPVEQ1T1I3aXZiS3FrYmlYNVJxRzduYnRsNGU1TzlRRkFGejFnM2FsRjdqa3ZaUG1TL0g3L1dWUFRYTmciLCJtYWMiOiI5YWZhOGQ4Njc3ZmZkMGU0MzA0YzAzOWUwNjhmNDE2MWE0ZTVlYzU2NDE4OWI2MzY3MWI0NDllYzY2MDgyMDc5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2114310446\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-714244852 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-714244852\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2091211348 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:36:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik1Hck9oQ0JNVCtEam5YNDdkcHQyZlE9PSIsInZhbHVlIjoiMytBNXA2cHpoQ2NjNDU5M2FLV3JNV0tZK3lzaitaK1JHb0N4ZmVhWDk1WURKR2NnQzIvaG5QY1J6U0dJVHhWRGp1VEVib0R6dGU5U0ZIQ2dhS2VlM3Zab2dsSGtHbGpaWTd3a1dta2xXTG03MlYxckJLejFJdHVOcFh0VFBjMHh1cWcwTGMwbjFBemZUaTlNWnhJemFaS290QzhIUFdETnFqeWJWNE8vOEhmSVp5bXF5VzFFOVBjTDUycWFDR3Q5V2pBU3VyWFhpMWJxNlRBYWFVTnlDMzFYeWs2SkhQVmpqSlpIMGdBUmdteXpKTXVlbkN1UzR1UmlsK1RZdXQ0V3V2ZGV6dU5PNFdaMGFNd1B2b2xUbmxLMWJPSXV0NndqQzVzZFJjM2xtUlRSL0dWbDR6dDdWNmlNdG9Idi9oSlZKdzR1QmdxQ290ejBkMXY2MS8rekJWN3RTWjgveHNGR0tjaGY0SjkvYW9EbXpiendlVHV2NGUyZGk4QVRhbzRlUFhDZG5kSS9WUXRFY0k4MUNhZyswcUdpOHdDbUhnZitFMEs0NjRUaXBIMVBLcDU2QjdOTkRwb01HU0d1Uk5yam5sVG5iVE1OS3VxN1RMWitBU09GbFNsME90cjlhMHZaR0VQOGNiT2I0YVZuUTRaOWR0Nkk4alR6Q3FQYTdEMy8iLCJtYWMiOiI0ODhhYzRkNGUwZTBjMWUwZjEzNDA1MmQ2YTA2NzJhNmY4MDg5MzNkMzBlNzFjZDA5M2RmNDUyMjY1NTU5ZDBiIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:36:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im1nM1ZhYnd4bTFLQUw3RjUzYlFrNnc9PSIsInZhbHVlIjoib0NlTGhkNlliMXpXWmxITGQwRE5YTzZBV0dPV05ENmx0Z0lEamF2eG9JZk1JeFdDOVkvTTZlV1MxeWxKZlVUNzl5TjdERlVKOW9saStTQnJBMlJNNERCK2ovVldFRHpDT1llcVBJTDMwOTEwY2luNWxKK0xURUdqR2ZzR0VYV0Z3ODl3dUhDSTJSc2VQd3IyYUgyRkRQRmd4TkhFQm40enB2d2FHV2QydVdsRXM4UjRHZGNWV3FUMEFQaUtpWWMvUFRlYjBoaUR0NExZbzJsZTU0T0NDOWs0TU14SmRLUmc3eFlzUmJmM1d6cnZ1eC9CUjE5WVlpSjE1UDlHdStuMFRsVG1NLzEzNmczNlVDVVdQTWxCWTZQMEVnZWxCWDUrbEZTSnQ1VXQ4MGZEQ3JsTzRXZzVxS1hrL0wyMTdoMDhuYm0wUU15YXNwRHJtTEdRVVNpNGtZN0xaUnNaN3p4QUVvdXNHQnN0eksrNFgzWk03OTlScGI5S2tDNjYrbVplTm1nbzNpeEFqd1BBdHh0WE9JemY5WklSbzdMNmZYUEJFRTlTK3R2cldGZFJBV0dXUm9zWDRLbTAyTkxxS0xlR3JPeDIwbGIwekQ4WHFHS01LcENhOTNtZkZ5ZjhFeSs2UDU0WlFkQ1RJU0JHT2ZlUk5RZ1g4KzQ0alQwOVJ0WWYiLCJtYWMiOiJhYjQ5MGQ4Yjk5YzBmNTUwNjYzNWM4M2RjNmFiYWZlNjBhNTI0ZmMxYTYxY2Y4N2VhYTkzYzJkNjhiYmUzNWU3IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:36:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik1Hck9oQ0JNVCtEam5YNDdkcHQyZlE9PSIsInZhbHVlIjoiMytBNXA2cHpoQ2NjNDU5M2FLV3JNV0tZK3lzaitaK1JHb0N4ZmVhWDk1WURKR2NnQzIvaG5QY1J6U0dJVHhWRGp1VEVib0R6dGU5U0ZIQ2dhS2VlM3Zab2dsSGtHbGpaWTd3a1dta2xXTG03MlYxckJLejFJdHVOcFh0VFBjMHh1cWcwTGMwbjFBemZUaTlNWnhJemFaS290QzhIUFdETnFqeWJWNE8vOEhmSVp5bXF5VzFFOVBjTDUycWFDR3Q5V2pBU3VyWFhpMWJxNlRBYWFVTnlDMzFYeWs2SkhQVmpqSlpIMGdBUmdteXpKTXVlbkN1UzR1UmlsK1RZdXQ0V3V2ZGV6dU5PNFdaMGFNd1B2b2xUbmxLMWJPSXV0NndqQzVzZFJjM2xtUlRSL0dWbDR6dDdWNmlNdG9Idi9oSlZKdzR1QmdxQ290ejBkMXY2MS8rekJWN3RTWjgveHNGR0tjaGY0SjkvYW9EbXpiendlVHV2NGUyZGk4QVRhbzRlUFhDZG5kSS9WUXRFY0k4MUNhZyswcUdpOHdDbUhnZitFMEs0NjRUaXBIMVBLcDU2QjdOTkRwb01HU0d1Uk5yam5sVG5iVE1OS3VxN1RMWitBU09GbFNsME90cjlhMHZaR0VQOGNiT2I0YVZuUTRaOWR0Nkk4alR6Q3FQYTdEMy8iLCJtYWMiOiI0ODhhYzRkNGUwZTBjMWUwZjEzNDA1MmQ2YTA2NzJhNmY4MDg5MzNkMzBlNzFjZDA5M2RmNDUyMjY1NTU5ZDBiIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:36:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im1nM1ZhYnd4bTFLQUw3RjUzYlFrNnc9PSIsInZhbHVlIjoib0NlTGhkNlliMXpXWmxITGQwRE5YTzZBV0dPV05ENmx0Z0lEamF2eG9JZk1JeFdDOVkvTTZlV1MxeWxKZlVUNzl5TjdERlVKOW9saStTQnJBMlJNNERCK2ovVldFRHpDT1llcVBJTDMwOTEwY2luNWxKK0xURUdqR2ZzR0VYV0Z3ODl3dUhDSTJSc2VQd3IyYUgyRkRQRmd4TkhFQm40enB2d2FHV2QydVdsRXM4UjRHZGNWV3FUMEFQaUtpWWMvUFRlYjBoaUR0NExZbzJsZTU0T0NDOWs0TU14SmRLUmc3eFlzUmJmM1d6cnZ1eC9CUjE5WVlpSjE1UDlHdStuMFRsVG1NLzEzNmczNlVDVVdQTWxCWTZQMEVnZWxCWDUrbEZTSnQ1VXQ4MGZEQ3JsTzRXZzVxS1hrL0wyMTdoMDhuYm0wUU15YXNwRHJtTEdRVVNpNGtZN0xaUnNaN3p4QUVvdXNHQnN0eksrNFgzWk03OTlScGI5S2tDNjYrbVplTm1nbzNpeEFqd1BBdHh0WE9JemY5WklSbzdMNmZYUEJFRTlTK3R2cldGZFJBV0dXUm9zWDRLbTAyTkxxS0xlR3JPeDIwbGIwekQ4WHFHS01LcENhOTNtZkZ5ZjhFeSs2UDU0WlFkQ1RJU0JHT2ZlUk5RZ1g4KzQ0alQwOVJ0WWYiLCJtYWMiOiJhYjQ5MGQ4Yjk5YzBmNTUwNjYzNWM4M2RjNmFiYWZlNjBhNTI0ZmMxYTYxY2Y4N2VhYTkzYzJkNjhiYmUzNWU3IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:36:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2091211348\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1483888023 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1483888023\", {\"maxDepth\":0})</script>\n"}}