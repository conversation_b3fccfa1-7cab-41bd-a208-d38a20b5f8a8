{"__meta": {"id": "X46e95241089d788579bb883f55c55977", "datetime": "2025-06-17 15:02:38", "utime": **********.236669, "method": "GET", "uri": "/customer/check/delivery?customer_id=6", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750172557.653748, "end": **********.236694, "duration": 0.5829460620880127, "duration_str": "583ms", "measures": [{"label": "Booting", "start": 1750172557.653748, "relative_start": 0, "end": **********.175065, "relative_end": **********.175065, "duration": 0.5213170051574707, "duration_str": "521ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.175078, "relative_start": 0.5213298797607422, "end": **********.236697, "relative_end": 2.86102294921875e-06, "duration": 0.06161904335021973, "duration_str": "61.62ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44962072, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00515, "accumulated_duration_str": "5.15ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.216115, "duration": 0.00428, "duration_str": "4.28ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 83.107}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.225238, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 83.107, "width_percent": 16.893}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1686966988 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1686966988\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1966075464 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1966075464\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1052078090 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1052078090\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2045356375 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlA1OXlKOGpSaFlqeTRUQXRNSHpBalE9PSIsInZhbHVlIjoiRDJxRjhIbS91YnpxQzFEM29nenBvaVJaaDBoMWxGWGFaVDhkaEZObmtVZ0dhL0dFdVJ0eWkvelV2L3Y3NTZML3BNSHhPQnY2Mi9mUy9zcmJ4YU5lMzlwU3hGZG9XZ0hDOXI2aWw2b3VxdlVRSTg4L2llL1lUMDhkWlZRaXhqSlVrOTRpWThsaU9jaDNvRGJ4dmZWTkYwZkZZMy8yMkJTN2Jmcy82alRwMVRlZHFEcFpxYVJ0QmZQaDdwemhuU1luRTJvTWc4bHB0eEs3K3JCTzhwQ1RvWng1Z01DUDFaZ0lnYjFjVis1NXk4cWpFdHZ4ZFd1WTl0NnliQ3NDbnhNV3BsSDVWc0cxMkJOYXlqTVA0Sy9oWVBmOFZ6cjdPZ0FkWUkrck9FajIrcUV0S21NZVN1M09JZTN1dDh6d1laaDhJQ0FKVFQrUkQrQXl6WXVSbllwZXp3ZkhMNEN0OVViVWFHbXJ5eG9XSjM3d0dsRVFEbE9VdG5MVk1lQXE5QVdWZ2hWWjZrRkJlUVJWQjNQT2c2blB1em1UTGt1RXNJTWk3anVKU3RLazFwYmdpTityMmxYcFNqbXBSZDUrUGVsMXdiK0FacWNyWnRqYklZUU1pa1d1a0RoeWd0MjMwNVU2ZS9uNHRseVFQN1NSeG9hY0E5YXZhM2dFZHhuVUdjQm0iLCJtYWMiOiI4MTY2ZGM5ZThkYzg0YjkxNTcxZjM4MzIyN2IwZDFmZjM1YjViMzk2NGYyZTQ4OWNiYzcwMjE4OTQ4MTlmNjEyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InZ0THVCRmpYTkxhdEljRjhtekRwQWc9PSIsInZhbHVlIjoiSncwY3NJSjhOcnlScVl1ODBtd3FBLzFRR0c3OGZDc2tNb21OenRMcEQ3MVlManVSNVkyUDd2VmlQWDMzaGlBQWJuM0xtcUMrTDFnMmFFMHpPVVJRWFo2MnJuRXpXY3dIQVZPZ1RTSDZFcHFUOCtVUkV1TUZvRUhzUjNKYUNLUHovWDFoQ2NPWmFOSVVpeDFNTFYrR1VTZHJ2cVVGam9BVTgveTZFQ1gxQUVsUTlMQm9CdVozTkxIN1ZLQXp1L2ZDajUyb3ZQMm03YnVzUEFoaGdUd2xaWDdod1hSd0VTWVl6bkkvSHE5dm5FM2Ntam84WFpyU0tqV0h1QXc3T0xlYTdUcmh4SDdwUlkzRmNscUM0bE9zeE05UmY1Z3J0cGdUdFRqYWJHQ2Zhb1pJcDJVODhUWDQ1MzcvMFFSTmFhVktZeVpYU2F5RGFFVmZKQllCOTZpQjEvOFpDME5PZkxyYVM0aGIzUWxTQ2VwbFFMQmY5V21TMHNvNVpKMGhIVjVPeGhpL1B5dnNWdVU0YjZxY0t0RHExV2lBYTNnL2M4aFVuZlZvRThrMjg2aDJzbERhMjlLeTRKaDd1c1JSZ3ViUU5ydnpGdXlRdHVnL3JXQlJJZHFoTWpBWVpQSVFNQXA3WmtHa1RDWStrbnJnRmpIKy8wUyt4d0xYbHB4VmhIUnoiLCJtYWMiOiJmN2I0MTFiMjEwODMzMmUzZjAxYjJhNDM0ZDcwODRlNmEyN2ZkMTNkZGFkMjBjZGRlMTQ1YzI4NTBkMDY2ZmNiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2045356375\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1640660985 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1640660985\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1695210935 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:02:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBuZy9FL1dZeHM3VlZ1YmY2YzFoeUE9PSIsInZhbHVlIjoiQkcvTnBFbXVIZTRlYjNDTDR5U2xJWDlGWE1heTlVblIrMDNXcHFaejUraFZXUEkyOUFQc2RxYXl5Y0xHNTZNNStNTWNoak0rOXpoYzJNdmlCSUF4cmZ5YTB1WC9PWWVyZHZWVDYxTjY0d0VWR3p6a1puOTBXSXRPNGJUdzRiNy9Pb3g0UVVBeWhiZjZSTjA4T2pMeXF2bTNGKzB0Q2RDUDYzREZWSlV0d0h5cW1MeC9RM3RSSGdVeHROMkxBZDNXSnZjclVqTk8vajV0YzBNdXJoQW9SSTQ2MWVyb29JUlBYajZwMDNxeDJoMEdmUWVuWUpsU2xwYTlYSlVnRERyRFNXYnpzK05Pc1FtTGNkZmp4MWhOUVNGVUJ2S1NrOUtKMm1MVDFxTWFKWm1rc3AvTGhUdlVESnh4RkZvcW9lOHhIZWtPSUxsMDUyWXhLcW53bnF5eDNiazNvODNDNEVrYmk1cTZQTzFNY3VraGhZWlo1Yit0U1NwZ0Q5bDhSelJIV3JIOGRSVjBBR1Q3b2dMOTJ1NG40TDNiUDNVaE5nTWgyaDF4YVluZnNJSStDbXoya0ZzVlBXcVhhcjZRQzJ5Y1NQRTZvKzIwSlprNVArSUpLa2dwelRJdWNOdDJET3BsdlUxaTduMVQxOHY3SUUrbnd4eHhsc3BOVTBJbXpINUEiLCJtYWMiOiI4M2RkMzE1ODRjNDdhOWMwNDY3ZDEzMTliOGNkOTA0YzZkYjU5MmYwOWE0NzQ1YWViZTkyOTk4MWM1NDEzZjRhIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:02:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjU2NjlwUnczdERBVFhxeTE0aHpxZnc9PSIsInZhbHVlIjoiYTBoUGJydTJKZkpaYWJGcjNBaDUvM0hIdFpBcWV4ZXVzUnhqZmdOeW1NRmpXQUh4SHF1M3NCNlRnYnRxckpzSEVvaUJSQkFDT2xvVFduekMyUmZ0YmtxOGNnOVZMMSttRW43RUlmS2twTXBkVFV5amZ0QW95djVkajZSblBLcTNlQTh3a2VZTmxSSDFzWXVobjQ2UVlqNlFWOGswWEQ2aDRwemR0Vm1jbHlBcFhaTWVhZy92M2RSQk5qMmE5S2o2Vmt2V1g0ejJRWFlXWlhNaVhkUWs5VHhDY0VkWkcrSVNVS20vcnB4akYwa0ZYaTdyK0xUT3BpTmFxN1NZbEovWE4wb0VwRDVPRkNHQ0ZnRkROVk4xY3lJU2wzRFJ6cGpEak15YjFoNVV5cGJHbWFyT2ErZ2xwMTU4aWxPTkZzdVlFcWJSSlpmUWJKQlpsalZwMmZ4NUt1SUI0WDdDQ3RaSVQ1ZVBCOU9SSm4zZm1md3lpM09jYzdPdkFpSmI0RndCSTdMN3hpa0w0eEs0QXdKVlE0ZG1ZTy9WbEIzeE4rZW4yRFdiNWxDZzdXN3l4LzRqRVNhdjBmVWpURllFUHA0bkdveTFkSUR5UUduSjNyY1lrM3UrSWNHa1hlbW92Z1RMRHBXUmEwNDJMVXkydEQvdnBnbWI1YjlLdFN6dW9iRUoiLCJtYWMiOiIxYzMyYzRlOTVmODlkOWJhYjZkMGJlZWI4OGY5Yjk4YzQ1ZDk2YjQ0M2UwOTYxZjU1NWZlY2NjODAyYmYzZGQwIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:02:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBuZy9FL1dZeHM3VlZ1YmY2YzFoeUE9PSIsInZhbHVlIjoiQkcvTnBFbXVIZTRlYjNDTDR5U2xJWDlGWE1heTlVblIrMDNXcHFaejUraFZXUEkyOUFQc2RxYXl5Y0xHNTZNNStNTWNoak0rOXpoYzJNdmlCSUF4cmZ5YTB1WC9PWWVyZHZWVDYxTjY0d0VWR3p6a1puOTBXSXRPNGJUdzRiNy9Pb3g0UVVBeWhiZjZSTjA4T2pMeXF2bTNGKzB0Q2RDUDYzREZWSlV0d0h5cW1MeC9RM3RSSGdVeHROMkxBZDNXSnZjclVqTk8vajV0YzBNdXJoQW9SSTQ2MWVyb29JUlBYajZwMDNxeDJoMEdmUWVuWUpsU2xwYTlYSlVnRERyRFNXYnpzK05Pc1FtTGNkZmp4MWhOUVNGVUJ2S1NrOUtKMm1MVDFxTWFKWm1rc3AvTGhUdlVESnh4RkZvcW9lOHhIZWtPSUxsMDUyWXhLcW53bnF5eDNiazNvODNDNEVrYmk1cTZQTzFNY3VraGhZWlo1Yit0U1NwZ0Q5bDhSelJIV3JIOGRSVjBBR1Q3b2dMOTJ1NG40TDNiUDNVaE5nTWgyaDF4YVluZnNJSStDbXoya0ZzVlBXcVhhcjZRQzJ5Y1NQRTZvKzIwSlprNVArSUpLa2dwelRJdWNOdDJET3BsdlUxaTduMVQxOHY3SUUrbnd4eHhsc3BOVTBJbXpINUEiLCJtYWMiOiI4M2RkMzE1ODRjNDdhOWMwNDY3ZDEzMTliOGNkOTA0YzZkYjU5MmYwOWE0NzQ1YWViZTkyOTk4MWM1NDEzZjRhIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:02:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjU2NjlwUnczdERBVFhxeTE0aHpxZnc9PSIsInZhbHVlIjoiYTBoUGJydTJKZkpaYWJGcjNBaDUvM0hIdFpBcWV4ZXVzUnhqZmdOeW1NRmpXQUh4SHF1M3NCNlRnYnRxckpzSEVvaUJSQkFDT2xvVFduekMyUmZ0YmtxOGNnOVZMMSttRW43RUlmS2twTXBkVFV5amZ0QW95djVkajZSblBLcTNlQTh3a2VZTmxSSDFzWXVobjQ2UVlqNlFWOGswWEQ2aDRwemR0Vm1jbHlBcFhaTWVhZy92M2RSQk5qMmE5S2o2Vmt2V1g0ejJRWFlXWlhNaVhkUWs5VHhDY0VkWkcrSVNVS20vcnB4akYwa0ZYaTdyK0xUT3BpTmFxN1NZbEovWE4wb0VwRDVPRkNHQ0ZnRkROVk4xY3lJU2wzRFJ6cGpEak15YjFoNVV5cGJHbWFyT2ErZ2xwMTU4aWxPTkZzdVlFcWJSSlpmUWJKQlpsalZwMmZ4NUt1SUI0WDdDQ3RaSVQ1ZVBCOU9SSm4zZm1md3lpM09jYzdPdkFpSmI0RndCSTdMN3hpa0w0eEs0QXdKVlE0ZG1ZTy9WbEIzeE4rZW4yRFdiNWxDZzdXN3l4LzRqRVNhdjBmVWpURllFUHA0bkdveTFkSUR5UUduSjNyY1lrM3UrSWNHa1hlbW92Z1RMRHBXUmEwNDJMVXkydEQvdnBnbWI1YjlLdFN6dW9iRUoiLCJtYWMiOiIxYzMyYzRlOTVmODlkOWJhYjZkMGJlZWI4OGY5Yjk4YzQ1ZDk2YjQ0M2UwOTYxZjU1NWZlY2NjODAyYmYzZGQwIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:02:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1695210935\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1511056159 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1511056159\", {\"maxDepth\":0})</script>\n"}}