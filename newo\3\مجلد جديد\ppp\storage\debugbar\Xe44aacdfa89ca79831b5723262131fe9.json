{"__meta": {"id": "Xe44aacdfa89ca79831b5723262131fe9", "datetime": "2025-06-17 14:48:03", "utime": **********.289575, "method": "GET", "uri": "/import/productservice/file", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750171682.704211, "end": **********.289596, "duration": 0.5853850841522217, "duration_str": "585ms", "measures": [{"label": "Booting", "start": 1750171682.704211, "relative_start": 0, "end": **********.188036, "relative_end": **********.188036, "duration": 0.4838249683380127, "duration_str": "484ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.188048, "relative_start": 0.4838368892669678, "end": **********.289599, "relative_end": 2.86102294921875e-06, "duration": 0.10155105590820312, "duration_str": "102ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47994576, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x productservice.import", "param_count": null, "params": [], "start": **********.270291, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\resources\\views/productservice/import.blade.phpproductservice.import", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fresources%2Fviews%2Fproductservice%2Fimport.blade.php&line=1", "ajax": false, "filename": "import.blade.php", "line": "?"}, "render_count": 1, "name_original": "productservice.import"}]}, "route": {"uri": "GET import/productservice/file", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\ProductServiceController@importFile", "namespace": null, "prefix": "", "where": [], "as": "productservice.file.import", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=486\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:486-489</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.01611, "accumulated_duration_str": "16.11ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.234013, "duration": 0.01611, "duration_str": "16.11ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6", "_previous": "array:1 [\n  \"url\" => \"http://localhost/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/import/productservice/file", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1225018281 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1225018281\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-856846 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-856846\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1640997650 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://localhost/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=17t60l2%7C1750171681755%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlkxWERkMEhmczVaQ0ZOZnVPaWM5YWc9PSIsInZhbHVlIjoibEZIRS9TeU10WXVxeU5BU0VhQXBvVHZEQ2hoNURLWm94bURhQjcwUUxCbmVyRjFaejVMRUZpYlZrbWYzV3pzL0sydWNWdVkrazIvK0RMUll6QjlSdXBCNnc3RFJZS3VGNHdvUDlNUVp1Q3ZNblpmSnI4T0RTWnIyQXpJdktEVFRGaTgvZk1LdkYwVmhWV2RqQVI0THN1OU1UK2pZbm9RekxpRGpFb28xREdWMXFyTEhhMDFJNkc2ZzBSMFBISnF5dUszamxnOVdmY2puME9VblptSlhBTXVCTER0ZW9xbGhGYjJJYWdWR2EyaEZGZlBJbjlZNE4zMDJ5c0ZhemtMbVJUcjh5SWlaTGNLVDZMYmpObWRWK283c25OeDM3d0xhaGtoRUlLTS9FQ2QyRzRNOWZ6RWZBZzQ4c0VqQnk5SzlHK0loZVZnZGVaRnRvdVEyZitjZVRLQnlrQWN6RlVwc3FVcVZxT1dRZFlkTUFUS3dJUVFIODVDamJnVXRLSWZKK0Y0dGxhYW1kUHpEd3dRVVpGK1JURzV5bzlGeHdhRC9TYlIxSXc3UHNHcnpQN0lJTjJIK1NYTm1Ya2UzMllrcFBsWWxrWFd4NkxLRjJpSFpybVZMdmhacEJoS29JZTZVVHBVclBIUEF3b3VPRC9pM0dESDlwaTczVkZwSk04VlQiLCJtYWMiOiJmZDQ2MGQxNjk0ZDk5M2ExNWIxMjBiNTY5NDMxNjA1YjE5ZmZiMDRkODk5MTJkZGZiZTExOTVkZGJiOWRiNDQ1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjlQWklmYmVuc0NueTFUdTBzODFwNEE9PSIsInZhbHVlIjoiemRrMk4xMDU5Sy91RVJER0IzZGNCcSttc1BaZDhKcXBTaUZJVjhSYnVFSzcyN2hxSk12anY0RXpxQ1dseGdFMy9HTHBKb1hXNmJ3UG9DVmdWc0F6Q21pVU1FbVgzaitSdzkyTXM1eFhVYTVKZmYwaXd0QUZKZjlMeGV5UFZ0S21VNFM3U1hJdjR4enJ5QmJ0Q0l4dG1sVG90OE9WekQ2TVE4WS9nNWMrc3pxSFVTNXZqWWI1WDdkQ0VleHJqQWFFd2V0eFVEeUxDYmMwenV1K1UwSDJINTNHYUVZcU9kNkpPaS9MMFNWSUdHUS8rZFBrd1gzNDB3TzVJZGZQbjB4N3ZOaU15ck8xN2NUUzhYWlJoc245TlFCalRCclBqbHl3R3JZZEkzQVprWC9LekpneWkyYkZlZDl6TmxpK1B3bGs0ZzhoRzdhdVVrdjZGRnJPbjZyU3VoWkVrYUhPRW5MVWZ5NTNjUmhJekhRNWNwbDZvdWRjVlBudVZwUnJYZ094SUFnV0NVWjNSVUJmNFVtK1RKWm5GZjl3VzBVaEkvakFxZTN0V0Q2YkJIVmVYRk45aHVEdWxIeDZxNGEzNDNiSVFiUCs3QkdKY2V2K0dCR2o3SEIvSktSZ0VRbVBwSHIzZHZpNXFiYWJVSGV5RWFUSmViMjhSajJsazkxR1BxUDUiLCJtYWMiOiJmNzI0MDk2ZDAyOTIwMGE3ZGUxMGQyM2NhNWUzMWUyNGMzZGZmZGI5ODI1N2E1NDgxODM4ZGQ5OTFmMjRlNGFmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1640997650\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1399538607 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HxnOnSuhAAPTfN2VS4q58slgJoytwtHYqaX0oLoW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1399538607\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:48:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImtpeHJzZjZ1VjUxbEdkcTJ6d1Z3T0E9PSIsInZhbHVlIjoiSXhnOGh6blFnMU9HVjd5aUJYeTlZcjhPdThRL1MveEo4MzBvcUV0UEJMU2ZuUGp4QXRLT2Z5VXkrTXhvQnNvRnlNYlFJbjNIMHcrRVloVTdmR0tubTFPbXdERDZpZW5lWStQNFFmbEYwd05zR1VCZmdNam45ZTZKOUFjSDJyTnRQeHhheUp2Y0QzTVlqbThwNUw1eDR1ZldzcGJ5YWVUR1dsRGhaRkpsY1k0MlZDUTZPeFJGa09JMFpQcEpYVWZ6SGNvc2IwbmJHeUI5L1NwcmJEMUlSRVdiTllzMzBrVXpzYldmTzFPOUR5dkpPRzhRSC9JTSt3TEZWZ2tBLzlqeHZyMGlqUldzM0puVm9VTFo3UDR2ZkhLVldSK3IwSUlHa0c1L2RzMzB6YklGaDRsRm9NaWtuMHBDWW03aGNUNGwyWjIyUEpVSmFXMzI5dUkvYVZJSEdHWVRUbTVYbFRRRENhWWJ0R3lPc2FmMVdHQUdzSFJCNXNqc0VxOERMdEtSZ0xoVnQrUXRGSTErZllqWnZMcHB3djJLUTFNRVh0KzcrY1Izenovd2ZTNU9rblRPRDNrZkp6UDJTYis4NHRlajlrY0VWVU90SEo0aG1nZEtzT1VPTGVpNGtXakF4SnBTeXBDS1NhQjJHbDhvTm5wTlBFd1BNWlVUU2ZHRGxwb1YiLCJtYWMiOiIyZTc0YjgxMzM5OGJiZTg4ZDBmMWY4MDhkMjI3ZmRjM2Q4MDFhYzcwYzE0YzgzZTY5MjVlYWYyZjU4OWYxZGRlIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:48:03 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlpPa1dVbVJYODRlUkFqbi85MVB3bEE9PSIsInZhbHVlIjoiN2lSS0hEUGdXR2pYSk5hK2ZPZHJ2TGxWV3hNUU83eTdBd0JDREtodEV4VjljMEVjTkM1STE1L1hka0tUM1JXSXJqdnRScUJub0Rtbk5SMWdLZkwrUytFUVdyU1hBZU1VV1pnQXFxbkV6bmd4YlQ0WGtpWE9tZ3BUYzkxQ21DWGhFa1poVkhJeTFTbjBGRGJjSFV0ampicmtNVUVKcXU3L3RhNGFNYnYwclphejViMG1PV3JNZFhraXF6S2JNUFN0KzA2czlyckZ4TENBOUxRY1o4enVWK1VpUzBuSE1ZVnhSaUJPK0R6U3lHVytZMmZONUVWYjZMMkVNN2xhTi9zSG1oMkVkNTZFOVR4MDZ1dm8wU0dXaFo0TFcrdUp3aWk2TWxxVVFtQ1lrb1JndkdkVW5HYmk2M3Znay9TYktSMjFwSnBVWGUrUFRMVTBxeC9XZEM4ZDBjaDJlbHR0RDRIOGpiTS9PNnpjRVFwY3RhSjdlanh0RXJnaE8rUjYrWDNXZWpWU1pyNTFzakJjNi9CbEUwNkVaNzVSdFhmcDNnaWxxQndPMHZrZjR2NVBuVm04eDNhV3Bzd1JpcU0wdUNoaGdXREZVV2VtWDliQjYwd1M4bzdGUm8wRTEzbTljUENTMFI1OERDdXhMeVRBOHJhOVhXNk9YcVhyQkFwMEd3WTYiLCJtYWMiOiIxNGUzMWZkNTk2YWFjMDA2ODE3N2UxMzk3NjJhY2JhMzJiMGM2MTY0ODI4ZDRhNjAwNGYxZjg5ZTk5YzIyMDlkIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:48:03 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImtpeHJzZjZ1VjUxbEdkcTJ6d1Z3T0E9PSIsInZhbHVlIjoiSXhnOGh6blFnMU9HVjd5aUJYeTlZcjhPdThRL1MveEo4MzBvcUV0UEJMU2ZuUGp4QXRLT2Z5VXkrTXhvQnNvRnlNYlFJbjNIMHcrRVloVTdmR0tubTFPbXdERDZpZW5lWStQNFFmbEYwd05zR1VCZmdNam45ZTZKOUFjSDJyTnRQeHhheUp2Y0QzTVlqbThwNUw1eDR1ZldzcGJ5YWVUR1dsRGhaRkpsY1k0MlZDUTZPeFJGa09JMFpQcEpYVWZ6SGNvc2IwbmJHeUI5L1NwcmJEMUlSRVdiTllzMzBrVXpzYldmTzFPOUR5dkpPRzhRSC9JTSt3TEZWZ2tBLzlqeHZyMGlqUldzM0puVm9VTFo3UDR2ZkhLVldSK3IwSUlHa0c1L2RzMzB6YklGaDRsRm9NaWtuMHBDWW03aGNUNGwyWjIyUEpVSmFXMzI5dUkvYVZJSEdHWVRUbTVYbFRRRENhWWJ0R3lPc2FmMVdHQUdzSFJCNXNqc0VxOERMdEtSZ0xoVnQrUXRGSTErZllqWnZMcHB3djJLUTFNRVh0KzcrY1Izenovd2ZTNU9rblRPRDNrZkp6UDJTYis4NHRlajlrY0VWVU90SEo0aG1nZEtzT1VPTGVpNGtXakF4SnBTeXBDS1NhQjJHbDhvTm5wTlBFd1BNWlVUU2ZHRGxwb1YiLCJtYWMiOiIyZTc0YjgxMzM5OGJiZTg4ZDBmMWY4MDhkMjI3ZmRjM2Q4MDFhYzcwYzE0YzgzZTY5MjVlYWYyZjU4OWYxZGRlIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:48:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlpPa1dVbVJYODRlUkFqbi85MVB3bEE9PSIsInZhbHVlIjoiN2lSS0hEUGdXR2pYSk5hK2ZPZHJ2TGxWV3hNUU83eTdBd0JDREtodEV4VjljMEVjTkM1STE1L1hka0tUM1JXSXJqdnRScUJub0Rtbk5SMWdLZkwrUytFUVdyU1hBZU1VV1pnQXFxbkV6bmd4YlQ0WGtpWE9tZ3BUYzkxQ21DWGhFa1poVkhJeTFTbjBGRGJjSFV0ampicmtNVUVKcXU3L3RhNGFNYnYwclphejViMG1PV3JNZFhraXF6S2JNUFN0KzA2czlyckZ4TENBOUxRY1o4enVWK1VpUzBuSE1ZVnhSaUJPK0R6U3lHVytZMmZONUVWYjZMMkVNN2xhTi9zSG1oMkVkNTZFOVR4MDZ1dm8wU0dXaFo0TFcrdUp3aWk2TWxxVVFtQ1lrb1JndkdkVW5HYmk2M3Znay9TYktSMjFwSnBVWGUrUFRMVTBxeC9XZEM4ZDBjaDJlbHR0RDRIOGpiTS9PNnpjRVFwY3RhSjdlanh0RXJnaE8rUjYrWDNXZWpWU1pyNTFzakJjNi9CbEUwNkVaNzVSdFhmcDNnaWxxQndPMHZrZjR2NVBuVm04eDNhV3Bzd1JpcU0wdUNoaGdXREZVV2VtWDliQjYwd1M4bzdGUm8wRTEzbTljUENTMFI1OERDdXhMeVRBOHJhOVhXNk9YcVhyQkFwMEd3WTYiLCJtYWMiOiIxNGUzMWZkNTk2YWFjMDA2ODE3N2UxMzk3NjJhY2JhMzJiMGM2MTY0ODI4ZDRhNjAwNGYxZjg5ZTk5YzIyMDlkIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:48:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://localhost/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}