{"__meta": {"id": "X1fdd6da5408f309a65ab3e93982dc943", "datetime": "2025-06-17 15:06:00", "utime": **********.866057, "method": "GET", "uri": "/customer/check/warehouse?customer_id=7&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.289003, "end": **********.86608, "duration": 0.5770771503448486, "duration_str": "577ms", "measures": [{"label": "Booting", "start": **********.289003, "relative_start": 0, "end": **********.760707, "relative_end": **********.760707, "duration": 0.47170400619506836, "duration_str": "472ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.760724, "relative_start": 0.47172117233276367, "end": **********.866082, "relative_end": 1.9073486328125e-06, "duration": 0.10535788536071777, "duration_str": "105ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46248248, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02288, "accumulated_duration_str": "22.88ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8124459, "duration": 0.02094, "duration_str": "20.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 91.521}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.8484828, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 91.521, "width_percent": 4.065}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8540611, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 95.586, "width_percent": 4.414}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-2072611188 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2072611188\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-22771343 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-22771343\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-903864834 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-903864834\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-577502846 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im4rRTFyNXFuQ3J1OFVqM05FdUZGNlE9PSIsInZhbHVlIjoiVnZzdm5jYkJYd2IwZWU2Y0RjZG1xb2ljUjlFV0Z2TFRwMmFVak9WWERLZEUrdFdPOVhObnVOUHdJSEJvVVZPVWZsS1lIMGdhMGNVekk2QldhbkR2a3RnYUlIMktXT01sN0JFejh0ZGVRWlpBY1AyRUdIaGdRcVJtMkt1WjFnZVpudHErajZadzVKVzNuT0lJL09LSjZaeFpJNzF0MVVDQkJESkF1cFdPRllNaDJnMkQrWTZNVkwySWVYNjh2SzBUemFodUlINGVKM3JkZFF6UnZ0bVBtM0NXZjdZM0lhWThTZGlJL3RiTkNhL1h4Q09oMUpXVUY0bnB0ZnN1Qk1sSzlTYlNBTkdMeFkvUHZ0R2t1RG1NNklFSm13K05YZ20vNzZYWm9KU1RIY0N4WVhJUThLa0liRVE0UkxHaUkyTk1DWDQ5TlNZanJKakFjRXFCWFd4OEtBS0ZJeHo5dTZ5SC9mRURkNzlSc2RHalJ2dUdlaWFydFBscEpmUmkvdThjTnRBKzhKMkRjUERoS245SmpOdGxmOWtKZWt6NUd5bTBCQlBNZmJadDBzUXV1RE5PSWJiblQ4OWJKcGdjSldzaUN1Z2NVczhQc0pSME55VVJJQklXcEhHMnR1K1N4bGM5emZrKzhFT0EvVEhmWWJROVd2QW5UVVpaSXB0SHZlbmkiLCJtYWMiOiI1ODEyMDhlY2VjNmVhYjQ0MWUxODhhZGVjNzZhNDFjYzM1ZmViMTQwMDNjM2RjM2Q4ZGQ5Yjg3Y2Y3MWQ5NGMyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IndWdkVjMVc4cFViQmwzNEYxUE5Za1E9PSIsInZhbHVlIjoiemcvN0VOLzJSZWxndzlVeWg5c1FtSThmdGtzcUxzUzVoK2hVWExPMngraVV1biszbzNXanVQS21ob0hXZEgxaFREOUl1bW9oUEtGZUpmWm9XMGljSURjWHlaeC9IVmlzdlY1YmwzVk9pKzBjc0VzV2lybHpYL0JYVHpldHNkQ21VeWd0MFc1MUR3N1d1TERJRm1KeENnTlN1U0JIakJpLzVDVzVuRHpSWkRwWHA4UlZjTW0rVmJpV2lnZVFOaFlEbEpaQjNtSjlibVFrUjJmOHNtajZjd0dXNDZkektrMlBIcHBOTm53eUlKTVFnYVowaXBDY0pQN044UWhvazRKcUxiKzFBaGRybmNLd3Y3NjJybitScENDaEN4RVRIVjFmNjhMRGVjZEU4K2t4RFk3M3pvdVVjdUtwdi9YTzRQQTkraFk2NnYrOUwxZ21Ib3ZWTU5LUWJJeTN5YjM1dVYrZWs2OHkweXY2UHA1MEFvMTNpamNhRUJ5a1pBemVtQitaR2xGd0lKZGNjM2c3VmFEZDVJRGFjNElNLzFEdHVBZlNweVR4cm1VSFRadWQ4cFdFdXQzVlFyNnBoQWdEMUNmRDRLbXNhTllveXljcmpOY2NHY3lsK2liRFNLdmkzclNROXFweDYvc2tHR0QzM2dKR1YrVHJQMXJSYnhmeG1LNmUiLCJtYWMiOiI4ZTM0NzZiZTU0NzllOWFjZmViYmMyYTU4Mzc5ZTU5Zjk0NDllNGViMWNiMGRhYTlmZTljNmUzOTYxOGJjZTMwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-577502846\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-366572600 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-366572600\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-992376258 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:06:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ilg1MUsyMm8zcEFWZ2RPaS9nYUtGTnc9PSIsInZhbHVlIjoiUForOUJBU1JpZFM2U0QzRVBsZGFtVzJoeXhuak9jRUU5YXp4QnV1bnc3TUNxakFMT25KeHo4aDRzUzRKRnd3YnFwSVlmMlcreUZLQWJMQm0wS0ZNS1d5SDhpZnFYWE9zNWkrS2E0cXBlU3BBVGl2R0hpVkRydjAzSnhOVWM0aGFaQWhZN0hOcUVIOGU5RWdINzFQcVpQWkt1SnVidjJkYXdtdWJ5bmlzWXFaTkswdXlkcUxhZFF0VTE5KzhuN2NnQUJKcVMvVkpQUmJudXl6Tmlpbmg5dUNvNTBQUEI2ek5TZ1RZQ3BoaElFVSt2c01iUGZIWVZqU2hpczVSUlBnckdRaEhkVFRhZ3orM2hTMDJLTUVsNFhpdFB2QmxoS1pDZjV4QjdoZGtHOXVieUhJd1lLTFdlRVcySCs1MVpHMkw4eG1hajRKMllaQjVObmZvem50VG4vQzdOcFRMbnJrd1I4M0hxcW9jTDR1LzNONDgwd0YrNWFNeEF6ckM0SndyVlpWQmtrT1FDSm5kUEFUdTJkSzlkbkxOelVuNE52UzQxZ2RDLzNqUTRFYkRJbWh6eTF0VERWK1hmOXFwdGdpNW1KajBJOW9KQXNNNTAxNDBMOTUwQlJ2SHlIQ1ZpeGJiTmNRbnpZcE5lZGxDcXZ1N2ZIcEExaUQyYWR6eFN3V1giLCJtYWMiOiJlNjEwNGI3ZWFhYTQwMGFhZWRjYWExN2FmM2JmY2NiOWRiOGRkMGRmODE0ZjhlZDc1NTRjZDM3NTJiNzI4OTFiIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:06:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkRZeWxFdDByREdqRm5WaDNNM1pPcHc9PSIsInZhbHVlIjoiVzNZVmluYXFqUlVzUGwxZFdaemZ2ZlJ6YUdsYjBlUmZlMXhaYm9IekhOT3ExT0RJaG5KVDlGM1NsL2dZVkZyYlpUTng2VHVyb1d6L2JobUM3dDN3RHJabHFQS1NiN1l5U0lra1FWZWhDanJkU2ZLQjBMNTIvTk5VVlArNkNHQkZXWDJ3UmxndWRrZERlTWx4WDd4SFdoL2ZlQUEyRVgySHN4RzRsMXFYUWo4THFCd3UrQnEwK0VkTHAydmZVb3NkQU1MejFnU0k4R2ZDK1E4UU5iZ1d6eHJ4V2YrQUhJbmdnb2pJN2ZCdmhnK2xEUE9DZTgxdDN1K2dpa1BZb1I3ZCtWT2RNY2NSRUNyT1hPMVlnS3NBNWNnaWlxVXkvZlFHcFhrVjN4V3IxeWk5azhNRzgyaGVZcSswa0dRb0d1blIzeWJBY21RdktHbk9SNGczMjJoUFpUUmVUQ1NvRkt1RDV4eVZFWldUUDdzR3pXQVJ1MjEzeTljWFlheDRlSUpVUHF3SEhkTHRHMVFiT0M0L0lPM09ZRld3RElCbmV5Z3dHV2RXMnZNaTUxcm5iUXplS0JLWjRKUCt6bVNjOTZ6dEEwa2NBL1c1dzlkNWFkdXpVR3FJTGtUZTFPNXRDS0V3VEphbWtORTNhYkk2YjRNZEVOekVUVkZjbmNtR1BjbEoiLCJtYWMiOiJmYTQ4OTQyZTU3MzA3MmNmOTgxYjI4ZDBkOTMxOTQwNzM0NmJmZWI0N2UzZTdjYTdiMzAyMTQxN2VhNjQwZWY4IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:06:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ilg1MUsyMm8zcEFWZ2RPaS9nYUtGTnc9PSIsInZhbHVlIjoiUForOUJBU1JpZFM2U0QzRVBsZGFtVzJoeXhuak9jRUU5YXp4QnV1bnc3TUNxakFMT25KeHo4aDRzUzRKRnd3YnFwSVlmMlcreUZLQWJMQm0wS0ZNS1d5SDhpZnFYWE9zNWkrS2E0cXBlU3BBVGl2R0hpVkRydjAzSnhOVWM0aGFaQWhZN0hOcUVIOGU5RWdINzFQcVpQWkt1SnVidjJkYXdtdWJ5bmlzWXFaTkswdXlkcUxhZFF0VTE5KzhuN2NnQUJKcVMvVkpQUmJudXl6Tmlpbmg5dUNvNTBQUEI2ek5TZ1RZQ3BoaElFVSt2c01iUGZIWVZqU2hpczVSUlBnckdRaEhkVFRhZ3orM2hTMDJLTUVsNFhpdFB2QmxoS1pDZjV4QjdoZGtHOXVieUhJd1lLTFdlRVcySCs1MVpHMkw4eG1hajRKMllaQjVObmZvem50VG4vQzdOcFRMbnJrd1I4M0hxcW9jTDR1LzNONDgwd0YrNWFNeEF6ckM0SndyVlpWQmtrT1FDSm5kUEFUdTJkSzlkbkxOelVuNE52UzQxZ2RDLzNqUTRFYkRJbWh6eTF0VERWK1hmOXFwdGdpNW1KajBJOW9KQXNNNTAxNDBMOTUwQlJ2SHlIQ1ZpeGJiTmNRbnpZcE5lZGxDcXZ1N2ZIcEExaUQyYWR6eFN3V1giLCJtYWMiOiJlNjEwNGI3ZWFhYTQwMGFhZWRjYWExN2FmM2JmY2NiOWRiOGRkMGRmODE0ZjhlZDc1NTRjZDM3NTJiNzI4OTFiIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:06:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkRZeWxFdDByREdqRm5WaDNNM1pPcHc9PSIsInZhbHVlIjoiVzNZVmluYXFqUlVzUGwxZFdaemZ2ZlJ6YUdsYjBlUmZlMXhaYm9IekhOT3ExT0RJaG5KVDlGM1NsL2dZVkZyYlpUTng2VHVyb1d6L2JobUM3dDN3RHJabHFQS1NiN1l5U0lra1FWZWhDanJkU2ZLQjBMNTIvTk5VVlArNkNHQkZXWDJ3UmxndWRrZERlTWx4WDd4SFdoL2ZlQUEyRVgySHN4RzRsMXFYUWo4THFCd3UrQnEwK0VkTHAydmZVb3NkQU1MejFnU0k4R2ZDK1E4UU5iZ1d6eHJ4V2YrQUhJbmdnb2pJN2ZCdmhnK2xEUE9DZTgxdDN1K2dpa1BZb1I3ZCtWT2RNY2NSRUNyT1hPMVlnS3NBNWNnaWlxVXkvZlFHcFhrVjN4V3IxeWk5azhNRzgyaGVZcSswa0dRb0d1blIzeWJBY21RdktHbk9SNGczMjJoUFpUUmVUQ1NvRkt1RDV4eVZFWldUUDdzR3pXQVJ1MjEzeTljWFlheDRlSUpVUHF3SEhkTHRHMVFiT0M0L0lPM09ZRld3RElCbmV5Z3dHV2RXMnZNaTUxcm5iUXplS0JLWjRKUCt6bVNjOTZ6dEEwa2NBL1c1dzlkNWFkdXpVR3FJTGtUZTFPNXRDS0V3VEphbWtORTNhYkk2YjRNZEVOekVUVkZjbmNtR1BjbEoiLCJtYWMiOiJmYTQ4OTQyZTU3MzA3MmNmOTgxYjI4ZDBkOTMxOTQwNzM0NmJmZWI0N2UzZTdjYTdiMzAyMTQxN2VhNjQwZWY4IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:06:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-992376258\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1731723795 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1731723795\", {\"maxDepth\":0})</script>\n"}}