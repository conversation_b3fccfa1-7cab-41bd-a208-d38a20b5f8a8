{"__meta": {"id": "X67f41b5911bc341bebdca682821833c6", "datetime": "2025-06-17 14:00:36", "utime": **********.598795, "method": "GET", "uri": "/customer/check/delivery?customer_id=6", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750168835.940697, "end": **********.598818, "duration": 0.6581211090087891, "duration_str": "658ms", "measures": [{"label": "Booting", "start": 1750168835.940697, "relative_start": 0, "end": **********.517658, "relative_end": **********.517658, "duration": 0.5769610404968262, "duration_str": "577ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.51767, "relative_start": 0.5769729614257812, "end": **********.598821, "relative_end": 2.86102294921875e-06, "duration": 0.08115100860595703, "duration_str": "81.15ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44964848, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.019629999999999998, "accumulated_duration_str": "19.63ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.564296, "duration": 0.01823, "duration_str": "18.23ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 92.868}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.587502, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 92.868, "width_percent": 7.132}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:3 [\n  6 => array:9 [\n    \"name\" => \"خضار  فواكة\"\n    \"quantity\" => 1\n    \"price\" => \"9.00\"\n    \"id\" => \"6\"\n    \"tax\" => 0\n    \"subtotal\" => 9.0\n    \"originalquantity\" => 7\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  7 => array:8 [\n    \"name\" => \"ww\"\n    \"quantity\" => 1\n    \"price\" => \"9.00\"\n    \"tax\" => 0\n    \"subtotal\" => 9.0\n    \"id\" => \"7\"\n    \"originalquantity\" => -5\n    \"product_tax\" => \"-\"\n  ]\n  5 => array:8 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"id\" => \"5\"\n    \"originalquantity\" => 43\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1833492014 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1833492014\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1124770725 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168503247%7C8%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InBHQndFSGdSL0p3akhValdJUGJzUWc9PSIsInZhbHVlIjoiaW4wbUV0QkZtTjlVK0drdG4rTmxya0VLR0V3VzZTcks1K3Rqb0JvR3Fkcm9CRXVDNk5ZTEdGbmp5UkFSNUhacnRrcXRPTkVpRXlCd0pDRnZTRTVhNk03d1ovejZocXA3K3krVVBFVEl6eGpOeUcvbXJsYklSUXQ0Wnh3dUV5dXlDWXdNcGdLMGtZTlBJMktTV1JzTFZ1UnZlQnFmRitkRnhRYnNlcksyZFpIcUE2Mlp1dkZMMHVmMjlLTm13cFBuR1kxYWZidEJYMVc0TG9LZHJJRno5OVFNeWZSdEJCUXRCbU9pa1Rzc3Fhd05DbndzdkNXa3B6QjJLcGdUeHdhSWhSbzFPbElkTFFEV3dsazdKTXhEcnRleDVHbGY0aDN3bmZUdWtYV0RSSm1RQjNHak5PYkFPNUhWWTFZWFZsaGFrVGVtbjJTZWIxa0J0NWxoREUzNWdQd21jcjdzMUdmeDFJREtlMGF6Mi9mNTRVQ2FQQ0R2bE1GdmNEdjZ4cC9oRDM3UHk3TU13L0hhUFpva0g5a2sxR3dvTUFCSFhtK0V1c0RieEQwS2pweUEzelVyVlpqZ3hYZnBzVU91c3lpN1VMUk52NW8rZG1SdjBvU1ZZOUVrV05CZ1FWcE1sOUxPSitiTlIrS3RXQXhsTDFKVXM0SGxRMW9sUXlhc3RQTHMiLCJtYWMiOiIwYjMwZjAzMmJjNzBlYmRiZTc0NzQ0ZjljZTYwMzFhMzJhOGI3OWE5ZDE0ZmM1ZTQ5ZTQ2MjBlOWQ2NmJjMjE1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ii9jTTN3SVpObFZ1TlhWYzFXQ0ZVTFE9PSIsInZhbHVlIjoiL2VsdTFNalJqSFdXUy9NZUkzQzVCRHlsTi9sc0dEWXhZMWtEazhTc2NiaVFhTVIzYW9jVzhsdEFoRmVhb3M2cUdZWEo3cnpvQ1BFUlczN1V0RVNzaTR6VStISUdDSEJVdDRvMXRwaGExbFdpSlNoU2Q4Lzl5ZHBOL2U2Z2VXdmp3SW13d05YYjNqNnRFY3BwT2R3UXdhM0FSdUhJMVJraTI0TmdnMXdDTElhcVhEZHdRUDRrQk9sQmozcmhvazRsbW10d2M0UytkN2FoOGJ5QTNFNXhQWWtKamdackIxTDVwOGdPWHg1elQyckdGZ211UXpyMnVPT0RvbE9GUVg2Wm41Z2NDdEtMaVZIOFZHamFhb1FIWkxDekROeW9YWGRramU5d01BTndDRHFZZWE5MHBCTUdSZmlzTmllQUdQSG9wY2xocXZzVFJLZHljMUpJYng4NGxQNXZGY1hXNWdoWDBER1oxQ2dGdTFoUUNqdzVHYWZlOEJ4ZWxkYS9CY2swWC9vRzY3M3FGeXVSV2l3VVhrRDVwcnpOemJBU2UzM1hBZ0JXNER4SlFTZXJ4bGhZVnBHdFRRcXQ2MFI5d1p3SkVZRXdGMUR4c09ObHhSakR1amRQSjNlcSt5dVo0U3IrVDlLQkUxVzFZY1Z4dlR5Z3ZzWW40dmZYZDVPdk4wbFAiLCJtYWMiOiJlOTJjOTE1NTc3ZDA5MjA1MGQ4YjllMWRjZTQ4OGE4ODljMzYzOGE2ZTJkNzFiMWRlY2IxZjllOWJkYzI0ZTkyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1124770725\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-10078174 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-10078174\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-277890569 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:00:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkRpOHFJUHhuaTJBOTNmSExIdDJiVEE9PSIsInZhbHVlIjoic1A0cCtIT0hjYzRWMDlZVW52eUVsUkhWVjdGQ0xQQ0svd0FrTXkwNVgrZS9SeDhTeUVsM3JMVUNub2xmRW4zT3FtV0JlYnR6U2I5d2Z3QVYvS1RadUI1cTdoYnJlNi9SdWFRa1NWN0hFL0trQ25UcWxObWpiMWJDdHkrMlcxQ1N1TCt2SGVPYjQxL3d2QjdUUmZvdlAwR2tyOW93ZGJZekNBbVY3cERmTExFOE42RFY3MmRzS01aV2ZzZnB5YStmU2pjbXltQzBkbk02ZnBiTVRzQmZxc1B4czFtVVlVRDgxREtwaTVBYytCV0dXMkZFOTZRTU1xbkhmanVhSEhSNTZ0WTlCYXp4YkpiT1dPWE9ua0c2R29aaGFmQ2U0TFYybk51Z3lHWXdxR1V2RS9kYU9pSThGVkdtZW5YVnUrbTlaVU1JTEJ6YnNYYlNnb3ptcE5zNVR1Nk55RGNFOWxGaXdNdzNYNkwra29OYkNTUmNaRFpUUVpDcVNHMFlEQSs2QVFqVnJKRzYxeFd3aGFKTFlpdWRwYVNBclVNdWhYQU5ianF2Z0lPUU9aNHpvM0Z1cTJjcnN2ZnFEODFjZHBVaFdiZ0g2ejM1ZHZOaVZaOGZva2EyL2Zqb2ZqSGpiWlJXY3FOZ1RMbzBZRWpTd2ptVDlWQ0RKRk42Z2dSYXp6ZFciLCJtYWMiOiI2YmExNTU4MzFkMGQ1OTgyYWNiOTFjZjIxNGE1NDBmZTE1ODkyMjQzMzIyYmJlY2NhMzIyZWE2MTM5MGY1MWE3IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:00:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImZaR2pObzRoUjVJczEyQ1FuczFtckE9PSIsInZhbHVlIjoiTGtoSnVOUkxCMTgzR1FpY0FUYW84ZnFpVitua21KTEdSZXpyU0x1OWxUWkNDSFpMUzgxTGNUcmdjR1lMQ2RsUWZzMHVYYnFrOFBEbnd3bmVna2xidHZieVFNNTI0WDZQY1R6Q0kya0ZyWGF2eFN2WVFuNWNoc3NDSHFZZlBOSS9DdXQ0d05QbDJoZkRkSHRTNWY2RCtHalQvOG1hNEJ3TG1pLzVpeGw1dENqaHMwak1TRjBXTXZlN1VIalR3dHc5RFRaS2Q1MWZKcDBaajEvR2tZSmlndzJEeEVBQU51OUVOZ01VSEtvanB5QjRIYXFCZTltcjVhRVpEZ0p6MnJMbERKdmpBMU5uWVJDOGw3ZkU2d2ZWR0VvRGxVWVhSNEhkZ0JURm9EbXdMV1FVdzBzN2ZCK2dJMkRkMjNCR2ZiaGNOVS9CNERKaGtNZ1B2N2NraDMrVzQzL1BzY00vYUxweGRPOU5LdGdHcVJ6cjNVM1I5TXBvUStVbGVFazlHc25wV1lzZjMyQThxamRWQnUzQ2VMK2NSNXRadnhScldNdnRwaGU2eWlHUWNQK0dTenFxMVY4Wk4yQXdsVC9jNFZ6ZjdHa1VacXd2My94aGMyTXAxRHhQdjQxVzJKRmhaVThTNXVVNk40dnl1TjM3R2psTjExZU1FdjYyTUNRMTJWaTQiLCJtYWMiOiJlNTlhY2I4NjY3OTkzODllMDkwMmM5YmUyZTJjZjQyOTI0ZDdhODIyOGI2ZDVhODdjOGQ4ZWYxMDA5YjgyMjdkIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:00:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkRpOHFJUHhuaTJBOTNmSExIdDJiVEE9PSIsInZhbHVlIjoic1A0cCtIT0hjYzRWMDlZVW52eUVsUkhWVjdGQ0xQQ0svd0FrTXkwNVgrZS9SeDhTeUVsM3JMVUNub2xmRW4zT3FtV0JlYnR6U2I5d2Z3QVYvS1RadUI1cTdoYnJlNi9SdWFRa1NWN0hFL0trQ25UcWxObWpiMWJDdHkrMlcxQ1N1TCt2SGVPYjQxL3d2QjdUUmZvdlAwR2tyOW93ZGJZekNBbVY3cERmTExFOE42RFY3MmRzS01aV2ZzZnB5YStmU2pjbXltQzBkbk02ZnBiTVRzQmZxc1B4czFtVVlVRDgxREtwaTVBYytCV0dXMkZFOTZRTU1xbkhmanVhSEhSNTZ0WTlCYXp4YkpiT1dPWE9ua0c2R29aaGFmQ2U0TFYybk51Z3lHWXdxR1V2RS9kYU9pSThGVkdtZW5YVnUrbTlaVU1JTEJ6YnNYYlNnb3ptcE5zNVR1Nk55RGNFOWxGaXdNdzNYNkwra29OYkNTUmNaRFpUUVpDcVNHMFlEQSs2QVFqVnJKRzYxeFd3aGFKTFlpdWRwYVNBclVNdWhYQU5ianF2Z0lPUU9aNHpvM0Z1cTJjcnN2ZnFEODFjZHBVaFdiZ0g2ejM1ZHZOaVZaOGZva2EyL2Zqb2ZqSGpiWlJXY3FOZ1RMbzBZRWpTd2ptVDlWQ0RKRk42Z2dSYXp6ZFciLCJtYWMiOiI2YmExNTU4MzFkMGQ1OTgyYWNiOTFjZjIxNGE1NDBmZTE1ODkyMjQzMzIyYmJlY2NhMzIyZWE2MTM5MGY1MWE3IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:00:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImZaR2pObzRoUjVJczEyQ1FuczFtckE9PSIsInZhbHVlIjoiTGtoSnVOUkxCMTgzR1FpY0FUYW84ZnFpVitua21KTEdSZXpyU0x1OWxUWkNDSFpMUzgxTGNUcmdjR1lMQ2RsUWZzMHVYYnFrOFBEbnd3bmVna2xidHZieVFNNTI0WDZQY1R6Q0kya0ZyWGF2eFN2WVFuNWNoc3NDSHFZZlBOSS9DdXQ0d05QbDJoZkRkSHRTNWY2RCtHalQvOG1hNEJ3TG1pLzVpeGw1dENqaHMwak1TRjBXTXZlN1VIalR3dHc5RFRaS2Q1MWZKcDBaajEvR2tZSmlndzJEeEVBQU51OUVOZ01VSEtvanB5QjRIYXFCZTltcjVhRVpEZ0p6MnJMbERKdmpBMU5uWVJDOGw3ZkU2d2ZWR0VvRGxVWVhSNEhkZ0JURm9EbXdMV1FVdzBzN2ZCK2dJMkRkMjNCR2ZiaGNOVS9CNERKaGtNZ1B2N2NraDMrVzQzL1BzY00vYUxweGRPOU5LdGdHcVJ6cjNVM1I5TXBvUStVbGVFazlHc25wV1lzZjMyQThxamRWQnUzQ2VMK2NSNXRadnhScldNdnRwaGU2eWlHUWNQK0dTenFxMVY4Wk4yQXdsVC9jNFZ6ZjdHa1VacXd2My94aGMyTXAxRHhQdjQxVzJKRmhaVThTNXVVNk40dnl1TjM3R2psTjExZU1FdjYyTUNRMTJWaTQiLCJtYWMiOiJlNTlhY2I4NjY3OTkzODllMDkwMmM5YmUyZTJjZjQyOTI0ZDdhODIyOGI2ZDVhODdjOGQ4ZWYxMDA5YjgyMjdkIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:00:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-277890569\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>6</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">&#1582;&#1590;&#1575;&#1585;  &#1601;&#1608;&#1575;&#1603;&#1577;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">9.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>6</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>9.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>7</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>7</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ww</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">9.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>9.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>7</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>-5</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>43</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}