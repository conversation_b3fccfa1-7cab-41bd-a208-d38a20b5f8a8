{"__meta": {"id": "Xf62e4351b8cf6df8848ea2c93478d8b1", "datetime": "2025-06-17 15:06:01", "utime": **********.438268, "method": "GET", "uri": "/customer/check/delivery?customer_id=7", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750172760.881149, "end": **********.438295, "duration": 0.5571458339691162, "duration_str": "557ms", "measures": [{"label": "Booting", "start": 1750172760.881149, "relative_start": 0, "end": **********.362255, "relative_end": **********.362255, "duration": 0.4811060428619385, "duration_str": "481ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.36227, "relative_start": 0.4811210632324219, "end": **********.438297, "relative_end": 2.1457672119140625e-06, "duration": 0.07602691650390625, "duration_str": "76.03ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44976936, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02136, "accumulated_duration_str": "21.36ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.401664, "duration": 0.02019, "duration_str": "20.19ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 94.522}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.427153, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 94.522, "width_percent": 5.478}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1531589654 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1531589654\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1803654782 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1803654782\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1612780042 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1612780042\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-697337463 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ilg1MUsyMm8zcEFWZ2RPaS9nYUtGTnc9PSIsInZhbHVlIjoiUForOUJBU1JpZFM2U0QzRVBsZGFtVzJoeXhuak9jRUU5YXp4QnV1bnc3TUNxakFMT25KeHo4aDRzUzRKRnd3YnFwSVlmMlcreUZLQWJMQm0wS0ZNS1d5SDhpZnFYWE9zNWkrS2E0cXBlU3BBVGl2R0hpVkRydjAzSnhOVWM0aGFaQWhZN0hOcUVIOGU5RWdINzFQcVpQWkt1SnVidjJkYXdtdWJ5bmlzWXFaTkswdXlkcUxhZFF0VTE5KzhuN2NnQUJKcVMvVkpQUmJudXl6Tmlpbmg5dUNvNTBQUEI2ek5TZ1RZQ3BoaElFVSt2c01iUGZIWVZqU2hpczVSUlBnckdRaEhkVFRhZ3orM2hTMDJLTUVsNFhpdFB2QmxoS1pDZjV4QjdoZGtHOXVieUhJd1lLTFdlRVcySCs1MVpHMkw4eG1hajRKMllaQjVObmZvem50VG4vQzdOcFRMbnJrd1I4M0hxcW9jTDR1LzNONDgwd0YrNWFNeEF6ckM0SndyVlpWQmtrT1FDSm5kUEFUdTJkSzlkbkxOelVuNE52UzQxZ2RDLzNqUTRFYkRJbWh6eTF0VERWK1hmOXFwdGdpNW1KajBJOW9KQXNNNTAxNDBMOTUwQlJ2SHlIQ1ZpeGJiTmNRbnpZcE5lZGxDcXZ1N2ZIcEExaUQyYWR6eFN3V1giLCJtYWMiOiJlNjEwNGI3ZWFhYTQwMGFhZWRjYWExN2FmM2JmY2NiOWRiOGRkMGRmODE0ZjhlZDc1NTRjZDM3NTJiNzI4OTFiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkRZeWxFdDByREdqRm5WaDNNM1pPcHc9PSIsInZhbHVlIjoiVzNZVmluYXFqUlVzUGwxZFdaemZ2ZlJ6YUdsYjBlUmZlMXhaYm9IekhOT3ExT0RJaG5KVDlGM1NsL2dZVkZyYlpUTng2VHVyb1d6L2JobUM3dDN3RHJabHFQS1NiN1l5U0lra1FWZWhDanJkU2ZLQjBMNTIvTk5VVlArNkNHQkZXWDJ3UmxndWRrZERlTWx4WDd4SFdoL2ZlQUEyRVgySHN4RzRsMXFYUWo4THFCd3UrQnEwK0VkTHAydmZVb3NkQU1MejFnU0k4R2ZDK1E4UU5iZ1d6eHJ4V2YrQUhJbmdnb2pJN2ZCdmhnK2xEUE9DZTgxdDN1K2dpa1BZb1I3ZCtWT2RNY2NSRUNyT1hPMVlnS3NBNWNnaWlxVXkvZlFHcFhrVjN4V3IxeWk5azhNRzgyaGVZcSswa0dRb0d1blIzeWJBY21RdktHbk9SNGczMjJoUFpUUmVUQ1NvRkt1RDV4eVZFWldUUDdzR3pXQVJ1MjEzeTljWFlheDRlSUpVUHF3SEhkTHRHMVFiT0M0L0lPM09ZRld3RElCbmV5Z3dHV2RXMnZNaTUxcm5iUXplS0JLWjRKUCt6bVNjOTZ6dEEwa2NBL1c1dzlkNWFkdXpVR3FJTGtUZTFPNXRDS0V3VEphbWtORTNhYkk2YjRNZEVOekVUVkZjbmNtR1BjbEoiLCJtYWMiOiJmYTQ4OTQyZTU3MzA3MmNmOTgxYjI4ZDBkOTMxOTQwNzM0NmJmZWI0N2UzZTdjYTdiMzAyMTQxN2VhNjQwZWY4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-697337463\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1509282490 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1509282490\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-80635389 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:06:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InlnUFI1VkhNVHJxWHhQTGQ5SVNxenc9PSIsInZhbHVlIjoiUC9QTXgveUVnV2xsSmJXV0RBMTJ0cVVxc1lqZVhmWktkVFRsZjliditUZGR4aFdtOHlLME8xaktTS0QxcW5ucG1tckt0Ky8zeHI0RUM5TmtLK1JqMEhqVlpFMytwYTAybExvRk41MVBYU1Q0TVJ5SCt3SkYxZ2o2alMvZFdNSmc1dFZBcElIMzRqa2dOZE1XOEZiSnVjY2FiQ01kYnVMdHp6UXNpMmQ0NXRiUE5Zc2ttVm1USzdZa1VjWjVGdkttM2EvelBRZlU5M2ZVaytjaWNpQlhjQVh1UE03M05zeENXV0hQZno4dnBNZ21Ia3ZFeTl6VENCbThHenU2cnNFYmpNMVgvQVY0VEhnNGJYN3hlcUZYallmUm52UXVOaDU5V2RvYjcxQWtNdW44dDNwU09qcC9lakNERFo0VlEvOVRTRHRsWkd6VkNXWjlUUXhTY3E3MWd1Vml6ZTFoUkUzZmRtVjVvMzFDVjhOcldOc2tvdmMyUnBXU1ZmdXZaVmY3Y2dZMmtsVWgycGpxL2EwbE0wYzZPT0diNzZRK0h3cG9OTHZuWG50ZmZLMG5wR1duc3dPSUZIYlpaSWQrUGNTR2pMMm92T0h3VnZqZmUybU5MNVNYbnBPZVhjVi9hVjZSd1M1L05Cc2NQMDN4K3JWVFFMdFBSWlUzWlNiRFoxSVgiLCJtYWMiOiJhOWE4ZGI2YTIwM2Q3ZWJlYTdkNDUxNmIzNDM5MzY4ZTI3MGZiZWViMmMwMmJhZDRhNGZjOTMyMzFlMDljYjM5IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:06:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkZ6M2NVNVBXZWpyRlVsVWpGby82UUE9PSIsInZhbHVlIjoiblBXbkJFNkpGTjEvTHB0NVdGQnJIMnNVclI3ODJGK0FUcStjQzJ5cndJUkRQOW4rTllMVkpWcEk1Lzl1RDZtS1BKSGgrczdDL3FiZVduSzJDYUdaWm9OMHJtUThNOG83UnZIb2U5WlJlWFpZZ0FMT1l4blhLa2t6TXF3bUx6T2hRYlZPcHFobFYwb3RvNWxKZ1dxTDhVQ09lWDhFcnkxKzhoNXdmUDFoaFhjUm44NUNwK2FnU1Z1N3JJVG9Bd1hWSzZiUEEzY1MwcWNmdDlSVEIxYjdPaGpNR2tkaEFjbzBVSk05NjZBZjBjY09LQlNTdFU4eEFvRzQydTN4VkxnSHdzNEdjWDhzam13eEJ3bDFQZHJ2bGFqSTZKMU5oVVYyOEMyNFVhMmR3WWZqM05QOHZSTUVIb3EvYWxXS2I3VURXemJMaW9mcE1aa0drRVZ6U2FzM3NoREVDMHdTU2RSVDNzQzBmQjBrM00zdUcxL0c5Q1VHZGVsUGpIOTBtSmdwVDVuQ292bENmd2RoVVdSS05TTjY0V3BPcXd2NWJwQmZZOFlSREhzcXdaam9CdzQvQkdib1c2Y0NHc0tER0k5MDRPSjVHU3pHUE05VzF5OWVVWnhOWE9oRFRiR1g1Z1lPRnFqQSt1S1hzREU2Uk54b0tsRWQ5cmV1YnJla2Q4cDIiLCJtYWMiOiI5YjFiMTZmNDNjZGY1YTYwYWEyOGVhMDc4ODRlZjRiNDhlODM5NGY1ODQ1ODdjOTlkMDU1ZmRiYzViNDFhMjA0IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:06:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InlnUFI1VkhNVHJxWHhQTGQ5SVNxenc9PSIsInZhbHVlIjoiUC9QTXgveUVnV2xsSmJXV0RBMTJ0cVVxc1lqZVhmWktkVFRsZjliditUZGR4aFdtOHlLME8xaktTS0QxcW5ucG1tckt0Ky8zeHI0RUM5TmtLK1JqMEhqVlpFMytwYTAybExvRk41MVBYU1Q0TVJ5SCt3SkYxZ2o2alMvZFdNSmc1dFZBcElIMzRqa2dOZE1XOEZiSnVjY2FiQ01kYnVMdHp6UXNpMmQ0NXRiUE5Zc2ttVm1USzdZa1VjWjVGdkttM2EvelBRZlU5M2ZVaytjaWNpQlhjQVh1UE03M05zeENXV0hQZno4dnBNZ21Ia3ZFeTl6VENCbThHenU2cnNFYmpNMVgvQVY0VEhnNGJYN3hlcUZYallmUm52UXVOaDU5V2RvYjcxQWtNdW44dDNwU09qcC9lakNERFo0VlEvOVRTRHRsWkd6VkNXWjlUUXhTY3E3MWd1Vml6ZTFoUkUzZmRtVjVvMzFDVjhOcldOc2tvdmMyUnBXU1ZmdXZaVmY3Y2dZMmtsVWgycGpxL2EwbE0wYzZPT0diNzZRK0h3cG9OTHZuWG50ZmZLMG5wR1duc3dPSUZIYlpaSWQrUGNTR2pMMm92T0h3VnZqZmUybU5MNVNYbnBPZVhjVi9hVjZSd1M1L05Cc2NQMDN4K3JWVFFMdFBSWlUzWlNiRFoxSVgiLCJtYWMiOiJhOWE4ZGI2YTIwM2Q3ZWJlYTdkNDUxNmIzNDM5MzY4ZTI3MGZiZWViMmMwMmJhZDRhNGZjOTMyMzFlMDljYjM5IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:06:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkZ6M2NVNVBXZWpyRlVsVWpGby82UUE9PSIsInZhbHVlIjoiblBXbkJFNkpGTjEvTHB0NVdGQnJIMnNVclI3ODJGK0FUcStjQzJ5cndJUkRQOW4rTllMVkpWcEk1Lzl1RDZtS1BKSGgrczdDL3FiZVduSzJDYUdaWm9OMHJtUThNOG83UnZIb2U5WlJlWFpZZ0FMT1l4blhLa2t6TXF3bUx6T2hRYlZPcHFobFYwb3RvNWxKZ1dxTDhVQ09lWDhFcnkxKzhoNXdmUDFoaFhjUm44NUNwK2FnU1Z1N3JJVG9Bd1hWSzZiUEEzY1MwcWNmdDlSVEIxYjdPaGpNR2tkaEFjbzBVSk05NjZBZjBjY09LQlNTdFU4eEFvRzQydTN4VkxnSHdzNEdjWDhzam13eEJ3bDFQZHJ2bGFqSTZKMU5oVVYyOEMyNFVhMmR3WWZqM05QOHZSTUVIb3EvYWxXS2I3VURXemJMaW9mcE1aa0drRVZ6U2FzM3NoREVDMHdTU2RSVDNzQzBmQjBrM00zdUcxL0c5Q1VHZGVsUGpIOTBtSmdwVDVuQ292bENmd2RoVVdSS05TTjY0V3BPcXd2NWJwQmZZOFlSREhzcXdaam9CdzQvQkdib1c2Y0NHc0tER0k5MDRPSjVHU3pHUE05VzF5OWVVWnhOWE9oRFRiR1g1Z1lPRnFqQSt1S1hzREU2Uk54b0tsRWQ5cmV1YnJla2Q4cDIiLCJtYWMiOiI5YjFiMTZmNDNjZGY1YTYwYWEyOGVhMDc4ODRlZjRiNDhlODM5NGY1ODQ1ODdjOTlkMDU1ZmRiYzViNDFhMjA0IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:06:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-80635389\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-713193430 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-713193430\", {\"maxDepth\":0})</script>\n"}}