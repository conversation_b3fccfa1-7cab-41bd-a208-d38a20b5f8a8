{"__meta": {"id": "Xccfbd02db46d3f442f9f1110cb01a13f", "datetime": "2025-06-17 14:36:05", "utime": **********.18414, "method": "GET", "uri": "/customer/check/delivery?customer_id=10", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750170964.447652, "end": **********.184175, "duration": 0.736522912979126, "duration_str": "737ms", "measures": [{"label": "Booting", "start": 1750170964.447652, "relative_start": 0, "end": **********.101384, "relative_end": **********.101384, "duration": 0.6537318229675293, "duration_str": "654ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.101397, "relative_start": 0.6537449359893799, "end": **********.184178, "relative_end": 3.0994415283203125e-06, "duration": 0.08278107643127441, "duration_str": "82.78ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44962128, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01204, "accumulated_duration_str": "12.04ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.153814, "duration": 0.01106, "duration_str": "11.06ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 91.86}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1703591, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 91.86, "width_percent": 8.14}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1978137093 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1978137093\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-36520163 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-36520163\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2073931474 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2073931474\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjZXMGQ3dXdVVVZMaU9sUWVYUWFlWHc9PSIsInZhbHVlIjoiZjNoRHY0aU42UHVkd3NtQk02cWpHY0NyZWNwNFU0SFM1RjFiNWVXK0RaQlV0Q3JFSk16UXpwWlBEbmdYVUJ6UkhhYzFNbXFzMFhtL0E2cUo0cVliTGsxZ3didEVHMWRodlUwSHh3WlNOSFNtb0I1M243TGdkdVljWlZ6a3MyM3FoeUVRUWlNUmRTWWQ3RmF6MzJkTzFGdFNlOEF3WmtheEVhaytlOCtaa0lvYWk4UWhJQ1VEcTRqcXpiUzluN0RlVjZPTkJLdHFzTzQxeUt1WThNZDdMK0prSVNpaWdLUHNDaFZwV0xwa2cvZjJRb1N1cXNNakRlbUZmbkwxZVNpcU9qU1NPaThUdGdwZVdyYVgwMFkvRjFXN1RCZm5pMjZ6L2QvaU5HMzA4SXR1U2ROdGE5MWRtZGRxUmJwY3YwcnZDb2U4cFVBaG1Uck5RemtoRytMa2lHNElpZXM5UnpzRW1VeSs3aXJyTWIwMDgrK1Q2Yi9FcmQ2ckdKbWZsZFgyWUFubVA5MnlSV21vc3BsOEZpcWFrL3d1YlFoSmRTZG5DUjBObTgvSVlMbERWWXFyRU1lRm95NmNwTmlWSU5FWDlZdUNXT2VmcXVrS2Q2ZkxnaHNTVnpzK3NSK211a2lmbUVRYXdKSHd5cjErL2x3d3hndlhNRVVWSVZJOExrbm0iLCJtYWMiOiI0N2Y2MDdhNDc1NmQwNTQ0YzU3ZDY1ODMyMjQ1MjZiMmIyZjZkNjlkZWQ3NmFlNjE5ZmE3OTAxNzNiNTZmMmU2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InphWWh6ODBRenNSQnMrSVpNakVNL0E9PSIsInZhbHVlIjoidENyMTlmalQ4Wk9HTURaYndxWjFzbDhLM2hoT2xJdUcyREdsQlNYdXVnc08wVFU3bnlYb1N6YWhGQXNnU0xGQkxucG1yU1JqRHNZSmJCR3l4UC9kRU43MnVJSUZlTU5ZZEhZSTZaVjZ0VzdneHY0VERRZDV6a0JuQXZlOXlYTnFtcjJlTElPWmJQazlZeTlKR1J4d01kV2djM3RKMHRSakJzekFwYkdEdFFQOTlLUkQ0QzUzcERzNmpKbWdHejZCbzFGT2JrUHNaOVhMOFFFVlJCWG8zSkdFcWZZOUxVNE5TMFVWMWt3Yy8xWEZtUmJBZjY3ZEhCdXF2Z2tqcExyQUt4RWR4Z3hNSVdWUTVYd0w3emVBalVXWGJZVnAvRGtUZ2d2Y2NBU014bWVILzdCaTExNFM3RTNwL0JaTTN6Ylo5U2xybHpTUXQ5ZmU2MkdGRmFnK1lHTFlXcjhhN0NWMnRuN1p2bzhoeWI1Q2MySUZvU0JxVTJnRXYyNW4yK0FvY1UvSUgxRVFwaHJFN1ptc0xhaEpVQUxITFg2SUhKMllHYlJvVEQ1TUYyT3ZQK29nckM5ODAwMk1FdmtkVGlyeVVuNjE5Y1ZBMnZCUnY1SjR1eXZYL1JZUXBVTERWeUxaWDg5bUxjU3N1R1lGRzdRNUtEM01VLzhkbnI1WkxacHYiLCJtYWMiOiI0NDhmNTdiZTBhMTIxMTZjNGI4NDhhMTU2N2UwNzRlOGNiZjYzOTRlNzEzNzA2NDJmOWMzMzVmZWNjZTgwNWI0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1496790509 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1496790509\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1024074282 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:36:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InFTNm8zU3BxbVEzNDZudURPSkM4c0E9PSIsInZhbHVlIjoiYWx6anRqSm4ySzkrL1ZXemdRYWswTFFSZ0QwK1lBZnRidXdLRXZZdDRvbk85N0swa0c0M2wrQzJrdEFaWUhuZDNiT0NoY2YyYmtSWU5jZlRFNnIzeUI5MHZ0YmY1cFBBTFlrRU1VVytRRmFlb1RvRHVGWnNwUm02Mkk0TFZ0Z0k1OGFjcjBJbUsva2xCazh2dUxibjdQa0owd3R0eGdpcVFCRjJIRjFmUVlCdjU0WGJlT2Z1MFlGcFRqcFNtcU5zUjN2QmxTTFlJU00wUHMyRmtHVzZqd0V6OGo1em8yOHJ0VVFiM0F6WjlGbDVLQ2tFaENVWDVaRmYvOFQ3cFVLSkM0MGFaWlBzNmdJNWRSUzIzY2ZCZVdRUS8xeUs3U1NJOExpNTRKREYzSUtSWlBKejk0M05SM0pLOGloT3F1aDdsd2dETzYwRjZlcFZWbWw2YmVVZVRzaVV1SStLOXVPUlRwUjl1azBhRHlZSlJIVjN5a0pmT25jZW5UemNDdWFnSWNQbjhrKytTNlR4L0Z3SkRuK0ZoV3NUdFl6UHVDWXN3MExCY212c2ZDOEFBNWNiUnNzMTloYmJ0aHlibmF3NXlxblVlYWFCdUZhL043b01oSEpXcHJvbURXbWJtOFZQRWZZSUNlRDBndW5wOVlwNER1NUtsSHdBZnJsUGlORW8iLCJtYWMiOiJlYjA2Yjc5NzQ3ZjJlNjdiOTJkMDc3MWY5NWNjYTcwOTlkYjVlOWZmZTMwZTMyOWM0NmIwNTg4ODI1MzE2YjE2IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:36:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImRSR3ZMaHZuYW9PaTRmdDBGZ1JaTkE9PSIsInZhbHVlIjoiTkRDMk8rd1VOSUNsOVl5cEJ3dW9MeVR3bjhXZVBlSmd6RGVQK2JxaVlicHgwWmRIMzYrUTU0TWtRUGFiNVFNWCthY0pwOU1QQ1JCVUZuakd4eWc1V05JVDVydVVlS2JMWFF6MjFVNTdwYUVQNDAxTkM5WnYyb0p6NVVTRGhxWFNYK3ArOGc3SGZwQTYveHhIVGNsaFJpaVNmS2x3aC9aUk9FNXJocUVSV3RyWnRuOW51Ris3YmZOVnZZbnplZmJZWnNodXR2aU40QjlxM2VJZ0QyeVpvQXA0c1pwd0VxQ2I2TTVldnkxZGx0YkVCd3ppR0EwbUkwRHdzQWNNTWlrWHNKVmRtTHZBZjhsK1FpTUIySktBY0lIbnB6VmFMZVZZbkZHL1Z2TmJvQjRwd0U2OGdQM2NjZkJzVUxUZWZTOTFCT3MwUFdjV1hWc3VVSVlrSzJWRTVpVEJjUmZKU1NRdHhVQ0d1TTVnanIvSUJSNWxvNmxVdUN1NVBmd2w4U2tMaE9MZWpWTlczcnc2RDNUYmlXUW1RaUdSTVJWNi9nd08zUituK2xtOERGMUI1L09sL2VLalY1WTQ5M3ArUlB3akZyKzR5VmVBSW0xakdCNzhuRXFEa3paUDUxem44VThOb0RUUHhFNmpCWmZMakdFMGtncVRNd2JyUDZHUEllemwiLCJtYWMiOiJiMDEzNTdlODczOTczZjU1MjgwZDVlNWQ0NWNhNjQwNTdiYWE4MWM0OGIzMGU1Y2M2NDJmYjQyMzkzYzA1MjBkIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:36:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InFTNm8zU3BxbVEzNDZudURPSkM4c0E9PSIsInZhbHVlIjoiYWx6anRqSm4ySzkrL1ZXemdRYWswTFFSZ0QwK1lBZnRidXdLRXZZdDRvbk85N0swa0c0M2wrQzJrdEFaWUhuZDNiT0NoY2YyYmtSWU5jZlRFNnIzeUI5MHZ0YmY1cFBBTFlrRU1VVytRRmFlb1RvRHVGWnNwUm02Mkk0TFZ0Z0k1OGFjcjBJbUsva2xCazh2dUxibjdQa0owd3R0eGdpcVFCRjJIRjFmUVlCdjU0WGJlT2Z1MFlGcFRqcFNtcU5zUjN2QmxTTFlJU00wUHMyRmtHVzZqd0V6OGo1em8yOHJ0VVFiM0F6WjlGbDVLQ2tFaENVWDVaRmYvOFQ3cFVLSkM0MGFaWlBzNmdJNWRSUzIzY2ZCZVdRUS8xeUs3U1NJOExpNTRKREYzSUtSWlBKejk0M05SM0pLOGloT3F1aDdsd2dETzYwRjZlcFZWbWw2YmVVZVRzaVV1SStLOXVPUlRwUjl1azBhRHlZSlJIVjN5a0pmT25jZW5UemNDdWFnSWNQbjhrKytTNlR4L0Z3SkRuK0ZoV3NUdFl6UHVDWXN3MExCY212c2ZDOEFBNWNiUnNzMTloYmJ0aHlibmF3NXlxblVlYWFCdUZhL043b01oSEpXcHJvbURXbWJtOFZQRWZZSUNlRDBndW5wOVlwNER1NUtsSHdBZnJsUGlORW8iLCJtYWMiOiJlYjA2Yjc5NzQ3ZjJlNjdiOTJkMDc3MWY5NWNjYTcwOTlkYjVlOWZmZTMwZTMyOWM0NmIwNTg4ODI1MzE2YjE2IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:36:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImRSR3ZMaHZuYW9PaTRmdDBGZ1JaTkE9PSIsInZhbHVlIjoiTkRDMk8rd1VOSUNsOVl5cEJ3dW9MeVR3bjhXZVBlSmd6RGVQK2JxaVlicHgwWmRIMzYrUTU0TWtRUGFiNVFNWCthY0pwOU1QQ1JCVUZuakd4eWc1V05JVDVydVVlS2JMWFF6MjFVNTdwYUVQNDAxTkM5WnYyb0p6NVVTRGhxWFNYK3ArOGc3SGZwQTYveHhIVGNsaFJpaVNmS2x3aC9aUk9FNXJocUVSV3RyWnRuOW51Ris3YmZOVnZZbnplZmJZWnNodXR2aU40QjlxM2VJZ0QyeVpvQXA0c1pwd0VxQ2I2TTVldnkxZGx0YkVCd3ppR0EwbUkwRHdzQWNNTWlrWHNKVmRtTHZBZjhsK1FpTUIySktBY0lIbnB6VmFMZVZZbkZHL1Z2TmJvQjRwd0U2OGdQM2NjZkJzVUxUZWZTOTFCT3MwUFdjV1hWc3VVSVlrSzJWRTVpVEJjUmZKU1NRdHhVQ0d1TTVnanIvSUJSNWxvNmxVdUN1NVBmd2w4U2tMaE9MZWpWTlczcnc2RDNUYmlXUW1RaUdSTVJWNi9nd08zUituK2xtOERGMUI1L09sL2VLalY1WTQ5M3ArUlB3akZyKzR5VmVBSW0xakdCNzhuRXFEa3paUDUxem44VThOb0RUUHhFNmpCWmZMakdFMGtncVRNd2JyUDZHUEllemwiLCJtYWMiOiJiMDEzNTdlODczOTczZjU1MjgwZDVlNWQ0NWNhNjQwNTdiYWE4MWM0OGIzMGU1Y2M2NDJmYjQyMzkzYzA1MjBkIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:36:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1024074282\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-312555949 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-312555949\", {\"maxDepth\":0})</script>\n"}}