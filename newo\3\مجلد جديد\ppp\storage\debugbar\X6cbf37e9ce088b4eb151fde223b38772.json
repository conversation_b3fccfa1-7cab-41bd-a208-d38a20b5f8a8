{"__meta": {"id": "X6cbf37e9ce088b4eb151fde223b38772", "datetime": "2025-06-17 15:06:02", "utime": **********.461288, "method": "GET", "uri": "/customer/check/delivery?customer_id=6", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750172761.904887, "end": **********.461311, "duration": 0.5564241409301758, "duration_str": "556ms", "measures": [{"label": "Booting", "start": 1750172761.904887, "relative_start": 0, "end": **********.383061, "relative_end": **********.383061, "duration": 0.47817397117614746, "duration_str": "478ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.38307, "relative_start": 0.4781830310821533, "end": **********.461313, "relative_end": 1.9073486328125e-06, "duration": 0.07824301719665527, "duration_str": "78.24ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44962072, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01923, "accumulated_duration_str": "19.23ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.426873, "duration": 0.01814, "duration_str": "18.14ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 94.332}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4499679, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 94.332, "width_percent": 5.668}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-2018652876 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2018652876\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1575288358 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1575288358\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1288050913 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1288050913\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2017671493 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik1pKzBmSGlWZ1ZGdjBIa280SFVnQ3c9PSIsInZhbHVlIjoiTzZZL2lXdUY4REdFME5aZFlrcGUyVmhJYVNvN1lSSXRPWU11UjhCY3dhUmtibEJ1WkRLQzRjdEdDZWFGRGxVWTdlLzhZRDA2UERQVTZ1QXVmNjZ5d3dXVTJyeHpnTmkzQUdlczhTSzFUemI1cm51dzFBdUVNRGFtbXIvaWJRaHY0ZjhZWlFVNFNoN1B6djhJaHdkNktmUlI0T3RLKzAraFJGVEZmTVc3b1dnQlBVQzB6NXFyNkYzMHNPLyt6YnFlcmQrdkZCM1I2alVEZmY1UGlDRWgyVmxNLzJ4bGpybnN5NUxsYnZwVVBXRkpTMGdBekJoU3EyMDgvemFiL1Y1M3I0OU5Yb2tTektkckoxSGozNGlXbHpNR2NDbFlhSVU0bEt6SzR2K3VmTVpJdER1azNhSkdyNXF2ZG1NTVpza0RCU1huZ2xRT2FMVGxaMDc3N0lHT05QZWhPdHNuR29vVUYvSHoraXJoMEovay81RUcxMEY0THJjSjJkRWNyb1Yrc0RoSlVmeTdmN1BVSnNDSGNFYnpDV3JJVDRpQ3k0MzdkN3FhV1VOME1DMkk0SU5ySlVUWExaR01BOWplUmNtaloyL216WFZyTmhIUGFkaGgzellPSEMrWUdXMnNwaWhINFdublNKSU00S0sxRWhsYjVSam9NTUI1aVRucHcvcVMiLCJtYWMiOiJkYmI3ODJlMWJlODNlOGNiNDJjY2VlZjM3NTQ0YjE4NmNjMjAyZWI1ZGNmYTkxNDVjYzYzYzNhZWY0OTY3ZTM3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkhncVlBQXFLM2tLaW00VDd3dU5tOGc9PSIsInZhbHVlIjoiaEdvV2xNNXdMMnB3MGVWZnRkMWVpTEx1b1pFRzlYbGhKU25iNlo4SE1TREtYeGV1WFpWMlczaTJjbzQybFJKMkNuMCs5YzZURkJhZ2s5akJHQWQyQzlvU3NRVU4zaEo0S05BZTNIYjNGSnhCYWVIMDhhenVNZkVhaDJTMU45M040YlNtZ2QxeGdnd1oxY3dlY0dEOFQ0WDllZnlMb01MZGVsYzdUOTZ2NnhRZTBnNDBHbnBLOXFnZFV4MFBxUlpodlpkZjFwRU9yTStaMGRSdTJsOHpTMHZGV25kUEdLbmR2ZXg4WklLR2NnK1gzSkQ2dXBwZXU1dWxTYTJVS1FGOXpHVlVDTEFNc1hRcUU0dVVkRlVpVkxvdUZsbzVlZmp3TDcvRW1PRW41ZHBhajlmbVNBaDgyMkE0bzd6N2g5YVY2R3JTeHlEK1ZOK1R6SE1TWW0zaTdtNXNhc3hDdUFwU2tIcnAyb2JzNytjMTA1anFnNFBHbGVpeFQrY01vaDEyMXZQeUFuYWQzSFpUaUVwa3Vjc3AxWnB4VDU3RjZZVFVOeFpCbXp6djlqYnZBVmNwSDZ2YmNUYTNydC8rWUVYODlqWGNyTzdtUS8wZGRQV3JmYWt2aU1yTHh3ak1nNjVKUmxxaUxGVjg4LzJoK2xrWHFPcnhjSzNBN3lqUTIxWG0iLCJtYWMiOiIyYTZjZDhiYTY5NTY3NmU1MTY2OTYxY2ViMzRjMjRlZGNhYWQ1M2NkMzU3ZWQyMGU0M2I1MjM1MDYxZDc4Njc2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2017671493\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-395971225 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-395971225\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-804497368 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:06:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjgrK1BoZnhjZXFRYWNZMHphdktoZ3c9PSIsInZhbHVlIjoiOTFqTU9YN2lZZktFeUNLRHFaWDR6WlgyZUVnTjRVL2llbGxFc3lJaXdLdFhDWFBBZ2JhWGNpN0lja01RbEJNcExPU0FyWS9PQXR5bHNpQWRtbXQ5RVBONysvdTFhK2Q3SU1ReWJqeXZKK2NBN2xnZlYwamF6VG1UZEd0Q2x2c1VBb2xyNEhCU0NEZ1AzTDhxdWh1cVZod1JTT290Sk1BcGU0YkErV0xITXQ3M25MekxvMndtMWd6U0FrVWhEYSt3L2Y3SlNrbjFTWEdlanNVS080eTRaVG51RlhWSTBINzRSWUMyT0lwZFNjWkkyNkI2cFRrb3FKSVNiTG1ISFpLZ1lNajF0dng0R05OSDlLaEYvWStMZUtMZEZHSHBmVUpLcUlldWk2WEJQdnEvL2V3MHZRVmNNVGFWL041U3kxeWY0T0V3OWhWUEN3N01PSjllLzFFTGwweHp3d2w2aCs2WGg2d0MrcFdwNTVTWkRhZ1lxT3hrWmd4NlhuLzJDdVRGY21uaDBOZ1NrR1hRL1FTWTBEWXI3S2hLRjV5ZkFIRDFiTVVCYy9UMTlZMFpnU0xjMUVqMjJ1dzhRZ1huQ3h6U0Mrc2FBUnE3ZCtPWllXcWVKcWxCREIwUkNqbjgrQ0ttSmtjUEoxajQwNFNBTk1hUTF0SXFHNmdieUFIa2JuUFEiLCJtYWMiOiI0Y2Y0ZTQ2Y2I2ZDNlZWU1ZmZiZTRiOGJlMDAwNDc3NDE2OTk4NjhkYjRkM2VhM2I4MTdiYzg1MzYzZmEzNTI0IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:06:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImV3RHhicEhoeVU4YVo0ZFdpanVtcnc9PSIsInZhbHVlIjoiSnBlL2FrNGU5VVIxc2FRZ3JhQ1daYVg3anJDbVpVTDdISnpwN2ZVSE9oc1dGbDBKNGc5aEFybjlMQzFEUkZDYmFWeldjNTFlTExCUWIzdXhyVXlFckIwZWo1NG93QkZ3VzVxYmt6SlAwLzBUOFFWK3JhOWxzblYyUG00NnlSdUprOWZJOXZXcnlVUUlsR3RDUlYzcWVPTEozMzhDcXhOZXgxNEJHV3R1Y3dKdzR5c0JMeU14emZzWFhPaVpsZ3R1Vi81TEppZzVhUVVVbXptVnl6cHlPM0ZHQ2hyMzk2a2Q0bzlaY20wNGs0dkMwbnVwc21WQ09TTEsweXFaanNoc1c0SGJyZG1mQjBaNXAxbHFkTEwxSktuYnl1bTUzb0Q0Z3dlSE5tMVZVRW9HV1liL2N5Mi84S0VoSmtUQ1c1TjN3NmkveHNkTkM1WGpMYXFjMXIySFd0UUhHTDlqMGJ4dlI3M3NZKzFMenpWbm8xM0s5NUNUa1ZCakhDUkhHSTg1TEZwSGt1VWx4NjdKWm1QM0IyODZ6L2d6OFB6ZjJiUlBZV1A3NXNqVENWdzE5M1hGR0dWZE9BRXBTQkEzUVdkUmFDUWNHOUNNNWNzZGE4TXNYVHpiaUNUS0J1bFdHUGJ2ZnlrbE1XbnBpYkxzQ1FGUGtaV2JKSmdmUTdhNGhEOTEiLCJtYWMiOiJhMGQ3MzVhMjFiMTNiZDk1ZGM1Y2FlNjkxMWI4ZDRhYTM0YzRkNzEwYWI3ZWRiMzEwZjc0MGMwZDA5NTdkMTE4IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:06:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjgrK1BoZnhjZXFRYWNZMHphdktoZ3c9PSIsInZhbHVlIjoiOTFqTU9YN2lZZktFeUNLRHFaWDR6WlgyZUVnTjRVL2llbGxFc3lJaXdLdFhDWFBBZ2JhWGNpN0lja01RbEJNcExPU0FyWS9PQXR5bHNpQWRtbXQ5RVBONysvdTFhK2Q3SU1ReWJqeXZKK2NBN2xnZlYwamF6VG1UZEd0Q2x2c1VBb2xyNEhCU0NEZ1AzTDhxdWh1cVZod1JTT290Sk1BcGU0YkErV0xITXQ3M25MekxvMndtMWd6U0FrVWhEYSt3L2Y3SlNrbjFTWEdlanNVS080eTRaVG51RlhWSTBINzRSWUMyT0lwZFNjWkkyNkI2cFRrb3FKSVNiTG1ISFpLZ1lNajF0dng0R05OSDlLaEYvWStMZUtMZEZHSHBmVUpLcUlldWk2WEJQdnEvL2V3MHZRVmNNVGFWL041U3kxeWY0T0V3OWhWUEN3N01PSjllLzFFTGwweHp3d2w2aCs2WGg2d0MrcFdwNTVTWkRhZ1lxT3hrWmd4NlhuLzJDdVRGY21uaDBOZ1NrR1hRL1FTWTBEWXI3S2hLRjV5ZkFIRDFiTVVCYy9UMTlZMFpnU0xjMUVqMjJ1dzhRZ1huQ3h6U0Mrc2FBUnE3ZCtPWllXcWVKcWxCREIwUkNqbjgrQ0ttSmtjUEoxajQwNFNBTk1hUTF0SXFHNmdieUFIa2JuUFEiLCJtYWMiOiI0Y2Y0ZTQ2Y2I2ZDNlZWU1ZmZiZTRiOGJlMDAwNDc3NDE2OTk4NjhkYjRkM2VhM2I4MTdiYzg1MzYzZmEzNTI0IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:06:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImV3RHhicEhoeVU4YVo0ZFdpanVtcnc9PSIsInZhbHVlIjoiSnBlL2FrNGU5VVIxc2FRZ3JhQ1daYVg3anJDbVpVTDdISnpwN2ZVSE9oc1dGbDBKNGc5aEFybjlMQzFEUkZDYmFWeldjNTFlTExCUWIzdXhyVXlFckIwZWo1NG93QkZ3VzVxYmt6SlAwLzBUOFFWK3JhOWxzblYyUG00NnlSdUprOWZJOXZXcnlVUUlsR3RDUlYzcWVPTEozMzhDcXhOZXgxNEJHV3R1Y3dKdzR5c0JMeU14emZzWFhPaVpsZ3R1Vi81TEppZzVhUVVVbXptVnl6cHlPM0ZHQ2hyMzk2a2Q0bzlaY20wNGs0dkMwbnVwc21WQ09TTEsweXFaanNoc1c0SGJyZG1mQjBaNXAxbHFkTEwxSktuYnl1bTUzb0Q0Z3dlSE5tMVZVRW9HV1liL2N5Mi84S0VoSmtUQ1c1TjN3NmkveHNkTkM1WGpMYXFjMXIySFd0UUhHTDlqMGJ4dlI3M3NZKzFMenpWbm8xM0s5NUNUa1ZCakhDUkhHSTg1TEZwSGt1VWx4NjdKWm1QM0IyODZ6L2d6OFB6ZjJiUlBZV1A3NXNqVENWdzE5M1hGR0dWZE9BRXBTQkEzUVdkUmFDUWNHOUNNNWNzZGE4TXNYVHpiaUNUS0J1bFdHUGJ2ZnlrbE1XbnBpYkxzQ1FGUGtaV2JKSmdmUTdhNGhEOTEiLCJtYWMiOiJhMGQ3MzVhMjFiMTNiZDk1ZGM1Y2FlNjkxMWI4ZDRhYTM0YzRkNzEwYWI3ZWRiMzEwZjc0MGMwZDA5NTdkMTE4IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:06:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-804497368\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-17355767 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-17355767\", {\"maxDepth\":0})</script>\n"}}