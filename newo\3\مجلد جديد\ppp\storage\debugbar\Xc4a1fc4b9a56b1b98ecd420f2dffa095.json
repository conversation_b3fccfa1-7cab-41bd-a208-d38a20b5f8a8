{"__meta": {"id": "Xc4a1fc4b9a56b1b98ecd420f2dffa095", "datetime": "2025-06-17 14:48:15", "utime": **********.153498, "method": "POST", "uri": "/csv/import", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750171694.542293, "end": **********.153519, "duration": 0.6112258434295654, "duration_str": "611ms", "measures": [{"label": "Booting", "start": 1750171694.542293, "relative_start": 0, "end": **********.034763, "relative_end": **********.034763, "duration": 0.49247002601623535, "duration_str": "492ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.034776, "relative_start": 0.49248290061950684, "end": **********.153521, "relative_end": 2.1457672119140625e-06, "duration": 0.11874508857727051, "duration_str": "119ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49716576, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST csv/import", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\ImportController@fileImport", "namespace": null, "prefix": "", "where": [], "as": "csv.import", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FImportController.php&line=70\" onclick=\"\">app/Http/Controllers/ImportController.php:70-123</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02151, "accumulated_duration_str": "21.51ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.078974, "duration": 0.01196, "duration_str": "11.96ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 55.602}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 6274}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/ImportController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ImportController.php", "line": 44}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ImportController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ImportController.php", "line": 89}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.102081, "duration": 0.0065899999999999995, "duration_str": "6.59ms", "memory": 0, "memory_str": null, "filename": "Utility.php:6274", "source": "app/Models/Utility.php:6274", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=6274", "ajax": false, "filename": "Utility.php", "line": "6274"}, "connection": "ty", "start_percent": 55.602, "width_percent": 30.637}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'ty' and table_name = 'product_services' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 6275}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/ImportController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ImportController.php", "line": 44}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ImportController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ImportController.php", "line": 89}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.112201, "duration": 0.00296, "duration_str": "2.96ms", "memory": 0, "memory_str": null, "filename": "Utility.php:6275", "source": "app/Models/Utility.php:6275", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=6275", "ajax": false, "filename": "Utility.php", "line": "6275"}, "connection": "ty", "start_percent": 86.239, "width_percent": 13.761}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6", "_previous": "array:1 [\n  \"url\" => \"http://localhost/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/csv/import", "status_code": "<pre class=sf-dump id=sf-dump-300440114 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-300440114\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1195085980 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1195085980\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1534439354 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>table</span>\" => \"<span class=sf-dump-str title=\"16 characters\">product_services</span>\"\n  \"<span class=sf-dump-key>file</span>\" => <span class=sf-dump-note title=\"Illuminate\\Http\\UploadedFile\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>UploadedFile</span> {<a class=sf-dump-ref>#3836</a><samp data-depth=2 class=sf-dump-compact>\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">test</span>: <span class=sf-dump-const>false</span>\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">originalName</span>: \"<span class=sf-dump-str title=\"51 characters\">product_services (4) - product_services (4) (2).csv</span>\"\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">mimeType</span>: \"<span class=sf-dump-str title=\"8 characters\">text/csv</span>\"\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">error</span>: <span class=sf-dump-num>0</span>\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">originalPath</span>: \"<span class=sf-dump-str title=\"51 characters\">product_services (4) - product_services (4) (2).csv</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">hashName</span>: <span class=sf-dump-const>null</span>\n    <span class=sf-dump-meta>path</span>: \"<span class=sf-dump-str title=\"33 characters\">C:\\Users\\<USER>\\AppData\\Local\\Temp</span>\"\n    <span class=sf-dump-meta>filename</span>: \"<span class=sf-dump-str title=\"11 characters\">phpF07F.tmp</span>\"\n    <span class=sf-dump-meta>basename</span>: \"<span class=sf-dump-str title=\"11 characters\">phpF07F.tmp</span>\"\n    <span class=sf-dump-meta>pathname</span>: \"<span class=sf-dump-str title=\"45 characters\">C:\\Users\\<USER>\\AppData\\Local\\Temp\\phpF07F.tmp</span>\"\n    <span class=sf-dump-meta>extension</span>: \"<span class=sf-dump-str title=\"3 characters\">tmp</span>\"\n    <span class=sf-dump-meta>realPath</span>: \"<span class=sf-dump-str title=\"C:\\Users\\<USER>\\AppData\\Local\\Temp\\phpF07F.tmp\n45 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\AppData\\Local</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span>Temp\\phpF07F.tmp</span>\"\n    <span class=sf-dump-meta>aTime</span>: <span class=sf-dump-const title=\"1750171694\">2025-06-17 14:48:14</span>\n    <span class=sf-dump-meta>mTime</span>: <span class=sf-dump-const title=\"1750171694\">2025-06-17 14:48:14</span>\n    <span class=sf-dump-meta>cTime</span>: <span class=sf-dump-const title=\"1750171694\">2025-06-17 14:48:14</span>\n    <span class=sf-dump-meta>inode</span>: <span class=sf-dump-num>24769797950774136</span>\n    <span class=sf-dump-meta>size</span>: <span class=sf-dump-num>306711</span>\n    <span class=sf-dump-meta>perms</span>: <span class=sf-dump-const title=\"33206\">0100666</span>\n    <span class=sf-dump-meta>owner</span>: <span class=sf-dump-num>0</span>\n    <span class=sf-dump-meta>group</span>: <span class=sf-dump-num>0</span>\n    <span class=sf-dump-meta>type</span>: \"<span class=sf-dump-str title=\"4 characters\">file</span>\"\n    <span class=sf-dump-meta>writable</span>: <span class=sf-dump-const>true</span>\n    <span class=sf-dump-meta>readable</span>: <span class=sf-dump-const>true</span>\n    <span class=sf-dump-meta>executable</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>file</span>: <span class=sf-dump-const>true</span>\n    <span class=sf-dump-meta>dir</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>link</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>linkTarget</span>: \"<span class=sf-dump-str title=\"45 characters\">C:\\Users\\<USER>\\AppData\\Local\\Temp\\phpF07F.tmp</span>\"\n  </samp>}\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1534439354\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">307308</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryIZamKZkD8OAapAzI</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://localhost/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=17t60l2%7C1750171683312%7C3%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImtpeHJzZjZ1VjUxbEdkcTJ6d1Z3T0E9PSIsInZhbHVlIjoiSXhnOGh6blFnMU9HVjd5aUJYeTlZcjhPdThRL1MveEo4MzBvcUV0UEJMU2ZuUGp4QXRLT2Z5VXkrTXhvQnNvRnlNYlFJbjNIMHcrRVloVTdmR0tubTFPbXdERDZpZW5lWStQNFFmbEYwd05zR1VCZmdNam45ZTZKOUFjSDJyTnRQeHhheUp2Y0QzTVlqbThwNUw1eDR1ZldzcGJ5YWVUR1dsRGhaRkpsY1k0MlZDUTZPeFJGa09JMFpQcEpYVWZ6SGNvc2IwbmJHeUI5L1NwcmJEMUlSRVdiTllzMzBrVXpzYldmTzFPOUR5dkpPRzhRSC9JTSt3TEZWZ2tBLzlqeHZyMGlqUldzM0puVm9VTFo3UDR2ZkhLVldSK3IwSUlHa0c1L2RzMzB6YklGaDRsRm9NaWtuMHBDWW03aGNUNGwyWjIyUEpVSmFXMzI5dUkvYVZJSEdHWVRUbTVYbFRRRENhWWJ0R3lPc2FmMVdHQUdzSFJCNXNqc0VxOERMdEtSZ0xoVnQrUXRGSTErZllqWnZMcHB3djJLUTFNRVh0KzcrY1Izenovd2ZTNU9rblRPRDNrZkp6UDJTYis4NHRlajlrY0VWVU90SEo0aG1nZEtzT1VPTGVpNGtXakF4SnBTeXBDS1NhQjJHbDhvTm5wTlBFd1BNWlVUU2ZHRGxwb1YiLCJtYWMiOiIyZTc0YjgxMzM5OGJiZTg4ZDBmMWY4MDhkMjI3ZmRjM2Q4MDFhYzcwYzE0YzgzZTY5MjVlYWYyZjU4OWYxZGRlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlpPa1dVbVJYODRlUkFqbi85MVB3bEE9PSIsInZhbHVlIjoiN2lSS0hEUGdXR2pYSk5hK2ZPZHJ2TGxWV3hNUU83eTdBd0JDREtodEV4VjljMEVjTkM1STE1L1hka0tUM1JXSXJqdnRScUJub0Rtbk5SMWdLZkwrUytFUVdyU1hBZU1VV1pnQXFxbkV6bmd4YlQ0WGtpWE9tZ3BUYzkxQ21DWGhFa1poVkhJeTFTbjBGRGJjSFV0ampicmtNVUVKcXU3L3RhNGFNYnYwclphejViMG1PV3JNZFhraXF6S2JNUFN0KzA2czlyckZ4TENBOUxRY1o4enVWK1VpUzBuSE1ZVnhSaUJPK0R6U3lHVytZMmZONUVWYjZMMkVNN2xhTi9zSG1oMkVkNTZFOVR4MDZ1dm8wU0dXaFo0TFcrdUp3aWk2TWxxVVFtQ1lrb1JndkdkVW5HYmk2M3Znay9TYktSMjFwSnBVWGUrUFRMVTBxeC9XZEM4ZDBjaDJlbHR0RDRIOGpiTS9PNnpjRVFwY3RhSjdlanh0RXJnaE8rUjYrWDNXZWpWU1pyNTFzakJjNi9CbEUwNkVaNzVSdFhmcDNnaWxxQndPMHZrZjR2NVBuVm04eDNhV3Bzd1JpcU0wdUNoaGdXREZVV2VtWDliQjYwd1M4bzdGUm8wRTEzbTljUENTMFI1OERDdXhMeVRBOHJhOVhXNk9YcVhyQkFwMEd3WTYiLCJtYWMiOiIxNGUzMWZkNTk2YWFjMDA2ODE3N2UxMzk3NjJhY2JhMzJiMGM2MTY0ODI4ZDRhNjAwNGYxZjg5ZTk5YzIyMDlkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HxnOnSuhAAPTfN2VS4q58slgJoytwtHYqaX0oLoW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-22480910 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:48:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImpycjdqN2tVY2J2bHVERG5IYUpPN2c9PSIsInZhbHVlIjoiRlNLMjJ1d1d1dmRER1EwL3IrWkcrTFpReHUvL3dXSFFDOG0yZ0dwK2VjZmtaakNEMkNIUk1TMHh1VG1abUt4cmd3OEZyNDVXNVRRZWw5b25jNmd5eDBDcUp5ZVUwakVmUXRyWGNXMWpNL1QrcUhhYVpCZ2xEMlhKdEtmaVU0Z2JpdkQ1elppNnJYMkpoL2J6SFlub1NKUVlxNGJIUmkySmh6VkZ1cXVoQVNjZnczOWV1OEFPMW1KcWVORjU4VEFrMGxidS9Sc01PWVB1R25XNExJblFhZ0ladUxqcWxHL0UxQXQ2WVhDUlhzbjJnaW1jRzUrMHIvd3dEcGNKQnZZVC9LK2d5c2ZveEQxNzRLRnAwOEMwVGtWUkQrVGxiUW5pZy9yZ2xaSnhRRUt1THFBUUV1cGFpV1JwQksrU014cHpTK1RHeXhkOUVnd2g0UzcrNnNna1NoN012K0NZMmtEZDdzRlB6d2kvMWdmczhUZDNIYmtubE9Kd1E3NlpoY0c2K0c0Mk03OGJYZjd1eHZVSFZqYVhwNjZLWFZ2eUF5MFh6OExtWWhqSzR6ZVR4UGdNTWxPemFnZ3lFT3VCUStlOGpUNlBKVzNRVGthSW0wSU9MSUFGMFZrQklxWnZJUFVVUUhpWDJCdzJqbDlOZ2dSa1krd0tkbjFwa3ZGVXFtcEEiLCJtYWMiOiJlZTVmYjMxZjFjMWQ0NWRmZGJhMGNiMjllNjhhZjJmZDZiYzZhNzExNDkyMTljMWE5OWEzNjQ2ZDNhMjEzMzc1IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:48:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImRZVlZGZ05jaG1GNWxzNm55eXJXZ0E9PSIsInZhbHVlIjoiT2w2VTRUYmhsOXJXL29ySFYxWGx5bWhSTzFtZ1FVenMrVGZoNUVUQlZDR09rTTFKaGV5bnMycDVZWkdoQm9kRThYdk85dENWOGYwYm0vbXRYU1NsU1JaTDRmRlRKOVNPTHlmeUR4ZG5KdUpndEJjand5bFRXNFVQemtDUXdWbTY2d3l5aXBIeGJmeUF3bjFjSUhJb3ZDSFQ4OTRTTnB0NlpmZFcyOFgxbWRuN1JMR3kzOGdhVXh2NEdKbjNKaXJLdnNlUERMY0N5OGlCOUxQV0tiajZaOXRZbnBMZnpSc0xVTzJFUmVORnBKSkRadHk5U1JFTUVFMGZ0MThUVHhQaS96UnJNKzRmTmpTUWk4TE5NT3dweTN1eGVYUllicHJuL0d3Q01ITXlFVHlrbVFNYnhqcW1iQVZnMHM0Rk1qa29oT3Z6dXkzWWxCMForeUp6Nk9EaVBvL1kvR1gzWTdmMldKTmh0RmFIVUN5SGJPTWtBME9RVkc5SXZheU1Eb0c5ZktRK1M3cDBXWlQ3QjN1QzZnSklndjk5dGpyQm9BU0dVSTcwU2h6S21rNm9jR2VXRTA4c3B3NlZKcngycHNaMXpQRDNwQWJkVGJZdVNQRjZVRFNmK3h2WHNvMCsvVWd4cW1yRnNVZk43T1RsZUpOK2c3VE40WkE5V1dBZWV6R2kiLCJtYWMiOiJlZjg0YzdiODMwOTY5NzQ4Y2Q5MTBiYzg2OGNiYjgwYzFiYWE5ZTlkZGU2YzBiYzgwYWQwOGI3NGIwMmMxNDQ0IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:48:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImpycjdqN2tVY2J2bHVERG5IYUpPN2c9PSIsInZhbHVlIjoiRlNLMjJ1d1d1dmRER1EwL3IrWkcrTFpReHUvL3dXSFFDOG0yZ0dwK2VjZmtaakNEMkNIUk1TMHh1VG1abUt4cmd3OEZyNDVXNVRRZWw5b25jNmd5eDBDcUp5ZVUwakVmUXRyWGNXMWpNL1QrcUhhYVpCZ2xEMlhKdEtmaVU0Z2JpdkQ1elppNnJYMkpoL2J6SFlub1NKUVlxNGJIUmkySmh6VkZ1cXVoQVNjZnczOWV1OEFPMW1KcWVORjU4VEFrMGxidS9Sc01PWVB1R25XNExJblFhZ0ladUxqcWxHL0UxQXQ2WVhDUlhzbjJnaW1jRzUrMHIvd3dEcGNKQnZZVC9LK2d5c2ZveEQxNzRLRnAwOEMwVGtWUkQrVGxiUW5pZy9yZ2xaSnhRRUt1THFBUUV1cGFpV1JwQksrU014cHpTK1RHeXhkOUVnd2g0UzcrNnNna1NoN012K0NZMmtEZDdzRlB6d2kvMWdmczhUZDNIYmtubE9Kd1E3NlpoY0c2K0c0Mk03OGJYZjd1eHZVSFZqYVhwNjZLWFZ2eUF5MFh6OExtWWhqSzR6ZVR4UGdNTWxPemFnZ3lFT3VCUStlOGpUNlBKVzNRVGthSW0wSU9MSUFGMFZrQklxWnZJUFVVUUhpWDJCdzJqbDlOZ2dSa1krd0tkbjFwa3ZGVXFtcEEiLCJtYWMiOiJlZTVmYjMxZjFjMWQ0NWRmZGJhMGNiMjllNjhhZjJmZDZiYzZhNzExNDkyMTljMWE5OWEzNjQ2ZDNhMjEzMzc1IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:48:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImRZVlZGZ05jaG1GNWxzNm55eXJXZ0E9PSIsInZhbHVlIjoiT2w2VTRUYmhsOXJXL29ySFYxWGx5bWhSTzFtZ1FVenMrVGZoNUVUQlZDR09rTTFKaGV5bnMycDVZWkdoQm9kRThYdk85dENWOGYwYm0vbXRYU1NsU1JaTDRmRlRKOVNPTHlmeUR4ZG5KdUpndEJjand5bFRXNFVQemtDUXdWbTY2d3l5aXBIeGJmeUF3bjFjSUhJb3ZDSFQ4OTRTTnB0NlpmZFcyOFgxbWRuN1JMR3kzOGdhVXh2NEdKbjNKaXJLdnNlUERMY0N5OGlCOUxQV0tiajZaOXRZbnBMZnpSc0xVTzJFUmVORnBKSkRadHk5U1JFTUVFMGZ0MThUVHhQaS96UnJNKzRmTmpTUWk4TE5NT3dweTN1eGVYUllicHJuL0d3Q01ITXlFVHlrbVFNYnhqcW1iQVZnMHM0Rk1qa29oT3Z6dXkzWWxCMForeUp6Nk9EaVBvL1kvR1gzWTdmMldKTmh0RmFIVUN5SGJPTWtBME9RVkc5SXZheU1Eb0c5ZktRK1M3cDBXWlQ3QjN1QzZnSklndjk5dGpyQm9BU0dVSTcwU2h6S21rNm9jR2VXRTA4c3B3NlZKcngycHNaMXpQRDNwQWJkVGJZdVNQRjZVRFNmK3h2WHNvMCsvVWd4cW1yRnNVZk43T1RsZUpOK2c3VE40WkE5V1dBZWV6R2kiLCJtYWMiOiJlZjg0YzdiODMwOTY5NzQ4Y2Q5MTBiYzg2OGNiYjgwYzFiYWE5ZTlkZGU2YzBiYzgwYWQwOGI3NGIwMmMxNDQ0IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:48:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-22480910\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1147036175 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://localhost/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1147036175\", {\"maxDepth\":0})</script>\n"}}