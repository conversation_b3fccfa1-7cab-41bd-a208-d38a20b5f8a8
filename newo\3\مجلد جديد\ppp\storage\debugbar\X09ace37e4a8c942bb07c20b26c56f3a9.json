{"__meta": {"id": "X09ace37e4a8c942bb07c20b26c56f3a9", "datetime": "2025-06-17 14:50:21", "utime": 1750171821.009407, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.363249, "end": 1750171821.009435, "duration": 0.6461858749389648, "duration_str": "646ms", "measures": [{"label": "Booting", "start": **********.363249, "relative_start": 0, "end": **********.879503, "relative_end": **********.879503, "duration": 0.5162539482116699, "duration_str": "516ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.879518, "relative_start": 0.5162689685821533, "end": 1750171821.009438, "relative_end": 3.0994415283203125e-06, "duration": 0.12992000579833984, "duration_str": "130ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49205496, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.022510000000000002, "accumulated_duration_str": "22.51ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.927749, "duration": 0.01334, "duration_str": "13.34ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 59.263}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.953505, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 59.263, "width_percent": 5.642}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.977339, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 64.904, "width_percent": 5.864}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.981243, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 70.769, "width_percent": 5.686}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.989403, "duration": 0.00347, "duration_str": "3.47ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 76.455, "width_percent": 15.415}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.996959, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 91.87, "width_percent": 8.13}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2013144608 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2013144608\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.988004, "xdebug_link": null}]}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-599327826 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-599327826\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1134195779 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1134195779\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-34234707 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-34234707\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1041425359 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjlYRXdPaGEzSGhMN3hncis1S0tXY1E9PSIsInZhbHVlIjoiVnFCODh2bzZ0SnJiaXZMSFBiUmRpbXltUkVjTWpmZk43UWpvREk3c3d4b2Flbkh6NmNwR3czWmxya0lxakE3NzZNRWs4M0JxazJIQ2w5M2czMVl6S2wrckFENWpndEtTKzZlQ3NmbEV3ZWNwaldNRmpLSzJNQ3VFTU5mNGNqQWZCWmdLUVA5NFdNS3c3SjV4R3RmaW4zMk0yb3RUekFGYUtJZzJqcUMxQzFjdSsvR01rbnFsMlY1bVJUcWc2c002c1A3MENZWGlFT2IySlVoaCttYnI1WEJCRUtKZFJvV1dRbml3a0xDR0pFWnZhM1ZGVjVIWjVtZzlNblZDWWVDSVU1L1hiVXBURlh0Mm0xT2ZMa0dxNXMzVWtpam1DN1RIaUJBOTdUdE9wajRnWGErSGlIRE1LY0g0aldNc3BhVVpReUFJNHQzeldkcm5YejRDKzdMWDllaWtOazhwanJrRi8xSTdMTUR4T3pNWkJKY0xJVmVmZFE3cDdZVDVXeWRwck5BQTRhanJSVFB5NVNqNEZ0a3dPUG91R3ZNWE1SU1NlQ090OTZSN0hrU0ptTTJOQ1ozT0Q0eFZieDI0YnZmU0UyQTJPQUJVdUY1dWZqL0txMTVJKzBJenhpQ3J6MDBRL3FQMWNWb3R0Skh2ZHllYWtBQkplZTRZTUx0TmdGV3UiLCJtYWMiOiJjNTMyMjRlYWFmMWY1MmM5ZjBkMzZiZTYyNTFkYjhmODk3YWI4NjgwZmUyZmEwYWNjMDQ4ZTBkYjRjNDFiMmQxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkhLU0kxdElvakxOUVUxOHI2b2xsa2c9PSIsInZhbHVlIjoiRFc3UENjckc2dmtRS09nY255ZVNIaU8zSFY1Ny9RR1VobmpWZTl4dTRHRklhNkpmZ2RsMXVpVmxCZXdmMnYvL3YrT2xDaHVzbDNLY1lqRnRDNXVyd3BWK2ZXUmNna1JmUkNldWFMREhITjAwdlFBbiszTWhFakhHWWthZjE3VTl5bGRNUXJ6QTg3bDFSek1yMmlYK01OZko4K0RGU2g5eS9DMEdMdVJ4UlZSbnZvcHh4UkU0eE1mSjl4ektuMndDL3p3d2E2eGI4R2Yxd2srcnhpVjFWZWZhT0pJWCtMR2ZNZWl6MGU4cFJEelNWN09VYmVQSTI4c2RwcW1LWGdQU3VxcmhjbUw5Z09lSU9TMVg3aW00Qm9JeHB5S2xrbTB4YVdULzQ3ZUlwNHBzVnFmbWtXOFFQRW1UeUxzRWtFRTVsMXE3WFN2aHE0a2NtNVNIVzNESEFwRFdqdWRVcU5HK3BiNlBnTDZFR3Rld1BZNnY1Um5tYzlJay9WNktKMHl2b3JaZzgrTjdqK1RZSFZqeDFWMFJpdjJTd0VSVmNBUUZjdXUvajdMYkl0K3VFWjdKUUNUU1ptWGhMeVhDUkZZWnc4dmlWc1FYaGtJS1dNVVVSN0U4dGw5T1Zvc3h5bmUvWllwb2ErWnlNQnd0UDhjRjJCWXVXTWVBeUNldEJMTG0iLCJtYWMiOiJkZTYxZmUwMDJmZjI2NTZlMjk5ODU0YWE2Y2E0NzgzNWE5NTFlZjA1MjI3NDRlMTZlODlmZTkwZjNhNGM3NDNmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1041425359\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-726011272 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-726011272\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1958691824 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:50:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik5CajBpZlVnb3A2Ukxzd1ZxVzVnSUE9PSIsInZhbHVlIjoiWmJaaE52UC8vWG94Z0x1czBaVTVWWkxKY0hVaFFEQjRzZDhJaEFYM0gyTGJjc0V0anJFLy85dUI0RkhIbmgxeTJ3dGZ6SHRHc3I3dmh0L2M5ZVEwTFBXMkdSSTYwU1phVDZja0NrMzhuczNHQzFBZVUyWGd1d2czNm40dHhOUDl2WkxmeGQxVlp2Wmh2UkVzNDVIcHdXa28zL0Q2ZlFrMzRMMk1Zc0N3a1pRY2pNUUxEYi9JN0ZualpLa3Azd2V4dWxHQXlBNHFVUTRHWmxXYlpMWG1xVUVsWmF3WklpWC9xV0tITERIMW1FV1REZXFlNkFKZXFCNEtHL0N5a2tZTE9iaWF0MmU4anB1VDEwOXVaVlVzZTR4NFQ3ZU9PTnptRUlkRWdWYWI4NXFMTFMvZVoxazlPaDJkQmlNQm1pUDdTNFQrdTQyV3dLUnFkMlBQTmVHQmwvTHZZWHhXbjVsZVN3aEt6cEd1RUhSdTdvMFY5U2tuVHpRbkZ4UFNlQUt1OERqUStaK2dqN04zTS9NYjlFdDlNdjBOaXQzdkZQUkFOaWc0UTFyRlhEb296UDM2NFIzUHhPVHJTUUt4RjNid0NrNUptUUkyQXFXUkpyS08rellJSXgvR0Y5MG8zL213aC91NlNEcW4zL25VeElKSFRURytqa1BkbjZmOTlaaUgiLCJtYWMiOiJlYjEwYWMxMjE4NWIxMTdhZWIyYjMzMTUyOTE0YzNmNTg2ODdlYWU3NWIwNWU5MzEzMDEyMWZlMzM1ZDIxY2M5IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:50:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkxNL25EekNRNUNodnZUSGI2OXJyTUE9PSIsInZhbHVlIjoiTVIvZkFCRkhnQm1wRWlZRGFOT3ZjQXk1Wlh1RERXM2IyVXdiVXFIcGF5YkFRZ2xIZXd2N3RwU2dFSHNmY2M4a2t6YWIvZ3NjRGRkYXVRQkk0QlhXdnhNRWFqN2Q2QUxmdTF4SVQvdEFOWnlCRVUxOVQvQ2plSGZrTkRIaDUvMXY1QWRlMlI5KzQ2b3dXQ01pcURjUTU3ZFhHcTNkUVdjdEhVWUpVR3RNVXBQbGRQN21GbytuTFpsMXZXSUFZZGdkQ2xwNGlsbmNGTmNaT2R1T1pUZDZBRS9CTUJyUVNpNTBwRlYyaHVZYWgzcVFVT1NWRzluTk1KbGxaZStMYTFiSXFOeGZKQzVUanROemxtUVRUNFBDcDRBMVRQRHpTZnNhWm5BME4zNEp6a1ZWRnpRN05qMzV4allnVllYVzFIcE85MGhwUlA3U2JpV3NZR0pkZHNOUjhYY3pDZTZmU2JlU05MNXBlY3NZYnF2cGkvM0FvdnhzT1hkWG1tK011NGpWTjVpUXFYeGVJTEpPaFRKUWM0MkVJRGVRQUxmUjc4amxuam95Y1hDYUtXdHhMbHRSZjBBM2ZYbGJaMWpIY2tUUVhIWlQxWnpES2NsWDNwNG5ZWUdYRGw0cGdqM1QraGFhZFR2azBuWHlUYzI3enJ3cXZ6MFBlc1dzNjczQ1FwZHUiLCJtYWMiOiJjNjA0MWZiOWQyNDliNGJkYzBlMjNiMjJkMGNmNzU5NmMzZjJlMGRjNjc0ZTkzMTg0YTg4NGRlMjgwMjkwY2I1IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:50:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik5CajBpZlVnb3A2Ukxzd1ZxVzVnSUE9PSIsInZhbHVlIjoiWmJaaE52UC8vWG94Z0x1czBaVTVWWkxKY0hVaFFEQjRzZDhJaEFYM0gyTGJjc0V0anJFLy85dUI0RkhIbmgxeTJ3dGZ6SHRHc3I3dmh0L2M5ZVEwTFBXMkdSSTYwU1phVDZja0NrMzhuczNHQzFBZVUyWGd1d2czNm40dHhOUDl2WkxmeGQxVlp2Wmh2UkVzNDVIcHdXa28zL0Q2ZlFrMzRMMk1Zc0N3a1pRY2pNUUxEYi9JN0ZualpLa3Azd2V4dWxHQXlBNHFVUTRHWmxXYlpMWG1xVUVsWmF3WklpWC9xV0tITERIMW1FV1REZXFlNkFKZXFCNEtHL0N5a2tZTE9iaWF0MmU4anB1VDEwOXVaVlVzZTR4NFQ3ZU9PTnptRUlkRWdWYWI4NXFMTFMvZVoxazlPaDJkQmlNQm1pUDdTNFQrdTQyV3dLUnFkMlBQTmVHQmwvTHZZWHhXbjVsZVN3aEt6cEd1RUhSdTdvMFY5U2tuVHpRbkZ4UFNlQUt1OERqUStaK2dqN04zTS9NYjlFdDlNdjBOaXQzdkZQUkFOaWc0UTFyRlhEb296UDM2NFIzUHhPVHJTUUt4RjNid0NrNUptUUkyQXFXUkpyS08rellJSXgvR0Y5MG8zL213aC91NlNEcW4zL25VeElKSFRURytqa1BkbjZmOTlaaUgiLCJtYWMiOiJlYjEwYWMxMjE4NWIxMTdhZWIyYjMzMTUyOTE0YzNmNTg2ODdlYWU3NWIwNWU5MzEzMDEyMWZlMzM1ZDIxY2M5IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:50:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkxNL25EekNRNUNodnZUSGI2OXJyTUE9PSIsInZhbHVlIjoiTVIvZkFCRkhnQm1wRWlZRGFOT3ZjQXk1Wlh1RERXM2IyVXdiVXFIcGF5YkFRZ2xIZXd2N3RwU2dFSHNmY2M4a2t6YWIvZ3NjRGRkYXVRQkk0QlhXdnhNRWFqN2Q2QUxmdTF4SVQvdEFOWnlCRVUxOVQvQ2plSGZrTkRIaDUvMXY1QWRlMlI5KzQ2b3dXQ01pcURjUTU3ZFhHcTNkUVdjdEhVWUpVR3RNVXBQbGRQN21GbytuTFpsMXZXSUFZZGdkQ2xwNGlsbmNGTmNaT2R1T1pUZDZBRS9CTUJyUVNpNTBwRlYyaHVZYWgzcVFVT1NWRzluTk1KbGxaZStMYTFiSXFOeGZKQzVUanROemxtUVRUNFBDcDRBMVRQRHpTZnNhWm5BME4zNEp6a1ZWRnpRN05qMzV4allnVllYVzFIcE85MGhwUlA3U2JpV3NZR0pkZHNOUjhYY3pDZTZmU2JlU05MNXBlY3NZYnF2cGkvM0FvdnhzT1hkWG1tK011NGpWTjVpUXFYeGVJTEpPaFRKUWM0MkVJRGVRQUxmUjc4amxuam95Y1hDYUtXdHhMbHRSZjBBM2ZYbGJaMWpIY2tUUVhIWlQxWnpES2NsWDNwNG5ZWUdYRGw0cGdqM1QraGFhZFR2azBuWHlUYzI3enJ3cXZ6MFBlc1dzNjczQ1FwZHUiLCJtYWMiOiJjNjA0MWZiOWQyNDliNGJkYzBlMjNiMjJkMGNmNzU5NmMzZjJlMGRjNjc0ZTkzMTg0YTg4NGRlMjgwMjkwY2I1IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:50:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1958691824\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-145508203 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-145508203\", {\"maxDepth\":0})</script>\n"}}