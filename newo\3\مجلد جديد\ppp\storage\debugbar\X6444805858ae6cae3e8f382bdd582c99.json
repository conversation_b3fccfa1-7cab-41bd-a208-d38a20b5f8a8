{"__meta": {"id": "X6444805858ae6cae3e8f382bdd582c99", "datetime": "2025-06-17 14:36:40", "utime": **********.597706, "method": "POST", "uri": "/productservice", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 5, "messages": [{"message": "[14:36:40] LOG.info: Product creation request received {\n    \"user_id\": 15,\n    \"is_ajax\": true,\n    \"request_data\": {\n        \"name\": \"\\u062e\\u0648\\u062e\",\n        \"sku\": \"09385\",\n        \"sale_price\": \"101\",\n        \"purchase_price\": \"101\",\n        \"category_id\": \"4\",\n        \"unit_id\": \"5\",\n        \"tax_id\": [\n            null\n        ],\n        \"sale_chartaccount_id\": \"274\",\n        \"expense_chartaccount_id\": \"282\",\n        \"type\": \"product\",\n        \"quantity\": \"0\",\n        \"description\": null\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.499663, "xdebug_link": null, "collector": "log"}, {"message": "[14:36:40] LOG.info: Processing tax_id {\n    \"raw_tax_id\": [\n        null\n    ],\n    \"user_id\": 15\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.579502, "xdebug_link": null, "collector": "log"}, {"message": "[14:36:40] LOG.info: Filtered tax_id {\n    \"filtered_tax_ids\": [],\n    \"user_id\": 15\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.579739, "xdebug_link": null, "collector": "log"}, {"message": "[14:36:40] LOG.info: Product created successfully {\n    \"user_id\": 15,\n    \"product_id\": 2046,\n    \"product_name\": \"\\u062e\\u0648\\u062e\",\n    \"sku\": \"09385\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.586158, "xdebug_link": null, "collector": "log"}, {"message": "[14:36:40] LOG.info: Returning AJAX success response {\n    \"user_id\": 15,\n    \"product_id\": 2046\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.586358, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1750170999.90298, "end": **********.597741, "duration": 0.694760799407959, "duration_str": "695ms", "measures": [{"label": "Booting", "start": 1750170999.90298, "relative_start": 0, "end": **********.417953, "relative_end": **********.417953, "duration": 0.5149729251861572, "duration_str": "515ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.41797, "relative_start": 0.5149898529052734, "end": **********.597743, "relative_end": 2.1457672119140625e-06, "duration": 0.17977309226989746, "duration_str": "180ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53102864, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST productservice", "middleware": "web, verified, auth, XSS, revalidate", "as": "productservice.store", "controller": "App\\Http\\Controllers\\ProductServiceController@store", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=103\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:103-313</a>"}, "queries": {"nb_statements": 12, "nb_failed_statements": 0, "accumulated_duration": 0.030369999999999994, "accumulated_duration_str": "30.37ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.462478, "duration": 0.01833, "duration_str": "18.33ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 60.356}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.492766, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 60.356, "width_percent": 2.865}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.526387, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 63.22, "width_percent": 5.466}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.532762, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 68.686, "width_percent": 2.832}, {"sql": "select count(*) as aggregate from `product_services` where `sku` = '09385' and (`created_by` = 15)", "type": "query", "params": [], "bindings": ["09385", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 983}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 494}], "start": **********.552491, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "kdmkjkqknb", "start_percent": 71.518, "width_percent": 5.433}, {"sql": "select count(*) as aggregate from `product_service_categories` where `id` = '4'", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.557405, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "kdmkjkqknb", "start_percent": 76.951, "width_percent": 2.47}, {"sql": "select count(*) as aggregate from `product_service_units` where `id` = '5'", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.56089, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "kdmkjkqknb", "start_percent": 79.42, "width_percent": 2.371}, {"sql": "select count(*) as aggregate from `chart_of_accounts` where `id` = '274'", "type": "query", "params": [], "bindings": ["274"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.5642989, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "kdmkjkqknb", "start_percent": 81.791, "width_percent": 2.371}, {"sql": "select count(*) as aggregate from `chart_of_accounts` where `id` = '282'", "type": "query", "params": [], "bindings": ["282"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.567604, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "kdmkjkqknb", "start_percent": 84.162, "width_percent": 1.613}, {"sql": "select * from `product_service_categories` where `id` = '4' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["4", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceController.php", "line": 170}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5715458, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:170", "source": "app/Http/Controllers/ProductServiceController.php:170", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=170", "ajax": false, "filename": "ProductServiceController.php", "line": "170"}, "connection": "kdmkjkqknb", "start_percent": 85.775, "width_percent": 1.91}, {"sql": "select * from `product_service_units` where `id` = '5' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["5", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceController.php", "line": 184}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5752442, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:184", "source": "app/Http/Controllers/ProductServiceController.php:184", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=184", "ajax": false, "filename": "ProductServiceController.php", "line": "184"}, "connection": "kdmkjkqknb", "start_percent": 87.685, "width_percent": 2.041}, {"sql": "insert into `product_services` (`name`, `description`, `sku`, `sale_price`, `purchase_price`, `tax_id`, `unit_id`, `quantity`, `type`, `sale_chartaccount_id`, `expense_chartaccount_id`, `category_id`, `created_by`, `updated_at`, `created_at`) values ('خوخ', '', '09385', '101', '101', '', '5', '0', 'product', '274', '282', '4', 15, '2025-06-17 14:36:40', '2025-06-17 14:36:40')", "type": "query", "params": [], "bindings": ["خوخ", "", "09385", "101", "101", "", "5", "0", "product", "274", "282", "4", "15", "2025-06-17 14:36:40", "2025-06-17 14:36:40"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceController.php", "line": 258}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5803041, "duration": 0.00312, "duration_str": "3.12ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:258", "source": "app/Http/Controllers/ProductServiceController.php:258", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=258", "ajax": false, "filename": "ProductServiceController.php", "line": "258"}, "connection": "kdmkjkqknb", "start_percent": 89.727, "width_percent": 10.273}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\ProductServiceUnit": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceUnit.php&line=1", "ajax": false, "filename": "ProductServiceUnit.php", "line": "?"}}}, "count": 4, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => create product & service,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-174422633 data-indent-pad=\"  \"><span class=sf-dump-note>create product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">create product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-174422633\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.539092, "xdebug_link": null}]}, "session": {"_token": "zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6", "_previous": "array:1 [\n  \"url\" => \"http://localhost/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/productservice", "status_code": "<pre class=sf-dump id=sf-dump-233481209 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-233481209\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-781210736 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-781210736\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1582;&#1608;&#1582;</span>\"\n  \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"5 characters\">09385</span>\"\n  \"<span class=sf-dump-key>sale_price</span>\" => \"<span class=sf-dump-str title=\"3 characters\">101</span>\"\n  \"<span class=sf-dump-key>purchase_price</span>\" => \"<span class=sf-dump-str title=\"3 characters\">101</span>\"\n  \"<span class=sf-dump-key>category_id</span>\" => \"<span class=sf-dump-str>4</span>\"\n  \"<span class=sf-dump-key>unit_id</span>\" => \"<span class=sf-dump-str>5</span>\"\n  \"<span class=sf-dump-key>tax_id</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>sale_chartaccount_id</span>\" => \"<span class=sf-dump-str title=\"3 characters\">274</span>\"\n  \"<span class=sf-dump-key>expense_chartaccount_id</span>\" => \"<span class=sf-dump-str title=\"3 characters\">282</span>\"\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">product</span>\"\n  \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>description</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1526</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryMo0pGFTXQkoffvLx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=3pzjoc%7C1750170827229%7C5%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlRJeFVYNG9JSFNnd3hHSjdKcFRFa2c9PSIsInZhbHVlIjoiZk9scHZVV1pSQ01OK0xEcWJMS0xGUHRhR0FiY2l2N054NDRMV29nd1k2cTNyTmVjd0dVNEpRK3RFeE94SmRnbTdwRzNhMXJuYUtLZTAzamtoYkJUYXBDSjFaeC9NUDFlZWFicllkN3RvaEVxR3dNenIrQzNUSVJCeGNKeHQyOVRXLzdLRUE3MllwL3lES0VPdVNHQW1MQjkxS01lTFZlWTBTa0F6MW16V3FENlBXWnZYNTNRa2x6NytYZ2ViMFNEa2N4Mm1oN0N4RDlLUXJyT3l5eGo0T0NyMVl6V1p3OGFSaGRBYXhrM2N6cTE4ZS9sZlZHdVVINEJyZVJyb3I2ZGV6MHZ0LzFOVEhpYWhySnNlMWhRemM4dm0wV01hQ2h6M2o1TG9haC83cjZFKzhrTkQxYThnT0FtOWhQNFdWaG8vVTNFai9hY2docU5Gc295SDJFRU53Y2d1bTJiWnkxRXZNejRLV3BIQjZZOHdQY0VyZGdock1pei9nZ2VqaU5vdTI3d01YUHhSaUxsRW9QYkJCS1B0aGVJL1A1Ly9Wejd3MmtNbXFXbkpjMk1uTUhicElLUEdWVFlwTzZHY1R4dmJCM3A2aU9YRkdMUk40VzhSS0pUbkw3NkZ6SzMyU0x2eFU2dVpkZ3BaejVTTmh2RDBhQ0htendVYUNXYmlVejciLCJtYWMiOiIwYjgyZGVhM2QyMWFhNjcyMmMyMDk0NWRhYzNjMDNhYzFmODI3YTIxMWRiMTIwY2EzODUwZTJiMWQzZTI3MTJhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImpibkRXU0NjYXVDMHZ3bzhBN0hJa2c9PSIsInZhbHVlIjoicCtjcllTTlY0ZFd6VEF4TkVhcWNGUGFEV3RncXBJSmdXd085S2lxaHBBeTlJNkI3aERpUjkvbUM2aU5rQWw4S0NlKzF4bmtycjlubmR4cE1TdTBxc1VySjFpV3pPTWJ0R2FwK0RpaFl0NlJ4RXNNOEFxb3k2NWp1TTAyNTJTYm8rWEE1N1BFLy9QeEJaNXpveHhMSmdZWXdHQXdVYXF3aWN4SVdxMFhHcVFjNGNHMnZhcjNXQnNSbU52TGM0VG1nS1U2S0pTYVZIeEhCOVhIK3o1cXZuckFsc3VpZWdKTHd2RUpPMGlMS2J5eXRkNGgxWVBvYWk3T0s5WkFLSE5xdWx6T2lITDZiZ1RaWFVvWEpOeEZqRGEvdlBwZFQ5UDlLWThCeHVsT0MvWStVYUVKNEgzZVYrbE9MdEhIVEJDd2R6VjJHTVpTMmZsVCswcE9pbmFHUTZXdytEQUJuTWROM3dTa29LTzFVYzNuRm5PM3RPdG0yLytXZkxFMDFwUWpEOStIQW9rSTluSkZjUnVwckI2Q2NiWUk0Y3FlanpLbjhJdS80ODhKcVRZTU1EakVYMWdYUERSY2c4QkJMSXU2ZVNOZk1QR0c3aldzTzNKWVk0S1lUTVFXQVVkNW1pS1lsQ3lTaFh1UGFyazlnZG14eXhBYnNJM2NaWjgwcUpLT3IiLCJtYWMiOiJiMTIzZTAxMDIyMjM0ODc5YmIzMjc3YTJjMjc2MGM2OTkwNWMwMzExZDJlMDRiMTc3NGExOWQ1Y2U5ZDM3YjAwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HxnOnSuhAAPTfN2VS4q58slgJoytwtHYqaX0oLoW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:36:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjVyRHdHVXV2NTNUMktMa2xrdmhkM3c9PSIsInZhbHVlIjoiRm5PbDNkYU1UeE9sdU5UakIxMnNleUJ6Tkw3NS9DZStnVzBVRjA4cC8xUzhyMnF5ekJYMndXendvSFFPUm5adGhONXJrejlGQ0NHRjdkYTJIeHpUVzdzNTFhemNvVUttRG9tZFVTd3VpU0hLaW9FMW0xZkdjVHlib3BoclJXaHZFZWlFUWp2aExXS2lobHZ0dVR6NS83N0piWGJvWENOZ2Mvb2F6THRHZUppNFV0UFQ3THFJVW4zb2JVeE1USmVuK1YrVEJFajN4cFBObWQyMjRMbm1GSkRjQWJrbktKSERLUlFlOWx3UzFXMUdhRGlWZFR3TXNvOFJEdUJ2Y2RINkFwcjFYZ1ZLcE1lQ0pSQU9zSEZ2TCtCOWF5aCtwQTU1Y2xwMmpDOFN6Vncyck16V3pDeWR1Zisvbi9adnpHSzRJU2J0UjJNRlRBNVl4bElzYVlvZ1l0bjRlUzZ5MlpBUjdRb3RzMmZPWkhrb3prOGFodWdXWUJqWE1hcWp4NG9SNURtczNpY0JiTnJjYkNIRFRpcUwwSXVicmRnT1VvdjhpOUpzeXh0Q0Y2enp6NWNyeUlrdGx0VkxVZis3dG55OUt2a1o2Sk5zMXdZL0MyL2libEg5VTU5dHV3eW1ZUVI3V0IvN0VxYVBUV2JpVkh6NUVGNnorN3ZzU09DZThvd3IiLCJtYWMiOiJlZTJkZTFlMjA3ZTA3ZGU2YjA1NDdmY2E1NTdkMmZmNjQzZDdhMGU5ZDg3ZmI1ZTYxYzUwYTkzMzZlNzE0ODcwIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:36:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjBTeUREMDRjZzZHcTVDZnJtb0hVOVE9PSIsInZhbHVlIjoiMEJmeXdZRFlYYXNQUmJDaXd4aVVOL2Q1cFJYa3Urd1NOME9aSi9saFNWTXBxLzBHYjNpMWRjWkEyWXFvWXlTeldEditMcFo0anVMY292TUpXbVVuWDMrSzZTR2libHd4V3gyNUdRVk9BeHdDdHp3UnFJR3NtNjNvNGd6NlBGU2lNZHhMbk5nTTBoNzQ2aS96R3dlcHA4Z1J4Y3BkWnN2MEZ5emFJeDlSN2R2ZEF2MmJJWFZxUGZLTkNGdC9Ld2ZUcjVVZWhmUkRtNHZvTTd5UEMyYWs0TXJzNHI2T0sxNm0zVUh2NEZKcWVBS2Npbzgxd2pCdWZ0N0J5bUxWa1RlV1pyUCtqL294NmZVQXpWclZJOW5TcllwQ1R2NktqaEJLOEJxSjBDa0k2VjZGNzc3Vlg1SGxIbHZkVVE2VGNCMmtOZnVhRFpYNlM4U3F2WFNuZ0haMlRza1NobXlnS1FFNEZWYWZEMXRCOHBJNCs0QUgyRG9HNzc5RnFIeGZNaWlXZjVoblg1ZXBnTzZRaGw4MzI1dWlCZnRoVCsvekFMSjFLOUJPc2RJVDN2b2x5aFl5N2RFdlEzaTRBWExHYVBGdkFQZG8rM2s1QzBBV2R3dDlOOTJrN1VZV1Mxc3FQckJoVjJvZXVsNW44dytaMkxQZlFzUlpvOUJDMnJyVy9EaWIiLCJtYWMiOiI3YjcxMTE1OWVjZjZiYzFiZWExNTRjNjA4ZGViM2NkMmYyYmU1ZDViMDFiYWY5YzJkZjU1ZjY1ZmEyNmFlY2RlIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:36:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjVyRHdHVXV2NTNUMktMa2xrdmhkM3c9PSIsInZhbHVlIjoiRm5PbDNkYU1UeE9sdU5UakIxMnNleUJ6Tkw3NS9DZStnVzBVRjA4cC8xUzhyMnF5ekJYMndXendvSFFPUm5adGhONXJrejlGQ0NHRjdkYTJIeHpUVzdzNTFhemNvVUttRG9tZFVTd3VpU0hLaW9FMW0xZkdjVHlib3BoclJXaHZFZWlFUWp2aExXS2lobHZ0dVR6NS83N0piWGJvWENOZ2Mvb2F6THRHZUppNFV0UFQ3THFJVW4zb2JVeE1USmVuK1YrVEJFajN4cFBObWQyMjRMbm1GSkRjQWJrbktKSERLUlFlOWx3UzFXMUdhRGlWZFR3TXNvOFJEdUJ2Y2RINkFwcjFYZ1ZLcE1lQ0pSQU9zSEZ2TCtCOWF5aCtwQTU1Y2xwMmpDOFN6Vncyck16V3pDeWR1Zisvbi9adnpHSzRJU2J0UjJNRlRBNVl4bElzYVlvZ1l0bjRlUzZ5MlpBUjdRb3RzMmZPWkhrb3prOGFodWdXWUJqWE1hcWp4NG9SNURtczNpY0JiTnJjYkNIRFRpcUwwSXVicmRnT1VvdjhpOUpzeXh0Q0Y2enp6NWNyeUlrdGx0VkxVZis3dG55OUt2a1o2Sk5zMXdZL0MyL2libEg5VTU5dHV3eW1ZUVI3V0IvN0VxYVBUV2JpVkh6NUVGNnorN3ZzU09DZThvd3IiLCJtYWMiOiJlZTJkZTFlMjA3ZTA3ZGU2YjA1NDdmY2E1NTdkMmZmNjQzZDdhMGU5ZDg3ZmI1ZTYxYzUwYTkzMzZlNzE0ODcwIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:36:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjBTeUREMDRjZzZHcTVDZnJtb0hVOVE9PSIsInZhbHVlIjoiMEJmeXdZRFlYYXNQUmJDaXd4aVVOL2Q1cFJYa3Urd1NOME9aSi9saFNWTXBxLzBHYjNpMWRjWkEyWXFvWXlTeldEditMcFo0anVMY292TUpXbVVuWDMrSzZTR2libHd4V3gyNUdRVk9BeHdDdHp3UnFJR3NtNjNvNGd6NlBGU2lNZHhMbk5nTTBoNzQ2aS96R3dlcHA4Z1J4Y3BkWnN2MEZ5emFJeDlSN2R2ZEF2MmJJWFZxUGZLTkNGdC9Ld2ZUcjVVZWhmUkRtNHZvTTd5UEMyYWs0TXJzNHI2T0sxNm0zVUh2NEZKcWVBS2Npbzgxd2pCdWZ0N0J5bUxWa1RlV1pyUCtqL294NmZVQXpWclZJOW5TcllwQ1R2NktqaEJLOEJxSjBDa0k2VjZGNzc3Vlg1SGxIbHZkVVE2VGNCMmtOZnVhRFpYNlM4U3F2WFNuZ0haMlRza1NobXlnS1FFNEZWYWZEMXRCOHBJNCs0QUgyRG9HNzc5RnFIeGZNaWlXZjVoblg1ZXBnTzZRaGw4MzI1dWlCZnRoVCsvekFMSjFLOUJPc2RJVDN2b2x5aFl5N2RFdlEzaTRBWExHYVBGdkFQZG8rM2s1QzBBV2R3dDlOOTJrN1VZV1Mxc3FQckJoVjJvZXVsNW44dytaMkxQZlFzUlpvOUJDMnJyVy9EaWIiLCJtYWMiOiI3YjcxMTE1OWVjZjZiYzFiZWExNTRjNjA4ZGViM2NkMmYyYmU1ZDViMDFiYWY5YzJkZjU1ZjY1ZmEyNmFlY2RlIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:36:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-60980369 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://localhost/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-60980369\", {\"maxDepth\":0})</script>\n"}}