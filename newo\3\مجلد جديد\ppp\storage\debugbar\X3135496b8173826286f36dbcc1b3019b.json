{"__meta": {"id": "X3135496b8173826286f36dbcc1b3019b", "datetime": "2025-06-17 14:54:40", "utime": **********.063014, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750172079.459542, "end": **********.063035, "duration": 0.6034929752349854, "duration_str": "603ms", "measures": [{"label": "Booting", "start": 1750172079.459542, "relative_start": 0, "end": 1750172079.968318, "relative_end": 1750172079.968318, "duration": 0.5087759494781494, "duration_str": "509ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750172079.968332, "relative_start": 0.5087900161743164, "end": **********.063038, "relative_end": 3.0994415283203125e-06, "duration": 0.09470605850219727, "duration_str": "94.71ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46341216, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1663\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1663-1673</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02327, "accumulated_duration_str": "23.27ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.013592, "duration": 0.02229, "duration_str": "22.29ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 95.789}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.0492291, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 95.789, "width_percent": 4.211}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-637715781 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-637715781\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-180907430 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-180907430\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-788825591 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-788825591\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-835955682 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkNjZEliQjBYQ1dCa2lGUG9uVk1CUHc9PSIsInZhbHVlIjoiOEpxZmdqRHYyQTBQYXBsbmN0Y1lSS0FleFpTNGEwdXQzdmtLVjdnNEp3bUh4aDQyd3U1NE5IcDZJam9VSnBuV2NDRGtRUFpyVmJrQk5udTJzSjJwbU9ETDN3SUw1aHkxOThTSWswMjlVWm1GT1dtVjlESkI1UjJ5eUFnVVFUT0phR2o0TVl2bUpRckNZY0p1ME9OYSsxNmdROWtuU3d0K3VuRUdkMXJwNVQ1TzczTHVLSlZiejlKVmpQY3JDeTFlZFVZUGFNbHJ1bkNUT01vNlpaOHpteHh5U2RuVHlyMjNxY1hXREVaWm03S2QvVTRkc0pEZjhkVHhha0JDbFUvaFpKQ1U2aGhKNW9JdVI3NGtHakdDdDVyT3duQms4VVhOclVPcFMvZE1kUHlZT05pVkVvSDVEY1VNcXZ5cXp4cHpFSWdjN3JTK0pLSnB4c2RUeEVrNUl6SUh6cVRqSFhpM1FVNk9DTmczblZReDIxaW5NdmlLUVVrZXpjMElqRUhST255cklYZjRzV09UaElub3ZocUNqUnlwZW9NUDZnSkd0OXVjOFFiOHUzQ3E2M3c2WTRPRVdoZjZsWlFtTzM0WExzOXhpL2hvMCs1ZDJyY3hxaWh2V0J5aVREeG0vcmw2azBoTDRBLzRUNFNIbmFRYXVXSkk5Q1k3ZGlsN2ZGNkYiLCJtYWMiOiJkMDg2YjJjOTU3YzNkYjU1ZDZmNGM5NGEyYzgyNzYxMGI5MmYzYjNkNmY4NTRjMDQ0YmRiZDA0OGMwMTA2MGM1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ik9wNE1ndnB1NldyaStOdkEwTFEveVE9PSIsInZhbHVlIjoiU3lLYVBrclVOaFpaMXNzTENZZllPdkszeTYzdWdLVGJJSTR4ZzRQWXFVckFSK3A5TkxtM1RpbnBmWjBOK1hhU0cwOVR3SW9TVUFTSURDdThmZ3hVR0ppSjdORGFKSlp6bjgxeXFGczZDQWZwUjkwYWZNZHdFT09MbGdZbVBTR2g0MjljOUwzUEIrQmtiN3dXcVpWa1g0U21ZeE03V2ZnN3E3RUhHWklyNy9UY29CTlpyN2dOeWN2bVhOK3NGYVNtTkJCVzdGdERFVVhKdFN0Y0lEOW43ZFdqL2VQbkdsT2hyMzhhbTY0ZTZxZWZad2N3blM0UEFkK3htdDJ2WjJVbTJBcUJUQmZRR3dIZEg5N0tsWER5ZFZoTC9scTVaQjVUN3ZGTU5IMm53OEppZXBTQnN2MXhSUjRzbDhEbEJTVmhwZkRGbzhmOGR1dTFQWk5YRm9pVlRSRG93cHEzRkxQRVFiR2dwTlloR0dyUFJXR0Z4eU40OVR1M2pqdUJDWDA2VFl0THBRSDlRQis3Q29VaS9QK0VTZXZSM3MvZ3gzdXFCM3MyVVZUZ0JqQ29rWmVkYmRwL01JRG5nRFNmL0lrWElVdmFCOXc2M3kyQXRXQ0lzVmlSTHVhcE91Nit0NGNtOVlHblJ0bDhkV04xb2tHYmdCQTNuTVlsb0gvZGN2RjgiLCJtYWMiOiI3OWIwZDk1ZmVhZmUyMThmMWMxYTljZmU1ZmI4ODk3YzU0NDdjYmRhOWE3Mzg2NWUxMTY4ZWRjMTQwM2MwYTdiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-835955682\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1986359998 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1986359998\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1952107596 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:54:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjdsWUFQcFNhZzJVUHVPNUsveVhvQUE9PSIsInZhbHVlIjoiWlhVaElwQnozZTQ5RUlpODVhdEhMSFBYMC9vZzVYUDJkRUIzTXlhbmZDZ1BRdzRRSUN2VktmdjBJSjIvUmRhQUZaazk3ejZTeDZ4N1NqdTQyMWtWMnBnNWtSckhvcXBaemJ1Z29YYjBpTnhvblMyWUliTTZEZkQ2MURZQVJWNTBUZFlJSXhTb2VFc0k0aHd0aUxWSkl4MTBBaXFxekgzeDBwTVlHYk5Mbnh6ODR6MTAzdFBGL2pxN0xmRkhFYVZiR3ZvMHF2b3F6OTdRSzltYmlRRWNPWEltUWpKbUY0b2Yva3pKZ283SnM4NzMraDZhSzAwWDZDK3lKZmtxeEszKzJ5NlBqTGxsTGk4N2F2UEJySUNwRDZxUzZPbTZWc2JXVENnbFF6YXlxUm40MGhpTUlneDlET2J6RHBtd1Zwa1crT3BJSzNKZUtNenRWTDl6Y1ZQSEdjSy8wZ3NFMUJxYks0TnNWaW9lOEY5LzZFK0JWaVN0MzMwMFpUa0JSS1c1Yk8vY1NRTlM5aWVKZWQxREhrRmlDcDRUSmdSRlBLcU13bjUzQnJWSHBZM3QyNDZ2aXZSeHZ2QjJzV3dPZGE0RmVTV0gva09JdCtBUXNmSEZqQzJGZnRocW0vZ2NkWWw4cnI4OVc2VTUxdjREL2F1MzhKNnJwaEJoNGdwRDZxT00iLCJtYWMiOiIxOTI5OGVkYjJhZmY1MjU0Yzk0Y2EyMjY1YzQ3NjMzMDYzZmIzZGFiMmMwYWI0MjY5MDg2YmExNWQ2NDQ2NjhkIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:54:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik1OTkROTGU3dVBicDhWRHliTUdkTmc9PSIsInZhbHVlIjoiVFFhU01sVURXVmxxVzVqQkJtT3FVN21MaHVYOTlSUisvR3ZiMzlLTzBQMnlZOUtYd1BnOTVTbzJSVDFvQ3RlbDFRdDZNWldsUjVFcElhRDhQcFkzdzI2REZUV01oSU5Va1B6VHNFaVg5SFZ3Z1RsdHMzZEx1dXdDUk9LNDJLc0kvUlJrNldqbjRnUGdaNUtwbVdtMEUzc0F4bW8zbTcrUEl3QmdKRW5XVlpRYWxGamJyR25YL3AvTW5BQzhyVy82ejlKS2xzWkZOblhNcFdDYnZlSjBUVWM1UFR4WVpENjR0Y2poWWtWclRTbnRZRzc3SjFQenhWclhPWW5hQ0t5bzdQTVgxa1ZxRCtGdDJiUkFnRDQ5MGlNVGZkVGRCQVVTcFFueFhBWmgxSzlBczdPY3VaQXlDTTBqVTJhZjVjc2YyOTREeDdRSHBnbE13SHJLeUZ6R3Q4MEdHZ2RMWnJzUGFvc2NsV2pPTWczckNxL3p4MkhpRmF4d3ZwNkxLM0YweHo4UUw3dURSdUpvdTlmNENHNGV4Y3lTMjRQWEYzblJSK0RrbUthK3k3ZURoSXVMZWw4UG5nb2luOHBZRUw4OWk4Q0t0Q2V6YTU0czZVR3MyT0RiQWNzYzFmWXZ0VTcxeTFPUXordXhNdjE1d002ZDJYakx4VE4vZWJDOGtwS08iLCJtYWMiOiJiOGZjMmU5Y2E3ZTNlYWEyODUzZWZmZGI5ZWIxZDY0NDY1ZWFmNzY3OTcxOGQxZTIzY2Q5NDY4YjFjNTQ5ZmMxIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:54:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjdsWUFQcFNhZzJVUHVPNUsveVhvQUE9PSIsInZhbHVlIjoiWlhVaElwQnozZTQ5RUlpODVhdEhMSFBYMC9vZzVYUDJkRUIzTXlhbmZDZ1BRdzRRSUN2VktmdjBJSjIvUmRhQUZaazk3ejZTeDZ4N1NqdTQyMWtWMnBnNWtSckhvcXBaemJ1Z29YYjBpTnhvblMyWUliTTZEZkQ2MURZQVJWNTBUZFlJSXhTb2VFc0k0aHd0aUxWSkl4MTBBaXFxekgzeDBwTVlHYk5Mbnh6ODR6MTAzdFBGL2pxN0xmRkhFYVZiR3ZvMHF2b3F6OTdRSzltYmlRRWNPWEltUWpKbUY0b2Yva3pKZ283SnM4NzMraDZhSzAwWDZDK3lKZmtxeEszKzJ5NlBqTGxsTGk4N2F2UEJySUNwRDZxUzZPbTZWc2JXVENnbFF6YXlxUm40MGhpTUlneDlET2J6RHBtd1Zwa1crT3BJSzNKZUtNenRWTDl6Y1ZQSEdjSy8wZ3NFMUJxYks0TnNWaW9lOEY5LzZFK0JWaVN0MzMwMFpUa0JSS1c1Yk8vY1NRTlM5aWVKZWQxREhrRmlDcDRUSmdSRlBLcU13bjUzQnJWSHBZM3QyNDZ2aXZSeHZ2QjJzV3dPZGE0RmVTV0gva09JdCtBUXNmSEZqQzJGZnRocW0vZ2NkWWw4cnI4OVc2VTUxdjREL2F1MzhKNnJwaEJoNGdwRDZxT00iLCJtYWMiOiIxOTI5OGVkYjJhZmY1MjU0Yzk0Y2EyMjY1YzQ3NjMzMDYzZmIzZGFiMmMwYWI0MjY5MDg2YmExNWQ2NDQ2NjhkIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:54:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik1OTkROTGU3dVBicDhWRHliTUdkTmc9PSIsInZhbHVlIjoiVFFhU01sVURXVmxxVzVqQkJtT3FVN21MaHVYOTlSUisvR3ZiMzlLTzBQMnlZOUtYd1BnOTVTbzJSVDFvQ3RlbDFRdDZNWldsUjVFcElhRDhQcFkzdzI2REZUV01oSU5Va1B6VHNFaVg5SFZ3Z1RsdHMzZEx1dXdDUk9LNDJLc0kvUlJrNldqbjRnUGdaNUtwbVdtMEUzc0F4bW8zbTcrUEl3QmdKRW5XVlpRYWxGamJyR25YL3AvTW5BQzhyVy82ejlKS2xzWkZOblhNcFdDYnZlSjBUVWM1UFR4WVpENjR0Y2poWWtWclRTbnRZRzc3SjFQenhWclhPWW5hQ0t5bzdQTVgxa1ZxRCtGdDJiUkFnRDQ5MGlNVGZkVGRCQVVTcFFueFhBWmgxSzlBczdPY3VaQXlDTTBqVTJhZjVjc2YyOTREeDdRSHBnbE13SHJLeUZ6R3Q4MEdHZ2RMWnJzUGFvc2NsV2pPTWczckNxL3p4MkhpRmF4d3ZwNkxLM0YweHo4UUw3dURSdUpvdTlmNENHNGV4Y3lTMjRQWEYzblJSK0RrbUthK3k3ZURoSXVMZWw4UG5nb2luOHBZRUw4OWk4Q0t0Q2V6YTU0czZVR3MyT0RiQWNzYzFmWXZ0VTcxeTFPUXordXhNdjE1d002ZDJYakx4VE4vZWJDOGtwS08iLCJtYWMiOiJiOGZjMmU5Y2E3ZTNlYWEyODUzZWZmZGI5ZWIxZDY0NDY1ZWFmNzY3OTcxOGQxZTIzY2Q5NDY4YjFjNTQ5ZmMxIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:54:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1952107596\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-587008070 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-587008070\", {\"maxDepth\":0})</script>\n"}}