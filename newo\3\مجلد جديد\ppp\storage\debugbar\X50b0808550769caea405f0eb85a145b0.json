{"__meta": {"id": "X50b0808550769caea405f0eb85a145b0", "datetime": "2025-06-17 14:56:33", "utime": **********.24117, "method": "DELETE", "uri": "/remove-from-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 3, "messages": [{"message": "[14:56:33] LOG.info: removeFromCart called {\n    \"id\": \"5\",\n    \"session_key\": \"pos\",\n    \"request_data\": {\n        \"id\": \"5\",\n        \"session_key\": \"pos\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.203799, "xdebug_link": null, "collector": "log"}, {"message": "[14:56:33] LOG.info: Cart before removal {\n    \"cart\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.232605, "xdebug_link": null, "collector": "log"}, {"message": "[14:56:33] LOG.warning: Product not found in cart {\n    \"id\": \"5\",\n    \"cart_keys\": []\n}", "message_html": null, "is_string": false, "label": "warning", "time": **********.232797, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1750172192.650268, "end": **********.241195, "duration": 0.5909268856048584, "duration_str": "591ms", "measures": [{"label": "Booting", "start": 1750172192.650268, "relative_start": 0, "end": **********.135965, "relative_end": **********.135965, "duration": 0.48569703102111816, "duration_str": "486ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.135978, "relative_start": 0.48570990562438965, "end": **********.241198, "relative_end": 3.0994415283203125e-06, "duration": 0.10522007942199707, "duration_str": "105ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48632760, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "DELETE remove-from-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@removeFromCart", "namespace": null, "prefix": "", "where": [], "as": "remove-from-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1675\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1675-1748</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.01481, "accumulated_duration_str": "14.81ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.175136, "duration": 0.0124, "duration_str": "12.4ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 83.727}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.199002, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 83.727, "width_percent": 5.942}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.222108, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 89.669, "width_percent": 5.132}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.22573, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 94.801, "width_percent": 5.199}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 16,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1105218119 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1105218119\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.232042, "xdebug_link": null}]}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/remove-from-cart", "status_code": "<pre class=sf-dump id=sf-dump-976144395 data-indent-pad=\"  \"><span class=sf-dump-num>404</span>\n</pre><script>Sfdump(\"sf-dump-976144395\", {\"maxDepth\":0})</script>\n", "status_text": "Not Found", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-502384328 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-502384328\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-103032248 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-103032248\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-222355005 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">20</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ikx0WFkrRWZaM01HcFFGMnFhRTg2dEE9PSIsInZhbHVlIjoiQTZsa0RpLzRZa3dmZmlRbFZ6NVJsTjBqZXFRTzVCWGtrSGw0aW45aGwwV0ZYYU94endvaXVBamxJVG1XL21ZS1NWb3c1YUJTSlNLc0prNHZEOHZ5aWVyZEZyN2xRcERQSWlOVUQxY3NIT3BSUEE3a0FPZHNUaDltRXpUZkRXSU1xUk44cnJsQUZ2eDMvQmtCWUZDTWhvMUVqNHRBYXlxNmZJaWJvWTVzdUlRbmZVYmE5SE8wU1pPN1BkLzVPaFVDMEtWRVdLSE92NEY0RkRXejVveER0b0xNUVlCcVVZVldyTVJLWUJQZlJOUnpYSHR3dWEyRnYxT3IwTWh2cWRZUFRxVnFkRERoVWVpQmdOY1ZXK0o5dnhUNjFuSzkyK3hPUFhFa3R6bzZKMUFFRElxUzJJdEY0Z1JzYmxIUDEzWFYrL1hQSkRjMzBJVkZ6TmhoZ0R6WU4rczlpdDZxQXJMTnU5M3NIYzFxZmZUNlJIUENObHpCeVI4UkxQS29KYTRROEpjVnBhMmx6MzRqVGVkK2tMb0F4WEtaU0taQmZqSDJoSzY0Tkh6bUgzcUNBbEd6emFETXNiUGR3Q0hFblBjbm5DVlJqMWM5c20yMWVrZVNoK3MvcXNTaU54cy9Zd0dabk5xdm9NMG9qbnkxMDNLVStNeFgvYi95R3pLbWRXVmQiLCJtYWMiOiI2MTRjMWQ5ZGVjZWUwOGQ5YWRjYmE5YzcyYTAzZDM2ZDgxYmM3MjUwNGQxOWY3YjI5Njg2NmI3NTIwYmJhYmI0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlNkb25RSjZWTmZYNVZxU2taWXVxckE9PSIsInZhbHVlIjoicmZrRzlXUmNhUnR6S3RnUEN5RVJXbUtsL3ZCcE9MSGdnT1FwL2x6ZnZpcC9YSFRTR0dhUXlvK0hFclRvZVlGd1ZKYnFSWHdJd21EZUQ1MDJkSnk1ZktNaFhxOS9UWFJ1dUJkVkNybGUwT3FQaFBxQVhYSlM1ZTM2NHlwRjEyeEtuR004TjVDMWZEOWJxMWdtS0VDdmdxRXNLNE9sOEFlNDVyYjNpWWhpaWFEbEN3bkVyclZxWmVUSHpDY1lvcmdhTjlSK1UyQVB4MytoQ3BDZ21tYUpSbTc2bXpmT0VRMndCN2xMY2czL2hhUW44M05rVFR3TmthaUJBN1VXWVRMRDFobWhYV0pHOHhKUDZLWGtBbGNNeERCMGlFUDNsandlS2tXcWV6RlkvSmRLdVR4Z2ZVbnl3K1JscTBkWVpjTXkydi8xYThhdnJSK2htZTh6Rm5XSUFiKzZ5NCtqYWtrVzlYU3c1aEtlZmNZdEEwdTZXNXhBSlZZdkxYWFhXZmQwZllMS3N0K1UybklwK2VaZ0hHdVJ1djVkUDRWSzIwM2VIb2NyUTBuVDZNM0RVdHh6K3Mvb1FFWXNieHJUVEtVTkJWQ1RMWUtRSzhZK3haVVBObW55a3c0bFYzT1pCWldjZENMcE1JRlFxbnhBcmNGeW1UdUE1Wmp5NFVHbXdZbVUiLCJtYWMiOiIyMDBmNTU4MTdmYzhkM2E4ODg5OWRiNmExMGMxNDM2NTczOTdmNjVmYzJhMDUxMDc4YWY0Mzg1ZDI4MTdjZWY1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-222355005\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1478600512 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1478600512\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1324018915 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:56:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImhLNm9rTEs2Z0xWb09WRVlPQkpXZGc9PSIsInZhbHVlIjoiUWNCSXhpMGJER3JMVDd0dkdrWjBSL1R5dGxGbkZYY0ZVSTQ3Z0RjOTBqYlNOcXEzRWdjbUdQOUljTmdOOTlMQWVLZHRleXNqemNzd3BXRmwvbVd2VjR5NFhPeGRVTUY2Ukd5WSsxUGMxYklDcnZBWXp4ZXJYMlFQUURQY1MvT0JuaHE4MHE4U0xLNWhKbW1udTJMa1gwbjZseDJuLzVFN0hTUGliOUNyTEllRWpVK3J4d05HNUNSMVBROFZxcWhJY0ljeE1WTVpsbWpLenNGSHo3QVpaWTdXNGxvclo5UlVrTit5SlNNTkhuQm83SnJORHI1YWpnQkJTS3E3dDdMQmdlM0VzTHgwS0twam1BQi96NDBSWmZqYXE0SmJxMHlYeUQ4cWt0amJaMHk1aFIyR2h4aUZuZ2pTZFhOOUhkdTdGMDg5Y052NFJvQ3l3NWVHYnFZUXpQTjBZVGRYTmdiQURiWW1IbEpQbHpkaWo0WFFYVHZjcFBWY3RmZzBvbnRnWGZPTTVlM0t5dWJYNFhLak9kL0JSSEVXd0NMTkpyOWNkOE0xZDN1dlJGZ2RuKzhTaUNpNXhOY0grY1IwVnJHc1ppbUJySm1SMXhHSVBmZ2h6NUFOcWNtdnJTODl1dno3aVUzVzFlajQrYTRzRjFBSGdHWW10eWlwem9tSTdmZ0giLCJtYWMiOiJjZTgzODNjNjkyNTRhN2IwMzQyM2QwM2YyMThiOTRmYTU2YTJjNzUzY2IwMjI4MjEzMjUzZmM5M2U1ZmU0NmRmIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:56:33 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjJEQ1VRMUFRZWpDZHMwTDA2KzhmVGc9PSIsInZhbHVlIjoiOWRSL3c0TmkwdnhmQy9aMUdmR0hWVzFxcFUrb0tMQ1lmRGl1RkVwSVN2eldlMldSM3RESXdCbGNwZ29jVHNUaHArKzJzaXlkWGdmQVJ2R0FxQkFlTjlqOXJtUUJYZnZCK2ExL3dwOUh3Q2xVai9EQzJoWnV6dTE2ZllFaFdtczVncUQzeTllMm1zQndaK2ppYzZVNTlEV00yQzNVOVJvZnYrcktOaVhnN2FMWWt0cGNxeUJudlFidTkxc0NLT0Jlb1R1MXl4S2hpT2t0enNWR0tOSDV6L1MzR3NlcXJKbjc3clB5K0RJQVlQNzljUzhxakgrQW9PZzdoQWc5cmljbGlLSGd2eU5leWJteDhKUm1PZkhVZ3BORDdyd3ZraFlMT0ZDVEZTM3c3dzFvQVQxSmhSRXhsbFdNcW9UZVR6SUJEUmZMeTlWR0cxeHhtb3Y1MXV2bDBPckNVMDBwUGdwSkNnMDdsSkhvOVhWdmNFNUIreklJOVQ2dDZHL00yRzlpcjJVKzcvOVNyRXlvcExGVSsraHE0VEhMbEJTRDFsMysycDNkbXJvMW9VRHdjNFlVdy9DOEN2TmpxVHJBMHB6aTVzUnMrcWlPRlA5ajRuZ3dMSFd1OURkZEpSZUZuWURkVjFXNFVCeEhWSEhWbG5uOWR2WVl0V1NEeE41S3RpWVkiLCJtYWMiOiI5OTc2YzNlNmViM2MxMzMxYTFkOTE1MWFlOTY2ZTU5ZjNlMmQzMTkxNzJkNjI4ODFiNjlkNjE3NWU3ZmFkYmU3IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:56:33 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImhLNm9rTEs2Z0xWb09WRVlPQkpXZGc9PSIsInZhbHVlIjoiUWNCSXhpMGJER3JMVDd0dkdrWjBSL1R5dGxGbkZYY0ZVSTQ3Z0RjOTBqYlNOcXEzRWdjbUdQOUljTmdOOTlMQWVLZHRleXNqemNzd3BXRmwvbVd2VjR5NFhPeGRVTUY2Ukd5WSsxUGMxYklDcnZBWXp4ZXJYMlFQUURQY1MvT0JuaHE4MHE4U0xLNWhKbW1udTJMa1gwbjZseDJuLzVFN0hTUGliOUNyTEllRWpVK3J4d05HNUNSMVBROFZxcWhJY0ljeE1WTVpsbWpLenNGSHo3QVpaWTdXNGxvclo5UlVrTit5SlNNTkhuQm83SnJORHI1YWpnQkJTS3E3dDdMQmdlM0VzTHgwS0twam1BQi96NDBSWmZqYXE0SmJxMHlYeUQ4cWt0amJaMHk1aFIyR2h4aUZuZ2pTZFhOOUhkdTdGMDg5Y052NFJvQ3l3NWVHYnFZUXpQTjBZVGRYTmdiQURiWW1IbEpQbHpkaWo0WFFYVHZjcFBWY3RmZzBvbnRnWGZPTTVlM0t5dWJYNFhLak9kL0JSSEVXd0NMTkpyOWNkOE0xZDN1dlJGZ2RuKzhTaUNpNXhOY0grY1IwVnJHc1ppbUJySm1SMXhHSVBmZ2h6NUFOcWNtdnJTODl1dno3aVUzVzFlajQrYTRzRjFBSGdHWW10eWlwem9tSTdmZ0giLCJtYWMiOiJjZTgzODNjNjkyNTRhN2IwMzQyM2QwM2YyMThiOTRmYTU2YTJjNzUzY2IwMjI4MjEzMjUzZmM5M2U1ZmU0NmRmIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:56:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjJEQ1VRMUFRZWpDZHMwTDA2KzhmVGc9PSIsInZhbHVlIjoiOWRSL3c0TmkwdnhmQy9aMUdmR0hWVzFxcFUrb0tMQ1lmRGl1RkVwSVN2eldlMldSM3RESXdCbGNwZ29jVHNUaHArKzJzaXlkWGdmQVJ2R0FxQkFlTjlqOXJtUUJYZnZCK2ExL3dwOUh3Q2xVai9EQzJoWnV6dTE2ZllFaFdtczVncUQzeTllMm1zQndaK2ppYzZVNTlEV00yQzNVOVJvZnYrcktOaVhnN2FMWWt0cGNxeUJudlFidTkxc0NLT0Jlb1R1MXl4S2hpT2t0enNWR0tOSDV6L1MzR3NlcXJKbjc3clB5K0RJQVlQNzljUzhxakgrQW9PZzdoQWc5cmljbGlLSGd2eU5leWJteDhKUm1PZkhVZ3BORDdyd3ZraFlMT0ZDVEZTM3c3dzFvQVQxSmhSRXhsbFdNcW9UZVR6SUJEUmZMeTlWR0cxeHhtb3Y1MXV2bDBPckNVMDBwUGdwSkNnMDdsSkhvOVhWdmNFNUIreklJOVQ2dDZHL00yRzlpcjJVKzcvOVNyRXlvcExGVSsraHE0VEhMbEJTRDFsMysycDNkbXJvMW9VRHdjNFlVdy9DOEN2TmpxVHJBMHB6aTVzUnMrcWlPRlA5ajRuZ3dMSFd1OURkZEpSZUZuWURkVjFXNFVCeEhWSEhWbG5uOWR2WVl0V1NEeE41S3RpWVkiLCJtYWMiOiI5OTc2YzNlNmViM2MxMzMxYTFkOTE1MWFlOTY2ZTU5ZjNlMmQzMTkxNzJkNjI4ODFiNjlkNjE3NWU3ZmFkYmU3IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:56:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1324018915\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1201553608 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1201553608\", {\"maxDepth\":0})</script>\n"}}