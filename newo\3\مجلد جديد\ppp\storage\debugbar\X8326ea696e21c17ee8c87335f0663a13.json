{"__meta": {"id": "X8326ea696e21c17ee8c87335f0663a13", "datetime": "2025-06-17 14:36:03", "utime": **********.587936, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750170962.755693, "end": **********.587963, "duration": 0.8322701454162598, "duration_str": "832ms", "measures": [{"label": "Booting", "start": 1750170962.755693, "relative_start": 0, "end": **********.485725, "relative_end": **********.485725, "duration": 0.7300319671630859, "duration_str": "730ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.485739, "relative_start": 0.7300460338592529, "end": **********.587966, "relative_end": 2.86102294921875e-06, "duration": 0.10222697257995605, "duration_str": "102ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46326408, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1663\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1663-1673</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.009420000000000001, "accumulated_duration_str": "9.42ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.540447, "duration": 0.007940000000000001, "duration_str": "7.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 84.289}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.568255, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 84.289, "width_percent": 15.711}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1845871051 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1845871051\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-255349620 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-255349620\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-489136666 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-489136666\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1285532153 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik1Hck9oQ0JNVCtEam5YNDdkcHQyZlE9PSIsInZhbHVlIjoiMytBNXA2cHpoQ2NjNDU5M2FLV3JNV0tZK3lzaitaK1JHb0N4ZmVhWDk1WURKR2NnQzIvaG5QY1J6U0dJVHhWRGp1VEVib0R6dGU5U0ZIQ2dhS2VlM3Zab2dsSGtHbGpaWTd3a1dta2xXTG03MlYxckJLejFJdHVOcFh0VFBjMHh1cWcwTGMwbjFBemZUaTlNWnhJemFaS290QzhIUFdETnFqeWJWNE8vOEhmSVp5bXF5VzFFOVBjTDUycWFDR3Q5V2pBU3VyWFhpMWJxNlRBYWFVTnlDMzFYeWs2SkhQVmpqSlpIMGdBUmdteXpKTXVlbkN1UzR1UmlsK1RZdXQ0V3V2ZGV6dU5PNFdaMGFNd1B2b2xUbmxLMWJPSXV0NndqQzVzZFJjM2xtUlRSL0dWbDR6dDdWNmlNdG9Idi9oSlZKdzR1QmdxQ290ejBkMXY2MS8rekJWN3RTWjgveHNGR0tjaGY0SjkvYW9EbXpiendlVHV2NGUyZGk4QVRhbzRlUFhDZG5kSS9WUXRFY0k4MUNhZyswcUdpOHdDbUhnZitFMEs0NjRUaXBIMVBLcDU2QjdOTkRwb01HU0d1Uk5yam5sVG5iVE1OS3VxN1RMWitBU09GbFNsME90cjlhMHZaR0VQOGNiT2I0YVZuUTRaOWR0Nkk4alR6Q3FQYTdEMy8iLCJtYWMiOiI0ODhhYzRkNGUwZTBjMWUwZjEzNDA1MmQ2YTA2NzJhNmY4MDg5MzNkMzBlNzFjZDA5M2RmNDUyMjY1NTU5ZDBiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Im1nM1ZhYnd4bTFLQUw3RjUzYlFrNnc9PSIsInZhbHVlIjoib0NlTGhkNlliMXpXWmxITGQwRE5YTzZBV0dPV05ENmx0Z0lEamF2eG9JZk1JeFdDOVkvTTZlV1MxeWxKZlVUNzl5TjdERlVKOW9saStTQnJBMlJNNERCK2ovVldFRHpDT1llcVBJTDMwOTEwY2luNWxKK0xURUdqR2ZzR0VYV0Z3ODl3dUhDSTJSc2VQd3IyYUgyRkRQRmd4TkhFQm40enB2d2FHV2QydVdsRXM4UjRHZGNWV3FUMEFQaUtpWWMvUFRlYjBoaUR0NExZbzJsZTU0T0NDOWs0TU14SmRLUmc3eFlzUmJmM1d6cnZ1eC9CUjE5WVlpSjE1UDlHdStuMFRsVG1NLzEzNmczNlVDVVdQTWxCWTZQMEVnZWxCWDUrbEZTSnQ1VXQ4MGZEQ3JsTzRXZzVxS1hrL0wyMTdoMDhuYm0wUU15YXNwRHJtTEdRVVNpNGtZN0xaUnNaN3p4QUVvdXNHQnN0eksrNFgzWk03OTlScGI5S2tDNjYrbVplTm1nbzNpeEFqd1BBdHh0WE9JemY5WklSbzdMNmZYUEJFRTlTK3R2cldGZFJBV0dXUm9zWDRLbTAyTkxxS0xlR3JPeDIwbGIwekQ4WHFHS01LcENhOTNtZkZ5ZjhFeSs2UDU0WlFkQ1RJU0JHT2ZlUk5RZ1g4KzQ0alQwOVJ0WWYiLCJtYWMiOiJhYjQ5MGQ4Yjk5YzBmNTUwNjYzNWM4M2RjNmFiYWZlNjBhNTI0ZmMxYTYxY2Y4N2VhYTkzYzJkNjhiYmUzNWU3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1285532153\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1477639587 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1477639587\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1967833970 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:36:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlBIZENMSFNZVW9TL0hsY2tIeDBzUHc9PSIsInZhbHVlIjoiZ2dmMDRjbmNzVlNLNkEzN0FSRFRvLzl2Q0RMYXJwU3A5R3RwMUk0dmFQS3IrLysrK2ZrYm1NbndPMUJDMHFqTnFzZVI0Z2wvR3habENyZVNXWHBCT2FPWjdqdHBuMzhQZFViRHhtb2ptWXRoZUxtN3VUSm5tcEh6RDUyQStVb3RCa0VMUTZDeGVteWF4VXVJNHlkWWZkREt6SStxeSsvUjNEV0M1VUlPZzZhdTlPeS91dXhoV3VYclZVREVTaENITG5KcHBxa01RT3NHUU8vcnZFcGZOK0wxUitpQ2kzUmtOT2dOU1piT3JvczFEc1NibFV5QUhqYTNCTnRBWHArRDYvNDltR0RlMUdQTWFnRytuOGo2SU1HT3RIcnJrSVBlaERCMFpYanB4V2E2RGxOUUU3all3WjJPRnVzVHIwbG5PcHJmRm5zcW9XdG1qOERXcXMwc1Q2ZUttR2RSRHVwd3VoMGcxWFREU1dmTFdDT3R0ZmJjaUovemtwRzd2NGNzdkdKRXl4MUtPV25rSE5TRDNWNDZFQldqTXhZTVg5b05DTSt6Y2JIdjBoVEdIYmdsb05UVjlDaUFYVi9waEpjSkRsQm1UQTRtaUtXdWUxMHRsV2h5TUhFTXR1SXMwcjdKK3ZXTmwrYkJScFF5MXVicE5SQURxay9qNytVblpUdVciLCJtYWMiOiIwNDkzZDc1MGMyZGFmNzAyMGM0NjJjZDM1OGJlYzc2MmQ1N2RhOGUwNDMzMDJkZjk2NWZlY2YyNDAwOGY5ODIxIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:36:03 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InRpMW83RjZPK1A2SEV2NHRsa3dKenc9PSIsInZhbHVlIjoiNU5zMlQ4bXpWN0F3WWVLWEZBMkUyZlFuRkNUQmt6ZWwxWHNwQ1lvRkxEblZSb2k1MllUUkpzRjUzVlljUUplYUd4ZURmbGo4cWhJZUg4VW0yZUtRSzVsbDhORVF0Z05IaWduaGhoTklsK0xNOGNMc2hlOUorbGNtRkxIVlM3VmRkVTF5UitFbG1uRTVJZFN2N3ZYN0w4WnpkOEhQR2RCckxlQmtoanlHK0ZES3NFSU44NFBKU3FoRXN6eFNOMnZ1TEo5TFpuU1A3dUROek5mS2xPZ3NIeW1NcVRoL2pqNEtodG5QaCtKbGV2OW5iZ0hGU1dhQTVmMGhaeVI0N01oTEJkTCttTTFJK2NINUh0bS95eVNNa1JOUndXMTlnS3plSHh1SmxrYS9NekNldEZSRjZFY21zRUczMXNBK0FNaWt2bG5udDdMVlk1U1d2MWV1V0F2YnEzMXdHYnU5K1FhR0E4RUhtTXR6ZFR1NUFBTUdGaWtOYlNEOTl2eTNKdVNSTWVCNUZnV1RKcWNISk5IV0JjT09EU1Q2STVRSGI0K25vUHgyRitnZmpmV1RRdEJ5a3F5ZVJUYU9VNmEzUEZ3dkZsc1dMSjR0N2FJc2hVL1Q2aVhNTE9ybENSR3BNZXdGZUhSc29DTGlKR0sxVjZVS2V2bTVsaUg5VnBEMitwdC8iLCJtYWMiOiJhNjU5YWE2YTExODdiNGQ2NDJkYmIwYWMxY2M4MmYwZDhkZDI5ZWYyNDg1MDM1MGEzMjkyZmMxM2RlYjExMTZlIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:36:03 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlBIZENMSFNZVW9TL0hsY2tIeDBzUHc9PSIsInZhbHVlIjoiZ2dmMDRjbmNzVlNLNkEzN0FSRFRvLzl2Q0RMYXJwU3A5R3RwMUk0dmFQS3IrLysrK2ZrYm1NbndPMUJDMHFqTnFzZVI0Z2wvR3habENyZVNXWHBCT2FPWjdqdHBuMzhQZFViRHhtb2ptWXRoZUxtN3VUSm5tcEh6RDUyQStVb3RCa0VMUTZDeGVteWF4VXVJNHlkWWZkREt6SStxeSsvUjNEV0M1VUlPZzZhdTlPeS91dXhoV3VYclZVREVTaENITG5KcHBxa01RT3NHUU8vcnZFcGZOK0wxUitpQ2kzUmtOT2dOU1piT3JvczFEc1NibFV5QUhqYTNCTnRBWHArRDYvNDltR0RlMUdQTWFnRytuOGo2SU1HT3RIcnJrSVBlaERCMFpYanB4V2E2RGxOUUU3all3WjJPRnVzVHIwbG5PcHJmRm5zcW9XdG1qOERXcXMwc1Q2ZUttR2RSRHVwd3VoMGcxWFREU1dmTFdDT3R0ZmJjaUovemtwRzd2NGNzdkdKRXl4MUtPV25rSE5TRDNWNDZFQldqTXhZTVg5b05DTSt6Y2JIdjBoVEdIYmdsb05UVjlDaUFYVi9waEpjSkRsQm1UQTRtaUtXdWUxMHRsV2h5TUhFTXR1SXMwcjdKK3ZXTmwrYkJScFF5MXVicE5SQURxay9qNytVblpUdVciLCJtYWMiOiIwNDkzZDc1MGMyZGFmNzAyMGM0NjJjZDM1OGJlYzc2MmQ1N2RhOGUwNDMzMDJkZjk2NWZlY2YyNDAwOGY5ODIxIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:36:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InRpMW83RjZPK1A2SEV2NHRsa3dKenc9PSIsInZhbHVlIjoiNU5zMlQ4bXpWN0F3WWVLWEZBMkUyZlFuRkNUQmt6ZWwxWHNwQ1lvRkxEblZSb2k1MllUUkpzRjUzVlljUUplYUd4ZURmbGo4cWhJZUg4VW0yZUtRSzVsbDhORVF0Z05IaWduaGhoTklsK0xNOGNMc2hlOUorbGNtRkxIVlM3VmRkVTF5UitFbG1uRTVJZFN2N3ZYN0w4WnpkOEhQR2RCckxlQmtoanlHK0ZES3NFSU44NFBKU3FoRXN6eFNOMnZ1TEo5TFpuU1A3dUROek5mS2xPZ3NIeW1NcVRoL2pqNEtodG5QaCtKbGV2OW5iZ0hGU1dhQTVmMGhaeVI0N01oTEJkTCttTTFJK2NINUh0bS95eVNNa1JOUndXMTlnS3plSHh1SmxrYS9NekNldEZSRjZFY21zRUczMXNBK0FNaWt2bG5udDdMVlk1U1d2MWV1V0F2YnEzMXdHYnU5K1FhR0E4RUhtTXR6ZFR1NUFBTUdGaWtOYlNEOTl2eTNKdVNSTWVCNUZnV1RKcWNISk5IV0JjT09EU1Q2STVRSGI0K25vUHgyRitnZmpmV1RRdEJ5a3F5ZVJUYU9VNmEzUEZ3dkZsc1dMSjR0N2FJc2hVL1Q2aVhNTE9ybENSR3BNZXdGZUhSc29DTGlKR0sxVjZVS2V2bTVsaUg5VnBEMitwdC8iLCJtYWMiOiJhNjU5YWE2YTExODdiNGQ2NDJkYmIwYWMxY2M4MmYwZDhkZDI5ZWYyNDg1MDM1MGEzMjkyZmMxM2RlYjExMTZlIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:36:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1967833970\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-12688834 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-12688834\", {\"maxDepth\":0})</script>\n"}}