{"__meta": {"id": "X584e05e4520e755e0e798ff88c2cd41a", "datetime": "2025-06-17 14:57:07", "utime": **********.93859, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.306645, "end": **********.938614, "duration": 0.6319689750671387, "duration_str": "632ms", "measures": [{"label": "Booting", "start": **********.306645, "relative_start": 0, "end": **********.796766, "relative_end": **********.796766, "duration": 0.49012112617492676, "duration_str": "490ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.79678, "relative_start": 0.49013519287109375, "end": **********.938617, "relative_end": 3.0994415283203125e-06, "duration": 0.14183688163757324, "duration_str": "142ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49205496, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.03431000000000001, "accumulated_duration_str": "34.31ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8434129, "duration": 0.02691, "duration_str": "26.91ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 78.432}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.881673, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 78.432, "width_percent": 3.031}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.9062319, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 81.463, "width_percent": 2.652}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.90993, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 84.115, "width_percent": 2.39}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.917143, "duration": 0.00256, "duration_str": "2.56ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 86.505, "width_percent": 7.461}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.924565, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 93.967, "width_percent": 6.033}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1439299529 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1439299529\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.915536, "xdebug_link": null}]}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-808871746 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-808871746\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1562669756 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1562669756\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-86034437 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-86034437\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-137402796 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InF1QkJIRHFXMWd1OW5id05YR0lPeXc9PSIsInZhbHVlIjoiaVY5ckRIc2o1bkpHakVubHpIVHl2MllhVmFINkJveHhzWDY3NDhRcjB1Z0Z1ZVdDMllmR09wQzBPL2lpU0ROZHdDZG14cUNBUmdrekJnL0p0dHpMZHdwd1Bza1d5SEd5d09zKzBlbUIvd3d3MDd6emdwMGVzMjkwcVVOdkFXS1hRREVUZEJ6Tm8zL05ZWnhIYmJXcWVlTHdUWndGc0JQRXNVa0RjWnhTbkplQ0RMWGpGTmR2WmF5eXl4cHhybmhWUGt4dlh5cUc2UFA3b21GTzlnOTUrUGliRkJMVjByMmJsYXVYSjNhVlBnTDlsSXZJckoybkVLUU4ra2wvR2hGTzQydW1vR2xqZStoSmJlcmVxNGlaUmttTm9ld0s1dHEvVE13UVdtRkhXQTRxQnM1cnNsZjBRVFFoWWlmWHoxcmEvMVo3MnlKRjlaQXNoRncxbm8zZ3VQNmNBL1hmVllabHlna3hSTHd1ZWhXanlHMEovU1ZqZWtUeEhkd1U1RlZBdkx2ck01MzlJWklTNGJzbis4U2hnNnBnWUlzVXlXZWlDdzVpK3ZPUDcrak90VVNHRzNLV1huWi9rN0N4Ky94SUpyRjVBMGtDNnc3azJ4bUs0bnJFWDRsd2trYW83aGd2Tk8rMVFWWERpMFE0RC80bWsyaTlyZUJOVVJzOXptejMiLCJtYWMiOiIyMjZlMzRjZDc0Y2U4ZjRkNmU5MDU1NjE3ZTAxNDZiMTA0MTA4OTZiYmY0NzE2Mzc2MzNkYTc2OTgyMjVmNGM4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlpnUGRHeU8rNC90aTFrQWRYNTB2NWc9PSIsInZhbHVlIjoiMHBrN0J3dGFyelU3QzJnTytSM05wUlJiMzhpSDNlUllHTFAxSGpZN3Z1TisrNGdQbGg4U3c1OU5mMlFaYjdvMjh0WVRJZjN5dFl3OGdEVkFEZXBDVFdNd05oSjRkL3JXL3lUei9SNjRscTNwM051QytTVVkrWkt6dzZKclZIbEFtNWZ5elNucG5IRlNBV1I5ci9YamtqMktCNG1JU3YvdkxzeVZ5b0didkRVUU9SZ2lGTjJnSXo4YTdCTWpvTVhVZ2xneFdCcDV6MUFINURUOSs4MXhySXFiRjM4RURLYU8zR1BRcmVkb0dtU1lkVHUwOHZzR3lSOGs3WWxKdTBoYTZYbW1MY0ROM1o4ZkhiQmpkYkNUUTNrZ3JvWFdnY0Mya2c0a0tnZms2QS94Smo1b2NWdGRRTm92K0xOTm1nQVE2NVlnRUYrai9VM2MxbFEybzVJMnRlWERzeWd3bmhwdFVPdFREVHJIN28vVFpObUhZajFHbkJGakJRVno3L0pGQStCNkV0TjhyMnU5QUQzcWswdjB0VDB5WTNMZEF0NDVrcTFadjh0dE5qRXdrOXJZTXZRelZ6SDNNUDJLTVpLeU5nUldLVjNhZmNrNVpJUDZUeXhlU2VJaWVCMEV0ZGY0dGJ3M2Y1OHlRYmZJeHFxdncwdXZBMERJMFB2VE9UZFIiLCJtYWMiOiJlNTRkM2RkZjIyMWM3MTRkZmNmYmZlZDViMGM4NTMwMjEwMDVlNGNhZWMzYWM0Yzc1OWZmNmJkOWRhMDNiZGFiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-137402796\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1861981065 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1861981065\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-998947787 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:57:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjlETklGSE1OazRuVkhMbUFuSXFzQlE9PSIsInZhbHVlIjoicGZqR1VSaEVUUEdZNElqOTh3UGlHdlhTZ01Wb0NUazVxVFgwZ3VDSDdFc0txeFZ4c0N1dFRnOUJzSFpSLzEraUZwNDFQcDVhcFBjWVpkajFoQTFINDh6RERnZ3M3S2EzaVUvYjFrQ01xWFBENVZHMFh1NHdxYTdzWHVJUFUvNW5zT25UTFpwLzlESTVwRWZIdEd2ZStyZkhQOTRHNkYrN3FuUVNwSE9PUDZCTURNY1JZSzdxZGNrb3lJU1JNWGlRQ1JKa21jdW1jNHVEVk02Q2VCeS90SFI5NnEzaHJxTTdZdDBEenNHcXlwYkNsQXc1MDFPMVZ3djJTbDRveTllaVJHUkRVM2xJUGQ2SkxuV1oxZFU2MGpGNlg5N0g2aGxja2pZM1J3bTlGblppV0hUbTR5NHlYWkF5M2Z5ZWVBemkrN2FYMHR5OFZWZldsMWRJSzdLRUdZU3k0ZDczMzlUQkNvZXZvVVNBdjllNFk5NXVXNWVmam5iN0ZuU1Z1NTE3ZU1FQWZvd0l2V0FoLzNkeFZnSXRlUHRVeVZ6TWZaRTh3dkdPZ0RlZzFlSUI0YUpFK3dkZ010aysyUHdRMGZ3OFY0MEdFSGVXUlBmMkxSVXVMbSs5K3dCZGtLNTJ6VnRSRS9SV3hoU1NnWHBsYTFDTkx2L0thY1dIQ2htWjVZbWgiLCJtYWMiOiJlN2VhMGQ5ZTg4YTcxNDNhYjZlYWQwMTEwNTY3OGExNzBmYTVlYmEzOTIwYzQzYzk0YWIxNzNjOTU3YTAyZWZjIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:57:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ijg5UW10RTUwdUM5M1dOamYxWUV6TlE9PSIsInZhbHVlIjoiaTY2K0YzM0RIR3BWeFRVVWdpUmY3VGJSaVdUY054eW1MajR0WUJsQkUybldPQThtNVowVVAyRjE1RzUzZ3EvYUNvVXo0YmRCSGtIc0FtQTJka1llS3dZL2RicFEyQ0ttbXJ4RzJPaytkVTl0MWw2VFh5RlhBc1NBT2c0VEtMTUFyQTRWRDk5Qk1oV3FEbUhhaDVCOXVZYTdGNll0NDVBMGtMdzR6RFFmRTJjVGx2ZFBaN1ZzRmhrQjIwSHhIV1h4LzA0T0I5UjdVMUhRUWVtWVdnK0dLUWZsRVhhalJOaE84ZlNMUytFRFJ6MVVRK2JIVzZUNFBFeWIrbU15Z2g0OWI4Q0hxNGFkZHo1QUxSVm1mcnM1ODJMeEs4Q0NBOEQyMTlWbjNwTXArcDI3elNwQUFDczI4L3lTdUZhRy9XckVsSHVZdUdyNWM4ZWF6dmo0VHBvU1BBd1l0aUp5Sk5hZ3JIa0YxcDVEb28yVWZzeHhwaHF2WXJ1dnlyZzh2eFEzN3VjTmNSRFI0MFpjbnVCczB6dXREMjhqMkMreHN1QXZaeGJEOHM1c2xuY2tHRnBVWUxuNmlNV09ncngyNVRYSUprOFRmSjRydy92ZTZIQ3pLNDhPVElZZXZ6dTlzN2VNVnEyNDQ0ZWRtQUUwdlJBSFkrdGhmMWE4VXR4eXNtdVUiLCJtYWMiOiJmZGIxNGMzMzA3NDc4NzNiZWIxMTllNjBjY2Q1YWVjYTY3ZDUxZTNhZWM1ODUxNmM3ZTIxNmNkMWEyZDdmMjc4IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:57:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjlETklGSE1OazRuVkhMbUFuSXFzQlE9PSIsInZhbHVlIjoicGZqR1VSaEVUUEdZNElqOTh3UGlHdlhTZ01Wb0NUazVxVFgwZ3VDSDdFc0txeFZ4c0N1dFRnOUJzSFpSLzEraUZwNDFQcDVhcFBjWVpkajFoQTFINDh6RERnZ3M3S2EzaVUvYjFrQ01xWFBENVZHMFh1NHdxYTdzWHVJUFUvNW5zT25UTFpwLzlESTVwRWZIdEd2ZStyZkhQOTRHNkYrN3FuUVNwSE9PUDZCTURNY1JZSzdxZGNrb3lJU1JNWGlRQ1JKa21jdW1jNHVEVk02Q2VCeS90SFI5NnEzaHJxTTdZdDBEenNHcXlwYkNsQXc1MDFPMVZ3djJTbDRveTllaVJHUkRVM2xJUGQ2SkxuV1oxZFU2MGpGNlg5N0g2aGxja2pZM1J3bTlGblppV0hUbTR5NHlYWkF5M2Z5ZWVBemkrN2FYMHR5OFZWZldsMWRJSzdLRUdZU3k0ZDczMzlUQkNvZXZvVVNBdjllNFk5NXVXNWVmam5iN0ZuU1Z1NTE3ZU1FQWZvd0l2V0FoLzNkeFZnSXRlUHRVeVZ6TWZaRTh3dkdPZ0RlZzFlSUI0YUpFK3dkZ010aysyUHdRMGZ3OFY0MEdFSGVXUlBmMkxSVXVMbSs5K3dCZGtLNTJ6VnRSRS9SV3hoU1NnWHBsYTFDTkx2L0thY1dIQ2htWjVZbWgiLCJtYWMiOiJlN2VhMGQ5ZTg4YTcxNDNhYjZlYWQwMTEwNTY3OGExNzBmYTVlYmEzOTIwYzQzYzk0YWIxNzNjOTU3YTAyZWZjIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:57:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ijg5UW10RTUwdUM5M1dOamYxWUV6TlE9PSIsInZhbHVlIjoiaTY2K0YzM0RIR3BWeFRVVWdpUmY3VGJSaVdUY054eW1MajR0WUJsQkUybldPQThtNVowVVAyRjE1RzUzZ3EvYUNvVXo0YmRCSGtIc0FtQTJka1llS3dZL2RicFEyQ0ttbXJ4RzJPaytkVTl0MWw2VFh5RlhBc1NBT2c0VEtMTUFyQTRWRDk5Qk1oV3FEbUhhaDVCOXVZYTdGNll0NDVBMGtMdzR6RFFmRTJjVGx2ZFBaN1ZzRmhrQjIwSHhIV1h4LzA0T0I5UjdVMUhRUWVtWVdnK0dLUWZsRVhhalJOaE84ZlNMUytFRFJ6MVVRK2JIVzZUNFBFeWIrbU15Z2g0OWI4Q0hxNGFkZHo1QUxSVm1mcnM1ODJMeEs4Q0NBOEQyMTlWbjNwTXArcDI3elNwQUFDczI4L3lTdUZhRy9XckVsSHVZdUdyNWM4ZWF6dmo0VHBvU1BBd1l0aUp5Sk5hZ3JIa0YxcDVEb28yVWZzeHhwaHF2WXJ1dnlyZzh2eFEzN3VjTmNSRFI0MFpjbnVCczB6dXREMjhqMkMreHN1QXZaeGJEOHM1c2xuY2tHRnBVWUxuNmlNV09ncngyNVRYSUprOFRmSjRydy92ZTZIQ3pLNDhPVElZZXZ6dTlzN2VNVnEyNDQ0ZWRtQUUwdlJBSFkrdGhmMWE4VXR4eXNtdVUiLCJtYWMiOiJmZGIxNGMzMzA3NDc4NzNiZWIxMTllNjBjY2Q1YWVjYTY3ZDUxZTNhZWM1ODUxNmM3ZTIxNmNkMWEyZDdmMjc4IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:57:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-998947787\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2078410252 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2078410252\", {\"maxDepth\":0})</script>\n"}}