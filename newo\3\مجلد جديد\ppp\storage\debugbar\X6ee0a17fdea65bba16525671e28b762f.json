{"__meta": {"id": "X6ee0a17fdea65bba16525671e28b762f", "datetime": "2025-06-17 14:43:21", "utime": **********.119249, "method": "GET", "uri": "/customer/check/warehouse?customer_id=10&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750171400.540481, "end": **********.119272, "duration": 0.5787909030914307, "duration_str": "579ms", "measures": [{"label": "Booting", "start": 1750171400.540481, "relative_start": 0, "end": **********.034056, "relative_end": **********.034056, "duration": 0.493574857711792, "duration_str": "494ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.03407, "relative_start": 0.493588924407959, "end": **********.119275, "relative_end": 3.0994415283203125e-06, "duration": 0.085205078125, "duration_str": "85.21ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46235896, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01515, "accumulated_duration_str": "15.15ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0760431, "duration": 0.01293, "duration_str": "12.93ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 85.347}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.103195, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 85.347, "width_percent": 5.545}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.108979, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "kdmkjkqknb", "start_percent": 90.891, "width_percent": 9.109}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  2045 => array:9 [\n    \"name\" => \"علي\"\n    \"quantity\" => 1\n    \"price\" => \"8.00\"\n    \"id\" => \"2045\"\n    \"tax\" => 0\n    \"subtotal\" => 8.0\n    \"originalquantity\" => 3\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-1181068136 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1181068136\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-490126389 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlBMVjNxRzlaRklyNlJKNE1QV1JTQXc9PSIsInZhbHVlIjoiUkZ2WUhJTWRYTCtkU09uK3MyR2VzVjZlb3k4UUV0a1hFc05KR2NCTDdsSU5neHpqaDZ3ZG5rYXR4L0h1QVJpejRJYkU4RHNnS3lTRkFJWUtmUHJ3QXVybXI4MlBhbzBYV2N0dy9TVXBTdkloSjV0T2pBZHBLUjZxMDNTMnVPdTl5L3MxZVQzOVRTRGQwZFZULzNBbFZteC9zd3ZDcUpBVW1PN1pPamw1RG9ITENDNEM2aFU1R29kMkFjM0owODZWRGpLUForM09XZGFyOVdHWnlORkV3bmVoV3F3K0RDNXRraDNaWTR2M0JoVUtjMC9YbThicHlWc1gzSS9ac3I4TS9nWldoZ1BEeGhPZ1o0RFQwS0pWVlhPOFNGTUNiMDh4ZEhnZDJUYjBCVXJ5aGtLV2x6dk5hRGZLN0Z6cDY2VDZEVkkyeGFZUEVlV3Vnd3JSbmxUaWkwRURGdHpKcldzYWRkSVlVZ04rTTllZnRmMFpQMU1aUmVZb2h0VWttcjJkYnZVVVpxVXo3ekdxQ0lZS0h2L1FCL2JrS3cxdjUxNDZOeWlOdUdiZGVTOWtiSWZtWDh4aHZldnQxWTMrQTBJQWNhZ3VjMXRrUWJuei96MTNhSEtEdFhoOWhIbFN3YmdHWnlINEJhb1ZYNjd6azVtME9wL1lRdjM0akhaQVROeUYiLCJtYWMiOiIyMjMwMjUyZWVkOWY2OTU1YmY5MWJmOTgxODE2MjkxMjczYjg0MjEzMmVjNDEyNTliMjE3ZDI3MmUxZmY2Y2I2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InV5dGVBaHM0R1c2bWp6VmRDcUpoNHc9PSIsInZhbHVlIjoiZjl6cStCSjhvOGNNbUloWGY0ejhaelpXNTRLMHVYWXI3NlN5N2t0YjF6WEl2dUpkQ0ZlV2srMzJOUWdhMkVKeW9nYVlsL1EwVHRoZEpSRnF4SDZpL0Nqc1ZaRG41WjlWSTNDUEtsVDAzbzc1Ui94SG0vbmExWGpXcHJ5Y2Q5N3ZYTkQ2djAzcVVPOEhDcnVRbmdCalczUS9HUnkvWWZLOUtUME1qMFNveUp2ZEEwemhHRGs1dHdNUm9GWDQ3bHFYcE1EclhjeXlyS3Y2dGF3L1A0ZVNySUg3SWhsYnpvandMQlA4YlV6dW4vUTFMbkN4OFllSTN2SnB5Qnh5N3U0aWpHWVlQMnI0ZFRlTjRDR2tZdnUvRDV4M29wbmNpeUtSemFNU3Rwd3NKSVI0cDRxc2ZqNi80cjhhOHBQQXl0d0VSN2dZaWtsYlhBcmtVZEtNdWZld1I4ekZpNDBCQ2xBZGNRTTQzTEU5M3lRUjZuUWYwNEN2UDFoZUpNMXgrY1pQSUp2UnBTdUhzSTJXMTlxY2Q2NUN0dE1lNnF0QWQ4dytsbG9iNnppcHdzRWhsQm5Pcm1vc3dsMWNES0FQTSsvUFV6QktoNjhndm5jNENHMzNIMS91MHRDa2pjSytjK3pIam1pbGxoUkNydEF3MVIvWWhieExjRjhqb3FTb2M2QnQiLCJtYWMiOiIxYWE4YWUxZDkyMzAyMDU5MWRiMjUzZGRjYzYxMDAwMDYxMDdjODZjMTNiYWE3YzVjZDcxNmMzNTRlMTU0MGJmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-490126389\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1658015999 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1658015999\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1695040271 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:43:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImQ1RzhvdUZVajJTZGhTbjZpOGUvZFE9PSIsInZhbHVlIjoiWG5DSVMrY3FLcnJLcXJCc0pKSDE4UUxrOC9qQXlpU1IrVzBpeUI2aDhnY3Q0cVpiZnlScWo1dDZrUHU0VWR3YW1PS2lJU0ZVVFlBSmJzd0dHNEhvWXRkR1hDb2Iyc0lPVUJDY3FCT01hNEYzeS9pQ2xQc0dOVTVEbHgvRmptM2U4UU9aSXpESXN3WURwbWliMEdYQVEvRG9IZEV3UmNMNFdGd2MrUGF6TktiMDVjb1Z1clJRQ3ZXU2hTSzA5dlRUY211NXNiS1NSdHZ6WEZad25mSDh3OFlKMlVjTWRnVEZOakZwOUhQcUlJbXRwNWxia1BPZGpzNDF5Z0N6elRIVncrZ0trN1RTMCtWSGRPdDlFRVhBNEhqSEVRSmpmRWk4YkRSQytBLytEUFlKcjFpOThUTHZPVDRFcjNIZFA0M041RER1QmFmRjB5M3FMbHVoWDBKRXVocTgvV1l5a1EvY0toeFZtVzh0Wm55NDc3aVBPY3ZpY0svREkyUG5OQ2lUczV2YWlHeGlTMUxlVFlBOVpVR3ZxTWFqNXRWc2ljYmJ4M2lRV2JhRndHUXEvS2g3YkN1TVgzMHl2eU53S1VKc2V0TitrUi9CZG9FckM2NkdqamtUKzZJd2dUZjVRUlBTdHBvdEllc1FKZ09PZGFiTU4xR3VzbEdWczdQbk5EVXQiLCJtYWMiOiI4N2Q2NjQ4NzBhNWU3YWM5MDkyMjE2MzFiNTU3OTU1NjE2MTY2NjgxMGY4NzNjMjQ4Y2M0ODRhZDk3YTA0MzM4IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:43:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjJ2VjhYUXFBcWZSNmV3OWZ1S3pFOXc9PSIsInZhbHVlIjoiOE5iTmhhVjRaMjllYTkwL2JVanlnajVKS0s4OTBxdUh0dlBFVFF6U0RYMmpJQ3JUT25CekxueXo0YzdLUHNQUG1pM0VzRm93N2J3VVh4RGZCTjEvM2pUZks2QnYvVnVrREtwMEIwWlgzNkl5MCszMU9odE5wWmQ4R3ZqS21vZnhYU1I1bm1BMS9xdE5OQ1NWZ3BhLzFRWU5iODBTRnlVRmwrcHVaMGhkUy9NVUplUC92cmgrY2RTWDVxZUc2T0VKUmxKTE5nT1NzaVJwbDBtdzVNcEMvcERZR0NQK1N4MTJ0aTB3Nk8xRTVDaVZSN0V5eldGK09jTWpRTm8zS2Q2UnNoQ1diV3pPNTRXeVpSWnVTeHYwRzd5amw1L1V0aithZnhjRUluSiszRTJhWWczZWlqNW9hS000em8xMS9mWFdZZmFuemloVVB1RlN5SEljVFA1U2FnR1JUaTh6eHNmOE1UeisrZmowVTJWNFdzVmYvRjgzN21QaHVCYURNaWUwSW1OVTNrWW9pV3BEY1hxMDJhOEJwUHBoSG50Yk9yLytMaENqajUvT1NHa0xWTUlnUkpYcDZ3enRYU3k4dDc0LzgvOWczMGNNZWZ1cW16ajBQamdaUm95b2xTWlBFaE5Qa2tIanJLdVViRC9kdndBMnBOVkFSY3h2ejNtdFBNSzgiLCJtYWMiOiJmMGM1NTMxNGQwZDAzNWVmZWU4NzY2ZjBkOTYyNTYwZGU3Njk1NzA0N2E5MjYwNTYzNWVkZGJhM2ViOTMyMzVkIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:43:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImQ1RzhvdUZVajJTZGhTbjZpOGUvZFE9PSIsInZhbHVlIjoiWG5DSVMrY3FLcnJLcXJCc0pKSDE4UUxrOC9qQXlpU1IrVzBpeUI2aDhnY3Q0cVpiZnlScWo1dDZrUHU0VWR3YW1PS2lJU0ZVVFlBSmJzd0dHNEhvWXRkR1hDb2Iyc0lPVUJDY3FCT01hNEYzeS9pQ2xQc0dOVTVEbHgvRmptM2U4UU9aSXpESXN3WURwbWliMEdYQVEvRG9IZEV3UmNMNFdGd2MrUGF6TktiMDVjb1Z1clJRQ3ZXU2hTSzA5dlRUY211NXNiS1NSdHZ6WEZad25mSDh3OFlKMlVjTWRnVEZOakZwOUhQcUlJbXRwNWxia1BPZGpzNDF5Z0N6elRIVncrZ0trN1RTMCtWSGRPdDlFRVhBNEhqSEVRSmpmRWk4YkRSQytBLytEUFlKcjFpOThUTHZPVDRFcjNIZFA0M041RER1QmFmRjB5M3FMbHVoWDBKRXVocTgvV1l5a1EvY0toeFZtVzh0Wm55NDc3aVBPY3ZpY0svREkyUG5OQ2lUczV2YWlHeGlTMUxlVFlBOVpVR3ZxTWFqNXRWc2ljYmJ4M2lRV2JhRndHUXEvS2g3YkN1TVgzMHl2eU53S1VKc2V0TitrUi9CZG9FckM2NkdqamtUKzZJd2dUZjVRUlBTdHBvdEllc1FKZ09PZGFiTU4xR3VzbEdWczdQbk5EVXQiLCJtYWMiOiI4N2Q2NjQ4NzBhNWU3YWM5MDkyMjE2MzFiNTU3OTU1NjE2MTY2NjgxMGY4NzNjMjQ4Y2M0ODRhZDk3YTA0MzM4IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:43:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjJ2VjhYUXFBcWZSNmV3OWZ1S3pFOXc9PSIsInZhbHVlIjoiOE5iTmhhVjRaMjllYTkwL2JVanlnajVKS0s4OTBxdUh0dlBFVFF6U0RYMmpJQ3JUT25CekxueXo0YzdLUHNQUG1pM0VzRm93N2J3VVh4RGZCTjEvM2pUZks2QnYvVnVrREtwMEIwWlgzNkl5MCszMU9odE5wWmQ4R3ZqS21vZnhYU1I1bm1BMS9xdE5OQ1NWZ3BhLzFRWU5iODBTRnlVRmwrcHVaMGhkUy9NVUplUC92cmgrY2RTWDVxZUc2T0VKUmxKTE5nT1NzaVJwbDBtdzVNcEMvcERZR0NQK1N4MTJ0aTB3Nk8xRTVDaVZSN0V5eldGK09jTWpRTm8zS2Q2UnNoQ1diV3pPNTRXeVpSWnVTeHYwRzd5amw1L1V0aithZnhjRUluSiszRTJhWWczZWlqNW9hS000em8xMS9mWFdZZmFuemloVVB1RlN5SEljVFA1U2FnR1JUaTh6eHNmOE1UeisrZmowVTJWNFdzVmYvRjgzN21QaHVCYURNaWUwSW1OVTNrWW9pV3BEY1hxMDJhOEJwUHBoSG50Yk9yLytMaENqajUvT1NHa0xWTUlnUkpYcDZ3enRYU3k4dDc0LzgvOWczMGNNZWZ1cW16ajBQamdaUm95b2xTWlBFaE5Qa2tIanJLdVViRC9kdndBMnBOVkFSY3h2ejNtdFBNSzgiLCJtYWMiOiJmMGM1NTMxNGQwZDAzNWVmZWU4NzY2ZjBkOTYyNTYwZGU3Njk1NzA0N2E5MjYwNTYzNWVkZGJhM2ViOTMyMzVkIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:43:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1695040271\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1920088356 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2045</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1593;&#1604;&#1610;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2045</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>8.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>3</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1920088356\", {\"maxDepth\":0})</script>\n"}}