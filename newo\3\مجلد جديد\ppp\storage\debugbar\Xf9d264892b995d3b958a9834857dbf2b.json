{"__meta": {"id": "Xf9d264892b995d3b958a9834857dbf2b", "datetime": "2025-06-17 14:05:53", "utime": **********.342437, "method": "GET", "uri": "/customer/check/warehouse?customer_id=6&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750169152.705031, "end": **********.342459, "duration": 0.6374280452728271, "duration_str": "637ms", "measures": [{"label": "Booting", "start": 1750169152.705031, "relative_start": 0, "end": **********.249171, "relative_end": **********.249171, "duration": 0.544140100479126, "duration_str": "544ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.249185, "relative_start": 0.544154167175293, "end": **********.342461, "relative_end": 2.1457672119140625e-06, "duration": 0.0932760238647461, "duration_str": "93.28ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46234432, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01632, "accumulated_duration_str": "16.32ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.294539, "duration": 0.01459, "duration_str": "14.59ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 89.4}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.3239899, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 89.4, "width_percent": 5.515}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.329677, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 94.914, "width_percent": 5.086}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-1927389499 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1927389499\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-443861308 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-443861308\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1391432015 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1391432015\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1330575639 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InJvQzdhdjF5N21wTVJWaHdCSmdGQmc9PSIsInZhbHVlIjoiU1NlK2RuaGppYnI2QVFoQ0ZYNDJjeEROd3Y2bFk4bVN2Y3VBVDE3eWVBMllxNTZnblZGTFdZVmUzazRxZEFZcEpEOXRmV21HRFhDVHNPTDlMeTdicEYxVXdzZVlNWDAxMUxPM3pNSmJ5aWNITy96TURndldkU3k4RlA5aGhaV05NR3VHWFlVN3hHZGs4U294WVBDQTJRRGxJVU9XYWJVeU9OT0VlNDlYeVRtSHFpN2EwMTlZYlF6N20vbE16Yk9GNjd5dGNvR3M2Ti9IcEoyaHlXYzVwMk84aFRPb1V5UHhSSW8xZ25VeTAwUktmUUJNU1lPczhXNURtT0g2VTBmNldQN2RmRk85MVNUcGZVeDFKbnR6MjFWb3pmdENVSHI1dlFtckMxQmE2Q3JjSGg4bVRPc2x5NktoZXFUejVUMlJGeDQ5MjIvblp4NEdGcUxmNDlzL0lNSk9zZmlmQVQra2lXRnhONjZEaTFqYi9hY2plendXUWxUNmpHdjBZVURZb1RMU0lXTk8rQm5tS2hueXE4d3lrUktSb0lwN29sd21ITVBSZ0FVc2ZUTTE5eHlsOCtEN3hHVEhBbEUxblhlSkFYNmRBYXhXeGI0cUhXY0JWQnVjY05lY3FjdE12enZacDZBbWI0bGdTRDF3cFJZeWFPbjcvZ0FIVVowaVdLNEkiLCJtYWMiOiI0NThlZjRmMTFjODZhM2Y4ZWJmNDIyOTlmMjkwZDc0YWJhZWRhY2ZlNjNiZmM0NGNjMmQ3Y2RlNTg1NjNhNTViIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlJEeWJkQUQ5bXU0NGYvMkZ4Z21pTFE9PSIsInZhbHVlIjoiWXQrVHRycVFIZEY4dFdUc1REdkZPSEtJYUNoU3FqU1pXV0VzVVhMZDUzL3dqUFh2WXkxRkFTZ1MyZE1CTDdvWkdLOVZEaG1IOEd0WEc3ZHNzTml1YWhFVm9KdWYxeXV3dWV5UVIvWG1QQTBsQ2xYSmFFS25ZU3d1MERrSys0WWk0U2YrMG55cmU1cWJXcDRTeFVTQmY1R0t6NEFYR2ZrWjZRd2dBQm41c3NDcWZ3bkJwcmM2bkovM3pqRk5XYXQ5REV1RW1DZUNhYWhHdEdOZndvejQ1enVtdHN4UHFTeGRVMVpIQWF0TGw2YTVNUlVpZXhvZW0rOTlWMGpIM2NPVmdxbG1lV1RNeGZScmpYblpXNEFQNHh5YVdtaHppdjA0NG1COE53cHFRTEV3c0I4cThEclR5T2xvNDdiaUVYeHRNbEdEays4ZmRnc3lwUmVieWxjNVV0U2ZGZ1dhMXNCeDhONmRMMytqL2RmcWNqTC8wdVhMK3B5bjVjUEVRaU1nNWpmQTBEeVBabC9DbzZLbVJ3Y3A4RUFQRlJhMG5EejBHS0EyK1E4bkhEakVueXg2V0dIOHFGR0l1ZmRiNnp2dFFVb0k4Q3FzWVBvUkZiL0JPZzNwWUF1b0paQ0NzTWs3WmRteGtDZGtNbkZzNm4wS1lKRnk0Q1RrZEhvRUxBV1UiLCJtYWMiOiIxNDE0NjYxZjU3YjJiMzA2MTQ1NzBmNTI2Y2UxYTAwOTkzMmIzNTJlNDM2N2YyMDQ2ZjE5YjY5YmNmODEwNmU5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1330575639\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1306177921 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1306177921\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-972106346 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:05:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InVDTnlkbUdoVXJLdnI5OXYxbTR1aFE9PSIsInZhbHVlIjoicjB0dTA2S1JSQlU2U1NSc3ZzUTd0QVhyVnRFY1djSzZ2U29waHRlbWNjbXVYRlBwUUM1b1g5RzFiMldraEhBYWRxSUdnaUR6K2xPWjZ2Tzd2NlhzRk4zc0NWdEU1eDlveDVIZFA3VXg5MldJUDdvaUNET3JnOHFjVk5EdUpTdzkxZzg3RnJmNzByZkw5TDN4MjM0K2ZZeS9RbUtBQkZDS3B6S0diNFF6NVdCazRsUkNzQjlMMW5kbUZCMUhUQkNLeDhqUThCdHdteU5abGVMdTdzaG1CZitKR3BTeEsvMy9GMmlQQno5N2JTMW1qa0R3V1ZlUStheCt4K2dtTkRBNEIzL2hGQkNycVVUdDIxMU1NRE4zdGFlWVNDZFNqWW9UMzU4T1pBaWdIOTZjVHh1cWV2dVJUSjlpRFpMVjZvZmdPSUxEVi92WGF3R2hxTXBaT091dWJGZTQ1bE5WaDQ3L2xod1RoTmZMb0lSUkRqRHA3d1dKRTduWER2d244UXRXUVJwQkxMR01aVXhTNWo3UTlvbmtoeU9qcE0zWTBiZjhzQWFKVzNNamVuRU8ybjVXMkZZRVp5SGFVZHdnU2FNRzROOHJKYm5ZckRURHlNOUoxWEw1Rkhha2RCaE1IL0ZwV1FCKzlXRVR3RWNwVkw3eGhnUWMvV1A3cndaODhPOUMiLCJtYWMiOiJhOGI3Y2UyNTc1MTBmYTFhM2FkNGM5MThlNjZiYjEzODliZWUxNDJkZTNhYzBjNjJkMzc4ZGJjMTYyYjM1OTU2IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:05:53 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImxDTUlMOFVaUzJNbmF6a0hXS0pmbWc9PSIsInZhbHVlIjoiWS8rZ0VhaFVBaWtqMVROWDJhMnJrWXZwSUUxMmFXZzhLZ2xBZ1JwTTVtQkRTVmlJblB0VzRkSWJYY05kWFlRN05YUCt2UU1lY0JpM2w4bDRlOFkvTHA1c0psem1SMzRPcGJ2OXBNWVpDajVqREhUWEVKOEgwNWFjT1pRMHl4VDVQOWwySHJzbTdsSnA4QkNwTWMwL21QOWxQVy9MSXhTbTUzSkY2UUFScEJpeXpFUzJPVHErVzF2WHNodkdjcXk2ODFZcDB2TE00Zm9xYkd0Skw0aUFCQ1M4eHhUWlluK0Zja2hFOWJpbTFDUGk5SWd0ZHlpMTRScUs3QzRwZ3RQVDUrQ0d5ZzhsajZlOHFYQmV6U0dXZjV0TkJDanJ1akIzRGhrdkE4b0JORkRyekM1ZnJjQlB0SEgvdkVuZ3ZvdUZzY0hsUjVrbEV5dDMyR3RWN0xqTzRiVmJRV0taSWJKVXNBakZCRFYzZmpzMXIxSkNaNlpBL2Z1NzFKM0tDaUdTUlFMSUdzTFpac2ZQWVRrNGdSR3dDMzVxQnh6dWtjclFMZTZIQzh1VmVESzY2Q05KSDFnbzdMaGJxQ2FvaXhMRjByeEtFZWxicTFicmdvamJvNUg2UHNBNFcrQjdEUkZ6QUFDWHB0TnMvOW16ZHB2bXRlVEZLTmxvZ3Vvei8zRG8iLCJtYWMiOiIzNTU1NGY5MDA0MmVmNDFlYjhlNjk1NmFjNzRjM2I5YWM2ZWNhYmFjZWRlZDIwZTM3NDdjN2M4YzI5NmRiNzEwIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:05:53 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InVDTnlkbUdoVXJLdnI5OXYxbTR1aFE9PSIsInZhbHVlIjoicjB0dTA2S1JSQlU2U1NSc3ZzUTd0QVhyVnRFY1djSzZ2U29waHRlbWNjbXVYRlBwUUM1b1g5RzFiMldraEhBYWRxSUdnaUR6K2xPWjZ2Tzd2NlhzRk4zc0NWdEU1eDlveDVIZFA3VXg5MldJUDdvaUNET3JnOHFjVk5EdUpTdzkxZzg3RnJmNzByZkw5TDN4MjM0K2ZZeS9RbUtBQkZDS3B6S0diNFF6NVdCazRsUkNzQjlMMW5kbUZCMUhUQkNLeDhqUThCdHdteU5abGVMdTdzaG1CZitKR3BTeEsvMy9GMmlQQno5N2JTMW1qa0R3V1ZlUStheCt4K2dtTkRBNEIzL2hGQkNycVVUdDIxMU1NRE4zdGFlWVNDZFNqWW9UMzU4T1pBaWdIOTZjVHh1cWV2dVJUSjlpRFpMVjZvZmdPSUxEVi92WGF3R2hxTXBaT091dWJGZTQ1bE5WaDQ3L2xod1RoTmZMb0lSUkRqRHA3d1dKRTduWER2d244UXRXUVJwQkxMR01aVXhTNWo3UTlvbmtoeU9qcE0zWTBiZjhzQWFKVzNNamVuRU8ybjVXMkZZRVp5SGFVZHdnU2FNRzROOHJKYm5ZckRURHlNOUoxWEw1Rkhha2RCaE1IL0ZwV1FCKzlXRVR3RWNwVkw3eGhnUWMvV1A3cndaODhPOUMiLCJtYWMiOiJhOGI3Y2UyNTc1MTBmYTFhM2FkNGM5MThlNjZiYjEzODliZWUxNDJkZTNhYzBjNjJkMzc4ZGJjMTYyYjM1OTU2IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:05:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImxDTUlMOFVaUzJNbmF6a0hXS0pmbWc9PSIsInZhbHVlIjoiWS8rZ0VhaFVBaWtqMVROWDJhMnJrWXZwSUUxMmFXZzhLZ2xBZ1JwTTVtQkRTVmlJblB0VzRkSWJYY05kWFlRN05YUCt2UU1lY0JpM2w4bDRlOFkvTHA1c0psem1SMzRPcGJ2OXBNWVpDajVqREhUWEVKOEgwNWFjT1pRMHl4VDVQOWwySHJzbTdsSnA4QkNwTWMwL21QOWxQVy9MSXhTbTUzSkY2UUFScEJpeXpFUzJPVHErVzF2WHNodkdjcXk2ODFZcDB2TE00Zm9xYkd0Skw0aUFCQ1M4eHhUWlluK0Zja2hFOWJpbTFDUGk5SWd0ZHlpMTRScUs3QzRwZ3RQVDUrQ0d5ZzhsajZlOHFYQmV6U0dXZjV0TkJDanJ1akIzRGhrdkE4b0JORkRyekM1ZnJjQlB0SEgvdkVuZ3ZvdUZzY0hsUjVrbEV5dDMyR3RWN0xqTzRiVmJRV0taSWJKVXNBakZCRFYzZmpzMXIxSkNaNlpBL2Z1NzFKM0tDaUdTUlFMSUdzTFpac2ZQWVRrNGdSR3dDMzVxQnh6dWtjclFMZTZIQzh1VmVESzY2Q05KSDFnbzdMaGJxQ2FvaXhMRjByeEtFZWxicTFicmdvamJvNUg2UHNBNFcrQjdEUkZ6QUFDWHB0TnMvOW16ZHB2bXRlVEZLTmxvZ3Vvei8zRG8iLCJtYWMiOiIzNTU1NGY5MDA0MmVmNDFlYjhlNjk1NmFjNzRjM2I5YWM2ZWNhYmFjZWRlZDIwZTM3NDdjN2M4YzI5NmRiNzEwIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:05:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-972106346\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1267803735 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1267803735\", {\"maxDepth\":0})</script>\n"}}