{"__meta": {"id": "X7347977477fbcff5dafa1fb011071171", "datetime": "2025-06-17 14:43:12", "utime": **********.452113, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750171391.817721, "end": **********.452136, "duration": 0.6344151496887207, "duration_str": "634ms", "measures": [{"label": "Booting", "start": 1750171391.817721, "relative_start": 0, "end": **********.309639, "relative_end": **********.309639, "duration": 0.49191808700561523, "duration_str": "492ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.309654, "relative_start": 0.49193310737609863, "end": **********.452138, "relative_end": 1.9073486328125e-06, "duration": 0.14248394966125488, "duration_str": "142ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49324168, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.028220000000000002, "accumulated_duration_str": "28.22ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.361746, "duration": 0.015130000000000001, "duration_str": "15.13ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 53.614}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.388648, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 53.614, "width_percent": 3.189}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.4130142, "duration": 0.00257, "duration_str": "2.57ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 56.804, "width_percent": 9.107}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.4189508, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 65.911, "width_percent": 3.969}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.4274552, "duration": 0.004849999999999999, "duration_str": "4.85ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 69.88, "width_percent": 17.186}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.437385, "duration": 0.00365, "duration_str": "3.65ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 87.066, "width_percent": 12.934}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1801644600 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1801644600\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.42554, "xdebug_link": null}]}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-463068869 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-463068869\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1525766985 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1525766985\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1894878388 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1894878388\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1315174032 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImUzVWpPRXBIcURXZ2VidXhoLzNMY3c9PSIsInZhbHVlIjoiMXdmTWNQblVFbnBGcVFUb1AyWnJsaXJIakJBejE2ajVMUGcwam5ib0tjQ3JVZFE2am5BR2VQejMrNmdMYnpwcmVnUDlsUUVuNGxMakRRN0w0UGtidkgzbmQ5WXg3bmhVNDFuRDIrQklncFhnQ2x1REkvaUQ0Z2JMTjBiSUFoMFFJQmhpM3F2SXp6ckRPSnlPWWZnMWd1cmF1TWFTOFE4bjZBUENndWo2clg4RXZtSXUxdXBpVVdxaXZPeWM2R0V2bnk0VkVDN2pUTStZVzJza1dBRmhxSzI2Mkl3Rkk1ZjJyRStCa0c1cmdhS3pnNzdNTlk4TDNJRCs3dDl0eEp2cTdLcDBjdEhnaGxHOFJXaFJnNGZrMzV2TWVMRVBPcWRFclQ2M0FDWWMrZU0yZXdHNmdUdjVHdlJ0dzFmTG5ZamhuR2s3VWF2SXBBb2V0T2xqSFZtYkJqZnZaMnh2cUk4U0xCU2EvT09wbnBoYmVURlE0OE1iVFlEV1E4aDB4bkJXcTNoL1dSYnczQUZadm96QnJHZFc0bThPQTZGckNVWGswSHhaUFN6TjlETHB5VGlqcGtOb2ZPNDFBcHV4Y3F6S2lCLzRBZGU5TTducytYU1RydzZhd1J5Q08yTnFUdTJ4a3dBd2hBcFQ4MnpyY3NWckZkS1pGYStYeDlNUk40K1IiLCJtYWMiOiIwOTgwYmIzZWZmMzE5NTY0ZjU1NmQ1MWQ2YzZhZjgxZWIzZjlmNjMzZWI5M2MwZGZmMzc4MTVlOTlkMGJlMGMwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ik1vR3h0eTN4ajVWaWtxYkxqNjVrbnc9PSIsInZhbHVlIjoiZUVnMWdpODlMSDRIQ21FblVFZEIvTnV2ZGtnWGRtOWsrazBVL3lhZ3IwdFd1VHpVd0hlL05NY1NzVDJ6V1EveVFRazJBdTFkbTA3Mk5hODF3a0E4dDlEbU9wbXBiM0tna1RBcFhiNnNrVEpLOHJjRy9jd2NjOElpMGl6UHFWVUFGM2lyOGthcUZrMXhBeXhvWWFjTTBTbWxidE16T0dCazhRdXlGY0MxdnNGbkkyZEJMMjg4SFRVKzZGM0F0eEgwSDNadVFJN0NCb1NJOWNvTkp0RmtwSVZab0VKNTR6YVhrWWVhemNvRWQyRDQxRlZZY1A0dUxnL2tEbmhMQUh1RU5RVXlTVW4relJkUURPOVcyYVBwTWNaL3hJRWJrVmlBOHNTY1pvUk9lSm82bGE2NDM1L09obHp1RFBrWGFqbXl2aTRWZDJqalVYT3ZhalI4dmxGVW9LYWZ5a3FaZy9VMnZCck8vZldRc1ZhWlBDbDJtMndidlRuOFBvT28ycG54STJKSURmNXNBZ3J6WlRvcEZiK3Z6TlJrNWtiZ3k4R0U5S3RxQWdQRkw1TUphaXBLalVFNFF6VkdEL2s5N0pKY2doR1hvVVg3d3FmMHNoRDljMUlQU3hnMXp2MkV1N2Urc0Q1VjNHWXJhRFMxb2wwQnZHZFlVcVo2ZC9QYnZqMUUiLCJtYWMiOiIxYmI1NzcwNWY1YTJjMDIwZTYxMGQ0YzRmZTllMzg4YTNjZDg3MTE4YzFlYzUwNTg1ZjQ3ZGY1ZmEwNjgyZTUxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1315174032\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1059423500 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1059423500\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1526432552 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:43:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkJlSytxczR0dHFKYi84VVhzSk41R0E9PSIsInZhbHVlIjoiODU5ZDNjRS9aV1JzblQwNHUvcTNuRVNoaDdlWjJNelhTZTBrd2Jla0YwNmpXQWU1Z0dLMkZYVnpKcG9ETWhZUzZaMmhsQ0VGWTZnZTUveUNBUFVLRGtqMWRPampvbitWcGZIdUJQV2VDV1hlTmJ1enFzNUxSY0hNVHk3V1I4bnVLeXUwL2RRUFBZSDZhQi9IZm9SVkZQY1ZvdEg0MFNOUFRlMGhmazRsSFN1U0VHSk1FdWNScU1PY3JmeC9EbU9MeEN2eUFjK0xveXF5Z0ppWk5zVFdxRUlXU09IK1ZoZEZ4bCtzVGQyTDduSDZUc3FxdVZVMWFBRkRxMXhRd0Znc242dk8yaFk5TW5sOUF3OUprNHlReWFOektZTWhLYXR2a2hQV0NnZTVaZTRFVmhtdjJ0YTJ2OXNBWnFLZU5UbjlaQkNKVkgvbnd4RnNJWUFSdFdMRm84NkF3Tld6UFlnRFFZakg2Z21zalQ1Z0pENGVGM0o1SCtTeG9OWFg3Ykc3RUt6QjNQaTBibUl2STVMUVZ0aW41M2IyWTVOdFVzeUZ1R1lKb0xQZlA2bEtFUnRzMzd6Lys1U3FtRGU4ZGw2cXo4ZGFSclI2bWs2MldtUGR0VGZnVUUxZVd5UVVia3d5QnhSUUQxSDhsOVY5em9FZXl3OVE2bWdVVjAyZnUvbDEiLCJtYWMiOiJlNmQzYWEzZTAyMWJmYmMxYmQ3MWMzYjY1OTU3YWExNjVjYjMyYjRjZTc2Njc5YWY5Yzg5N2UxZTliMGU4YTMxIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:43:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlVIWHk2ZlVib1hJYjVSaGZvVzZJUWc9PSIsInZhbHVlIjoicmljU3NaVmZ5cmppdklmMktJQjNJNkx4WTJqZ3Q3N3JrLzRpRDVEUzlSaUpvZnB4UzVLaURYUWREd0QzV2REdDZxMXowY0ZXM01ZUDVhazJJNTRMRUxzREh4L1pCM1g1TFE3NkMya1l4U2Z6eFRhdjExM0I1RmJyckZ4S0ttV01tclVDVDhUUlhqbFM2c2xDOWhVRVhrQlovUnpDQ1ZVRTN2UjFiN3lDK2htbHRuUVNoTTlIb3FIK1F4NHhwc1poWWwvTi91S2J0NkJ4ME9OLzc2aGJUcmVOUTNWNHhrMWd1NzN1WCthNThadTJsMUZkS0VDQ3RJVG0zSXRhL0U4VHdkSmhKUUxUSzZBS2RuWE1PcXNYU01SRUllY0VQSjM4emJkTmpLYkNVM0pUOFVjRjV6c0ZOUjB5eUVndUhiUFBnYWFacGFEVVZYcnplNC9rc09rQ0Q5UG1Bb3B1UDh3N05QOHB1ZkcxQWtWN3I4VVdCOHBKNTR6eWNJZ29qMFhBalFNQTFKbHdqWlBpS0ZhUy9EOFJQd3dxSGM1ckhORFgxVXpFcUZpd2ljZTBIUHcrM2h6S2NXVS82TDFxZlZJZlZubVRmT1BZcW1Jc3FVUk9HL0ZVa0RmQTBtUXR3R0lELzNjcWhPWk94d2FJalVSTkpwRXpKdFJHdVkwQlNUSlMiLCJtYWMiOiI4MTcyZjlkZDg1MjJhNzc4ZDk0OWU5ODM0MTYwNzc1NDI3NjFkZWQ3NmZlMDg0YjVlM2U0NmJiY2JjMWRmMmI3IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:43:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkJlSytxczR0dHFKYi84VVhzSk41R0E9PSIsInZhbHVlIjoiODU5ZDNjRS9aV1JzblQwNHUvcTNuRVNoaDdlWjJNelhTZTBrd2Jla0YwNmpXQWU1Z0dLMkZYVnpKcG9ETWhZUzZaMmhsQ0VGWTZnZTUveUNBUFVLRGtqMWRPampvbitWcGZIdUJQV2VDV1hlTmJ1enFzNUxSY0hNVHk3V1I4bnVLeXUwL2RRUFBZSDZhQi9IZm9SVkZQY1ZvdEg0MFNOUFRlMGhmazRsSFN1U0VHSk1FdWNScU1PY3JmeC9EbU9MeEN2eUFjK0xveXF5Z0ppWk5zVFdxRUlXU09IK1ZoZEZ4bCtzVGQyTDduSDZUc3FxdVZVMWFBRkRxMXhRd0Znc242dk8yaFk5TW5sOUF3OUprNHlReWFOektZTWhLYXR2a2hQV0NnZTVaZTRFVmhtdjJ0YTJ2OXNBWnFLZU5UbjlaQkNKVkgvbnd4RnNJWUFSdFdMRm84NkF3Tld6UFlnRFFZakg2Z21zalQ1Z0pENGVGM0o1SCtTeG9OWFg3Ykc3RUt6QjNQaTBibUl2STVMUVZ0aW41M2IyWTVOdFVzeUZ1R1lKb0xQZlA2bEtFUnRzMzd6Lys1U3FtRGU4ZGw2cXo4ZGFSclI2bWs2MldtUGR0VGZnVUUxZVd5UVVia3d5QnhSUUQxSDhsOVY5em9FZXl3OVE2bWdVVjAyZnUvbDEiLCJtYWMiOiJlNmQzYWEzZTAyMWJmYmMxYmQ3MWMzYjY1OTU3YWExNjVjYjMyYjRjZTc2Njc5YWY5Yzg5N2UxZTliMGU4YTMxIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:43:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlVIWHk2ZlVib1hJYjVSaGZvVzZJUWc9PSIsInZhbHVlIjoicmljU3NaVmZ5cmppdklmMktJQjNJNkx4WTJqZ3Q3N3JrLzRpRDVEUzlSaUpvZnB4UzVLaURYUWREd0QzV2REdDZxMXowY0ZXM01ZUDVhazJJNTRMRUxzREh4L1pCM1g1TFE3NkMya1l4U2Z6eFRhdjExM0I1RmJyckZ4S0ttV01tclVDVDhUUlhqbFM2c2xDOWhVRVhrQlovUnpDQ1ZVRTN2UjFiN3lDK2htbHRuUVNoTTlIb3FIK1F4NHhwc1poWWwvTi91S2J0NkJ4ME9OLzc2aGJUcmVOUTNWNHhrMWd1NzN1WCthNThadTJsMUZkS0VDQ3RJVG0zSXRhL0U4VHdkSmhKUUxUSzZBS2RuWE1PcXNYU01SRUllY0VQSjM4emJkTmpLYkNVM0pUOFVjRjV6c0ZOUjB5eUVndUhiUFBnYWFacGFEVVZYcnplNC9rc09rQ0Q5UG1Bb3B1UDh3N05QOHB1ZkcxQWtWN3I4VVdCOHBKNTR6eWNJZ29qMFhBalFNQTFKbHdqWlBpS0ZhUy9EOFJQd3dxSGM1ckhORFgxVXpFcUZpd2ljZTBIUHcrM2h6S2NXVS82TDFxZlZJZlZubVRmT1BZcW1Jc3FVUk9HL0ZVa0RmQTBtUXR3R0lELzNjcWhPWk94d2FJalVSTkpwRXpKdFJHdVkwQlNUSlMiLCJtYWMiOiI4MTcyZjlkZDg1MjJhNzc4ZDk0OWU5ODM0MTYwNzc1NDI3NjFkZWQ3NmZlMDg0YjVlM2U0NmJiY2JjMWRmMmI3IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:43:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1526432552\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1115440702 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1115440702\", {\"maxDepth\":0})</script>\n"}}