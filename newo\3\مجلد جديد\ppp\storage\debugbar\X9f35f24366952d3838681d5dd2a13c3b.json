{"__meta": {"id": "X9f35f24366952d3838681d5dd2a13c3b", "datetime": "2025-06-17 14:54:42", "utime": **********.275284, "method": "GET", "uri": "/customer/check/delivery?customer_id=6", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750172081.697613, "end": **********.275307, "duration": 0.5776939392089844, "duration_str": "578ms", "measures": [{"label": "Booting", "start": 1750172081.697613, "relative_start": 0, "end": **********.206228, "relative_end": **********.206228, "duration": 0.5086150169372559, "duration_str": "509ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.20624, "relative_start": 0.5086269378662109, "end": **********.275309, "relative_end": 2.1457672119140625e-06, "duration": 0.06906914710998535, "duration_str": "69.07ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44962072, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01383, "accumulated_duration_str": "13.83ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2469578, "duration": 0.01255, "duration_str": "12.55ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 90.745}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.264384, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 90.745, "width_percent": 9.255}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1357107073 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1357107073\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-731627489 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-731627489\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-506668485 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-506668485\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1518118357 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkY1c2dPU00vVzF0TVU3b0x5QnQ5Ymc9PSIsInZhbHVlIjoiS1UrRU9GK1hKc1BEOExLbzBjc0lxbEoybkRyZE53QkdBTmxzb1UvVjFVQUR0c0x0Vjg1ZDc1NHdhd2dMeWp0alJidFhqWE50cEVGVi9mNVNuV2pCUnF2dzhXajRWMEJnTERWSVltQzMvUmExdUZIZDlWM3hsUm1ibWsrd0lGQ2drQXVZU2tDZnpscnN3dng1OWxrK2dNTjFLYjd3UHdXZ3EwV2p4eC8yT2lER3ZEUU8zUFJVRzRodUN1aVJNRlhkZExQSUg1aytoVHpDbmRBMzAxc2FadGxBU2ZKS0JwMnM3MW5VSHBmbGpXbTEyN1RIY0J3OU9DNFA5L01md3RCQ0xiTUE2dldWYzA3MTAzdWZoenNXeHNoMUpTZnJyczJITU1WamVRTkxrMVJaS25hNWVQNjlLemkwU3YvallPL2s1WVlGNlVxTEN0SWpRcFhmQ1dXV1pVdzRUdG1pdWVid21xS29zajlsdTRtci9wVTF1ZTRYajVqREtOaUZXMkp1K0ZnMUwvN2xoV1c3aTB6TkphZDdMc2M2dVJGdm5oSURMTmk4b3RkRnZaUE5CaHZGVlNzc1BxcWRTMzFheXh5eFdQeitjMlZKWEJoY0ZmMDQvaEMrY2dkemdTTzh6VkhjT3BhY1l1UHdSVXhpS3Fjb0NVSTF0cHJwa0lha1c0T3UiLCJtYWMiOiI2MmQzZmQ4MmNjMWQ2M2YyY2JiMzUxZjYzMGY3NzM2OTk3ZDM1OGIyNzgzNzQ4YTU4NzRlNjMzYTgyN2JmODJiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkdyaUhZK2hFdnJHbFJQUE83bTZJaWc9PSIsInZhbHVlIjoicExyTnQ3d1ZCeVFRQXlSUEdEaytMeUtiU3FoTWlsTjhPTUEyZGRVSWNUWEUyZDFyaHNGaXJ1Uy9pU0QvQ3NYajBLVDRlVllYdk03TENmbTNIcFV3ZE5RbnlvTjRBVHRnN3NNUUhmMm9aUzVCdEFaaU5OSWNFaG5wM3lheWN0cVFZS0hiUWlYTUh2VHZIQ2piSTNsM3U3R3I4aWhacjJZVVJ4eHJpQW8xSDEyejRvR2xGYU9HTk5FS2wrVVZPSGhMZ1FqaGhORWs1QzJOZVc0b2k1VHNEQUdsMnE1Wno1aWc0RU1UNlVtaXRDS1c5aXlNRUdNUUVUNzExenRpR21aaGZFVDM1aElJNlE3ekh2Y2JYNm5MZUR3U3ZTZ1F2aEVtaU92RmpaTmtvRkJ0RUkxbW1zTDh5RnVLeFNIRnJFSGpqaFZvUG1ITDhEK1NmTVNybXFNOW4rUVRvTlpxRm9ZTmtEUWVqSWFSTEdiak80UXd4VTdSdDI1OVgrVlpGVklLYlU5TUVpSHRyMkxoWWZxSmlBdUh3UHh6QTRQMmlvSE01WkdPZW1ZT2xkRlZFajFFTGZ2WmpnVXZqek8xUnF0dEdneWR4dzkvVWpDS05rVGZtNU1UOSszNDBTeTB5aEFVRkZ2UTljSVdYb01PU1QrckwxMGQ5TEdWS2xucVpxT2MiLCJtYWMiOiIzZmU5YzkzODE2OGU2OWJlMzFhOGM0NzM5OTMzZWFjMTI5N2FmN2I2YjM1ZWZiY2JlMTFmNjM4OTQ3ZmQzMjk3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1518118357\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1099432260 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:54:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik51KzRCZFg5bExjeU40WjY3Um5HbEE9PSIsInZhbHVlIjoiMEQ5eVo4L2REQm1tM1ZOK1hKemZSdU5xa3JpN0hzRkVTSUc4UVJNTnZKbXJJdnpqMXRyWkpGa1FvTjF0Tjk1cDFpMlBwMkhIeWppRVhSaFk3Q3BUYjE1ZWVDZ1JzMmNFNVJHeTRNdmFGMlJPTWtBYmxSdzBMc2c1OWt0WDlLQ1NlYVM3NXdlV1JWYlpiOE90QUhiRzlPYU9yREhaOEE1bXhNaVVxV3B4VkNSMEVVdGtFMk00N1h1WFh1ZzJNVHZXUXFXM2o1SmRkbEE4c3dhRHdRb1J3U1RlWWwzNHF4TDZ4UytGc2sySWcrbWJrY3E0MWFUNTgrWkF2cHp1aGNvWEFEeVhsZUtDYzNaYTBXRC9WNkNoWXpqOUxMd0JSang1bG1uQ1R3NXZvUEszc2ZRdHJuWWxYNGtQakhlWXJEQm8zbVJSS1JxeStLZk9Xd0V6VDhHbEcwTHluWkNJVTBLcyszYXliQ3pMU0JIeWt2a0gwVEwwZTdEUHNFSnl0YTljSVlCVVIxQ001bWY0MTFoK0o5WVlZTlZxNnNzRzRIenQwenBpWjdqeXJKdkZoM2FRRDJjbUxJYW90MHczaVlEb1YxRkNoekRmS0o4Z3crcGNKajk1dDVZMkk5bmM4Yy9vUlIycUd4eXEyeG82YUN0SEE5MVZ2a3ZLRERsOWo0NzIiLCJtYWMiOiI5M2Q1NjBjMjEwYTJkMzEzNmNkNGQ1MTYyZWQ3NjIyZTJlYTk5N2ZiMjcyN2E1NjgwZWZlM2RjNDQwMDNlOGI3IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:54:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im5Za2RqQlhsU2ZTdFhMNHlxcElzWHc9PSIsInZhbHVlIjoiWkZmek9MeVErenJYcXJzZmRwSXp2blpGVzJKOUI2MW1nRHkxREZ6cm1ISFpyd2Njd2VWU0MzMkZvTW50YWl1RjZweTFjNTFnQlNTbFQzTGpEcmhwNVRyWTIyZWR0WWg2YlNoQmVGWUpZNDlXZlRBb2k4UW1NalpnYTYxTytpR0FBTWtaOVd4ZTdDQ1p4T0M4anBVMUQyMUFvWkhWaEVNUTQzd3dRZGFyRzk4MVhzT3VTdk8zOGZndlBSRkc3cXMrbW90elRGTEFqNVNVSS9iaUk1MDZZYlprZXZNNVdKN0oreHBlbkNyMzlUSDdPbUNsVllFZlpIand0Y1c4VkNkaWJFbmRhSnNWaGZ4UUJlR3hwUXh5ZVR2MDg3bU4yczQvR3JnVDZta1UyMjlUaGtObDBQNC9STEdxUEVaOVBIUlJzN1lJMnh4TEtBNXh5VTl1ZjBnWEE0VFhVNW1GUFRFSXlIQ3dKeGR3Ny9ENnN0OCt4RnpPTUFmOTdlUjV2STlLaSs2UUgxY0xnVkw5eWg2MVVQcW9kWGVGVWQxSnRmZ1VlenFhUHBiT1BoREFOWXhNVTJxTGRkc3ZNUVY1R3ErRzBOUFcxTFFreWpQT2pxRDY2UW5QSmxja3ZURHJiVlFYUTdYSDhOdU5vNmJDeGNoN0Y2YUJNMHJqNXA5N0dOTVYiLCJtYWMiOiI4OTdlODJjNmIwYTNmZmE3ZDZlZDg1ZDhhYWU4MDdjYTRjYzJlMzBmOTFhNmMxOThlYmEzOTEzMjVmNTNmMTZiIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:54:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik51KzRCZFg5bExjeU40WjY3Um5HbEE9PSIsInZhbHVlIjoiMEQ5eVo4L2REQm1tM1ZOK1hKemZSdU5xa3JpN0hzRkVTSUc4UVJNTnZKbXJJdnpqMXRyWkpGa1FvTjF0Tjk1cDFpMlBwMkhIeWppRVhSaFk3Q3BUYjE1ZWVDZ1JzMmNFNVJHeTRNdmFGMlJPTWtBYmxSdzBMc2c1OWt0WDlLQ1NlYVM3NXdlV1JWYlpiOE90QUhiRzlPYU9yREhaOEE1bXhNaVVxV3B4VkNSMEVVdGtFMk00N1h1WFh1ZzJNVHZXUXFXM2o1SmRkbEE4c3dhRHdRb1J3U1RlWWwzNHF4TDZ4UytGc2sySWcrbWJrY3E0MWFUNTgrWkF2cHp1aGNvWEFEeVhsZUtDYzNaYTBXRC9WNkNoWXpqOUxMd0JSang1bG1uQ1R3NXZvUEszc2ZRdHJuWWxYNGtQakhlWXJEQm8zbVJSS1JxeStLZk9Xd0V6VDhHbEcwTHluWkNJVTBLcyszYXliQ3pMU0JIeWt2a0gwVEwwZTdEUHNFSnl0YTljSVlCVVIxQ001bWY0MTFoK0o5WVlZTlZxNnNzRzRIenQwenBpWjdqeXJKdkZoM2FRRDJjbUxJYW90MHczaVlEb1YxRkNoekRmS0o4Z3crcGNKajk1dDVZMkk5bmM4Yy9vUlIycUd4eXEyeG82YUN0SEE5MVZ2a3ZLRERsOWo0NzIiLCJtYWMiOiI5M2Q1NjBjMjEwYTJkMzEzNmNkNGQ1MTYyZWQ3NjIyZTJlYTk5N2ZiMjcyN2E1NjgwZWZlM2RjNDQwMDNlOGI3IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:54:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im5Za2RqQlhsU2ZTdFhMNHlxcElzWHc9PSIsInZhbHVlIjoiWkZmek9MeVErenJYcXJzZmRwSXp2blpGVzJKOUI2MW1nRHkxREZ6cm1ISFpyd2Njd2VWU0MzMkZvTW50YWl1RjZweTFjNTFnQlNTbFQzTGpEcmhwNVRyWTIyZWR0WWg2YlNoQmVGWUpZNDlXZlRBb2k4UW1NalpnYTYxTytpR0FBTWtaOVd4ZTdDQ1p4T0M4anBVMUQyMUFvWkhWaEVNUTQzd3dRZGFyRzk4MVhzT3VTdk8zOGZndlBSRkc3cXMrbW90elRGTEFqNVNVSS9iaUk1MDZZYlprZXZNNVdKN0oreHBlbkNyMzlUSDdPbUNsVllFZlpIand0Y1c4VkNkaWJFbmRhSnNWaGZ4UUJlR3hwUXh5ZVR2MDg3bU4yczQvR3JnVDZta1UyMjlUaGtObDBQNC9STEdxUEVaOVBIUlJzN1lJMnh4TEtBNXh5VTl1ZjBnWEE0VFhVNW1GUFRFSXlIQ3dKeGR3Ny9ENnN0OCt4RnpPTUFmOTdlUjV2STlLaSs2UUgxY0xnVkw5eWg2MVVQcW9kWGVGVWQxSnRmZ1VlenFhUHBiT1BoREFOWXhNVTJxTGRkc3ZNUVY1R3ErRzBOUFcxTFFreWpQT2pxRDY2UW5QSmxja3ZURHJiVlFYUTdYSDhOdU5vNmJDeGNoN0Y2YUJNMHJqNXA5N0dOTVYiLCJtYWMiOiI4OTdlODJjNmIwYTNmZmE3ZDZlZDg1ZDhhYWU4MDdjYTRjYzJlMzBmOTFhNmMxOThlYmEzOTEzMjVmNTNmMTZiIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:54:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1099432260\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1806132721 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1806132721\", {\"maxDepth\":0})</script>\n"}}