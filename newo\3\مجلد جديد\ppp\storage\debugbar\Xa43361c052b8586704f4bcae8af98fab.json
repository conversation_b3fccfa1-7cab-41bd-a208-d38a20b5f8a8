{"__meta": {"id": "Xa43361c052b8586704f4bcae8af98fab", "datetime": "2025-06-17 14:43:13", "utime": **********.282354, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750171392.607303, "end": **********.282375, "duration": 0.675072193145752, "duration_str": "675ms", "measures": [{"label": "Booting", "start": 1750171392.607303, "relative_start": 0, "end": **********.202568, "relative_end": **********.202568, "duration": 0.5952651500701904, "duration_str": "595ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.202585, "relative_start": 0.5952820777893066, "end": **********.282377, "relative_end": 1.9073486328125e-06, "duration": 0.07979202270507812, "duration_str": "79.79ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46326408, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1663\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1663-1673</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00493, "accumulated_duration_str": "4.93ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.251034, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 79.108}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.270363, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 79.108, "width_percent": 20.892}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1408278651 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1408278651\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2099219344 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2099219344\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-565128513 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-565128513\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1954356424 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkJlSytxczR0dHFKYi84VVhzSk41R0E9PSIsInZhbHVlIjoiODU5ZDNjRS9aV1JzblQwNHUvcTNuRVNoaDdlWjJNelhTZTBrd2Jla0YwNmpXQWU1Z0dLMkZYVnpKcG9ETWhZUzZaMmhsQ0VGWTZnZTUveUNBUFVLRGtqMWRPampvbitWcGZIdUJQV2VDV1hlTmJ1enFzNUxSY0hNVHk3V1I4bnVLeXUwL2RRUFBZSDZhQi9IZm9SVkZQY1ZvdEg0MFNOUFRlMGhmazRsSFN1U0VHSk1FdWNScU1PY3JmeC9EbU9MeEN2eUFjK0xveXF5Z0ppWk5zVFdxRUlXU09IK1ZoZEZ4bCtzVGQyTDduSDZUc3FxdVZVMWFBRkRxMXhRd0Znc242dk8yaFk5TW5sOUF3OUprNHlReWFOektZTWhLYXR2a2hQV0NnZTVaZTRFVmhtdjJ0YTJ2OXNBWnFLZU5UbjlaQkNKVkgvbnd4RnNJWUFSdFdMRm84NkF3Tld6UFlnRFFZakg2Z21zalQ1Z0pENGVGM0o1SCtTeG9OWFg3Ykc3RUt6QjNQaTBibUl2STVMUVZ0aW41M2IyWTVOdFVzeUZ1R1lKb0xQZlA2bEtFUnRzMzd6Lys1U3FtRGU4ZGw2cXo4ZGFSclI2bWs2MldtUGR0VGZnVUUxZVd5UVVia3d5QnhSUUQxSDhsOVY5em9FZXl3OVE2bWdVVjAyZnUvbDEiLCJtYWMiOiJlNmQzYWEzZTAyMWJmYmMxYmQ3MWMzYjY1OTU3YWExNjVjYjMyYjRjZTc2Njc5YWY5Yzg5N2UxZTliMGU4YTMxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlVIWHk2ZlVib1hJYjVSaGZvVzZJUWc9PSIsInZhbHVlIjoicmljU3NaVmZ5cmppdklmMktJQjNJNkx4WTJqZ3Q3N3JrLzRpRDVEUzlSaUpvZnB4UzVLaURYUWREd0QzV2REdDZxMXowY0ZXM01ZUDVhazJJNTRMRUxzREh4L1pCM1g1TFE3NkMya1l4U2Z6eFRhdjExM0I1RmJyckZ4S0ttV01tclVDVDhUUlhqbFM2c2xDOWhVRVhrQlovUnpDQ1ZVRTN2UjFiN3lDK2htbHRuUVNoTTlIb3FIK1F4NHhwc1poWWwvTi91S2J0NkJ4ME9OLzc2aGJUcmVOUTNWNHhrMWd1NzN1WCthNThadTJsMUZkS0VDQ3RJVG0zSXRhL0U4VHdkSmhKUUxUSzZBS2RuWE1PcXNYU01SRUllY0VQSjM4emJkTmpLYkNVM0pUOFVjRjV6c0ZOUjB5eUVndUhiUFBnYWFacGFEVVZYcnplNC9rc09rQ0Q5UG1Bb3B1UDh3N05QOHB1ZkcxQWtWN3I4VVdCOHBKNTR6eWNJZ29qMFhBalFNQTFKbHdqWlBpS0ZhUy9EOFJQd3dxSGM1ckhORFgxVXpFcUZpd2ljZTBIUHcrM2h6S2NXVS82TDFxZlZJZlZubVRmT1BZcW1Jc3FVUk9HL0ZVa0RmQTBtUXR3R0lELzNjcWhPWk94d2FJalVSTkpwRXpKdFJHdVkwQlNUSlMiLCJtYWMiOiI4MTcyZjlkZDg1MjJhNzc4ZDk0OWU5ODM0MTYwNzc1NDI3NjFkZWQ3NmZlMDg0YjVlM2U0NmJiY2JjMWRmMmI3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1954356424\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-537144809 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-537144809\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1934068377 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:43:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImFBWDJMS045UFh6VFJ5elh5N29HNmc9PSIsInZhbHVlIjoiNVM3YUxocGFaekNOeFIyOFZOUzdHQm0wZkN0QWl0T0tEZGFMZ0N5UHQ3aG9KNHZqTFlLZkhNUE96Zk5OYyt5WUc0ampMak83Mks2Qm9KQkFlc3U5NStDTHVQQ0xvZ2J5OHVlZHVCd2ZuNVpTSDVNUlpIdUdLNVIzUU8ydWFsMmQ0R2F2K1JudWp0MmRKeEViZkI0ZUVKaitrOGVBdE1memVQUW1tbzZjczAwMXNUSkExbFNoV2xiOGtaMHRxZFkvWUdmQzg5S0NYYkNkbkMwemVMMWgwdWVWL0tKaGZzdVRHM2FMTGhjNEFycHcvSlc0TGp2ellFeUp1NEtrV3JCeWFzRGhKNndaOHhMUVJIOENaSU5vNXdBbm41VXR5OGdWakd0ckE2ZE4zeTg1ZFNDZnB2LzdGSlkyb0lXamtBNkg0Q2llWEdKTk5aM3AxSXBlaDYvcUVHRVhiZElwUTRmMElVU21RS2Faa0xsT0xKSTVhZDNCZ0VtSWdEY0ZYT2xMYWZaRGFmamt1TjZDK0I2ODl1bXZLOGVOUzIxbmdjanVTUlk1dmJsSFAwRThlcE5VRWRvMkpqMWF1Q0FYb2xNMmlmY2ZBT2xGc0hPQ05RZWNmSCtyZkZGL3VTQ1hUQUtabTlTL1ZHSm9KMHBRb2xxdnNtZVdXUWN3WXE2bmFVWU4iLCJtYWMiOiJkNDYzYmYyNDZkZDM2NmJiMDJlYTdlMGY0ZmM2NzdmNmQ3NDYzZDk2ZmE2ZTEzM2JiNGMxZjFhM2Q5NmZhZTc3IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:43:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlJVMjhKcWhlZG03SDFjMmpuYkFvM1E9PSIsInZhbHVlIjoiSFVTcE1UQ0lNR2pISkxpeXAxNk9XbURvUzlEUkplNzdRTUVvZkxTTU1rTW0wY0duWUdyQ09ERFNKZWdqSkN5aCtNcFNvUjNhbXVPT2xZVlh2QlhRUENpNlZIb2IyREMxbjBqZmdManRZVGEzUVQreThJR2Y2SC9IZUdLNk1wT3QrZ1JHMng4VllBZ1AzMXREQWVVM2JqVmVaaS8rM0lZN204YmJscWZNejNhdGpKSTRKWlMyNUE0WDFzTmpENEY4bW5qL2tVNWg1YldvU3VFZEREaXRFQ2x4Z3JDYzltdFdMNUFTVTN0RGRkOXFia0pJSWU3dCtJL0lFTmxyY3FxVU5PZHI3WE1oWDQrbnQzVVphbE1QV3lReFpRR3pCeHdJSHBWQytlZVhVd2Jhc0hwUUw5RlBaZkYvNzdOZ3pUY2RrTGh2d0k2RU9LSTJmb2V0bFVOYXVmVXRUR0pBWG5tWk1HYmJEM2pHNkN4WGtVclQ3K1lYRmJ4cENoTjRHazY3RG93elozSzBTR255ckRLWWlPTEZPNmgwM0ZKelNWd3BiSGxReFBkM3FYSW1PRzl6N21EcWpwNkZITXN5SDd5dUQ1SjRlNjhXaVZ6TDY3UlJ2bXpTeWRZYUwzWlliRjBJNWJ5eExqVk9ZUmxDaW91M1AvcjU5czdoOG1pR1lPVWsiLCJtYWMiOiJmZjk5ZjdhZWYzZmM5ZmJhNTdhMTM3MWY1NGEzODZmY2NlN2Q4NTE5Zjg0M2U2ZDQ1ODgzYzI0MGUyYjQ1ZjM4IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:43:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImFBWDJMS045UFh6VFJ5elh5N29HNmc9PSIsInZhbHVlIjoiNVM3YUxocGFaekNOeFIyOFZOUzdHQm0wZkN0QWl0T0tEZGFMZ0N5UHQ3aG9KNHZqTFlLZkhNUE96Zk5OYyt5WUc0ampMak83Mks2Qm9KQkFlc3U5NStDTHVQQ0xvZ2J5OHVlZHVCd2ZuNVpTSDVNUlpIdUdLNVIzUU8ydWFsMmQ0R2F2K1JudWp0MmRKeEViZkI0ZUVKaitrOGVBdE1memVQUW1tbzZjczAwMXNUSkExbFNoV2xiOGtaMHRxZFkvWUdmQzg5S0NYYkNkbkMwemVMMWgwdWVWL0tKaGZzdVRHM2FMTGhjNEFycHcvSlc0TGp2ellFeUp1NEtrV3JCeWFzRGhKNndaOHhMUVJIOENaSU5vNXdBbm41VXR5OGdWakd0ckE2ZE4zeTg1ZFNDZnB2LzdGSlkyb0lXamtBNkg0Q2llWEdKTk5aM3AxSXBlaDYvcUVHRVhiZElwUTRmMElVU21RS2Faa0xsT0xKSTVhZDNCZ0VtSWdEY0ZYT2xMYWZaRGFmamt1TjZDK0I2ODl1bXZLOGVOUzIxbmdjanVTUlk1dmJsSFAwRThlcE5VRWRvMkpqMWF1Q0FYb2xNMmlmY2ZBT2xGc0hPQ05RZWNmSCtyZkZGL3VTQ1hUQUtabTlTL1ZHSm9KMHBRb2xxdnNtZVdXUWN3WXE2bmFVWU4iLCJtYWMiOiJkNDYzYmYyNDZkZDM2NmJiMDJlYTdlMGY0ZmM2NzdmNmQ3NDYzZDk2ZmE2ZTEzM2JiNGMxZjFhM2Q5NmZhZTc3IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:43:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlJVMjhKcWhlZG03SDFjMmpuYkFvM1E9PSIsInZhbHVlIjoiSFVTcE1UQ0lNR2pISkxpeXAxNk9XbURvUzlEUkplNzdRTUVvZkxTTU1rTW0wY0duWUdyQ09ERFNKZWdqSkN5aCtNcFNvUjNhbXVPT2xZVlh2QlhRUENpNlZIb2IyREMxbjBqZmdManRZVGEzUVQreThJR2Y2SC9IZUdLNk1wT3QrZ1JHMng4VllBZ1AzMXREQWVVM2JqVmVaaS8rM0lZN204YmJscWZNejNhdGpKSTRKWlMyNUE0WDFzTmpENEY4bW5qL2tVNWg1YldvU3VFZEREaXRFQ2x4Z3JDYzltdFdMNUFTVTN0RGRkOXFia0pJSWU3dCtJL0lFTmxyY3FxVU5PZHI3WE1oWDQrbnQzVVphbE1QV3lReFpRR3pCeHdJSHBWQytlZVhVd2Jhc0hwUUw5RlBaZkYvNzdOZ3pUY2RrTGh2d0k2RU9LSTJmb2V0bFVOYXVmVXRUR0pBWG5tWk1HYmJEM2pHNkN4WGtVclQ3K1lYRmJ4cENoTjRHazY3RG93elozSzBTR255ckRLWWlPTEZPNmgwM0ZKelNWd3BiSGxReFBkM3FYSW1PRzl6N21EcWpwNkZITXN5SDd5dUQ1SjRlNjhXaVZ6TDY3UlJ2bXpTeWRZYUwzWlliRjBJNWJ5eExqVk9ZUmxDaW91M1AvcjU5czdoOG1pR1lPVWsiLCJtYWMiOiJmZjk5ZjdhZWYzZmM5ZmJhNTdhMTM3MWY1NGEzODZmY2NlN2Q4NTE5Zjg0M2U2ZDQ1ODgzYzI0MGUyYjQ1ZjM4IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:43:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1934068377\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1463629668 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1463629668\", {\"maxDepth\":0})</script>\n"}}