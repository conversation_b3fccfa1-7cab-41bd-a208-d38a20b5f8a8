{"__meta": {"id": "X959650c730b55cd0a67b948d23a1607b", "datetime": "2025-06-17 14:51:58", "utime": **********.788734, "method": "PUT", "uri": "/financial/products/8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.09927, "end": **********.788759, "duration": 0.6894888877868652, "duration_str": "689ms", "measures": [{"label": "Booting", "start": **********.09927, "relative_start": 0, "end": **********.656774, "relative_end": **********.656774, "duration": 0.5575039386749268, "duration_str": "558ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.656793, "relative_start": 0.5575230121612549, "end": **********.788761, "relative_end": 1.9073486328125e-06, "duration": 0.13196778297424316, "duration_str": "132ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47983176, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PUT financial/products/{id}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@financialUpdate", "namespace": null, "prefix": "", "where": [], "as": "financial.products.update", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=699\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:699-732</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.023309999999999997, "accumulated_duration_str": "23.31ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.704226, "duration": 0.01497, "duration_str": "14.97ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 64.221}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.731872, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 64.221, "width_percent": 6.478}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.7572358, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 70.699, "width_percent": 4.891}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.7611198, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 75.59, "width_percent": 5.577}, {"sql": "select * from `product_services` where `product_services`.`id` = '8' limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceController.php", "line": 702}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.769931, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:702", "source": "app/Http/Controllers/ProductServiceController.php:702", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=702", "ajax": false, "filename": "ProductServiceController.php", "line": "702"}, "connection": "ty", "start_percent": 81.167, "width_percent": 3.046}, {"sql": "update `product_services` set `category_id` = '4', `product_services`.`updated_at` = '2025-06-17 14:51:58' where `id` = 8", "type": "query", "params": [], "bindings": ["4", "2025-06-17 14:51:58", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceController.php", "line": 725}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.77359, "duration": 0.00368, "duration_str": "3.68ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:725", "source": "app/Http/Controllers/ProductServiceController.php:725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=725", "ajax": false, "filename": "ProductServiceController.php", "line": "725"}, "connection": "ty", "start_percent": 84.213, "width_percent": 15.787}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => edit product & service,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1376548288 data-indent-pad=\"  \"><span class=sf-dump-note>edit product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">edit product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1376548288\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.76853, "xdebug_link": null}]}, "session": {"_token": "zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial/products/8", "status_code": "<pre class=sf-dump id=sf-dump-1857615611 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1857615611\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1070775309 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1070775309\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1260241970 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>field</span>\" => \"<span class=sf-dump-str title=\"11 characters\">category_id</span>\"\n  \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str>4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1260241970\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1979309226 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">73</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2032 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; PHPSESSID=o1mek9etnn9ejsik46u9hjcosg; _clsk=17t60l2%7C1750171898702%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlEwSVNtN1FIQkJkWWJ2WDRZU0FFTlE9PSIsInZhbHVlIjoiWWxNcG1xdDEwSVh5WndIaGduM1kyU29LczR1QnVNUEFFSWd2V1JTZE5WcEZXREJpcUJLMU9uMkV5ZUQvUVpTVEt3akd6RTFrRnd2UVRqMHlQbU1jZEVOL3pTQitOUzdnMHI0WkNIZEtqLzFNOGZ0UHJzLzFGRngwcWVBMm9RaE9palNrMlhxa1ZJWXVBQ3RsS1R3T3FPRUtuS3VyOVNjTG0zenJCaVNkMnpkcjk4dnN2Wld0cythYUtlVjdKUHR0aDNZdG5yZHJWZnREdFpDQVVYZ0krOXVGV0diM3l5NFVTYnJGMFNobTh3aXc3S0NObnpPNkp1dGtvMUdwaU9GUC9zUEZNaWZtYW05bWlDUTFaRlpYQ2NCaDVwc0l0K25kTXp6ekp3VUcxNTBFRzdTRlA4MnVCYmxwNXB3cHp3eEdDWDMycUhnSHRkTExLbTB6U2FBb1lCUkhZWDQya2JDamlrYlNRLzhFQWNiYTdWaVlhVDR0N1BIR3ROVmdmbFpZeVlUbHozUnZFYTF4QkxJU3V2c3h5anFDcDZwQWRKbzBzbTMvTGY3Zm5LRnk5SWl2QlBhYytUaHVTSkQ4RVIzeWFPK2d6VzZseCs4S0VNZ0QzZ2hmMDk4QVBSWEdmZE11SXBkNjEvcEhDQXl4WnhWemcyMVRMcVlsbHJRWHJ6cnMiLCJtYWMiOiI2OGQ0N2U0YTRlZjY0NGVlYjcxNGQ0MTYwZGY4Njg4ZDM3Y2M5MWVmOGFhNTBkODQ1NGI3ODM4NWRkOGYyNzI0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlBIeDFTa1NRQ1hnQ1pCSG1RVXRmMHc9PSIsInZhbHVlIjoiemlpd2RkdnBaSyt2cXN0cTc3SlVnanY1UzJlZmF1QkhtSGJKVjgrSHFpQkhLbi9HNHNRaGhldS8rZlFaWjBuMmhHbDY0OTNSVWNSaUNSVjViK0RNWEZzem9INHZ5c2xTNktNL2l4QmNjUjRpYm55cmVuc2c0MTFXUGlwbEtiQ3d3Z0U1K1dNWUllSjBOREt3Yld6b29FRzVEaTJ5cUpMSC81cWVwNkF3WExVVENXQmRsWkNUWXhlUkUvc3pOcFNXdktTWHhhWUpheFRxRFF2WGhuMTFyaXBud29QUUhOaEVSUUh5dTd5N096WEVVUFR3KzM5d2RqTHBGWnhHbVM1dzAwRHZ5b1NGN2FmSFVWWFp1UXJtRSs4SzlPbVU4bGVCMmlsL2Y2NWhMcWhBNEEyU3dibzNlZTdKWU9hdmZrcWM2eDloM3Yvelo0eU9KMDA1ZmpzaXIrR3VQZ0x3ZkQ4SkN3RFNoalFlM3NJcE56aHBzREdXOU5jU1gydWZFZmFXdEJmYkw3TS80V0puTGVmOHU3VXlzek90UTB6cUVNRHAxQ3pPMGVYWW0ybko2bnJkZWNXTnVUUXRoODRsQW1ueTJtZWE1bTR5ejVBQWhoUUI4REs5aFJHczEwQ3U3K0NNc0liRXdXVWVZbmRPRmtFejc4ekhkUGtqaXBBOWVnTjQiLCJtYWMiOiJjZTljYmZjMzNkZTU2YjFjYjMyZTJjNjBmNWM1YmRkMzA2NTlkNWFjODNhNTgxZjNlYzRlNmNmMDQ2OWIxYWQ1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1979309226\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>PHPSESSID</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HxnOnSuhAAPTfN2VS4q58slgJoytwtHYqaX0oLoW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:51:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlJjTEwxUkFuTWZpYUNXVW9tTWU4TkE9PSIsInZhbHVlIjoidGdaUHpyclpjTkdVQmZqT2h0WGZNaDlKTUhFL05DVFIzbnBNeDhiOGsvZHNBYmlmZlVlQUtvZmo0YTUxM2d1RmZlQnk1d2xlbHh2RGtPck5CMVU5cjFhNS9EMTNhYytONzl4UzlaeHU3S0tYaEoydFZRNHhYbnJ2T1JVSUp4VjAxN3lMUnRkTy92SVBQdHBVK0VpTk4reDBlOHVIbWMwV3U2MWRrRHNqSEdldWtDUDRDV3cwbytWUFVlbmtnZTNDdS9mU3Q3QWpHK3NtdTFLdnFqTGRtcGVKNFR6aHdZQ3FyQUw1Q2loODJIVlZlbUFrYjZDaGQwZHJEd1BSWGtnYjhHY3VCQi9QVG41SlVzTzJYUDZWVDBEL1BIM1VHaUVUdWFaVWMrRkEwNEdwSFdUc0FGdmM4SzZCbWVvSk53Z3VOUHZ4VGlZSktiTjJkVXR3Z1RlSTYwRWxTWmpnaHB6V2FGVSt5WGk1U20xY1VaWVAwWXlhYXJFMmtNYXJRTE5XZ2hFK2QxN3d4L0xldERyc0JHWEc0L3F0RER3cHhkckdvczgwRU4yOEI3SENudElnUDlkMkphdzNHZXQ3U0NaZmM3VlBuS09iNnNHeUcyNlJjN0JzSkFNK092VmVwaGRldWRLZGp4cVNHRHUxWC9rYVIrMXRVTEdtRXQxeFFVeXMiLCJtYWMiOiI2YzNjYTY3ZjkyNmU2YzZkOGI4NDUzY2JiODg5MGIzNzdmM2RlNGY3NWIwYjExZWVkYTJjNTEwY2E2NmE5YTA0IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:51:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkZ0SlRMMVFvMFlQaGhSc3h5QzVrOEE9PSIsInZhbHVlIjoib2dXTEwzVGZZUSsyMHRGbGFURXJGcWh1S1VNZ0xpbERLcnpIZFdKcURtZ05tYW1ZOGJ4cDZTVGcybzN4MG5TTzQ4alpSeVJnYm8rY2hVVW9jK0dkdEMrNXFCNEY4VG80aDZ2SFJacjVGTXJGT3NaTEpzN1RPUlNtdkNhd1g2RkxuUU1rdW5EblJlV1JpRzZKNk96QzFWQi82TEUwYWE2MkRLV3VJZmNraVkxMDM4b1ZXN2FNWU9pZE1jYVBMaFcwVE9PbDUwaHBLdXFUTHE1YmxGTmp1eHROT0ptaEJuRjRjTjdZcmpOMHk4Z2dqWXVLN01Qb0xRa2twbXFSM0UybkRpNGJGc3Q5bWJveUZUdVBOazVMZ2VTR2QxZG5SNk5ZRlVKakgwVVZBT2pHdEE3Qi92V0hYbzRwVzlFb1JaWFJCWFVrVGdORS92NTZ6OE53SzJkd2s2Z0MwMHBsaDZIV2ZGUlN5NFhWV3kxOTdIamZnT1ErRnZ4VG13ME1QQkFuQS90ZnVXV25xblVhajhpSEVzczgxM2VwNk5NSndpNWlnbzBNclY3SDFpR3UzK0JDcWJMTWtrRGFtTC9uditDSjZ1N1BldWxsYngrZEVjRTZRRmkvWGM3UjFjeW0vb096T2lMQVl4U1RQOFgraTh6YmRJZml3eGtrbWRVa3V5RFEiLCJtYWMiOiI5N2YwMzFjNjc0NThlOTBjZTc0M2RhY2M3OGVlOGEyYWNhMDgzMTczMDZjNzQxMmY5ZDNjMTU4MmE5NGZlMmRiIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:51:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlJjTEwxUkFuTWZpYUNXVW9tTWU4TkE9PSIsInZhbHVlIjoidGdaUHpyclpjTkdVQmZqT2h0WGZNaDlKTUhFL05DVFIzbnBNeDhiOGsvZHNBYmlmZlVlQUtvZmo0YTUxM2d1RmZlQnk1d2xlbHh2RGtPck5CMVU5cjFhNS9EMTNhYytONzl4UzlaeHU3S0tYaEoydFZRNHhYbnJ2T1JVSUp4VjAxN3lMUnRkTy92SVBQdHBVK0VpTk4reDBlOHVIbWMwV3U2MWRrRHNqSEdldWtDUDRDV3cwbytWUFVlbmtnZTNDdS9mU3Q3QWpHK3NtdTFLdnFqTGRtcGVKNFR6aHdZQ3FyQUw1Q2loODJIVlZlbUFrYjZDaGQwZHJEd1BSWGtnYjhHY3VCQi9QVG41SlVzTzJYUDZWVDBEL1BIM1VHaUVUdWFaVWMrRkEwNEdwSFdUc0FGdmM4SzZCbWVvSk53Z3VOUHZ4VGlZSktiTjJkVXR3Z1RlSTYwRWxTWmpnaHB6V2FGVSt5WGk1U20xY1VaWVAwWXlhYXJFMmtNYXJRTE5XZ2hFK2QxN3d4L0xldERyc0JHWEc0L3F0RER3cHhkckdvczgwRU4yOEI3SENudElnUDlkMkphdzNHZXQ3U0NaZmM3VlBuS09iNnNHeUcyNlJjN0JzSkFNK092VmVwaGRldWRLZGp4cVNHRHUxWC9rYVIrMXRVTEdtRXQxeFFVeXMiLCJtYWMiOiI2YzNjYTY3ZjkyNmU2YzZkOGI4NDUzY2JiODg5MGIzNzdmM2RlNGY3NWIwYjExZWVkYTJjNTEwY2E2NmE5YTA0IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:51:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkZ0SlRMMVFvMFlQaGhSc3h5QzVrOEE9PSIsInZhbHVlIjoib2dXTEwzVGZZUSsyMHRGbGFURXJGcWh1S1VNZ0xpbERLcnpIZFdKcURtZ05tYW1ZOGJ4cDZTVGcybzN4MG5TTzQ4alpSeVJnYm8rY2hVVW9jK0dkdEMrNXFCNEY4VG80aDZ2SFJacjVGTXJGT3NaTEpzN1RPUlNtdkNhd1g2RkxuUU1rdW5EblJlV1JpRzZKNk96QzFWQi82TEUwYWE2MkRLV3VJZmNraVkxMDM4b1ZXN2FNWU9pZE1jYVBMaFcwVE9PbDUwaHBLdXFUTHE1YmxGTmp1eHROT0ptaEJuRjRjTjdZcmpOMHk4Z2dqWXVLN01Qb0xRa2twbXFSM0UybkRpNGJGc3Q5bWJveUZUdVBOazVMZ2VTR2QxZG5SNk5ZRlVKakgwVVZBT2pHdEE3Qi92V0hYbzRwVzlFb1JaWFJCWFVrVGdORS92NTZ6OE53SzJkd2s2Z0MwMHBsaDZIV2ZGUlN5NFhWV3kxOTdIamZnT1ErRnZ4VG13ME1QQkFuQS90ZnVXV25xblVhajhpSEVzczgxM2VwNk5NSndpNWlnbzBNclY3SDFpR3UzK0JDcWJMTWtrRGFtTC9uditDSjZ1N1BldWxsYngrZEVjRTZRRmkvWGM3UjFjeW0vb096T2lMQVl4U1RQOFgraTh6YmRJZml3eGtrbWRVa3V5RFEiLCJtYWMiOiI5N2YwMzFjNjc0NThlOTBjZTc0M2RhY2M3OGVlOGEyYWNhMDgzMTczMDZjNzQxMmY5ZDNjMTU4MmE5NGZlMmRiIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:51:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}