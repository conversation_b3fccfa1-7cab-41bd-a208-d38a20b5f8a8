{"__meta": {"id": "Xd70f02af4225ef61540eaf0ceb5a771e", "datetime": "2025-06-17 14:50:52", "utime": **********.412709, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750171851.750174, "end": **********.412741, "duration": 0.6625669002532959, "duration_str": "663ms", "measures": [{"label": "Booting", "start": 1750171851.750174, "relative_start": 0, "end": **********.319693, "relative_end": **********.319693, "duration": 0.56951904296875, "duration_str": "570ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.319715, "relative_start": 0.5695409774780273, "end": **********.412744, "relative_end": 3.0994415283203125e-06, "duration": 0.09302902221679688, "duration_str": "93.03ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46098736, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0069900000000000006, "accumulated_duration_str": "6.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.366187, "duration": 0.00419, "duration_str": "4.19ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 59.943}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.3865552, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 59.943, "width_percent": 19.027}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.3979938, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 78.97, "width_percent": 21.03}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6", "_previous": "array:1 [\n  \"url\" => \"http://localhost/inventory-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-342458423 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-342458423\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1818777833 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1818777833\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1723903845 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1723903845\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-980250802 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2032 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; PHPSESSID=o1mek9etnn9ejsik46u9hjcosg; _clsk=17t60l2%7C1750171847023%7C5%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImsvcUYwZGtGeVlVRXJuUU5YLzllaXc9PSIsInZhbHVlIjoiVHllRDhiL3R5dk1PTlR0VHVOTTBoaDA5bDNTUkJtKzE1NHBXSG5jdXhwa3lkSWpXUUNUcEVBeERabjVpKy8ybTI4R2JYUFY4M0RaM0pOK3RPTXRSamJmemJ0Mmkvbjc4aHhmeS9JT0RJaDNLYWhjV3FKS09qMkQxSUhETFEyNHhtVWM4a2phZWx4UVZ4OWZ1WWFkVDZaSDZsVERvb1UyWEY1VkZhTDFjVTR5UEtGbmVHdllnVnpKbHplaHcySW1KcVpMcGRTZGpGNEVHMXlGYmo0LzlVeUg2V0xyUkpndWttZytOV0FxNlRlTGpwaGZjZFZGZGFRWmJOTGh0cm9HK00raG4ycUpndFhPaW5ZS0c2MDVTMmFmSktWVzdUNExKY1VKaEZvcG9TK3VIN2dXN01zNmVBd3FWemp6cTFZT1htQmpSS0tmK3dQTnNiZ3ovTnM4TGdVREpZdmtBZmRuTHF2ZTBtM01rUVZkdCtGcDBMeXBWaWFQRUZCZjZBZ1pFSnUzYlllaGlPaEFZMmFpaFBzZlVXVXhFeURnQS9oN2U5QmRRM0ZiSmUrTk5JUU9PVkM3c3V6MnJrOVk1WmtKdWNhM2lpbS9HTHp0NVhHdmpERTk2NlRwRFVuSWo1VjBmWU1pbEpNRFBFRlFLY1BiL2UrM1BUdVQ2RzR0bndzMm0iLCJtYWMiOiJjMGQ0MmM2NDNkYjEyMGYyODViNjU4NDFkYTdkNGEzMTkyZjViNjIyNDlmZDY4NTFiNjQyZjU4NTJiNzY2ZjRkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlNnRExyZDVPNXlISDlhTzhFbFRySFE9PSIsInZhbHVlIjoiQ1Z0bGduYW4wZ1IxVmJ6SEd2ZDROUnQvaWlrN2hvUmdFdDZyQUg4Zm0rYzVxUG15d1FoVjRzT3ZyZUowTi8rOU5MSGNrTmkyRGQrUzBQcG9Rc3M2QUFVTTNEMDlxeHRNUVBEbWY1dXM2NEl2aWk0RzVFcU5JcnNUYUY5bjdsR2Q0VVZEQjBRb2dmdW5CT0RkR3VtUDBzKy9KL1dhQmNvRWx2OUhQWFZEK0JOUDdVNkRLSGlJVnpyRjNGR1JvTC8rdXgvVkxBRWhYSGRkV1krS2I1VHZMUXJ5WjN0TEw0NzNiLzBwM3Q1bmphUVlVT3hBcE1GSW80K3UrYVpoQXlZaDdJV2YzU0kvaTcxZ2p6N0JiZnd2L25Wblo3bE13a2MrbERDcjBRKzBEL0x3UjBzd2RNRENuNEZIZ0hGKzNPWDd2ZG1KaXdSVENJZldiTkFiWnF0b2hHZWs0NUF3VHc3MXJiMjdtT3Z6dWc3SFlvejRjN3NEMU1NL1ZRTUdxQURFSHJwK2d3czF1WVRBMk9YcmZvRGNyQmtzbUQ3Q0JhUnFrSzgwUmxlTDE4cXBKUmhjRjgzZFZEaGZUaGh4OWVFajR3elpxV1R3MWl1R2JBYUc1dGZhbzJEN3BWeEgrblBuZnFNWFJqcy81dUEyTlBMay9QMVlQSHRIYWY1dHhOWloiLCJtYWMiOiJiNjE2NmRlOWUyMmI5Nzk5YzRjMjljOWNjNzQ5YWEwYWNiMDExNWRmYjE5MGFiMzZjNjQ5YmEwNTA3ZTc1ZTYzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-980250802\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1126412453 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>PHPSESSID</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HxnOnSuhAAPTfN2VS4q58slgJoytwtHYqaX0oLoW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1126412453\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:50:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InZWMzl0L3lUTERXWVBFVHNWZkZvaHc9PSIsInZhbHVlIjoiMjkrSm10ejFBQlIwYnJIcFpJRHk0ZStReW9WRldNWEdsOWVpUmtQS3cyWG1seWFGNU43aTQ5cFAzWDUzRGovOGMwN1NjYmdjbGk3OStxeVhoTWtqRmlTUDlJYUhxcDdVd0t0TWtvVjVPc2QvdjRDWWRNYmpZMVIvNGJSR3BZdnhHdUtOOHk5RzZqdC9QeFQ5VlpVdUp6NmFGUkwvbWpRVVMvL3dsVU5VM1oybjUxanRSMmkrTE93dlpnQWFITmhNRGNZTzNGWnNzbmpWK1RSWGNadXNyM3Uyc2R0c2haQWV1Z2lseWd2d0s1RWxrK3ZxVkNnOFJWVHI3bHFpSzhTMDBJanVHVVB6WnpkTk1JQklPZzR2N0sxanE2Z2NMMGdmbkk4N2t4WHVXZTh0VzZsUm9SL3FndVplamxQc3drdy9tZlZkZ2RiU1Q4dVRwY3dVYVViZWZhazh5alNVeUhnSlVmNmlSSnZsQ0FGK0NxeDBPZXpGYitFSGh2M0xDNitWU0YrZThhalBJOGQvR041QjN0TnhCZ242ZkdCbXdhQmYyTFRqaFg1Qm9ENEkrM2I1UHJ3bEIxcWxkYnhlTDEwbHNSSjZpZ3hxNDBncHNYZFl1SXV3c2p4aE04UzIwc0JycVdnVjFvR29zSkdieEpyUFk0RW14SHRPMjY1SXUrOTgiLCJtYWMiOiJiYmIxNDg1ZDZhZTljYjMyNDkyYjNkMzMxNDJhMjU5YmQ2M2FiMTk3MmZjNDE5NzMwMWVjMzQyM2VlNWVhY2EwIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:50:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkdCME9hUjRDY25qUE5hRS80RW9ncUE9PSIsInZhbHVlIjoiblZ2R3JLQlh3Mkx2eHJKemdqOUU1VXljVGo0c1F0TkhlVzdzOUxoWWhvMEpoZEhITUxrYXk2cFRFUkRDcFJIMWo2OWpiRVFYZ1pHK1ByNml5RVFwVGJMcWVIc1JaeFlWUE5uQiswNnlaejVqeFd1NG1Md2orUThtdHpOc0ZXUVFDY01CL09CdkFlVTBxbWZKUm5lVkFCcEhxZXNtZGV6cDRSdWs3TDlhTGMxWnhrcnFmY29BajlpNjMvS21oMzE4bVBMeHJhMEprREo0ZGtQT2o5MlRLUVZIOHNXYmxtb2xzTmtQWUFnZ1dyWmtjMjZQVC9EZ1FSZGZhN1BhaURqcGFlMC9DVnVDU01FV0VzSkRGZ1I0eVJHb1B5WWdhejIvUUxKditkVktQQnZyWTFaLy9TNUtWK3huUGVSYzFDMUdKWGpPQWNVNkd1bGJCbEJoSDEyc1VHZTNvWFdEdnlUZG40RUVSRlpMT3Z1UDk4RjdRczExZUIyb0JyM1Q1bEY2c3JKdHJ4aklDbGFXQlNlWjlrWkJtSWNNMW5CbmxLNEhrVVR1czI1YzA2OFBKdk5POVo4SHhaTE5vR3FNYkRsaEw3RmJQaGNwTEYvcDRMR1BYQlc3b2tHbkcwQVZjb05QMFNja0RHeE9uY2lCQkRDelBZVEZJVHcyVjhnaTBqWDQiLCJtYWMiOiI2MjA5NDU4MmNiMzMxODgzYzEyMWE2YWYzMWNlZmIzNmYyNzg4NTM5YTVlNGJkMGFhN2ZmMzI4Njg1Y2IzMGMxIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:50:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InZWMzl0L3lUTERXWVBFVHNWZkZvaHc9PSIsInZhbHVlIjoiMjkrSm10ejFBQlIwYnJIcFpJRHk0ZStReW9WRldNWEdsOWVpUmtQS3cyWG1seWFGNU43aTQ5cFAzWDUzRGovOGMwN1NjYmdjbGk3OStxeVhoTWtqRmlTUDlJYUhxcDdVd0t0TWtvVjVPc2QvdjRDWWRNYmpZMVIvNGJSR3BZdnhHdUtOOHk5RzZqdC9QeFQ5VlpVdUp6NmFGUkwvbWpRVVMvL3dsVU5VM1oybjUxanRSMmkrTE93dlpnQWFITmhNRGNZTzNGWnNzbmpWK1RSWGNadXNyM3Uyc2R0c2haQWV1Z2lseWd2d0s1RWxrK3ZxVkNnOFJWVHI3bHFpSzhTMDBJanVHVVB6WnpkTk1JQklPZzR2N0sxanE2Z2NMMGdmbkk4N2t4WHVXZTh0VzZsUm9SL3FndVplamxQc3drdy9tZlZkZ2RiU1Q4dVRwY3dVYVViZWZhazh5alNVeUhnSlVmNmlSSnZsQ0FGK0NxeDBPZXpGYitFSGh2M0xDNitWU0YrZThhalBJOGQvR041QjN0TnhCZ242ZkdCbXdhQmYyTFRqaFg1Qm9ENEkrM2I1UHJ3bEIxcWxkYnhlTDEwbHNSSjZpZ3hxNDBncHNYZFl1SXV3c2p4aE04UzIwc0JycVdnVjFvR29zSkdieEpyUFk0RW14SHRPMjY1SXUrOTgiLCJtYWMiOiJiYmIxNDg1ZDZhZTljYjMyNDkyYjNkMzMxNDJhMjU5YmQ2M2FiMTk3MmZjNDE5NzMwMWVjMzQyM2VlNWVhY2EwIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:50:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkdCME9hUjRDY25qUE5hRS80RW9ncUE9PSIsInZhbHVlIjoiblZ2R3JLQlh3Mkx2eHJKemdqOUU1VXljVGo0c1F0TkhlVzdzOUxoWWhvMEpoZEhITUxrYXk2cFRFUkRDcFJIMWo2OWpiRVFYZ1pHK1ByNml5RVFwVGJMcWVIc1JaeFlWUE5uQiswNnlaejVqeFd1NG1Md2orUThtdHpOc0ZXUVFDY01CL09CdkFlVTBxbWZKUm5lVkFCcEhxZXNtZGV6cDRSdWs3TDlhTGMxWnhrcnFmY29BajlpNjMvS21oMzE4bVBMeHJhMEprREo0ZGtQT2o5MlRLUVZIOHNXYmxtb2xzTmtQWUFnZ1dyWmtjMjZQVC9EZ1FSZGZhN1BhaURqcGFlMC9DVnVDU01FV0VzSkRGZ1I0eVJHb1B5WWdhejIvUUxKditkVktQQnZyWTFaLy9TNUtWK3huUGVSYzFDMUdKWGpPQWNVNkd1bGJCbEJoSDEyc1VHZTNvWFdEdnlUZG40RUVSRlpMT3Z1UDk4RjdRczExZUIyb0JyM1Q1bEY2c3JKdHJ4aklDbGFXQlNlWjlrWkJtSWNNMW5CbmxLNEhrVVR1czI1YzA2OFBKdk5POVo4SHhaTE5vR3FNYkRsaEw3RmJQaGNwTEYvcDRMR1BYQlc3b2tHbkcwQVZjb05QMFNja0RHeE9uY2lCQkRDelBZVEZJVHcyVjhnaTBqWDQiLCJtYWMiOiI2MjA5NDU4MmNiMzMxODgzYzEyMWE2YWYzMWNlZmIzNmYyNzg4NTM5YTVlNGJkMGFhN2ZmMzI4Njg1Y2IzMGMxIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:50:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-672520271 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-672520271\", {\"maxDepth\":0})</script>\n"}}