{"__meta": {"id": "Xa25df377edd293059d5cc4f1f8730318", "datetime": "2025-06-17 14:01:31", "utime": **********.171887, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750168890.452725, "end": **********.171914, "duration": 0.7191891670227051, "duration_str": "719ms", "measures": [{"label": "Booting", "start": 1750168890.452725, "relative_start": 0, "end": **********.083208, "relative_end": **********.083208, "duration": 0.6304831504821777, "duration_str": "630ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.083222, "relative_start": 0.6304969787597656, "end": **********.171917, "relative_end": 2.86102294921875e-06, "duration": 0.08869504928588867, "duration_str": "88.7ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46326352, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1663\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1663-1673</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01599, "accumulated_duration_str": "15.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.128427, "duration": 0.01523, "duration_str": "15.23ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 95.247}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.159756, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 95.247, "width_percent": 4.753}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1084146812 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1084146812\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-104286218 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-104286218\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1409793706 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1409793706\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-950595630 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik1RUGVYeDdoY3RYckgxaDJtay91OGc9PSIsInZhbHVlIjoiZkxqQmZ1cmIrdEIvNGk3TDRDb3UzL3ZaQ2lzc2Ircy9GR1o0Tk9nSktxT1grUmdLY3NXdTVEa1lYb2ZQblZzVTUyellTUlM0TDdUSkQ5aEI0aWowdTZwWnc1V2U4ZmIvQzQ2eU5KbE9raXE2ZzBwSGNkUHQxZndLbHVRM2VvSlVLMDY4WGc1QUF3WThqR1pDdVFYVzlZUEVGdWJPRVdXaFlpMGIzOGFNa0NqWWUrTy9taGVnMllHZWt1TXN5VEdJSUNkTGYxWkFHVTVDYnJHa1ZJODVJdWlsOWpZMmF2aUhER1Vwc1RuMkJmWklVSWMvYVMyaU5sYUVRaDVwVm9nRzdOc3pJdFdUeU5BNm16SmhCdnNMSmZ1bHE1bkNYL2hYZGRxUU5Vc29xS2lDVDRBaUJEdzJtSVdTR1Z5MmpBaHMvRFRST1l6Z0pjY0k3YmljR2t0VUtKdGlSQU9rQzR2eSt6SmJiT0NOaTZDVDNaQ3BMSlNTckNVYmFnM0w2d0hPeUkxbGZkRmdlWFNWblRmcjltN05OY1orYVczWUJwVlkyUVdqMklSb2I1MHRMUDVObmFhY1hoTFIrR0R4S2hBTGFUVENKZlpyV3E4bytzc1hMdmg3NW53YUVrVWx4Y0JTcU9ZbmVZTGh4WDBqc1VrSUY0L3ZSb3EzMEVDdDhGNjciLCJtYWMiOiIyNTQxODZlNGZkNDBmMjhmODQzMjgxZTk2NjFhZjc1NGNmZmYyZDhiOWIzOWY4OWJlM2ZmMGIzNDVmZDI3ODE5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImQ4M014UEcxS0lVZ2xoYktVMjUydUE9PSIsInZhbHVlIjoicjhWVHFBemtjS3hsOGV1ME56UU92WTM5ZjVOak9UQmVZbVVaRGswY003cWV0c0xMN2w2VUtMY1hOZ1UzUXhDckUxZTZaT1dXS29pdm1QbXpNbEZoZDlKTTZHV2NtVWRiWklmRW9KWlovOVhWSkFDT3QzNlhKbFJFNmlaQ2s1Z05wWlhkMW0vM3hHdVhaYWV5YTVNNWZRZHRtMHdVNjRsc0VlNzgzMmZiblNtd1NWNzhPT1dmTDIvdU5CMVhLcGE1aDFnU2dFbU9pbEU2STZFT3dWNFZuaWU0bGtDZjBRNmNZcTZSdmM0R1IrN2ZoN3BJclJ5YzJNdjh4YW9TelBPTFhIVlR0MzlaMUpZS3NTRlpld3p1dmUvRnhady9rMmM4VGVXWVkwWWVoVi9HdUdLa2NHc0c4Ni8yYThSWHdBR3hRNlZ2c2V0K205Y2dWMnZobzE5YVdZS3lSS3dBejc3ZFVKaTErM0ZFcnJVSE1oWFNWRmV1UXBRRHhyRkh2eEFhMU0rc1dvdDQzdWR1NVM3clovOGZaWmpOZ1hhU2krZGZ2RkpuNS94Tzd0akE0TDRvKzZrSGRFU1pIQ3g3OWZmU1YrSVdOTGVLeDlKdDlIMkdiMWM0bU1SdEp2dzZLRDJSS01tY1VKbWdVRkZnc1hTc2F0NFg0TEo4V1M3cXpaNVoiLCJtYWMiOiI0MjQ0MjRiYzYyZmUxMDEzMjQ0YWU5ZjlhNDY0ZjFjOTIxY2FjNGVhMmQ1MDVjMzliMDhlYjlmNDYxMzRlNGYwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-950595630\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1287714158 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1287714158\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:01:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImRSRFQrNERpTDNTODljZmovRDF5NVE9PSIsInZhbHVlIjoic2VjVzViUXZ6SGdxVklXUDRoR3l3cEpqOEx3MUlhTy8vaVlObDJHalVrNzVyWHcwMDBPbGtoZS93amg2TC9iRVZGV2NjUjIydGxtUlBLQnBwR25WUFkzdVZqRC9ScWgrWDRDYkx0YkVXdUVHeHowb25HME1vOU9tMkxWejJzOEVwTzdIcDRMRlFCSmI4QU5jYXI1ZXl2YUFiL09DdG9CdVFYdnlKQ1JQMWxhaG9RMEhBQ2ZXUkhOVzc1Vnd0NW5ISXB0QW5nUS9qMlNQV0M0eS85cjBhT3MvbllmM3VJNmh6d0tiZHVWcUJkNGx6dlZDQzRhVXU2c0tOcXJtVDlPUUxEWndBeXRZSlJPZ2tOVUYxUEdDYmVwNXJENCtmcUppNVZYQXAyUzkrK2tzbVJHTmpHazBBbTJpemlIZjN5NFM4U2JxekFQblpWemxRL0dPZm9MenlSTDZWemNHU04ydzZFRG5XQUhSNTMvU3VudFcxcXlLbGZ2RWxZeHAwaTdzZnZENVhVT0d1OHJQQ05yTlBjcW5qblArakpzanFyM2xZa1MrUS9FOG9Cclg4QVhCZVhJSWhyWjFRWEJ0VTE1R3FZS3JYQ3p5RDZjZ2VzRUFrakh3eTYvaG1hc0lQU1ljMmtBWUFVR3pwM2RNaDV0RHphMVBLN2ZWSk1aVmxIM3kiLCJtYWMiOiJlZGFhNjE2YjczMGRlY2Y2YWVkMGY5MjNkZWMxYjU5OTNjZjBlOTQ1MWRhMTNiOTgyYTZlMzI4NjhjNTMyNzE3IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:01:31 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImJjOS95RHhDQjlUdk9KZmNJaS9hMGc9PSIsInZhbHVlIjoiSG1iSlBlZ1N3N3FKMWxUNDRWMzNpOUdYMWpKcFVNQ1pscDZGYXhtZm54UHVhN3M1NVJwYm5IS0o0NXBXWUVGU3g0K3ZZM0VqZWVQN2N1cGl0WkxaaVhyRkNlb3BpdEsvaWk1d1djekV5cm4yWTdJditFNFJKTmFCN0ZmUm8yVWU2Zi8xdDJCQjU5blhtWEF1NXVWMVNNNDhEVk5jN3JpSG02dlBHQ0pqcWFZTGx4L0c2b3BtSExXcEJ5Yk1VWEQ2Uy9ZbFhYeWZuRnQwQWxqQndGRTlpcFd5NTVCMmpqWFZnY2FWREhBTFNkMkExYXVhME1Pc3ZqaXRMdXVKUkpXY015ckxKaTcyaGZ1cXRDSlV2RFFwd2hiY2VCTXFVa3NwdnJ5bWFoMlIxYUM5TTJXa2dMSjFDZDRDczA3L1NDOWR6WVNqSmFaT2Uwd015ZHByR3hGeXJ3eFp2TWVYODhORnI2OGtwcUxjSVZTR2xMRUJ3RG5BNXZzNndaUjFCMTl6aUxYYzJTRExLQnZ3UWUxRkJlWllRTldUaEJtRllCYmxpaWd5YjJFY1h3Qm1vM00xeHNqS1VuTUw0bE90YUZLY3M3czM1T1B6RS9TYVQrZW43b25JeG1lKzBBcElJNkg5L051RkVVaFZaYmdxTWdBeldxL3VFb1lSc1Q0S2tiOVEiLCJtYWMiOiJlNzY0ZGU1ZTQ1NjViNzMxYzhiYThlNjZlZWY2M2MxYTRiZTk4NTU1MjdlNDI4NWE0NjZhYmNmNzFmOTRkOGVhIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:01:31 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImRSRFQrNERpTDNTODljZmovRDF5NVE9PSIsInZhbHVlIjoic2VjVzViUXZ6SGdxVklXUDRoR3l3cEpqOEx3MUlhTy8vaVlObDJHalVrNzVyWHcwMDBPbGtoZS93amg2TC9iRVZGV2NjUjIydGxtUlBLQnBwR25WUFkzdVZqRC9ScWgrWDRDYkx0YkVXdUVHeHowb25HME1vOU9tMkxWejJzOEVwTzdIcDRMRlFCSmI4QU5jYXI1ZXl2YUFiL09DdG9CdVFYdnlKQ1JQMWxhaG9RMEhBQ2ZXUkhOVzc1Vnd0NW5ISXB0QW5nUS9qMlNQV0M0eS85cjBhT3MvbllmM3VJNmh6d0tiZHVWcUJkNGx6dlZDQzRhVXU2c0tOcXJtVDlPUUxEWndBeXRZSlJPZ2tOVUYxUEdDYmVwNXJENCtmcUppNVZYQXAyUzkrK2tzbVJHTmpHazBBbTJpemlIZjN5NFM4U2JxekFQblpWemxRL0dPZm9MenlSTDZWemNHU04ydzZFRG5XQUhSNTMvU3VudFcxcXlLbGZ2RWxZeHAwaTdzZnZENVhVT0d1OHJQQ05yTlBjcW5qblArakpzanFyM2xZa1MrUS9FOG9Cclg4QVhCZVhJSWhyWjFRWEJ0VTE1R3FZS3JYQ3p5RDZjZ2VzRUFrakh3eTYvaG1hc0lQU1ljMmtBWUFVR3pwM2RNaDV0RHphMVBLN2ZWSk1aVmxIM3kiLCJtYWMiOiJlZGFhNjE2YjczMGRlY2Y2YWVkMGY5MjNkZWMxYjU5OTNjZjBlOTQ1MWRhMTNiOTgyYTZlMzI4NjhjNTMyNzE3IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:01:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImJjOS95RHhDQjlUdk9KZmNJaS9hMGc9PSIsInZhbHVlIjoiSG1iSlBlZ1N3N3FKMWxUNDRWMzNpOUdYMWpKcFVNQ1pscDZGYXhtZm54UHVhN3M1NVJwYm5IS0o0NXBXWUVGU3g0K3ZZM0VqZWVQN2N1cGl0WkxaaVhyRkNlb3BpdEsvaWk1d1djekV5cm4yWTdJditFNFJKTmFCN0ZmUm8yVWU2Zi8xdDJCQjU5blhtWEF1NXVWMVNNNDhEVk5jN3JpSG02dlBHQ0pqcWFZTGx4L0c2b3BtSExXcEJ5Yk1VWEQ2Uy9ZbFhYeWZuRnQwQWxqQndGRTlpcFd5NTVCMmpqWFZnY2FWREhBTFNkMkExYXVhME1Pc3ZqaXRMdXVKUkpXY015ckxKaTcyaGZ1cXRDSlV2RFFwd2hiY2VCTXFVa3NwdnJ5bWFoMlIxYUM5TTJXa2dMSjFDZDRDczA3L1NDOWR6WVNqSmFaT2Uwd015ZHByR3hGeXJ3eFp2TWVYODhORnI2OGtwcUxjSVZTR2xMRUJ3RG5BNXZzNndaUjFCMTl6aUxYYzJTRExLQnZ3UWUxRkJlWllRTldUaEJtRllCYmxpaWd5YjJFY1h3Qm1vM00xeHNqS1VuTUw0bE90YUZLY3M3czM1T1B6RS9TYVQrZW43b25JeG1lKzBBcElJNkg5L051RkVVaFZaYmdxTWdBeldxL3VFb1lSc1Q0S2tiOVEiLCJtYWMiOiJlNzY0ZGU1ZTQ1NjViNzMxYzhiYThlNjZlZWY2M2MxYTRiZTk4NTU1MjdlNDI4NWE0NjZhYmNmNzFmOTRkOGVhIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:01:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1948361450 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1948361450\", {\"maxDepth\":0})</script>\n"}}