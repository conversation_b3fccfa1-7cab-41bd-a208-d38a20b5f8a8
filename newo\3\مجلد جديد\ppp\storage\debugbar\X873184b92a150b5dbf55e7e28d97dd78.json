{"__meta": {"id": "X873184b92a150b5dbf55e7e28d97dd78", "datetime": "2025-06-17 15:02:25", "utime": **********.470045, "method": "GET", "uri": "/customer/check/delivery?customer_id=6", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750172544.909257, "end": **********.470068, "duration": 0.5608110427856445, "duration_str": "561ms", "measures": [{"label": "Booting", "start": 1750172544.909257, "relative_start": 0, "end": **********.395564, "relative_end": **********.395564, "duration": 0.48630714416503906, "duration_str": "486ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.395577, "relative_start": 0.48632001876831055, "end": **********.470071, "relative_end": 3.0994415283203125e-06, "duration": 0.0744941234588623, "duration_str": "74.49ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44962072, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.017740000000000002, "accumulated_duration_str": "17.74ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.436869, "duration": 0.016730000000000002, "duration_str": "16.73ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 94.307}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.458295, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 94.307, "width_percent": 5.693}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1250471988 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1250471988\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-452038485 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-452038485\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2112291706 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2112291706\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-556079836 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkdhNTQzVTNYcmFOeU1Ldk1hUkRxOEE9PSIsInZhbHVlIjoib0FsSXJ5azliOENkcTZ0eHUzZ3JBeE1iaGlzQXpvQTNrbjdXZHN6OCsraHU0K2pZdXFhNDk0b3JMOThUbmR0aUNzRmZnOHp5WmptQmFBR3dZcmJ2UnM0VmkxWkVOYnQzN3Vna1pFUy8yMXVaVTREMnVwZjJZd0tZbnZ2OG9CUDlBUEMrS015Tjk1a0JFSUE2MFpPNTRYVVY3Y0dGdHEzNjZST3JvRnYrNU9HT3ViOUFnd04zMjJZVTYzY1Q2N0lraVhLWWZ6KzZvOFhSejBpTFIwWks5TjlnbUVMRlNBbk55ZXNYMUNPUlYvTENUMDdrQU9XVnNyQVZPcVpzbFhDQ0wzSVF2TWpRa21NRzNaUklENFFqU1RRMlp3SkV2d3R0cVNWS0hTQWJpblJvM2ZSSEdZbERsZkJ4ajNuVVJUNGNEOU9ucG4vb2phcGlaYUQ5MFc1Y3lwNTdrY3NiZ0swT003MnROcTdCTFUwRFFkUVhpTWFqNkhOZlIwbkpkZXJZclJkNHpCb0xSVkpidGFFd21Idis0V2RVQ3lQM2JiV2kwUUlCTUVIaFRxTUk0SGpKbk9aWmlIV0t1bEgwSmRHZDNWckRQUHdQRFZrSXFDcDZEcStBSUpUbjlmWW1Cem8vTGtSaGhZckJQSFR5b3FUZE9yYWVKK2JGOFdhcFdreG8iLCJtYWMiOiIwZGUzNGVjOTFkZDU2MTQ0YzEzZjE1NDRiYzAyYzU0YzA4NGNmYjNmOGQ0MTY0ZTBjOGZlN2RlZjJhMTQyZTQzIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InptLzJicUdkZTU5MTZwQVZPZHoyVGc9PSIsInZhbHVlIjoiNUszZE1tY2YvanJVRWEreWdYdlFLOHRjWlU4MmR4am5HeW9SbFIybVllQ3YzRndncmZNb0Q4WHoydHc0KzBTMUR5Q01nYW5XdHgvMEJxOUVCYW9MdGdCUllxc2NlaUZ4djJJMHRpZlAzWWhEWVdPZzRBMnl1RiswR1NxMXdRT3hTY0NlWVB0a1RROFB5NVdWQndsaWg2eDY2amhyVHo0aUkvb0tEdmNwdy95U3dzSmdiNVBoVmpWb1pTMk56dXhIZDN1N1picHdqTVVzd2lrUzd5akNiWFRlWGZrNzhEWkliRVl6Y3diS3FTb2hCUk1pb3VQMXh1UXg3dGJiL2U1cTUydDBBRDVRQU5NTHNncTEzVnl3VkJlTmFDem44cEpFa3EvZ2MxRGZCdTkycWZuVkRVOUNSekhZWGhIWmorV3hHL1NzZjNPNS9mckJDeXhjZkI4TmcwNE1BNkZhWmZNVmlTTFd5Z2pSdkRuUVhPMUYvaHAybkw4c1BiNWl0aEtCdTBxY1JCOXdaUVhiMFFHMzg2QjBpTUhoRis1aXB0ZG5mbml6T3NGbzA2bEpXS2xWWHZEcTgxS1h1Q0dWT3ZXYW1wNFg3VitNT042OWs5MEdzdUhyMjh6VWMybkJzUlRXM2c4VEluRlZNRi9EU1ZjZVZvYzljSVV3Ym5DbldSMVIiLCJtYWMiOiJiNzM5MzhhZWU1N2QyM2FlOGQ2NmUxYzY0NzRjODhlYTNiNjZlOWQ5OGJmMjAzMDdmM2RhNjdjMWNhYzNiMmU4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-556079836\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-214413734 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-214413734\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2089175285 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:02:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imd6VEtoSytWcEg4SHJnN2VuVGpOUGc9PSIsInZhbHVlIjoid1hGL2daUVRaNDlLcDUwamRjZ1JVbE0xY0JnU0RVL0ViNGxuN2ttZ2tLV2NJTFRwVW5UeGZ6Z3owd3F1UDhFZjFvbW1VTjNNNjlqRis0L25lRnZzWW9YcHVGZE9qTitqdU55QzhoaElpKzZDR1ltQnRRa2Z0VWFNOEIxSG5CVW1EWndyTC9UaWNnbDhKSFJoRUZxQnJlcGhubmlaL2wzL3ZuSjRDaXJEQkZYME5uNDhWSXlTaHcwWUR1ZHQwdmN5ZTFNU20ybkxGNkY5UHhQcG1yVWhEU1F2RVc2K3NVUzYyMVloais5VmV2amFBSjlYY0VsektWSXV2LzZWa1l3OWd6OTVFazIxTVZ4dThnMEhZSXMralB4UDM5NGhLMmVRWnV5WXhEckFvblJQblVMRGtZdHBkM2ZyQjZoaG5obGQ2SnoxOGNGczZVTi91UWQwcElZZWZaeHQ4eWtFUVBmTHhZMll6ckFDZFVGSXRGMHd5QUNBSGNvQ1JqUHYrWlJwTTJnT1lONjJUKzF1N2pHUzMzMUxkZHpiZXRVZ1RLanJFWnZkRU5vQlNYaG41V24vazBhMzdCdTNHRTRHN1p0UC90cnVLUXBkTEdDNktPMlpDUWNoeTZiejZXMk9JTGhZN09tTm04Z3lIMXFOZE9XOUtNRjlTTFcyUHhuMTFoOFQiLCJtYWMiOiJkNjQ2NzgxMDc1MzAwZDQ1OWQwNjQ2NTQ1NDQ1YzE3ODMzYWEwZGJhZTZlODkwYTJkN2I2MjUzMDE0YTM2YWZhIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:02:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjVoRytnN0JSZENYWG5KVXdqbEtCbHc9PSIsInZhbHVlIjoiNnVnRU8wR3NUKzlkUHF4akk3aExPNE9ncjA5RHBjQ3dtcGdtL3c4RGsyOWJaOWFCazJqVmJFQkNXRkN6Z0Zkd3Z0cXl1ZG9QVmxFbFQzZUZCNW5GNHR1RDBQTEdZU1VWV3BVWDFlc1lYVHBKb2xxVGE5K245eG52M1AwY0FmRzE3ZUZqKzhmeExFR0x2a1hYaFF0eVgrc3E2Mlk3d0JPODczNWJwLzNDNWh6MG5nNUs4S3EvbTRRdkxzc1FWMnhodE1xU2FvTGVSVDdZMFdsb2hrZGhvMjBGN1Y1Skl1VUJESTB2VzFuOXZOSWhIdGZwSCtHWjlsbS9WYjkwYkduZXdRaDByQU16SFdpc0VQeFlWeVV5a2FVdS82cXk0ZlhjY0NvNnkyR2dXS0dxRTJOY09VdWJoRHNTdWV0dmx5K2U5eU9Yb0pGbjc2TEordi9PcCtKNlhxVmJyYXQxQ3F2a3I2NXN1SW55aVNLVzkrSzFOdUh3Q0dQUDM2Lzh3RmFVRCtMWDJuR1ZwL3pVUDB3NVhZdk5JR240cXRScE15eUR4N29mSHVKUXorM1lCKzZVb3pIeG04VTJZb2RuWFF4cDJORENFamhVTjZadisvMy9mWldxMWFldnc0YWtRYzFPNFZmaE1iRW9UMjhISVE5U2k0L2cwcmpxa0NLRTEyMWUiLCJtYWMiOiI1NzdjYmY1NTMzOTIyNmYxZjQyMmE2MzJkZGUwNWNjZTYxMTE3M2QyMjIzZGJmYTdjNTI5ZjBlMGU5NmI0ZTUzIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:02:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imd6VEtoSytWcEg4SHJnN2VuVGpOUGc9PSIsInZhbHVlIjoid1hGL2daUVRaNDlLcDUwamRjZ1JVbE0xY0JnU0RVL0ViNGxuN2ttZ2tLV2NJTFRwVW5UeGZ6Z3owd3F1UDhFZjFvbW1VTjNNNjlqRis0L25lRnZzWW9YcHVGZE9qTitqdU55QzhoaElpKzZDR1ltQnRRa2Z0VWFNOEIxSG5CVW1EWndyTC9UaWNnbDhKSFJoRUZxQnJlcGhubmlaL2wzL3ZuSjRDaXJEQkZYME5uNDhWSXlTaHcwWUR1ZHQwdmN5ZTFNU20ybkxGNkY5UHhQcG1yVWhEU1F2RVc2K3NVUzYyMVloais5VmV2amFBSjlYY0VsektWSXV2LzZWa1l3OWd6OTVFazIxTVZ4dThnMEhZSXMralB4UDM5NGhLMmVRWnV5WXhEckFvblJQblVMRGtZdHBkM2ZyQjZoaG5obGQ2SnoxOGNGczZVTi91UWQwcElZZWZaeHQ4eWtFUVBmTHhZMll6ckFDZFVGSXRGMHd5QUNBSGNvQ1JqUHYrWlJwTTJnT1lONjJUKzF1N2pHUzMzMUxkZHpiZXRVZ1RLanJFWnZkRU5vQlNYaG41V24vazBhMzdCdTNHRTRHN1p0UC90cnVLUXBkTEdDNktPMlpDUWNoeTZiejZXMk9JTGhZN09tTm04Z3lIMXFOZE9XOUtNRjlTTFcyUHhuMTFoOFQiLCJtYWMiOiJkNjQ2NzgxMDc1MzAwZDQ1OWQwNjQ2NTQ1NDQ1YzE3ODMzYWEwZGJhZTZlODkwYTJkN2I2MjUzMDE0YTM2YWZhIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:02:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjVoRytnN0JSZENYWG5KVXdqbEtCbHc9PSIsInZhbHVlIjoiNnVnRU8wR3NUKzlkUHF4akk3aExPNE9ncjA5RHBjQ3dtcGdtL3c4RGsyOWJaOWFCazJqVmJFQkNXRkN6Z0Zkd3Z0cXl1ZG9QVmxFbFQzZUZCNW5GNHR1RDBQTEdZU1VWV3BVWDFlc1lYVHBKb2xxVGE5K245eG52M1AwY0FmRzE3ZUZqKzhmeExFR0x2a1hYaFF0eVgrc3E2Mlk3d0JPODczNWJwLzNDNWh6MG5nNUs4S3EvbTRRdkxzc1FWMnhodE1xU2FvTGVSVDdZMFdsb2hrZGhvMjBGN1Y1Skl1VUJESTB2VzFuOXZOSWhIdGZwSCtHWjlsbS9WYjkwYkduZXdRaDByQU16SFdpc0VQeFlWeVV5a2FVdS82cXk0ZlhjY0NvNnkyR2dXS0dxRTJOY09VdWJoRHNTdWV0dmx5K2U5eU9Yb0pGbjc2TEordi9PcCtKNlhxVmJyYXQxQ3F2a3I2NXN1SW55aVNLVzkrSzFOdUh3Q0dQUDM2Lzh3RmFVRCtMWDJuR1ZwL3pVUDB3NVhZdk5JR240cXRScE15eUR4N29mSHVKUXorM1lCKzZVb3pIeG04VTJZb2RuWFF4cDJORENFamhVTjZadisvMy9mWldxMWFldnc0YWtRYzFPNFZmaE1iRW9UMjhISVE5U2k0L2cwcmpxa0NLRTEyMWUiLCJtYWMiOiI1NzdjYmY1NTMzOTIyNmYxZjQyMmE2MzJkZGUwNWNjZTYxMTE3M2QyMjIzZGJmYTdjNTI5ZjBlMGU5NmI0ZTUzIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:02:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2089175285\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-876027932 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-876027932\", {\"maxDepth\":0})</script>\n"}}