{"__meta": {"id": "X14f0263dd0e1d39128dae1b3433f438d", "datetime": "2025-06-17 14:38:51", "utime": **********.677859, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750171130.967866, "end": **********.677883, "duration": 0.7100169658660889, "duration_str": "710ms", "measures": [{"label": "Booting", "start": 1750171130.967866, "relative_start": 0, "end": **********.567716, "relative_end": **********.567716, "duration": 0.5998499393463135, "duration_str": "600ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.567734, "relative_start": 0.5998680591583252, "end": **********.677886, "relative_end": 3.0994415283203125e-06, "duration": 0.11015200614929199, "duration_str": "110ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46326408, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1663\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1663-1673</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.027870000000000002, "accumulated_duration_str": "27.87ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.621902, "duration": 0.026940000000000002, "duration_str": "26.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.663}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.66569, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.663, "width_percent": 3.337}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1434738966 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1434738966\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2101751671 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2101751671\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-169916328 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-169916328\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-933921992 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImFnOTFNRjZxL0FkNEErMXJ6SC80OXc9PSIsInZhbHVlIjoiK1RBQUltYWlQY01wS3VoRndud3NxUEJueDRlRmh2U1BYdmNCWVhkWHNQOHJ0cWhwWnAyME9EeEczSzVaWGtxYWk2VFlsWEdvdkdFeXNoWkRRczF1SGFaR0V1YjdyTlNDMG8yZUsrQXhqNmVKMWt5QlRmV2hiazZPSitCZlhDUFN6OVZBeWVzZzdwcmJpTStMcnFxdCt6T1FLMjJHcVUraVROMFJlZHJEUFpIRGJ6cjZZWGhhY1ZhV1dUbjlJYUVVS2ZnbGc4L0x2ZXFYMUFJTlpkaGhRTDZhTitNWGs4RFYzam9lZER2L01wa29wTzR1ZG9yZEovU2FuTUFzRFh6UnFqUU5CcFIvM01VTWkrVXF2TWhoQnJianU5Mk9EMDdNWWY1a2hqRXBldGROcTUzaS9kY09UMG9peXZSNUg4UkpIZm01eTgvUUZMVDFDMVpZY2ZlbUtLSWtSZjdGZHFLUCtwL1pFdytTQmFvTGljUEpwcUpSK291N3dkOUx3aW5TU1V4UElyUGtGSTJCTG80SGsyTTRJR0gxR3V6YjJmaUFna2ZMV1JPT3FQRWZ0T0ZGYkN1bks3RGtvYytFVy93ZUs1elFhMlpXRVh2MzZ1VW5ReHVRc2JJSVF5TzBta2xPcVdwbzRRZWZUaWNCZnJ3dGFMY3NUTjJvUmNSeWsvM2EiLCJtYWMiOiI1YmVhODNmNDk5ODBiZjgwMzMxZTk2MGJlYmM1MWRjNGE4OWI3ZTZjNDgwYWJkZTBlN2NkNjBmNGM1M2UyZTMzIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjFVS2JzV1IvcGpwMlo4ckVVLytEZ2c9PSIsInZhbHVlIjoiZ3hUMXYwNzc2dDFmZU1YaW9Nb29VbEMrUDY3VGwreEZrNkcwQi85WDkxRXFZUTduUzl4N2IvWmNIUEdvMVNGcHhjeVp4VUR4SkdLRi9OY2RBQzBtSXBFc1hEZ2RuSUdEdHpSeWVmTnhUdkhsajVwT09INVVYdXhpd0dVd3NPOVRlRXF2ZzZ0dm5uUVd4a2RJL1czaG9Ia0dPckJaaklia1F4NkNlTnF4WXFha0xWYnpkOVhsQ2IwZU0ybVluTWZSbUpzbnlFbHZuU1d2YXIrZXpQMVY3YTFsQzYvdmprRzYrSGR4NUg0OVFzZGZTdFp2bUF6L3gwS1NMTm9vV3JPQ2huYUxLdGoranA4OEliQXV4bWY1Vmt1ZmZEekM5YU82QzkraGozRFVWNVlBZUordjNlS3dzM0dGdVRuTWdqZ0pGU3poRzFDd1BVR05tUXM2ZytqRlJ4b1gxRVA3Nzk4M1pUMmpzdmpKOEhEcWk2aWViSDJKeFdaR0FDN2ljbFMwOEJPV2g1Ny9mS24wMCtGS3RQcGNZdUxCNkF5ZzdKQVpabWVXaVE0b1M3SGpwbUFZa0I2Qmd2bjExZmF4UXZHVVpYcnROS1FDb3luaVYzazVuayt6UUNNaWJ3bUJCOW4vSVpXZ3UwMHcvd0tobmI5TUJvd1ljTVo2VXBRRzd2TW0iLCJtYWMiOiJkMjYzNzhhNjc2MWNjOGI1MmJlZmQ2M2VkYTFiZTAzZDlmNjRjNjE5ZThmZmRkMmFhNGY2MWIzNGU4ZWUwY2E2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-933921992\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1331793823 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1331793823\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2133103150 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:38:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjdYRTEyM2lYQWdaWkpkUWs0RkZrbWc9PSIsInZhbHVlIjoiUWs5WmJsK0JjWXJ5SUZpa3FRVEhFbzVUSXU1d3kxTm9yaWJuRzY3bjZMTVNwRDVoZlVibUpYKzdmUStMaHExNnZKNS90L2VsMHZ3VzRERnBtTXAwM2ducjdPb2dXUStuVmxhNlZqc3J5M1JzUEdFOHh6eVg5R3NBejVNZU0yRmU5bGM1czQrUFVUcktwSGE0QnF4TjV3eXB3ZFZBWXNFVERtUVBFbkd2YjBUZVhIQmtLZVVxSmdFaWNKSVRnSmVlUnlEYW1aZzBYUTRNYkg1c0JsSGxQNDNmV21hQm1keFdLNjVPVG1KS3BoYjlGaUhEYzhTRG1mZ2d5WHBiaS9MQXhMZGpQaUE2bVZvN01nRjJ3TUdRcm1rSEh3TkZHRDFIaHhpMFJGejh5VGgvZk4zM1JEZ3Jqd2FheGtZdTdNc0hTRmVEcjhSNXIwSmxMd0UzMGtRdk8zQ2lRdTFUOWk3V2pwYmF5dnV3VjRGejNDaC8yM2VLMkxMWFczbktmWnJLSUcvZ1pWbnhvSUpMUmpjOGJKeGVuaE4zb1Y0c0dySGlvT3AvSjVuWWN3cWxlOEM0VkJTOGZla2hVNUZOeXRkVkJZYTVweWp1ci9jMFp5VzBiam5XRGpRdmJDRVNrWWh1QS9uUnJTbjlZbkYraFBSLzhWOVEwYkI0Mm9SVnZVZkMiLCJtYWMiOiI1OGU2YWE3NmY4ODcxZWVkNjcwNzcwYWQ1MTRlODVhMjVmNWMxZjJkOGRkZDhlNzVjYzcxZTkwNDk0ZTUzODAwIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:38:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InV4dDJJU3ZTc0FwQ2VMeFNHeUFzWHc9PSIsInZhbHVlIjoia2RHcXJLWHVNd093ZWNHalFla05jOHE3eFYrQld1QUlGMnV4UTJ4TkZYbEN2VURuajI0RVRnRmdwcWZ5eHZJL0o0MnRaWGhWV0FJcktRWjM1OTdqdUd1OVpRU2NFclNPQ2hsQVh6V2RyNFN5aW56djNYem02dFp4cUpXS0E2S2VwQjFVeStvdWdITXI4TnhXNjFwcUZiVmJXUk5JQ3Q2WFhFWkZBY05NM1NEdWNoZ0VDMW9mSnRMRWhoQUhzM3RHcnRmNU1FRG96Sk5GdUlqTGpNdmxiMWRsbk5LRm1XOVZsR0pEdjBCSEJ0RmRLV1V3TElNaXlNOG1nelMrZDlCeVh2MEdQS2c1Yi9aR0ZLR0x5NFNEVG8wUnNQKzNqWFc2UEg5T2RjM0dqM2l0VTJLTVJRdlpjSEJUT2Q0TDQ3RlhwcWRNTG1Xb3YwZE5iRzdRaUpQOW5QZkI1RkdaRUU3bnFFVlZub2RDSTRXR0htR2M0U1lxeHRvR25Bcm1WdlRuVmNHYkVoMWVUVlhLRlBDVUpRcC9NM0RvTkZTdzlTdVFNWkxnZVBWem04Sm9HWDd4MEdlY05KaVZxalNsYXJ0eHI4bXlaNXY3dy9rUmxWU2hscFlINVJ1dVA0VWZIOEI5MmxZb0ZpZnpwVlRtcEkwN1ZDRUxudTFZNlJrRWFwbHEiLCJtYWMiOiJhOGJmZGVhNjNiYzcyNTU1NGYwZmJiNWM0YTRlMmRmYjRjNWJkMTEwZTc2MGRjNGE3MzI4NTMzZmI3NjNiMWU5IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:38:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjdYRTEyM2lYQWdaWkpkUWs0RkZrbWc9PSIsInZhbHVlIjoiUWs5WmJsK0JjWXJ5SUZpa3FRVEhFbzVUSXU1d3kxTm9yaWJuRzY3bjZMTVNwRDVoZlVibUpYKzdmUStMaHExNnZKNS90L2VsMHZ3VzRERnBtTXAwM2ducjdPb2dXUStuVmxhNlZqc3J5M1JzUEdFOHh6eVg5R3NBejVNZU0yRmU5bGM1czQrUFVUcktwSGE0QnF4TjV3eXB3ZFZBWXNFVERtUVBFbkd2YjBUZVhIQmtLZVVxSmdFaWNKSVRnSmVlUnlEYW1aZzBYUTRNYkg1c0JsSGxQNDNmV21hQm1keFdLNjVPVG1KS3BoYjlGaUhEYzhTRG1mZ2d5WHBiaS9MQXhMZGpQaUE2bVZvN01nRjJ3TUdRcm1rSEh3TkZHRDFIaHhpMFJGejh5VGgvZk4zM1JEZ3Jqd2FheGtZdTdNc0hTRmVEcjhSNXIwSmxMd0UzMGtRdk8zQ2lRdTFUOWk3V2pwYmF5dnV3VjRGejNDaC8yM2VLMkxMWFczbktmWnJLSUcvZ1pWbnhvSUpMUmpjOGJKeGVuaE4zb1Y0c0dySGlvT3AvSjVuWWN3cWxlOEM0VkJTOGZla2hVNUZOeXRkVkJZYTVweWp1ci9jMFp5VzBiam5XRGpRdmJDRVNrWWh1QS9uUnJTbjlZbkYraFBSLzhWOVEwYkI0Mm9SVnZVZkMiLCJtYWMiOiI1OGU2YWE3NmY4ODcxZWVkNjcwNzcwYWQ1MTRlODVhMjVmNWMxZjJkOGRkZDhlNzVjYzcxZTkwNDk0ZTUzODAwIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:38:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InV4dDJJU3ZTc0FwQ2VMeFNHeUFzWHc9PSIsInZhbHVlIjoia2RHcXJLWHVNd093ZWNHalFla05jOHE3eFYrQld1QUlGMnV4UTJ4TkZYbEN2VURuajI0RVRnRmdwcWZ5eHZJL0o0MnRaWGhWV0FJcktRWjM1OTdqdUd1OVpRU2NFclNPQ2hsQVh6V2RyNFN5aW56djNYem02dFp4cUpXS0E2S2VwQjFVeStvdWdITXI4TnhXNjFwcUZiVmJXUk5JQ3Q2WFhFWkZBY05NM1NEdWNoZ0VDMW9mSnRMRWhoQUhzM3RHcnRmNU1FRG96Sk5GdUlqTGpNdmxiMWRsbk5LRm1XOVZsR0pEdjBCSEJ0RmRLV1V3TElNaXlNOG1nelMrZDlCeVh2MEdQS2c1Yi9aR0ZLR0x5NFNEVG8wUnNQKzNqWFc2UEg5T2RjM0dqM2l0VTJLTVJRdlpjSEJUT2Q0TDQ3RlhwcWRNTG1Xb3YwZE5iRzdRaUpQOW5QZkI1RkdaRUU3bnFFVlZub2RDSTRXR0htR2M0U1lxeHRvR25Bcm1WdlRuVmNHYkVoMWVUVlhLRlBDVUpRcC9NM0RvTkZTdzlTdVFNWkxnZVBWem04Sm9HWDd4MEdlY05KaVZxalNsYXJ0eHI4bXlaNXY3dy9rUmxWU2hscFlINVJ1dVA0VWZIOEI5MmxZb0ZpZnpwVlRtcEkwN1ZDRUxudTFZNlJrRWFwbHEiLCJtYWMiOiJhOGJmZGVhNjNiYzcyNTU1NGYwZmJiNWM0YTRlMmRmYjRjNWJkMTEwZTc2MGRjNGE3MzI4NTMzZmI3NjNiMWU5IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:38:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2133103150\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1722241126 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1722241126\", {\"maxDepth\":0})</script>\n"}}