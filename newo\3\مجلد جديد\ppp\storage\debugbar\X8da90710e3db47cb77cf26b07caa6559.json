{"__meta": {"id": "X8da90710e3db47cb77cf26b07caa6559", "datetime": "2025-06-17 14:05:52", "utime": **********.335427, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750169151.526935, "end": **********.335447, "duration": 0.8085119724273682, "duration_str": "809ms", "measures": [{"label": "Booting", "start": 1750169151.526935, "relative_start": 0, "end": **********.241735, "relative_end": **********.241735, "duration": 0.7147998809814453, "duration_str": "715ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.241753, "relative_start": 0.714818000793457, "end": **********.335449, "relative_end": 1.9073486328125e-06, "duration": 0.09369587898254395, "duration_str": "93.7ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46326352, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1663\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1663-1673</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.014680000000000002, "accumulated_duration_str": "14.68ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.293077, "duration": 0.013800000000000002, "duration_str": "13.8ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 94.005}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.323387, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 94.005, "width_percent": 5.995}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1431819052 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1431819052\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1053694647 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1053694647\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1429541673 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1429541673\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1080932404 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkZVcVlBTVgzUHNNRmwrTTQ5bnhnRGc9PSIsInZhbHVlIjoiVUtUUUdnbVp1TWppa1E4NkVNcXlKeWJIRHh2UFRJeVNrZlRXbm5QdTR6YTBlRHJ0ZXhRV2ZnTHNpQ2hTUGxGQUNRYjdzcitNNEdSVVYvTk1XNjYwTm94dG1nQ1Q1aE9VL0FoTTFOekw0ZEhCUWV2N1RFVUZLeWk2U1NTSnVma0ZZM2p1Z2k5cW1GK3MvQlllZ2F0elZVQS9MR0doWGtVRncwaGhQSDZkTEZLZGdiZDM2V0hNUUNlakkxVTg2SVNXRFYrL2pDc0dPQVd3QUt6VUc1R0JPM2w5dFpXV2ZyT2pPVzFBSDQzRzRlUDBoWTNEQ1c0MDh5aHZnV3h2NlBhaFR1RUFlc05ZT2FpeW9ZanRrcW1xN2N1dUxPRG1RTXg0aXp4T0dJWGtoQ1NIejBCL0FrZUxxOEw4M3VIY2xhTnpGa09sMEEyVFAvWTEzRXhyNDhZR1UrZTVXK0RWdEhKT2h3eXY2WWNtOS9Ec2FjV2hoc29WMjdVMnJIdEZLKzV4Um9nWi9Rc2pUbzlRdWUyQS9xMjlaWDRWVVFyb2RUUDBvdDQ0eFVCSkRrbzdWL0g5MlNhUGZ4RlQwYTZIN05ka0xkTzFZbVFlNDNQTENkQ1hmTVM4RlpPYUZWL3k0RTU1ZzM4UHRVb3kzckZNSmM4L0lGQmFseCs2T2dhcDlkdnIiLCJtYWMiOiI0NzczN2E3NTY0MTNhZjE2ZTMwZjBhZGEyZGNiMWM4NjY3MzMzMjA0NTkzYWI5M2Q3ODc3MDIyODA0M2JkOTliIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjJ0WGM3azYrWjlFaEhjRVVId0thREE9PSIsInZhbHVlIjoiN1RzdnhkaXZlMy9KaDEwWnVEL1NIeXByNjhkbS9ZdlpsMlcxTmJxNTFrY0ZaVDc3Y2liSXV0NWR3YVpPdXRxK0VxSi80YndLcFR5TFFNOFZUblhVcUthZ3A3TzV5R0prY0F4Y3ZyNUlPRGdOMzZ2a2RwbFRNeW1uT0k5TEZ1Y0RBR3JOMWlsYm9yRHF6aGd5LzM1ZE43eE5xak5TSlRGYzUwTjhNd3J1MkdPaWpEemJzZW5DWU9tSDdhQ2FCbXJUMHdrSGRvY1g3bjEydUs4cWRjeGpjQ1ZDRTlxT3pGdG94VGVOc1NGdTNUU1Vzb3hEeUFEdzlpZ2xOOHI5NUdTYVA3QlVjRXR0NjhrK0FvWTI3RGE1ZjA4bHMxV1cyandzd0Nvd25mc0Q5TERBaFlvSzgrc3JyZ0REaHRkSG5xK3QzdVhBR1VxdG1iRWx4WFcwRSt6Sm90dWFkOFBxKzlwM2pYN0FUMFgybmRNNkxic3ZjOFcxK2FlUk1OZ2p4eGJOeWM4Yk8wb2xlQ3k2V29HalAvK1JzZFpKbmRaMXlaTWZHalRJQ0hlWkVOZ1I2c2N0M2JnazZWRzdvTzE3WHkwbzhTMDFGNUtOc28vOUVnNVg4cTRIN0Z1YUVlbXdKNkxPSzU5MlkyTStiMEUxRDQva1BsUFlOb0dvbFI1b3ovUHgiLCJtYWMiOiI1NWEzMmE4MTBjMGU1MjhmZjRlOTY4NDRkNTQ3MzdiMjY4MmE5N2E2NGUyMGIxMzQ0OGQ1ODA1ODU3NjI1ZjJiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1080932404\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1060362334 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1060362334\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1260048137 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:05:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InJ0ei9kWXZHaGllRUpESFlCYkJ0SUE9PSIsInZhbHVlIjoiUTBkN0FDdU9VbE5DUWRnSy9BR3hvcE4wWG9rL3djYVdmQitkdEg4SE9mTVJKUUNBR2lGb1VvS20reGI5SDNydjlyMFlxVW1iRmh2dG9mZTMyNmg0N2NVd3BpNnY3dWxQbWtjejFRL2R1S2kzUFcrNVRmQklDNUpEOUNYYWdCSFVMZ0pINTNWTS9HTmZaejA5M3BRRmZIN2VYZUY5Qy9yRlBaeklJV3E0Tldkb2FCa1hrWUJSTy9VRENuakYwNVBseVNxbWxjUnlmRnRBb1BtWCtnNGttWXdIWW9Cdkw0K0VkMGo4dWMreEIwYjNya1FEaWhsc21GMmZmR3kxV2hqRU85ZEs1dG9XVUx2VFhFbVMvYWQ1UlpNajl1N3o3TXhVZVJvdDlISFVOV2srOHRXTG5zMEhjZURDMUpyYUJlU2RXMTl1VW82RllmeTVlNDNhb2tPcWJJV1FPeEVqSDE1MTg5Zk5YemdLMFhOUE8vTEFMWVJnVkxTS3BpOEhRUFRHKytqbkNtK2JhcE5HMmxNTUZaSXNMT0lydUM1K0I5S3QrbmpKMGFNZU1NTVJHRHNVRFdzdTkvTnFjVUQ3ZXRkcVZxNGNHYzREQkJWcnc0MTBEYlhvbTFGREdabnY3dXFGbm5JUU00bmVUN2I4T3VEcnFyOUpYdDIvT0g3aVh4dE0iLCJtYWMiOiI5NDQ4Nzc0NmE1OGQ5MDA1YzUwN2EyNzc5ZGUyZDQ0NDgxOTk2ZTA1MmE2ZTZmMGEyNzE5ODJlMjhlMDg3MzQ2IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:05:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImxVWmhZblJIUVBVczZYQXhpQ2RpSFE9PSIsInZhbHVlIjoienBxTVUrK25VQlpEWHhTWU14ZUhJVURDSnl1QWhmeEhyeFkrbGFzeFdvcEpsVWR4UjJnNTQ4Z2FFWS9KbUlUVDRHM3VreTA1SWduM29ZRGVTUkU4akUwK1NnNHorRVpZZHBVWERsaXlrZENvRC9RV3NRcldTMXNDUFZaZVB2SGFKQmFIUHFkR1d4bGZ0SXdyR0tmK3lRZzBzZFR1N0NNZytobWhMQTZhbVZEV0VPNFFIY2JNRit3dmxDbVlRbUhlVkpZNEZVWW83UHJZYmFsR3R1M2drWE1pc20vczF4RGRkUWI0MVZnNE1KemY1S2hUVTZTYnJBbmNVeW9FQzMyZUdVNEplb0hYM0xGY0RVdmhDNURibS8rTTNWa1NUbDFQT3RLY0F0S3d5d1RBS0hPYVVTSkMzSGJVcW4vSkpXaC9rY1RYTm81NjJYeFNDVVBPSHdQejIxWFExV3BkUUM0VVE1djc0aWFEdU0yNENWVThaSnFLYVROQmlTd21uZWE4dU1SMmRFSjhSb3pySEVMRUNwWlhTMU9wU1dXWWx5bE1WVzJSdE10Wm9QUHBDaDNnK3FrMlpicEw1WFlYVm44WEdSZm1OTzgrUmU0MDBObFRmVW55SWYwK0pkSFUvRUoydkxUckZaNlQwaFhGd1NJRUw5QWdSZm5GVjNLcnJyQUciLCJtYWMiOiJiMjY5ZTI0YzViMGEyNDEzOTU4MzQ4ZmZkZTRiMjlmNjFkMThlZjZjMjE2MjVjNjQ3ZjQ0MjQ2M2NhMTZiNThjIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:05:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InJ0ei9kWXZHaGllRUpESFlCYkJ0SUE9PSIsInZhbHVlIjoiUTBkN0FDdU9VbE5DUWRnSy9BR3hvcE4wWG9rL3djYVdmQitkdEg4SE9mTVJKUUNBR2lGb1VvS20reGI5SDNydjlyMFlxVW1iRmh2dG9mZTMyNmg0N2NVd3BpNnY3dWxQbWtjejFRL2R1S2kzUFcrNVRmQklDNUpEOUNYYWdCSFVMZ0pINTNWTS9HTmZaejA5M3BRRmZIN2VYZUY5Qy9yRlBaeklJV3E0Tldkb2FCa1hrWUJSTy9VRENuakYwNVBseVNxbWxjUnlmRnRBb1BtWCtnNGttWXdIWW9Cdkw0K0VkMGo4dWMreEIwYjNya1FEaWhsc21GMmZmR3kxV2hqRU85ZEs1dG9XVUx2VFhFbVMvYWQ1UlpNajl1N3o3TXhVZVJvdDlISFVOV2srOHRXTG5zMEhjZURDMUpyYUJlU2RXMTl1VW82RllmeTVlNDNhb2tPcWJJV1FPeEVqSDE1MTg5Zk5YemdLMFhOUE8vTEFMWVJnVkxTS3BpOEhRUFRHKytqbkNtK2JhcE5HMmxNTUZaSXNMT0lydUM1K0I5S3QrbmpKMGFNZU1NTVJHRHNVRFdzdTkvTnFjVUQ3ZXRkcVZxNGNHYzREQkJWcnc0MTBEYlhvbTFGREdabnY3dXFGbm5JUU00bmVUN2I4T3VEcnFyOUpYdDIvT0g3aVh4dE0iLCJtYWMiOiI5NDQ4Nzc0NmE1OGQ5MDA1YzUwN2EyNzc5ZGUyZDQ0NDgxOTk2ZTA1MmE2ZTZmMGEyNzE5ODJlMjhlMDg3MzQ2IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:05:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImxVWmhZblJIUVBVczZYQXhpQ2RpSFE9PSIsInZhbHVlIjoienBxTVUrK25VQlpEWHhTWU14ZUhJVURDSnl1QWhmeEhyeFkrbGFzeFdvcEpsVWR4UjJnNTQ4Z2FFWS9KbUlUVDRHM3VreTA1SWduM29ZRGVTUkU4akUwK1NnNHorRVpZZHBVWERsaXlrZENvRC9RV3NRcldTMXNDUFZaZVB2SGFKQmFIUHFkR1d4bGZ0SXdyR0tmK3lRZzBzZFR1N0NNZytobWhMQTZhbVZEV0VPNFFIY2JNRit3dmxDbVlRbUhlVkpZNEZVWW83UHJZYmFsR3R1M2drWE1pc20vczF4RGRkUWI0MVZnNE1KemY1S2hUVTZTYnJBbmNVeW9FQzMyZUdVNEplb0hYM0xGY0RVdmhDNURibS8rTTNWa1NUbDFQT3RLY0F0S3d5d1RBS0hPYVVTSkMzSGJVcW4vSkpXaC9rY1RYTm81NjJYeFNDVVBPSHdQejIxWFExV3BkUUM0VVE1djc0aWFEdU0yNENWVThaSnFLYVROQmlTd21uZWE4dU1SMmRFSjhSb3pySEVMRUNwWlhTMU9wU1dXWWx5bE1WVzJSdE10Wm9QUHBDaDNnK3FrMlpicEw1WFlYVm44WEdSZm1OTzgrUmU0MDBObFRmVW55SWYwK0pkSFUvRUoydkxUckZaNlQwaFhGd1NJRUw5QWdSZm5GVjNLcnJyQUciLCJtYWMiOiJiMjY5ZTI0YzViMGEyNDEzOTU4MzQ4ZmZkZTRiMjlmNjFkMThlZjZjMjE2MjVjNjQ3ZjQ0MjQ2M2NhMTZiNThjIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:05:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1260048137\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1864989713 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1864989713\", {\"maxDepth\":0})</script>\n"}}