{"__meta": {"id": "X0fa067d638e774a7766325151cb42d63", "datetime": "2025-06-17 15:05:56", "utime": **********.5116, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750172755.827777, "end": **********.511625, "duration": 0.6838481426239014, "duration_str": "684ms", "measures": [{"label": "Booting", "start": 1750172755.827777, "relative_start": 0, "end": **********.409897, "relative_end": **********.409897, "duration": 0.5821201801300049, "duration_str": "582ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.409912, "relative_start": 0.5821352005004883, "end": **********.511628, "relative_end": 2.86102294921875e-06, "duration": 0.1017158031463623, "duration_str": "102ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46326352, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1663\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1663-1673</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01954, "accumulated_duration_str": "19.54ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4628088, "duration": 0.01865, "duration_str": "18.65ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 95.445}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.498285, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 95.445, "width_percent": 4.555}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1752182879 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1752182879\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1031233025 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1031233025\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1809100156 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1809100156\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1083170142 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InRPOVpweXljWlBwc2t3OU1objlxWEE9PSIsInZhbHVlIjoiTGdNSzJvUkM3MWh4THNvMUhnOFR2eHRuc2FXMzh5eGM5Y1lJdlZzd3IwNm41L3U1N1ZOV3oxMHNEUVBXaEoxMnJvNitjTWU0TGZ1Y1VGQjhVL0Z2YlNZa0Jjb2tuMXdsbEgrV1d3cUgycFVZY2lkRFJ5QmlaendJdVlvYkJNQlNpZzFtdUl3U0UwY2M0RkM0TURQVER0WXE5TjR3NmVteUI0MHV4d1pSODQ2eVZoZ0lqWXdqejhQZ0k1NmpHZjZnQWZCRmVEY3M5MnRtS3hLTFlGbDBvWjF4bFhkcUtwcjk0dWQwMGpnc01IMlJKZWpBR3dQMGhqbXgxeDZZMFp6S3prZDNWQ1hMTkxiL2tvRnMzK01XUkRlMEJiUEhGNGg3Y3JRYVA4Uk9iQzE2MmFBUUtkRXRKMDIzcU9oUUp1ZytaWUVrTjJIUjNWVUdIQzNabVpTYlRsZkMxV3dDWUJKWWorTm85ZC93SEF6azB3VFpzS09WenYvNFhMS283L3BLUVltL1ZVTlFSUzYza2poMnRNRTNLR3FhV0N1bjJhNkVKNSttdlpQQ2twSEhNazJWUUVXZE44bTVsYzh1WVM3cVVHcDFUdmhvYUpRYnZYcWVPOXFIaWluOG9LOXhSZktxdUd1S0tzQW56NGt1emRiamlHUXFOalU5emdCM2hFZXkiLCJtYWMiOiIwYWMzYTJhMGY5NGY1NjIwYzQ3MWEzZjNmYTcwNTI3YWYzNTdjZWVhZjhiZTIxNTZjNzI1ZTcyMGM4YmIyMjlkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IklYVVFmSG9ib1dGNG1BZll4eEpXRlE9PSIsInZhbHVlIjoiSVBDa3o1REdGT3BlaFRPcU1DaXpJUWp4MFhNZW1sMUhOUTFrYXlldDM5TmR5blBuVWhXTzZNRFZZa0pBU3V0V0lkQ3BaZ0ZpRW02eXpYM25LRC90SnZCaEVwK0lJZmdmOFBUYkhsdXkvMkJVRWpkR0RuZGdodWlFQVV2Nk9tazR0NjhXRGN3RHhsU2xJZTlJNmJteTJoQjBwcElybDBmeG15eXR5YTNmWWljYlFTQVhnUWdOMi9QRm1OLzJaUnBrUWJiSisrNklqdk8vWEgxRnA1ZUNoRk5sRzJLTHREY241MzdKTWJhMEJVeGNMdlBhb2VrenFvdHNFOGdpNTVGODJPR1pNanE0SGwzRnR6Tiszam5veUliMjRXS2k0aXVHaSt6N3VmMzA5S0dxdXg5SXdVTlRQUDZQeTFCT1pkRFJWQmRBM3BSdHVRZXo2RDVIa2xSNjFrWHduTU12eHJsSGFUcHVPekJoLzFBS3RLU2kwWXhLem5reDA1c3VFQnNSOU1Vb0p4UTBsa080OHB1cW5FTTBKa1ZKL1hDUXkyckllLzk0WUxPQnlybXVnNHVrQU1vRTJEYmVHV2ZUOW5HYzV3U0pXb0xoUEpoNXBYaWlORFFGMEpJTG5MbVMybmFyMDNJdDJEdHk3QU1YR24zSitpSDl2RTNkUG9IR3AzdEgiLCJtYWMiOiJlNWNiN2Q5ZGU5NjEzMGNjZTQ2ZmY2MzdlNWU4ZTMzZDFiZTVkMmUzYjRhMDEzZjg2ZWFhYTQzYzg5ZGVjNjYwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1083170142\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-125417148 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-125417148\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-490084667 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:05:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjVWdTF6OHoyVkthc2M1MUJGOXp3Unc9PSIsInZhbHVlIjoiSU1Lc2t6N2VDeG9IS3VlR0EwMndySml0RWhTL0VhNi9NZjdQcm1lOExlQU1xZHlCSGdjMkhITXRUNk12S2IxaVU4ZEJEY1Rpb3hqVDE0dC9zM08zVFJOcGVDUVpiSi95ODJKNkFJeDUvWkZ3ZUc2eWNnRmNRSWtRZEJUT0N5aTJJdVVicEEwSk85c1NRSmRwUmdpNm96b3ZjR05wRTB0dG84WmhFOVBIMEF5cGlEbWRnNGY2NzUxRFhxeFVRQmtYZzNJWmZpZkFjNVV0cjJhZFBCOEhEeVM1TVB0dEtVdHc1YVAzNDUzblI2clB0SzB3dDIrakwxcEhmWXhyZkpRSnI3YnhJbjZOMVpkY1Z5MkIydFlpdkptYzJPck5jbTlxemluczhWeHB3djQwemJPcCtvdHI3N29aMzFhV0hhNFdTdW9PRit5M3d0RThnRStYOGpFMGxYYVJ5SFZQYnVBSHlSR3pzaHNycW1LK2hQc0RaUURtdHNHNnVpT3dvV1dWNEZLckhML2Q2RE55MGxoRC8rWGFaMk52SC9BWm1WTnFBbGJDUElseENtYUdubEE2UHAzNUx2UVhkcHlDdDlqQkYzeVZ4OFp6a0tnQS9GOXpkclhmNlM3WFZCeGhneXlrTUx1a1NjY1N3S3JCUFVVT0xaT1gyUEpUSjBLRHpwM3IiLCJtYWMiOiI1N2Q5ZjFkYzliN2M5ZTg5OTA3YzdjMjk3ZTMxMzI5MTlkNjVmNDE2ZmYzMTQyMDgwYTFiYjhmODJiZjE5ZTEwIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:05:56 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlRYcGtxK2lpUFdlYkgrczJFRDZBTUE9PSIsInZhbHVlIjoiMFZxRndJRTZxZURHWmYwK3AxVVV6QXVFdDVsU3FuWmxzM0x5K050NVFUMlB0Nmw2STNqZzlkMmxFblQweC9OYW16TXUvZlkyU3p4Skx0ZzVBTnNDM1JiMjdaTEJQMThtaFdQWFFnVkhtaEtpTHRNYzNQdlpRYk4wRzZtN2hUTENYWlR6S1JIayswWkZYeDUrQURCb25McTZxV2FlY1dWZDV4U2dEV0p5Vm9NcFlncmRxV3pSbDRIV0diSjM3YzJBZmJSKzZKV2ZmdVExRmJGNTE5Y3Z5T1NYYlo2NXh2Y0VNdkJXdWhmQ0FhNWtJRjFUYjhlTUdwU1pueTZiRVRiUUdDbFoxNmZITEtucGFtelJubFpCQ0toaitBM2ljQ2xTcnhpYVd5b3JkNG1STFhVRkNaV3JaSGNuVWtiZWtDSW9GQ2I2WUNITTUxQUxhWjMyZk1udFhGdUtWOUxMcnB1MHRDb2hMOTBXOEhSL29SRlI2OFhSUVdrMFordkJaVjRWc2xRdjF1RzJJMjdyM0MweE9xRXowN0pYK3RBcGJ1MVo4VkJYUTNGZldTSUd5S3YzN2tJb1RWUWl1RDRxL1JvOStQMUNXRVhWejFJNXN3RGYrTEd0RlovVTNtTGFmTktReGl4ZElLWkJGcmk0cmRsVHJPb3NTb1pub3hEQ0JIR24iLCJtYWMiOiJkYWRmNTg0Yzc0OTdlNTgwYTA2ZTA4NWFlNDkzMDkxMTM2NGEzMmU5MjY3NWExMWEyZGRhZGFhNWUxNzhlMDY1IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:05:56 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjVWdTF6OHoyVkthc2M1MUJGOXp3Unc9PSIsInZhbHVlIjoiSU1Lc2t6N2VDeG9IS3VlR0EwMndySml0RWhTL0VhNi9NZjdQcm1lOExlQU1xZHlCSGdjMkhITXRUNk12S2IxaVU4ZEJEY1Rpb3hqVDE0dC9zM08zVFJOcGVDUVpiSi95ODJKNkFJeDUvWkZ3ZUc2eWNnRmNRSWtRZEJUT0N5aTJJdVVicEEwSk85c1NRSmRwUmdpNm96b3ZjR05wRTB0dG84WmhFOVBIMEF5cGlEbWRnNGY2NzUxRFhxeFVRQmtYZzNJWmZpZkFjNVV0cjJhZFBCOEhEeVM1TVB0dEtVdHc1YVAzNDUzblI2clB0SzB3dDIrakwxcEhmWXhyZkpRSnI3YnhJbjZOMVpkY1Z5MkIydFlpdkptYzJPck5jbTlxemluczhWeHB3djQwemJPcCtvdHI3N29aMzFhV0hhNFdTdW9PRit5M3d0RThnRStYOGpFMGxYYVJ5SFZQYnVBSHlSR3pzaHNycW1LK2hQc0RaUURtdHNHNnVpT3dvV1dWNEZLckhML2Q2RE55MGxoRC8rWGFaMk52SC9BWm1WTnFBbGJDUElseENtYUdubEE2UHAzNUx2UVhkcHlDdDlqQkYzeVZ4OFp6a0tnQS9GOXpkclhmNlM3WFZCeGhneXlrTUx1a1NjY1N3S3JCUFVVT0xaT1gyUEpUSjBLRHpwM3IiLCJtYWMiOiI1N2Q5ZjFkYzliN2M5ZTg5OTA3YzdjMjk3ZTMxMzI5MTlkNjVmNDE2ZmYzMTQyMDgwYTFiYjhmODJiZjE5ZTEwIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:05:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlRYcGtxK2lpUFdlYkgrczJFRDZBTUE9PSIsInZhbHVlIjoiMFZxRndJRTZxZURHWmYwK3AxVVV6QXVFdDVsU3FuWmxzM0x5K050NVFUMlB0Nmw2STNqZzlkMmxFblQweC9OYW16TXUvZlkyU3p4Skx0ZzVBTnNDM1JiMjdaTEJQMThtaFdQWFFnVkhtaEtpTHRNYzNQdlpRYk4wRzZtN2hUTENYWlR6S1JIayswWkZYeDUrQURCb25McTZxV2FlY1dWZDV4U2dEV0p5Vm9NcFlncmRxV3pSbDRIV0diSjM3YzJBZmJSKzZKV2ZmdVExRmJGNTE5Y3Z5T1NYYlo2NXh2Y0VNdkJXdWhmQ0FhNWtJRjFUYjhlTUdwU1pueTZiRVRiUUdDbFoxNmZITEtucGFtelJubFpCQ0toaitBM2ljQ2xTcnhpYVd5b3JkNG1STFhVRkNaV3JaSGNuVWtiZWtDSW9GQ2I2WUNITTUxQUxhWjMyZk1udFhGdUtWOUxMcnB1MHRDb2hMOTBXOEhSL29SRlI2OFhSUVdrMFordkJaVjRWc2xRdjF1RzJJMjdyM0MweE9xRXowN0pYK3RBcGJ1MVo4VkJYUTNGZldTSUd5S3YzN2tJb1RWUWl1RDRxL1JvOStQMUNXRVhWejFJNXN3RGYrTEd0RlovVTNtTGFmTktReGl4ZElLWkJGcmk0cmRsVHJPb3NTb1pub3hEQ0JIR24iLCJtYWMiOiJkYWRmNTg0Yzc0OTdlNTgwYTA2ZTA4NWFlNDkzMDkxMTM2NGEzMmU5MjY3NWExMWEyZGRhZGFhNWUxNzhlMDY1IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:05:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-490084667\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-557103626 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-557103626\", {\"maxDepth\":0})</script>\n"}}