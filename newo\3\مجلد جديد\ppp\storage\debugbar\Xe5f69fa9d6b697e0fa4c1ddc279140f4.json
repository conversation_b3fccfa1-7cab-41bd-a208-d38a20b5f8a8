{"__meta": {"id": "Xe5f69fa9d6b697e0fa4c1ddc279140f4", "datetime": "2025-06-17 14:57:11", "utime": **********.222096, "method": "GET", "uri": "/customer/check/delivery?customer_id=6", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750172230.653375, "end": **********.222124, "duration": 0.568749189376831, "duration_str": "569ms", "measures": [{"label": "Booting", "start": 1750172230.653375, "relative_start": 0, "end": **********.145957, "relative_end": **********.145957, "duration": 0.4925820827484131, "duration_str": "493ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.14597, "relative_start": 0.49259519577026367, "end": **********.222127, "relative_end": 2.86102294921875e-06, "duration": 0.0761568546295166, "duration_str": "76.16ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44962072, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01568, "accumulated_duration_str": "15.68ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.1881232, "duration": 0.01431, "duration_str": "14.31ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 91.263}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2095292, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 91.263, "width_percent": 8.737}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-197951598 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-197951598\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-94029444 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-94029444\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-860019463 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-860019463\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1098654674 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InlxZm9wWThxaXd0cEIzZ2tUQ2xNQlE9PSIsInZhbHVlIjoiNHFJc2o4eHFFTHYxVmEzOHcxdFN6d0p0NVZSZWt4dkhsRkJ1MFkxMjJGU1lKOTh4S3ZuR3J1bVo2a1FYdllucHorQ3ZZRkoxaEpKZ21HaWRtWHdzUXVNNEpqSVo2OWRQSXc5UzdsdWZJWldFeHFFYjhsSXNTV2o4VU9qS3RaTEtyakpidkVVQ0JWMFFVR1BicnRoUzdCS2M4dzlQVDRFKzlkejFlVjVRT2Y3Vy9xNThrb3ZkNVRjT1NyKzduMTdWd0NqYVptNnRQbU92a2JJSUUzVWpNUm9nNGdWMTV3NGt1a1pZMGJ1dE5LOVZsd2F1bFJTL1c0VUtPeTdSd2YyZitUS3hFQTZNQ1pUMzlzaVZQUjdHUVZSK21KN2E1YmtRTW5BLzJMWW94bFI1V00rWlJyVTh0czFoQm5OaTZ0cEZObVlhRVdmekcvQnV1NkEzRUtVdUExdkRtd3pVQ0ZkR3hlc1FPZk5CK3lqWVNFUHVLYXZheDRTYzk1YkszK3F5NEZHQWoxQ3Q1VS9SbmVuVjFhcXhkTW5Ec3pLM2Nwbm81OHJ6d0xkNmNOYnFmUlRNTHRUNmo1S1RYNktQRzd3RHhqQzJ1Zk5pVjNsRm9PN2xkWThKdDFMcVNjcU1yU0dzODNxdVZWemJTcWNIYWZuZ1RSUHlITWRCU001ZXMzQTYiLCJtYWMiOiI4NDRhYWRiYmI2MDMxYmQyNmIzYzk1NTVlMmUyM2M4MmYyNmNlZjcyZTQ2MDg5NmY2NjU2Mzk0OTNjZmY3ZDNiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImJuZ1VqOXdTdTlQSHdSc3NFL05ZYlE9PSIsInZhbHVlIjoiU24xTFBpUHJRaWRmTHUvVmxQUnBHSGhJOFliTGR0OVZaWXRxMDlQc2dhM1VCSUlyT2UxdXc0T2dQZWFBYkE2aEplNkphWmdLeUtMQUpBREI2ZTR0dzhESHFDVkhYbFlFcTN5SXoxcEYrbnJ3VHZ1dVZobVRJUjJ3aHhsWjRNRy9vQzNDYi9wVC9IM0loN2NJMjNQbVIxUlhDZUhZdVRwdzF6a3huMzVhY1VBdFVqUGRldC9KdUpBaEQ0bHFQN094Q3B2ZmhhbTU3Q2JjQ2dkTW5kT1lCKzJWUmNjTTd2NE1DdWkvTFpJTXVHRXlDK25uZCtZUlpWOEVLUm9zcWpmMnhwSGFIdEs5NzJWS01ReEhCZVFOUUxZZ01NeitFcnpUZFdpSU8wcmVrcDA4V1JTZ29RV0dwYnhMZFRvZ1FVQVJYNnMwSnYxSU93YUk0ZEZwSmdkbWxaRHFnOU8vZUlsWmE5QkJtdlh0aFVNVnNCVGJtS2Y4ZXRVeStIOHU4TWViZWQ2b1BHd2hYdmpzY0llanRuZE9OVTMrdS93OU1OZnNzdlV1bUtSTGdqSjZST0NXYTBIeDhjaWMzVXBhUWpuOWdVYXpMTFFlMmlzc0lXbnEyQlZIYUpGQWtnaVNKYUJ0VVZGbnl0T1BjUDllSEk0SzIzaUlMVzVoQldtbkhnYVAiLCJtYWMiOiI0ODNjZTZkY2ZjNjM1MjBmMTU0NzgwZTMzMWEzNTU5NmY1NzdmYjFkODJmOGRlOTVjZTY0MWNiMzk3YzgxM2YwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1098654674\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-938753916 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-938753916\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-373214923 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:57:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ijd1TGxJclJVakI5d1JlTGhQaDdIR1E9PSIsInZhbHVlIjoibnkzdmxZbkxDRU05OXlaR1paanU1UDVpVnhvMXhOdGpoRnVYVzQ0ZWZOckNkSi9lOHZ6WEpvZmtsOXpSa0tCYlROZUhlRXhCeHNTMit1djBOZGg1UTBtMmI5aGF6QThuR3hkaFFLR000TitDV0pUNHlwcXZPZDh5NGxRQjdxRFhoQ1VuNzYvRE1DYkpEcU44NDdhYUJlbGoyUkFxdGh0Rm5OUk14QkNKZm1JTmpvYzg3QTVsVnpnY3FIQzJIVUxsOTBiWWYvd0lyeEpNVG4zNXRGcVFOYkxZQXcxYlV2bktQNHNGQlE5eXdqdHRkbmhORkU1ZXRHdzVTcWprZmxacDE1MS9USENsS1JEWE5SOG5McSszeUo1ejc1SnpPZTdSTFRxZVJqTW45bFZyeHhZRU9id3d3WExGTHhhMlp2UmlmMC96eWVGK3Y3YURtOGZ6aGh4eGgzKy95cFpxVkQ1bUNqeVcwSHh0VGtVK2lhS3ZqZXM2TjdOMGNIZGs0cmJmUGVjT2U3UnorOTgwMURBeGZoY3dHSnV2V2YxbmUvMklvd0JyYm84Qmc3Kzd4TTFiLzRtVUg5eGVZZ3VlY2RUUjFUVUxlY0l6aFR0VVplNmhvN0hGdmM5ckdseDZXRm5zL0p6eXFkZDIxMjJaT1pOamtoMzJpdW1HZndObHNrT04iLCJtYWMiOiI3OGZiN2I5ZjI2YjI1MmZmZDJiMzc5N2RkMzhiOGJlM2FkNDBjYjM5NWEyMGNjYjE3MTllZTQ4Y2Q5NzY1NDEzIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:57:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImJ6eThTdUFmT3l3YjI1RERZU09QZWc9PSIsInZhbHVlIjoiNVhtNEZXeURPQVhlRUVCd1JjaENqN3E3SXBIK3VOMjAwWGU3TE1sSlFSdkE1OWJUZmFsanE2SkEyWitQemIwVWZOc2JOTGY5NTI1ZnI3eG12ZlBWMWJ4RlIzQURqZ3oyUFQ3ellQSUg5KzhlenBoLzdFODlGdG1XWU1BOW5RTGpHdXJFNHcvY1FnWmxWQ1JQalVIU214aEJtSXhDeTNwcjNXTURWZlJJaFR6YVhIczQvNkZqQ0Z0OTE1aDQwQVNSb3U1RXp5MHJsZFpWUWxPa2hvRkwrKzNwaWNRVUduZ25ydWtXUTMrTmVYRy9pOG16SVZvVFVxZ0JHQnVSV3c2NmFJQTJGZkY2NzdyNVhiZEFITHdLSUlORHhkaTdTTWNXREVzc25YaHRQckJwNWRvUitFb1JBSzNjTWRuRlVOL0VxYldQUVZRbXludmtKWG04bFcvN2kwU2dJNnZGMllLN1VQWUg5NU1RRDdmV2UrYUl6SVNEbzdNUHpINk1QRVVqL3dZb0hZTXhscTRkd2tIYWZYcG1PTHMxVW9KUldHOThhRmc2Y0pGakIzUDIxWVA3anVIN2h1dFZ2a0xkZGc3ZU9jVldsZ29hV0FmYkhDeU9CdEM4Rk1ham8xSFF6bHZqMGxlNGRDdUdpWXVGZVAvZ2lGa3VkS3BzaDhwWnd4TlgiLCJtYWMiOiIyMzkyNzI5MWRjYTc5YmY2MDBmZWNiMjI1NGE2ZDdjY2JjNjNmOTQzMjUxNjYyMTNlNzAwYTg4NzA5OWFjMWU2IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:57:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ijd1TGxJclJVakI5d1JlTGhQaDdIR1E9PSIsInZhbHVlIjoibnkzdmxZbkxDRU05OXlaR1paanU1UDVpVnhvMXhOdGpoRnVYVzQ0ZWZOckNkSi9lOHZ6WEpvZmtsOXpSa0tCYlROZUhlRXhCeHNTMit1djBOZGg1UTBtMmI5aGF6QThuR3hkaFFLR000TitDV0pUNHlwcXZPZDh5NGxRQjdxRFhoQ1VuNzYvRE1DYkpEcU44NDdhYUJlbGoyUkFxdGh0Rm5OUk14QkNKZm1JTmpvYzg3QTVsVnpnY3FIQzJIVUxsOTBiWWYvd0lyeEpNVG4zNXRGcVFOYkxZQXcxYlV2bktQNHNGQlE5eXdqdHRkbmhORkU1ZXRHdzVTcWprZmxacDE1MS9USENsS1JEWE5SOG5McSszeUo1ejc1SnpPZTdSTFRxZVJqTW45bFZyeHhZRU9id3d3WExGTHhhMlp2UmlmMC96eWVGK3Y3YURtOGZ6aGh4eGgzKy95cFpxVkQ1bUNqeVcwSHh0VGtVK2lhS3ZqZXM2TjdOMGNIZGs0cmJmUGVjT2U3UnorOTgwMURBeGZoY3dHSnV2V2YxbmUvMklvd0JyYm84Qmc3Kzd4TTFiLzRtVUg5eGVZZ3VlY2RUUjFUVUxlY0l6aFR0VVplNmhvN0hGdmM5ckdseDZXRm5zL0p6eXFkZDIxMjJaT1pOamtoMzJpdW1HZndObHNrT04iLCJtYWMiOiI3OGZiN2I5ZjI2YjI1MmZmZDJiMzc5N2RkMzhiOGJlM2FkNDBjYjM5NWEyMGNjYjE3MTllZTQ4Y2Q5NzY1NDEzIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:57:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImJ6eThTdUFmT3l3YjI1RERZU09QZWc9PSIsInZhbHVlIjoiNVhtNEZXeURPQVhlRUVCd1JjaENqN3E3SXBIK3VOMjAwWGU3TE1sSlFSdkE1OWJUZmFsanE2SkEyWitQemIwVWZOc2JOTGY5NTI1ZnI3eG12ZlBWMWJ4RlIzQURqZ3oyUFQ3ellQSUg5KzhlenBoLzdFODlGdG1XWU1BOW5RTGpHdXJFNHcvY1FnWmxWQ1JQalVIU214aEJtSXhDeTNwcjNXTURWZlJJaFR6YVhIczQvNkZqQ0Z0OTE1aDQwQVNSb3U1RXp5MHJsZFpWUWxPa2hvRkwrKzNwaWNRVUduZ25ydWtXUTMrTmVYRy9pOG16SVZvVFVxZ0JHQnVSV3c2NmFJQTJGZkY2NzdyNVhiZEFITHdLSUlORHhkaTdTTWNXREVzc25YaHRQckJwNWRvUitFb1JBSzNjTWRuRlVOL0VxYldQUVZRbXludmtKWG04bFcvN2kwU2dJNnZGMllLN1VQWUg5NU1RRDdmV2UrYUl6SVNEbzdNUHpINk1QRVVqL3dZb0hZTXhscTRkd2tIYWZYcG1PTHMxVW9KUldHOThhRmc2Y0pGakIzUDIxWVA3anVIN2h1dFZ2a0xkZGc3ZU9jVldsZ29hV0FmYkhDeU9CdEM4Rk1ham8xSFF6bHZqMGxlNGRDdUdpWXVGZVAvZ2lGa3VkS3BzaDhwWnd4TlgiLCJtYWMiOiIyMzkyNzI5MWRjYTc5YmY2MDBmZWNiMjI1NGE2ZDdjY2JjNjNmOTQzMjUxNjYyMTNlNzAwYTg4NzA5OWFjMWU2IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:57:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-373214923\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-247154418 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-247154418\", {\"maxDepth\":0})</script>\n"}}