{"__meta": {"id": "X5abdf95630cdec6c7d27e9f30fb7fc83", "datetime": "2025-06-17 14:39:21", "utime": 1750171161.081498, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750171159.825034, "end": 1750171161.081532, "duration": 1.256498098373413, "duration_str": "1.26s", "measures": [{"label": "Booting", "start": 1750171159.825034, "relative_start": 0, "end": **********.906957, "relative_end": **********.906957, "duration": 1.081923007965088, "duration_str": "1.08s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.906979, "relative_start": 1.0819451808929443, "end": 1750171161.081535, "relative_end": 3.0994415283203125e-06, "duration": 0.17455601692199707, "duration_str": "175ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46118336, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02967, "accumulated_duration_str": "29.67ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.987638, "duration": 0.02748, "duration_str": "27.48ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 92.619}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": 1750171161.040466, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 92.619, "width_percent": 3}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1750171161.0594819, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 95.618, "width_percent": 4.382}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6", "_previous": "array:1 [\n  \"url\" => \"http://localhost/inventory-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-188165352 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-188165352\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2023435376 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2023435376\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-934982449 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-934982449\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1552330521 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=3pzjoc%7C1750171148552%7C7%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlpYV3hOcmY3a2JJQjNQcnlqeUZmVWc9PSIsInZhbHVlIjoiQ0dZSXMxUDJXYUU1dklDaVpEVjJpdFhIQXlxODBramJFSjAzdDMxZzZZeUxNeFdEK0x2NFRlT3R2dHY4SGx5dGpCZ3U5cUM4TmxyODFUTDRTUnRhSzUzeFdLVmN4ZXREU1M3TFV2VjlsMGxpaXNoSlZLd01vaGNWZzc5eitMQ1FiWTluREJEQStEdGY1cVpSYUhya05KaHBzYmtseUVBdXpkTGFMc1Q2YkNPZVdxbHlOdnMxT1NoYUNJU0ZPOVJvb1FNVHpyMkpsNkpNZHNuci9sOHAxdXU5UFVBaXBSaWZ1VFFkWHJvY3lpbU93dVBDNFYydTRvdmhCdmJmZGNJUWl4M0w5NzZ2aENkQzcyWDVBY25OTmwwdExOdDFMVmRtSjlpcDRGMGtPQUJzWHRwYTFjU3QxbEhpM3ZHcitxWGptYmczb3YwWS9MT2I4UTJxaTdmMk5WM0xVTzhHcmdZNlpZcmxaSjNudWpDazBScDFCY0RreW1xZ3YzZjkzQzM4RU0zYklzZFBLbVN6Z2tlVmxhejlDRWNVUUxtN29OTXJKM2RaeWllTkRtbUhTNlZrTHpFN2hQYk05TWtiK0o4Q1I0UG5hYUI2bUNFQ2NvNER5Z1FqT2F4OGhhdWE4clNoWGNUZ0lMc3JEVXVrOXZkUVhTbldTL3dpcHNUVFRHSW0iLCJtYWMiOiI0ZGRkOTVkYmRjZTM4ODgwNWQ2NDlmNWRiYzA2NDY1ZDIzYzIzN2Q5OGMxOTc2MjM0YmY0M2FjYTI4NmI4ZTMyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImNERERaajZoOE5wOE1mSU5QKzRxVmc9PSIsInZhbHVlIjoic3VLcFQydHBJTnNBUlBFOHY5QnJ2UmRQRENDTnhtUlRURzYzMGtDVHM2QVltQmdXVFc0a21RSDdtMld0eEJqeldqUkF0YnpOOEgyRE5RZVVSMjlxVTJ2YTJPUTlxdzUvV2Jyb3VPektwdmtTbDdlNkdqT1hCZlBSWXdqZ3BlNnYrNjV1QUtDMlhsZUMxYXhXUUdtc0NvaTh0M0h0NFBBR2tOcWVEMUFrWHJyMDIwSVZLTlM5MzlSVlNmbUF6clhQTkdRMW5PdzdTblJ0MzhhcjJRVktQTzZJUWpjdmNnWkZGTkgvYjZUeGFWZ2RLM3JXK0Y3QWhYbkNSRk0wdHI0bTRxdEVyME1aWFBlRDViVXRNZ2lra0YzWCs5K0VTSnNkTkREM3dBRXZKWHhra0RqUFFKYVMyRmtvTUd5WXpZVkV5YlNkeGdNdFp5QWxkWjFxcmtZaDd2ZVkxNlJ3Y2E2R1piYTJKcGFVU2NKZVNvWnJFZ1VJR0ZXZkJ6K0pPdmxxejUxcHZLaTExSlBMN0orWXlRSmFmZW1mcEdpQ3dIbkNCVkYrc2xIQTBOUUdHUi9BcEx0aE55enNkdlJPT0QvZmtodFVKUGlWZysyd091a0FTMGppbW5uL21SKzdaL01YTUcwRnowMUNqQWViS3o2WE5pSTVuQzNCOGY1RmtpODIiLCJtYWMiOiI5MzUwOWFjODUwMTc0OTk4MzcxMDRiMWZlZjI0NTc4M2JjZDA4NjI4YzMyNDEyNjgwOTg1NGMxMjdhMDdmMmQ2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1552330521\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1695439349 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HxnOnSuhAAPTfN2VS4q58slgJoytwtHYqaX0oLoW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1695439349\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:39:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkdNZVQwak9jMkN1T1BFd29nVlpFdFE9PSIsInZhbHVlIjoiRno3bGhDdGdTVnQ2YmF5UExSSU51aXJmYklDQlpSUy8zY3l2TGZyazh0Ty9pc0hpVmVCK0dqNnA5ODJ4ZkppMnM0cTB2KzBBaEdnU2NtcmpWTkZ4Y2RvNUNybUdsc210QlhyMkFKOVRLQ2J6NFowZ0dRbzN2akJlZ0lhVlA1am1obWFBb0JmQmFXT0ZHMnRZWU8yT1l2SkduYWFVWnNpK3doS2xOdWZ1VmJuVEpiV0JheWdFWm02Ky95bWIvVEZickV5TVBTMXBlaHFWejFMMUgxZWRZaFc2enlRcFhYUEQ4UTJBazZDek5JS2tXbVVkeDlYRzlkdDduWHNTZ3dXNSt1Yk94aEVmOCsyWTdXUDR3SWxTc0trLzdGQmxKdlV0Y3JaK2pKeW5aZU8yWU02WXFIWFFJWTgrUjExZ1ZlSWlCS1NYWHhVTnJvSlFPWHBhYnlYYzZINlAwYlBpd1JJbksxSnU2ekd0aFdnOTRBUkFHTStmQkp3OElEamZZVDNwL0NQckxub1kvQmcrRDJWQ1JwTm9mMzJCMzc0Z0luckEwbkY0MEszNkZrNG9CL0V0VURJcHZid3FvdHkxb3ErRW5LL0FzQWRqQ0pDTEdpMEdHNVdqcG45RmhUbnNLeXpKdzFoeTlTcTFQWXV1aUVpR3ZxQTBoQnFibDhGY2tlbkUiLCJtYWMiOiIxNWY1MGIyNDIxMDdhODY0YTE5MmE5OWJiNjNjOGZjOGJkZjUzZDAwNWU3YzA2MjJjOGY0ZmU3MmU2MmYxMjBiIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:39:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkRpOGM4R054Y0tvZUQrNDJ6Z0trRHc9PSIsInZhbHVlIjoiUW1TMFYxL3ZWNEVLZmlTQ3EyeDE1U1ZuUFduakk2WTNEYzBFTE5Da1dyRTZkcUNzNmk2SUdiVzl6Nkh6R2lIbE9idS9KZDFvOVpwcmxUanVsTlRwWWZ4MmxpeDZqdkJqMmpSSTJnZ2NzVE15TUloaXF3VysyMHBmck5rRU1oT2tXZElZUDBOSlVzNFB3SkxTdTg2MGRzZndndzV0dTJVSHkwelJTNmxxZmxZK1BNTGh5a3NEWmtIL21xVFZBWE4yd1ppa1Q1QjhqVGdOUWE1YTdRYUQ1SHRQVTBGZXJwS21OVlJtMGRYS1h1dDhKU0E3ZVRleXpDYkRWMURXUk9tY3NLU2lNY2MrVU1CUnZQMFlJR1V0T0hFL2MyT3g1NXJjaSs1Ymp5dStxc2Jrbmo0dlFtckgyWnNLZFRmR3ZXdVdJRW9jWUhFWDZpV1N5T2xxMFpsSjVna1Fma3dLMjFaUEtjNmdPZk5FN0pxeUtSbDREVDIxYWV2RVBkeEh4MzI3L1ZHdEZCamJzckNjNFRCWFhGUjVxOWg4MUp1OU1wYUJzWUFiWHRGM0NrK3J0NXRjVTc0NVgwVktldTVlSzhCYWNQaGRLaXRSeVIvKzQwSGxTVFRiOXlldWxkK2FralZQOUROeWhJcVk5ZXV4TElvdHM1TzhwNURrQy9sUTNoL0UiLCJtYWMiOiJmOTZhZjZjMjYyN2EyMmUxZTAwNGU4ZjFlYjQ1ZDZhNDMwYzgxYzg1NDM4ZWQ4NGUyZTFiY2QyOWI3NzNkZDVkIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:39:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkdNZVQwak9jMkN1T1BFd29nVlpFdFE9PSIsInZhbHVlIjoiRno3bGhDdGdTVnQ2YmF5UExSSU51aXJmYklDQlpSUy8zY3l2TGZyazh0Ty9pc0hpVmVCK0dqNnA5ODJ4ZkppMnM0cTB2KzBBaEdnU2NtcmpWTkZ4Y2RvNUNybUdsc210QlhyMkFKOVRLQ2J6NFowZ0dRbzN2akJlZ0lhVlA1am1obWFBb0JmQmFXT0ZHMnRZWU8yT1l2SkduYWFVWnNpK3doS2xOdWZ1VmJuVEpiV0JheWdFWm02Ky95bWIvVEZickV5TVBTMXBlaHFWejFMMUgxZWRZaFc2enlRcFhYUEQ4UTJBazZDek5JS2tXbVVkeDlYRzlkdDduWHNTZ3dXNSt1Yk94aEVmOCsyWTdXUDR3SWxTc0trLzdGQmxKdlV0Y3JaK2pKeW5aZU8yWU02WXFIWFFJWTgrUjExZ1ZlSWlCS1NYWHhVTnJvSlFPWHBhYnlYYzZINlAwYlBpd1JJbksxSnU2ekd0aFdnOTRBUkFHTStmQkp3OElEamZZVDNwL0NQckxub1kvQmcrRDJWQ1JwTm9mMzJCMzc0Z0luckEwbkY0MEszNkZrNG9CL0V0VURJcHZid3FvdHkxb3ErRW5LL0FzQWRqQ0pDTEdpMEdHNVdqcG45RmhUbnNLeXpKdzFoeTlTcTFQWXV1aUVpR3ZxQTBoQnFibDhGY2tlbkUiLCJtYWMiOiIxNWY1MGIyNDIxMDdhODY0YTE5MmE5OWJiNjNjOGZjOGJkZjUzZDAwNWU3YzA2MjJjOGY0ZmU3MmU2MmYxMjBiIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:39:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkRpOGM4R054Y0tvZUQrNDJ6Z0trRHc9PSIsInZhbHVlIjoiUW1TMFYxL3ZWNEVLZmlTQ3EyeDE1U1ZuUFduakk2WTNEYzBFTE5Da1dyRTZkcUNzNmk2SUdiVzl6Nkh6R2lIbE9idS9KZDFvOVpwcmxUanVsTlRwWWZ4MmxpeDZqdkJqMmpSSTJnZ2NzVE15TUloaXF3VysyMHBmck5rRU1oT2tXZElZUDBOSlVzNFB3SkxTdTg2MGRzZndndzV0dTJVSHkwelJTNmxxZmxZK1BNTGh5a3NEWmtIL21xVFZBWE4yd1ppa1Q1QjhqVGdOUWE1YTdRYUQ1SHRQVTBGZXJwS21OVlJtMGRYS1h1dDhKU0E3ZVRleXpDYkRWMURXUk9tY3NLU2lNY2MrVU1CUnZQMFlJR1V0T0hFL2MyT3g1NXJjaSs1Ymp5dStxc2Jrbmo0dlFtckgyWnNLZFRmR3ZXdVdJRW9jWUhFWDZpV1N5T2xxMFpsSjVna1Fma3dLMjFaUEtjNmdPZk5FN0pxeUtSbDREVDIxYWV2RVBkeEh4MzI3L1ZHdEZCamJzckNjNFRCWFhGUjVxOWg4MUp1OU1wYUJzWUFiWHRGM0NrK3J0NXRjVTc0NVgwVktldTVlSzhCYWNQaGRLaXRSeVIvKzQwSGxTVFRiOXlldWxkK2FralZQOUROeWhJcVk5ZXV4TElvdHM1TzhwNURrQy9sUTNoL0UiLCJtYWMiOiJmOTZhZjZjMjYyN2EyMmUxZTAwNGU4ZjFlYjQ1ZDZhNDMwYzgxYzg1NDM4ZWQ4NGUyZTFiY2QyOWI3NzNkZDVkIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:39:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-853427523 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-853427523\", {\"maxDepth\":0})</script>\n"}}