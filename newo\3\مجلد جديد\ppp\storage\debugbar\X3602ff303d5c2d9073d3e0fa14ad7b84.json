{"__meta": {"id": "X3602ff303d5c2d9073d3e0fa14ad7b84", "datetime": "2025-06-17 14:51:10", "utime": **********.927323, "method": "POST", "uri": "/inventory-management/check-zero-quantities", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.319634, "end": **********.927344, "duration": 0.6077101230621338, "duration_str": "608ms", "measures": [{"label": "Booting", "start": **********.319634, "relative_start": 0, "end": **********.797402, "relative_end": **********.797402, "duration": 0.4777679443359375, "duration_str": "478ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.797414, "relative_start": 0.4777801036834717, "end": **********.927347, "relative_end": 2.86102294921875e-06, "duration": 0.12993288040161133, "duration_str": "130ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46558624, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST inventory-management/check-zero-quantities", "middleware": "web, auth, XSS", "controller": "App\\Http\\Controllers\\InventoryManagementController@checkZeroQuantities", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "inventory.check.zero.quantities", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=856\" onclick=\"\">app/Http/Controllers/InventoryManagementController.php:856-917</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.044840000000000005, "accumulated_duration_str": "44.84ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.840099, "duration": 0.03459, "duration_str": "34.59ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 77.141}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.88913, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 77.141, "width_percent": 2.319}, {"sql": "select * from `product_services` where `created_by` = 15 and `type` = 'product'", "type": "query", "params": [], "bindings": ["15", "product"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 871}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8942888, "duration": 0.0046500000000000005, "duration_str": "4.65ms", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:871", "source": "app/Http/Controllers/InventoryManagementController.php:871", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=871", "ajax": false, "filename": "InventoryManagementController.php", "line": "871"}, "connection": "ty", "start_percent": 79.46, "width_percent": 10.37}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '7' and `product_id` = 3 order by `updated_at` desc limit 1", "type": "query", "params": [], "bindings": ["7", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 881}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9033759, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:881", "source": "app/Http/Controllers/InventoryManagementController.php:881", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=881", "ajax": false, "filename": "InventoryManagementController.php", "line": "881"}, "connection": "ty", "start_percent": 89.831, "width_percent": 3.657}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '7' and `product_id` = 5 order by `updated_at` desc limit 1", "type": "query", "params": [], "bindings": ["7", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 881}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.908452, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:881", "source": "app/Http/Controllers/InventoryManagementController.php:881", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=881", "ajax": false, "filename": "InventoryManagementController.php", "line": "881"}, "connection": "ty", "start_percent": 93.488, "width_percent": 2.721}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '7' and `product_id` = 6 order by `updated_at` desc limit 1", "type": "query", "params": [], "bindings": ["7", "6"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 881}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9129632, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:881", "source": "app/Http/Controllers/InventoryManagementController.php:881", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=881", "ajax": false, "filename": "InventoryManagementController.php", "line": "881"}, "connection": "ty", "start_percent": 96.209, "width_percent": 2.275}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '7' and `product_id` = 7 order by `updated_at` desc limit 1", "type": "query", "params": [], "bindings": ["7", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 881}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9169931, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:881", "source": "app/Http/Controllers/InventoryManagementController.php:881", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=881", "ajax": false, "filename": "InventoryManagementController.php", "line": "881"}, "connection": "ty", "start_percent": 98.483, "width_percent": 1.517}]}, "models": {"data": {"App\\Models\\ProductService": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\WarehouseProduct": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FWarehouseProduct.php&line=1", "ajax": false, "filename": "WarehouseProduct.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 9, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6", "_previous": "array:1 [\n  \"url\" => \"http://localhost/inventory-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/inventory-management/check-zero-quantities", "status_code": "<pre class=sf-dump id=sf-dump-66751593 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-66751593\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1061653615 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1061653615\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1108971211 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1108971211\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-179515439 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">62</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2032 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; PHPSESSID=o1mek9etnn9ejsik46u9hjcosg; _clsk=17t60l2%7C1750171861220%7C7%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlBSSWNGdTJCamNUTW9pOUd4MXFGcWc9PSIsInZhbHVlIjoiTmcwYWw0bVNodFluV2diUWp2NktZcVA3dC92azlQdG9hRWsvdTd2NFdPanJkVWxhRDJsWFROcGZYdXFGNktNck1IMXFDRUpOVHlkU2VtcEtDK2xFMFk3WGcvaDZGU3RaN2VoWXZXOWpNVUJ0U3k5bStBMnY1TGxhQ1AwUHRqbklZWjZ2emlpOTNmTzdWTTRrRm5vdUhkd0tjOVlLVmxwSGhqTmlwclZNTXpLVVdVbnI3bWhZdEtnRkZQdW1vVlo5OFNzY0VNcXd1cmVKMGl2MHAyVUpLa1owa240dXM0ODc5RXFnRVI4QWRzZm9YV3dLelFpOTdOY2gzVGRia1lURW5VNlpBZkxGNTVsMk51TnRqTEcrRjdUN2JVMDl4MWtOdVBPampXUDNOaU1HV1JPZUV5MmM0WTludDhnUlNHNlo1WkUwcmxFRWRwS2ZEK3FDYmwxb1N5ZkNPZ0U4OU1qT2Y2OHZFUTl1cldsa1ZtaVRPM0d1Wi9RekY0MnhpcU1oM08vMUtFYkJtKzVDblpDeE4vTXY3NVhEeC9xdUkxQzJDaGNuOVRJWE10L2V5NDFXSDUwMmcwZnpwVkMxWnIwb1F0dU5BTk5MdGRBMjVJVDZJZzU1Q1hUQU4xeUducURVMkF6NFNoa0cxM0NZanpxOXQ0WU12TkNpaWp4bmptZEciLCJtYWMiOiJjZWU4NjgyMTdhNzM0ZGI0YTljNTBkNTg1ZTY5ZmUzMDAwYzRmNGZkM2RhYjU0NmJmOWI2MzZiOGZmOGQyYzYzIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImphREo5OUtiRFpzcGQ2VE0wM2lBa0E9PSIsInZhbHVlIjoiaFJRMDlzZkJJVGkrNDVUMUVndUl5enFiYjBtN203aEhlRG1MY3RVV1FMSHNZak5iM09YSGFURFdicXp4Nk1kRTVKbi9kd0lFbisxWE12WXlJdjFOU3pYNDBwZDRPY29wSVdQaXlLVmNnVVFTSDJtWFRjZlJjSXJkUjNGUEF6VTJqUEFtUER3czNCQUhnYXhHUDcxc3NVdkhGck5sTHN5dnNKSUlyT3k3ekdUdWdRdWhRT0ZPZ05iT3VJS05aRFRuNE1BaUx6ZXJVQnNvYjZxV1R1ZXd2TGphU2FwR0JmakJjQUdMZzVYbkd3QXNTZGdKamttbTk2eFI1dk5FTHI3aWphWThtL0hHZm5aM0NYSnY0STBkWDM1WnZRWDhpYW8rR0VDdkh4UHBLMXM4RGNFdVVDMEZPdTM1czQ5WUNtRlNqeDhidnNHNnZ5SUhsMkdKZ1hwQVJrcGJVaFdzOUEvMkppQWUwQS93N2tqRzBRUTB0VGFiVUVlelRYZkUyckNFdTFYcjNEZFlPK2kvVG1Wa1VSc1RKYmVmYVJxTWtRQ1k2V2hmdzF2bkpobmxOQ1Q5VkhqazAzUHBrRnMwUVdFQXNvMEJHZDZMVWNmd0l2V0ErbzFlSDFROTFPNlFDbWp2a2ZLMTc4ZEE3YXB0Tmo0MlpSdkFPa0tZaE8zUkVNYUciLCJtYWMiOiJjNjlkZWU3Y2FmMDIzNTEzZTFlMGZjMjdlYjIxMWI4Y2U4MTM1ZWU5OGZiOWM0MjY2ZWViOTk5ZmFlYjIwZjQ5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-179515439\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1971870462 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>PHPSESSID</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HxnOnSuhAAPTfN2VS4q58slgJoytwtHYqaX0oLoW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1971870462\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2120050423 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:51:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkQ2MURLV2I1K25obnVpZmYycFRZWmc9PSIsInZhbHVlIjoibUx0UkplTysrVW5Pd0pUTTluNEZ1MHZ2cjh6eDVUSXFEUHQra211UndCOU5YU2xIRW96ZGdaMDJqN3M5bkM3NVNObXNHemg4NHJkK2pQT0lyQWQ2Rm1kQnkrWFVML3hXZGU4ZHo3Z29qd0ZYMDZ6b1JZVERIcjFGUlBBeFUzMVRhRytsUTRNZDhFK3FRSnBiMGpJMXZuSXN2Y3V3TS9jbTlGeW9UMm8xam45R3dmekwrWEV6MGlBZjlPa1EzSUhFL0I5NmxQTjdZeTJRajVRYVYrbithVUZVYUE2NWF5ZkJ0Uk5UWnhIUTBUY2pqRWpwVXQ0Q1dsaDJLL3NsdWVEL0N0K2E5WEpTQ01BaGtacHRPNVJWN1hvWkRUbGF0MCtBWVNUZWFTMnVFSCtiWnJibW40MVBQeDVCMmlnV3FtM3N0dWdzTmxyQndFRzdRaWFCOXNkQlh6MkUvdjduYW95MDFGcjQ2bHR1Y0dQbkV2d3VGUktpSlRFcFZYN1BxTThCT1ZWWU81b3pQWWtsYktnTEUrMTlXMGM0QnM5MzFOTllSUFhjeTc2RjZIYTlUUzBlNXVqdVBLNlYwajUwaFphek0zWVpIek51akp4cStOY2VwY3k4cjJJeENzU3RFZVVLNVBPSDE0ZUp6MGcvaldlSXdXQlNZYS8zdHZtWXlUUEMiLCJtYWMiOiIzNmNjNzE0NWZlZGMzNjU1M2UzZmFiZDg4Yzg4NTk1NGEwNjQxOWE3NWZmNTEwM2I3YTYyNGMzZTk1OGE1NTg3IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:51:10 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IitjZHdpR0dYdG5MSCtldG9YY09tdmc9PSIsInZhbHVlIjoiM1daUkZ3Wk9URnkwclpGbllDazhQRldkS21oYWRKTjEvdzBaNjlML3NyNyt6WEFqZGxjTktja3hKdm8vTlJlRlU5RExTNjNzK2JCaGJPazc0T3NzWmt1b3EyOEUvTHZQaUJOSjVRR0hWWWpEcDZtMmpna1RHbExuNHBxaUZnTyt3ZDkrMGVYOGtuZGNwcWFXMHNVTDRKTkNuUG5jbGt6ZU1lK2UxQUpVOThUbWtUOGdrcitrdHZERjJVWFlZQjJZeVQxRlREd3MrN1JuZmFRWTREalprRmtvT1hBTzdIMVFWTERJUnVPY0taWDd0VlFrQWdRa3Y3b1loQ0lhbjVMK0IwVVd2Ykx2WVJ3eFlDNHdtQ2FsTHk4OC8ybkxzbDBpNndxbjU4Y0pFbWJmbnA3S0Q1MDhxNDZzWFpXQlVDcGZHOG1rbTdSRXErRzZndEN6VEFYcmd5Ulh0dEtJZlZVejJDMm80QWVPZ2FqTDh6Nmo2bkNFbmdEZmVCOFcwdUN5aWFSbC91d0lRSEJmeVU3Ym1RbGhJR2dGdE9mWkZKZGUxSDUzaTNSaERBaWxUQ3FPTVkxdW5nRkNaWU5ISGNIVGl0TnNzMms0L25VaXhsbWRNSlVpazByVVRHbGs1TFZ4NER1dVh5ZzhnWjBMREtaeU5UVE45MWxYZmJ4bjN6MWMiLCJtYWMiOiJkYWNiZjEwZDQxMmZjMDUwNDlhOThjZWZiZDM2NTgyMjkwYjk3Y2RiOTQ3MWQ4ODBhYTExZjhjMmIzZTJjNjM5IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:51:10 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkQ2MURLV2I1K25obnVpZmYycFRZWmc9PSIsInZhbHVlIjoibUx0UkplTysrVW5Pd0pUTTluNEZ1MHZ2cjh6eDVUSXFEUHQra211UndCOU5YU2xIRW96ZGdaMDJqN3M5bkM3NVNObXNHemg4NHJkK2pQT0lyQWQ2Rm1kQnkrWFVML3hXZGU4ZHo3Z29qd0ZYMDZ6b1JZVERIcjFGUlBBeFUzMVRhRytsUTRNZDhFK3FRSnBiMGpJMXZuSXN2Y3V3TS9jbTlGeW9UMm8xam45R3dmekwrWEV6MGlBZjlPa1EzSUhFL0I5NmxQTjdZeTJRajVRYVYrbithVUZVYUE2NWF5ZkJ0Uk5UWnhIUTBUY2pqRWpwVXQ0Q1dsaDJLL3NsdWVEL0N0K2E5WEpTQ01BaGtacHRPNVJWN1hvWkRUbGF0MCtBWVNUZWFTMnVFSCtiWnJibW40MVBQeDVCMmlnV3FtM3N0dWdzTmxyQndFRzdRaWFCOXNkQlh6MkUvdjduYW95MDFGcjQ2bHR1Y0dQbkV2d3VGUktpSlRFcFZYN1BxTThCT1ZWWU81b3pQWWtsYktnTEUrMTlXMGM0QnM5MzFOTllSUFhjeTc2RjZIYTlUUzBlNXVqdVBLNlYwajUwaFphek0zWVpIek51akp4cStOY2VwY3k4cjJJeENzU3RFZVVLNVBPSDE0ZUp6MGcvaldlSXdXQlNZYS8zdHZtWXlUUEMiLCJtYWMiOiIzNmNjNzE0NWZlZGMzNjU1M2UzZmFiZDg4Yzg4NTk1NGEwNjQxOWE3NWZmNTEwM2I3YTYyNGMzZTk1OGE1NTg3IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:51:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IitjZHdpR0dYdG5MSCtldG9YY09tdmc9PSIsInZhbHVlIjoiM1daUkZ3Wk9URnkwclpGbllDazhQRldkS21oYWRKTjEvdzBaNjlML3NyNyt6WEFqZGxjTktja3hKdm8vTlJlRlU5RExTNjNzK2JCaGJPazc0T3NzWmt1b3EyOEUvTHZQaUJOSjVRR0hWWWpEcDZtMmpna1RHbExuNHBxaUZnTyt3ZDkrMGVYOGtuZGNwcWFXMHNVTDRKTkNuUG5jbGt6ZU1lK2UxQUpVOThUbWtUOGdrcitrdHZERjJVWFlZQjJZeVQxRlREd3MrN1JuZmFRWTREalprRmtvT1hBTzdIMVFWTERJUnVPY0taWDd0VlFrQWdRa3Y3b1loQ0lhbjVMK0IwVVd2Ykx2WVJ3eFlDNHdtQ2FsTHk4OC8ybkxzbDBpNndxbjU4Y0pFbWJmbnA3S0Q1MDhxNDZzWFpXQlVDcGZHOG1rbTdSRXErRzZndEN6VEFYcmd5Ulh0dEtJZlZVejJDMm80QWVPZ2FqTDh6Nmo2bkNFbmdEZmVCOFcwdUN5aWFSbC91d0lRSEJmeVU3Ym1RbGhJR2dGdE9mWkZKZGUxSDUzaTNSaERBaWxUQ3FPTVkxdW5nRkNaWU5ISGNIVGl0TnNzMms0L25VaXhsbWRNSlVpazByVVRHbGs1TFZ4NER1dVh5ZzhnWjBMREtaeU5UVE45MWxYZmJ4bjN6MWMiLCJtYWMiOiJkYWNiZjEwZDQxMmZjMDUwNDlhOThjZWZiZDM2NTgyMjkwYjk3Y2RiOTQ3MWQ4ODBhYTExZjhjMmIzZTJjNjM5IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:51:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2120050423\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}