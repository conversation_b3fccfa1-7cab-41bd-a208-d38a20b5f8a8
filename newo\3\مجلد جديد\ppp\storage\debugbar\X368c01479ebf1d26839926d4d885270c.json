{"__meta": {"id": "X368c01479ebf1d26839926d4d885270c", "datetime": "2025-06-17 15:02:37", "utime": **********.647538, "method": "GET", "uri": "/customer/check/warehouse?customer_id=6&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.082824, "end": **********.64757, "duration": 0.5647459030151367, "duration_str": "565ms", "measures": [{"label": "Booting", "start": **********.082824, "relative_start": 0, "end": **********.560936, "relative_end": **********.560936, "duration": 0.47811198234558105, "duration_str": "478ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.560948, "relative_start": 0.47812390327453613, "end": **********.647575, "relative_end": 5.0067901611328125e-06, "duration": 0.08662700653076172, "duration_str": "86.63ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46234432, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02062, "accumulated_duration_str": "20.62ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.601099, "duration": 0.018179999999999998, "duration_str": "18.18ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 88.167}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.631027, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 88.167, "width_percent": 6.547}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6371388, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 94.714, "width_percent": 5.286}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-926307684 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-926307684\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-711255336 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-711255336\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-64399732 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-64399732\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1046351230 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlBRTGVEd29sdTl6dXpQbmRpbG9wUlE9PSIsInZhbHVlIjoiNDh2c0hEK0toTE5lV0F3cGp5VWxaRGtaNVNkUmFUbGV3RkFsVE1EZWVIRjZyNGJkczllRnJ2YklOT0YwN1RVTGRkL1hvMFExSWVZei9pSmJSclN6NmNCMmZMb282bHNrVnpTcElVWm5WaC9jQUNqa0JPVk5MTWJvZ240andpaTFDTUp6VmwzQjRYU2tzcU5lNWhKaG1ONHNyRk9naDUzMmIvL3M0K1RuVjc1UGhRTEQwU29kTEpTRDdSaDhZa0MrbDZyR1NYN3gzVENoMXdWcWs3b1RDMXg4c1BGUFNhVUhOVEhReWtuQzVtTU5Gc29Mc2J1dmpWREw3RDhGb0lzR3NibFA1TDMrZkt5alptYUlNaGkrWkxqNEV4SlVqUlkrSGQzcXlmUUx1ZFJkYlhsTVhOc0FlT2hnQnQxWkdMZTJMVWs1c3BhNjRjd1diZlNCQkpjYjY4SVlJeEJMWFhraG52WmNQaTRkRGh1bnFZdFFzQXBzaW5pRnNqNEtkSm85UUhtcHFqdVlWNXdEblNyQ3BVN005ZHg2Wi9iNExJVGJiQ0VlUXc3a1pudWJvOHlyVlZEV1B2Qmlnb1E2UjhXaDNxYThkaFZYNkg2blVDeGp4VHROUEpEQ2xFdGgybmQ2dmlSY0t1SlZ1YUdQLzNiMDBwNm1Yell5MHc4eGN3Q3giLCJtYWMiOiIwMzIxZGIwNWFjNzk5OTMxYzNiNWRhNzQ2NTdjY2M3ZWY0NTA0MzFhZDFlNDY0YjMyZDA2MjBjZGIyMThkOTcxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InVYTWlSZXZNSVAzYVpqZy85VjQ4ckE9PSIsInZhbHVlIjoiWjB4NjRJcXdMUmFacCtPcW1VU3Faa2NuQ3ZsYlZzK3ZNSFNWL1NlSmZpcGcwMFNqRDJSMnIyRGIyekh2cFY1UWZqQ2VvRzVLMjVGeWV1QTcrRTkva3V5TGtnVTltSjFmSUc0SC8vWHNvQkJEUnBhZUR1SGJVMkwxenVZSDBaclJPQm1OTmVJZ2ZyYk5NQUQ3WUg1TCtZRDFjU3J6b0hzVzlFcElUYnJaa0NtWXFvRGc4OHAyOWZoWG5zSm1lMHNMUitEK1Vya3didnZzRjQ5UEM3Qy95b1NaMU53TU5NVHplRHczbUdiVWV4ckF0SUc5ZXA3bFdGcEVsNDk0RG9BVE5PZnpnQks4T0IvWTRJcGFSejd2UjVIRW5uRFZjd2Y5aFFsaUkya1hBTzRHSWRZME90a01pSHowWnpaL2t5di9XNGNUSk5CTExuTHJFRzRGU0FMcVJBWTBydHp2QU1hTjAyU1haVCtSaWVRTUx3V093SmYyTGFOS3pINC9RcHlXamhOeVZmMTlDRnFxSDFVazYrR3Y2cWZOY1ozSzJYR1VDRDNuci80T0RWUmN6dUlNalIvYzEvQWhYZHRCbHJqR2lKSmR6NXFYUWVqYmZseUhNb0piMUFKVUdYbUpoZy9lZVBGZ3hWRnE4ZGF6dGw2RzlMSGlVM1VSUUdKejBEUysiLCJtYWMiOiI2ZjEzZWViMzUzYWRmNjc2MmU0NDY1MGZmOWUyNzVkNTRkMmY1MjY1OGY0ZjM5ZDNiMjExMjNmOWVkNmYxZTFkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1046351230\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1587672898 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1587672898\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1001467477 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:02:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlA1OXlKOGpSaFlqeTRUQXRNSHpBalE9PSIsInZhbHVlIjoiRDJxRjhIbS91YnpxQzFEM29nenBvaVJaaDBoMWxGWGFaVDhkaEZObmtVZ0dhL0dFdVJ0eWkvelV2L3Y3NTZML3BNSHhPQnY2Mi9mUy9zcmJ4YU5lMzlwU3hGZG9XZ0hDOXI2aWw2b3VxdlVRSTg4L2llL1lUMDhkWlZRaXhqSlVrOTRpWThsaU9jaDNvRGJ4dmZWTkYwZkZZMy8yMkJTN2Jmcy82alRwMVRlZHFEcFpxYVJ0QmZQaDdwemhuU1luRTJvTWc4bHB0eEs3K3JCTzhwQ1RvWng1Z01DUDFaZ0lnYjFjVis1NXk4cWpFdHZ4ZFd1WTl0NnliQ3NDbnhNV3BsSDVWc0cxMkJOYXlqTVA0Sy9oWVBmOFZ6cjdPZ0FkWUkrck9FajIrcUV0S21NZVN1M09JZTN1dDh6d1laaDhJQ0FKVFQrUkQrQXl6WXVSbllwZXp3ZkhMNEN0OVViVWFHbXJ5eG9XSjM3d0dsRVFEbE9VdG5MVk1lQXE5QVdWZ2hWWjZrRkJlUVJWQjNQT2c2blB1em1UTGt1RXNJTWk3anVKU3RLazFwYmdpTityMmxYcFNqbXBSZDUrUGVsMXdiK0FacWNyWnRqYklZUU1pa1d1a0RoeWd0MjMwNVU2ZS9uNHRseVFQN1NSeG9hY0E5YXZhM2dFZHhuVUdjQm0iLCJtYWMiOiI4MTY2ZGM5ZThkYzg0YjkxNTcxZjM4MzIyN2IwZDFmZjM1YjViMzk2NGYyZTQ4OWNiYzcwMjE4OTQ4MTlmNjEyIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:02:37 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InZ0THVCRmpYTkxhdEljRjhtekRwQWc9PSIsInZhbHVlIjoiSncwY3NJSjhOcnlScVl1ODBtd3FBLzFRR0c3OGZDc2tNb21OenRMcEQ3MVlManVSNVkyUDd2VmlQWDMzaGlBQWJuM0xtcUMrTDFnMmFFMHpPVVJRWFo2MnJuRXpXY3dIQVZPZ1RTSDZFcHFUOCtVUkV1TUZvRUhzUjNKYUNLUHovWDFoQ2NPWmFOSVVpeDFNTFYrR1VTZHJ2cVVGam9BVTgveTZFQ1gxQUVsUTlMQm9CdVozTkxIN1ZLQXp1L2ZDajUyb3ZQMm03YnVzUEFoaGdUd2xaWDdod1hSd0VTWVl6bkkvSHE5dm5FM2Ntam84WFpyU0tqV0h1QXc3T0xlYTdUcmh4SDdwUlkzRmNscUM0bE9zeE05UmY1Z3J0cGdUdFRqYWJHQ2Zhb1pJcDJVODhUWDQ1MzcvMFFSTmFhVktZeVpYU2F5RGFFVmZKQllCOTZpQjEvOFpDME5PZkxyYVM0aGIzUWxTQ2VwbFFMQmY5V21TMHNvNVpKMGhIVjVPeGhpL1B5dnNWdVU0YjZxY0t0RHExV2lBYTNnL2M4aFVuZlZvRThrMjg2aDJzbERhMjlLeTRKaDd1c1JSZ3ViUU5ydnpGdXlRdHVnL3JXQlJJZHFoTWpBWVpQSVFNQXA3WmtHa1RDWStrbnJnRmpIKy8wUyt4d0xYbHB4VmhIUnoiLCJtYWMiOiJmN2I0MTFiMjEwODMzMmUzZjAxYjJhNDM0ZDcwODRlNmEyN2ZkMTNkZGFkMjBjZGRlMTQ1YzI4NTBkMDY2ZmNiIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:02:37 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlA1OXlKOGpSaFlqeTRUQXRNSHpBalE9PSIsInZhbHVlIjoiRDJxRjhIbS91YnpxQzFEM29nenBvaVJaaDBoMWxGWGFaVDhkaEZObmtVZ0dhL0dFdVJ0eWkvelV2L3Y3NTZML3BNSHhPQnY2Mi9mUy9zcmJ4YU5lMzlwU3hGZG9XZ0hDOXI2aWw2b3VxdlVRSTg4L2llL1lUMDhkWlZRaXhqSlVrOTRpWThsaU9jaDNvRGJ4dmZWTkYwZkZZMy8yMkJTN2Jmcy82alRwMVRlZHFEcFpxYVJ0QmZQaDdwemhuU1luRTJvTWc4bHB0eEs3K3JCTzhwQ1RvWng1Z01DUDFaZ0lnYjFjVis1NXk4cWpFdHZ4ZFd1WTl0NnliQ3NDbnhNV3BsSDVWc0cxMkJOYXlqTVA0Sy9oWVBmOFZ6cjdPZ0FkWUkrck9FajIrcUV0S21NZVN1M09JZTN1dDh6d1laaDhJQ0FKVFQrUkQrQXl6WXVSbllwZXp3ZkhMNEN0OVViVWFHbXJ5eG9XSjM3d0dsRVFEbE9VdG5MVk1lQXE5QVdWZ2hWWjZrRkJlUVJWQjNQT2c2blB1em1UTGt1RXNJTWk3anVKU3RLazFwYmdpTityMmxYcFNqbXBSZDUrUGVsMXdiK0FacWNyWnRqYklZUU1pa1d1a0RoeWd0MjMwNVU2ZS9uNHRseVFQN1NSeG9hY0E5YXZhM2dFZHhuVUdjQm0iLCJtYWMiOiI4MTY2ZGM5ZThkYzg0YjkxNTcxZjM4MzIyN2IwZDFmZjM1YjViMzk2NGYyZTQ4OWNiYzcwMjE4OTQ4MTlmNjEyIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:02:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InZ0THVCRmpYTkxhdEljRjhtekRwQWc9PSIsInZhbHVlIjoiSncwY3NJSjhOcnlScVl1ODBtd3FBLzFRR0c3OGZDc2tNb21OenRMcEQ3MVlManVSNVkyUDd2VmlQWDMzaGlBQWJuM0xtcUMrTDFnMmFFMHpPVVJRWFo2MnJuRXpXY3dIQVZPZ1RTSDZFcHFUOCtVUkV1TUZvRUhzUjNKYUNLUHovWDFoQ2NPWmFOSVVpeDFNTFYrR1VTZHJ2cVVGam9BVTgveTZFQ1gxQUVsUTlMQm9CdVozTkxIN1ZLQXp1L2ZDajUyb3ZQMm03YnVzUEFoaGdUd2xaWDdod1hSd0VTWVl6bkkvSHE5dm5FM2Ntam84WFpyU0tqV0h1QXc3T0xlYTdUcmh4SDdwUlkzRmNscUM0bE9zeE05UmY1Z3J0cGdUdFRqYWJHQ2Zhb1pJcDJVODhUWDQ1MzcvMFFSTmFhVktZeVpYU2F5RGFFVmZKQllCOTZpQjEvOFpDME5PZkxyYVM0aGIzUWxTQ2VwbFFMQmY5V21TMHNvNVpKMGhIVjVPeGhpL1B5dnNWdVU0YjZxY0t0RHExV2lBYTNnL2M4aFVuZlZvRThrMjg2aDJzbERhMjlLeTRKaDd1c1JSZ3ViUU5ydnpGdXlRdHVnL3JXQlJJZHFoTWpBWVpQSVFNQXA3WmtHa1RDWStrbnJnRmpIKy8wUyt4d0xYbHB4VmhIUnoiLCJtYWMiOiJmN2I0MTFiMjEwODMzMmUzZjAxYjJhNDM0ZDcwODRlNmEyN2ZkMTNkZGFkMjBjZGRlMTQ1YzI4NTBkMDY2ZmNiIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:02:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1001467477\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-630212295 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-630212295\", {\"maxDepth\":0})</script>\n"}}