{"__meta": {"id": "X2a8bd61dd283695af83cf413168ad726", "datetime": "2025-06-17 14:01:08", "utime": **********.520192, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750168867.761711, "end": **********.520213, "duration": 0.7585020065307617, "duration_str": "759ms", "measures": [{"label": "Booting", "start": 1750168867.761711, "relative_start": 0, "end": **********.420729, "relative_end": **********.420729, "duration": 0.6590180397033691, "duration_str": "659ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.420748, "relative_start": 0.6590371131896973, "end": **********.520215, "relative_end": 2.1457672119140625e-06, "duration": 0.09946703910827637, "duration_str": "99.47ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46458080, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01238, "accumulated_duration_str": "12.38ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.477085, "duration": 0.01071, "duration_str": "10.71ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 86.511}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.5042632, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 86.511, "width_percent": 6.462}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.51058, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "ty", "start_percent": 92.973, "width_percent": 7.027}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168503247%7C8%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InhKcXd4dTAyMnp4aE9sbWNLN2NUQ1E9PSIsInZhbHVlIjoib3dDTE1NcWVsY1FkWUQyY0lvRFNNQm9vb2N2Z1FjYU1BNmhQNXJVQmFkUU83RldXUmg0TERKV3ArLzR0emNIRWhIdk03Qi9hODVUbEhFbkk4UjROWC9CQ1EyeWlqVzJNSDF2L1h2bktWY0FQamxHQWwxMldYTTZGNWlPOHZBa1VlcUZzZys4WFZ2dDhoYm1CMEtiN1BvdllNekdGQnFUWDdaa29VbUFPNExrV0FEdm5hYVYrZnlMNXNnVEdDK1QvaUc5UW51VExWT2JmRkZ6NFZSMnNXTG5GYWtVbGRGMk1iU2thTDQ5VDlMd2EyQUttaWpsZGJLRG5Ic1dBNHBhNFU2OHZEMXlhS1JFNi9rWk1IVWR4L1Q3VDRYT2hmUndHbjJKa1hpSFl6UmdNVUxNRWNsRHh4aFMxZ0FHTSticHQ2eEVLS1ZrekZnUGYzN1Jxb3NDR0pVQWhLRWJYb3FxQlV0Y2tiNjZvVXlpZHNFa1k5K0poYXBJRVYxZVhHaEU2NE9NR25aaGtaYkp2a0UzZHpvT0pGQkNVK0RRTXB4TDgwN0tGcnZXdU85MS85WjRoVGhKSkVVcEs2K1ByY24wTTJ3c3gzV1AwQnF5MTMxeE5vOHFEWUt3Ui96ZHhwcllNNUdXN29vK1prQkNrQTBkNjBPS2FuWHpEd2tBei9qaW4iLCJtYWMiOiI5MWQ1NDBlNjJhN2I0YTJmNGE5MjBhNThlMzk2ZmM1ZjgzNzJmZmFiMTRlNDc3OTZlZTFiM2Y2NjQyMmFmMmY5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkhyYWZ1TVk3cEtPNmZPSjNoRUJwcGc9PSIsInZhbHVlIjoiczNYeTVyei92aHg3OEE1MGN0QUc1aE9KMXNFWndoZXJMTDl6YzgySC9RTGswdnBXblVxSDdJeXJDbEY2OC82SGtkUVE4SzArbi9USUNEVGpYclBDV0R2R3JHbHpvaWEyaUIyeW10MkpQdUhpR1JYWW9sd05GaElkSEl1NGRKYldjQ3o4WGRQSjY2RnUzTndTazBZRzM5aTRkVVpCcGpTTkdLMkVqTVdlM2ZzMExQWEhpaEJJN3c0bXg0NnE2TFIrcHZWZG05NVZzSEZCZnBrVklCU0lEdTh3VHczMC9DTG1mNjJIZXpzTGwzREk2dkRjV0FCQmF3MjNzU3BHOTQ3OVhVRk5GMjJIRll3d3JrNzg3QUFJaXRjUjBNRXhKSXV2a1c4UE5KNktVKzltYUN6NDgxUk44QWsrd3oxbVl6a3I4WXplc09yNk96YlB6V1MzS0JZem5YM3VES0lYYXNKYnBGY3VnMDFkeTJVd2dNWFBCclJRV3BnYW1aMC9KaHp1OXJvMzJNcjZTVGZZMllVTjVONE9MWVdmZEdrZmdBN2NEQjEyN0kxM2tvanJzcmIxZDNPQTZGTk5KSVlmdWc1SGYvbWhhWFU0ZGdkdTI1WGtWVWhnclNwNXVVNWxYQTZPMXlDbEE2UFZYOGxsbS9QaUIrZkJ2d3loa0NyeG4yL0QiLCJtYWMiOiI0MWI2MGNlZGJiNjhmMGUyZWNkNmMxN2NhOGEwOTY2ZDY5YmE0MTEwNjBmMTBjOTE4YmYwZTI3MzVjZmRhMjY2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1396427524 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1396427524\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:01:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IitjaUpYZWRSdVU3UjEwajA3NW5kZ0E9PSIsInZhbHVlIjoiN1pCOFMzeVA5UnhXYlF3OVB0eXNLTmZjVW4xYlNsa3VQc1VrdU5EbkN2VlczMjZzazhCbGxDSUY1ZXJYTzBadUpmdzdWbUp5L2piT0t6VzZiYkM4VXloOHFIb3JsSFdhZlpGMERSeUhITkZWSHRUR0JKNzU0ZnhzRllOR0lnUm5nem56VWxrQSt3RzNHOUNjdnFBUyt1UG9tZitoRlBNcjZnaCs2bFQ4ck44M0FXMkhjNUN6UUoxOTFkbUdXSnE2RFUrRmdPcjBNMjlPYy9JWFhMUTU0bUcrWnJhRnZLYkM4SG8wM2lXaFJWYXpwc1dxckc3YnZ4NjFsRDlYTnFiT1U2OVdTL0JCRXZhR3FvWm13RlVOMTNqWnhmZGhpKzBUakJTbzJ6VC95TzNJR1dDTWJNMlo3NStjeWFXdTByOVBKNVBGVTVCZFI0NVhsYVg1Z0hhamFSdlEvVUFEQWRzUUIrSUtDZW4xYmo4TnNSZ1M1NmFiUU9kemlGcDJWOHNvSW96a0dhTEFXQi81bFFjOUJNZTJRSmhWVzRuWVNvRWZGZU9ieXFiZ0x3Q2hyMWF2NkpLbkl6bVJlak4wUW5pNW96NkNpTW5XbEhSMGlBU2xkMFBHRG84aXhYQ1REak1oVCs4UUxQWEFqSUpKZ0RUMit2dkNvb25sa0llRHNUQ0EiLCJtYWMiOiJkNTUzNDdmNGJjMWE0MzBhZGVlMzI2MWQ4NzgxMGEwYzQ1MDliNzM4NWViZTdhYjg3NTVkNzBjZjkwNjBjZGFhIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:01:08 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IklwQjFrRERDOGd5VUFRRnhhU3FRM0E9PSIsInZhbHVlIjoidVdraFdva2pqZGF2RndiNVE4eXJUcndqbC9wYjRkdk5HbHZUUFNJT0JHS2d0K3JWbVgzdEg3ZWZvYlVpd3gwaGRUU3o2N1dLTDZPcVpSdjQwMVVpbmtGcnl4UFp2S3ZFMEpYbjFxaUt3cXVRSldnWm1DMWVYSHU3N1RJWmNvekZjcjBPUXh3bXkxQS9MTWhDN25EcDlqS053elhmZ2tFNDFDSjJ3RkRjQzZLRU9ETmZrYkVGeGNJVWdUWG91QW1WTzlBdWVYMG1GVTNSQ3h3VGk2a1FqVlBuSFdLRjBxTS9JbkMyY21sMDNwZzZJaDFoYndIYkZGSlFnOXRvY1B2Q0VoNjd1WnhHYnNJY0gzTElEWnRQYnZNbEtrZm9HTytic08ydldzMnJPY1oyWkJXQUQ3Vk5aWXRpaTdldGNtaEx6OUc0QzV5UGVNUnRoR3IxaGRFWm9RVHlnVEZTWS9mRFZOUHpFVlgycU1abDBUbDBCTVlqVWN4OUdTU2JKZmVlRjllSzZKcHNDZ1EwaytMeVFDSGJvbnhiSzZleGgzYUxTd1FzTWdxV3l6aVNJUjdtSEdNNnJzMWF0MDB1WmZIV3RnKzM1QUVtalBNdVJIM2ppMGwxS090RWJsTUIxZEFWY05qOGRqYmJWRjZoUzNzVkhvREg5LysyZnFrWGRIQ2kiLCJtYWMiOiJlZTBmYjVkYjFmZWY2Nzk0ZGRmYzA3YTQyZmM2NDJlODY1NDdjNDQyZjUzOWIxYzRhYTUwMmI5NTFmZWJmZTFmIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:01:08 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IitjaUpYZWRSdVU3UjEwajA3NW5kZ0E9PSIsInZhbHVlIjoiN1pCOFMzeVA5UnhXYlF3OVB0eXNLTmZjVW4xYlNsa3VQc1VrdU5EbkN2VlczMjZzazhCbGxDSUY1ZXJYTzBadUpmdzdWbUp5L2piT0t6VzZiYkM4VXloOHFIb3JsSFdhZlpGMERSeUhITkZWSHRUR0JKNzU0ZnhzRllOR0lnUm5nem56VWxrQSt3RzNHOUNjdnFBUyt1UG9tZitoRlBNcjZnaCs2bFQ4ck44M0FXMkhjNUN6UUoxOTFkbUdXSnE2RFUrRmdPcjBNMjlPYy9JWFhMUTU0bUcrWnJhRnZLYkM4SG8wM2lXaFJWYXpwc1dxckc3YnZ4NjFsRDlYTnFiT1U2OVdTL0JCRXZhR3FvWm13RlVOMTNqWnhmZGhpKzBUakJTbzJ6VC95TzNJR1dDTWJNMlo3NStjeWFXdTByOVBKNVBGVTVCZFI0NVhsYVg1Z0hhamFSdlEvVUFEQWRzUUIrSUtDZW4xYmo4TnNSZ1M1NmFiUU9kemlGcDJWOHNvSW96a0dhTEFXQi81bFFjOUJNZTJRSmhWVzRuWVNvRWZGZU9ieXFiZ0x3Q2hyMWF2NkpLbkl6bVJlak4wUW5pNW96NkNpTW5XbEhSMGlBU2xkMFBHRG84aXhYQ1REak1oVCs4UUxQWEFqSUpKZ0RUMit2dkNvb25sa0llRHNUQ0EiLCJtYWMiOiJkNTUzNDdmNGJjMWE0MzBhZGVlMzI2MWQ4NzgxMGEwYzQ1MDliNzM4NWViZTdhYjg3NTVkNzBjZjkwNjBjZGFhIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:01:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IklwQjFrRERDOGd5VUFRRnhhU3FRM0E9PSIsInZhbHVlIjoidVdraFdva2pqZGF2RndiNVE4eXJUcndqbC9wYjRkdk5HbHZUUFNJT0JHS2d0K3JWbVgzdEg3ZWZvYlVpd3gwaGRUU3o2N1dLTDZPcVpSdjQwMVVpbmtGcnl4UFp2S3ZFMEpYbjFxaUt3cXVRSldnWm1DMWVYSHU3N1RJWmNvekZjcjBPUXh3bXkxQS9MTWhDN25EcDlqS053elhmZ2tFNDFDSjJ3RkRjQzZLRU9ETmZrYkVGeGNJVWdUWG91QW1WTzlBdWVYMG1GVTNSQ3h3VGk2a1FqVlBuSFdLRjBxTS9JbkMyY21sMDNwZzZJaDFoYndIYkZGSlFnOXRvY1B2Q0VoNjd1WnhHYnNJY0gzTElEWnRQYnZNbEtrZm9HTytic08ydldzMnJPY1oyWkJXQUQ3Vk5aWXRpaTdldGNtaEx6OUc0QzV5UGVNUnRoR3IxaGRFWm9RVHlnVEZTWS9mRFZOUHpFVlgycU1abDBUbDBCTVlqVWN4OUdTU2JKZmVlRjllSzZKcHNDZ1EwaytMeVFDSGJvbnhiSzZleGgzYUxTd1FzTWdxV3l6aVNJUjdtSEdNNnJzMWF0MDB1WmZIV3RnKzM1QUVtalBNdVJIM2ppMGwxS090RWJsTUIxZEFWY05qOGRqYmJWRjZoUzNzVkhvREg5LysyZnFrWGRIQ2kiLCJtYWMiOiJlZTBmYjVkYjFmZWY2Nzk0ZGRmYzA3YTQyZmM2NDJlODY1NDdjNDQyZjUzOWIxYzRhYTUwMmI5NTFmZWJmZTFmIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:01:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}