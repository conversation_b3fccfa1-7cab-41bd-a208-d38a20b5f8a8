<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\ProductService;
use App\Models\User;

class FixProductCreatedBy extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:product-created-by {--user-id= : ID of the user to assign products to} {--dry-run : Show what would be changed without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix created_by field for imported products that are not showing in POS';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('🔍 فحص المنتجات التي لا تظهر في POS...');
        
        $userId = $this->option('user-id');
        $dryRun = $this->option('dry-run');
        
        // إذا لم يتم تحديد معرف المستخدم، اختر المستخدم الأول
        if (!$userId) {
            $user = User::first();
            if (!$user) {
                $this->error('❌ لا يوجد مستخدمين في النظام');
                return 1;
            }
            $userId = $user->id;
            $this->info("📝 سيتم استخدام المستخدم: {$user->name} (ID: {$userId})");
        } else {
            $user = User::find($userId);
            if (!$user) {
                $this->error("❌ المستخدم بالمعرف {$userId} غير موجود");
                return 1;
            }
            $this->info("📝 سيتم استخدام المستخدم: {$user->name} (ID: {$userId})");
        }
        
        $creatorId = $user->creatorId();
        
        // فحص جميع المنتجات
        $allProducts = ProductService::all();
        $this->info("📊 إجمالي المنتجات في النظام: " . $allProducts->count());
        
        // فحص المنتجات التي لا تنتمي للمستخدم الحالي
        $orphanedProducts = ProductService::where('created_by', '!=', $creatorId)
            ->orWhereNull('created_by')
            ->get();
            
        $this->info("🔍 المنتجات التي لا تنتمي للمستخدم الحالي: " . $orphanedProducts->count());
        
        if ($orphanedProducts->count() == 0) {
            $this->info("✅ جميع المنتجات تنتمي للمستخدم الحالي");
            return 0;
        }
        
        // عرض تفاصيل المنتجات المشكوك فيها
        $this->table(
            ['ID', 'Name', 'SKU', 'Current Created By', 'Type'],
            $orphanedProducts->map(function($product) {
                return [
                    $product->id,
                    substr($product->name, 0, 30) . (strlen($product->name) > 30 ? '...' : ''),
                    $product->sku,
                    $product->created_by ?? 'NULL',
                    $product->type
                ];
            })->toArray()
        );
        
        if ($dryRun) {
            $this->warn("🔍 وضع المعاينة: لن يتم إجراء أي تغييرات");
            $this->info("💡 لتطبيق التغييرات، قم بتشغيل الأمر بدون --dry-run");
            return 0;
        }
        
        // طلب التأكيد
        if (!$this->confirm("هل تريد تحديث created_by لهذه المنتجات إلى {$creatorId}؟")) {
            $this->info("❌ تم إلغاء العملية");
            return 0;
        }
        
        // تحديث المنتجات
        $updated = 0;
        foreach ($orphanedProducts as $product) {
            $oldCreatedBy = $product->created_by;
            $product->created_by = $creatorId;
            
            if ($product->save()) {
                $updated++;
                $this->line("✅ تم تحديث المنتج: {$product->name} (ID: {$product->id}) من {$oldCreatedBy} إلى {$creatorId}");
            } else {
                $this->error("❌ فشل في تحديث المنتج: {$product->name} (ID: {$product->id})");
            }
        }
        
        $this->info("🎉 تم تحديث {$updated} منتج بنجاح");
        
        // فحص النتائج
        $remainingOrphaned = ProductService::where('created_by', '!=', $creatorId)
            ->orWhereNull('created_by')
            ->count();
            
        if ($remainingOrphaned == 0) {
            $this->info("✅ جميع المنتجات تنتمي الآن للمستخدم الحالي");
            $this->info("💡 يجب أن تظهر المنتجات الآن في POS بشكل طبيعي");
        } else {
            $this->warn("⚠️  لا يزال هناك {$remainingOrphaned} منتج لا ينتمي للمستخدم");
        }
        
        return 0;
    }
}
