<?php

require_once 'vendor/autoload.php';

// Load Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\ProductService;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

echo "=== تشخيص مشكلة المنتجات في POS ===\n\n";

// جلب أول مستخدم للاختبار
$user = User::first();
if (!$user) {
    echo "لا يوجد مستخدمين في النظام\n";
    exit;
}

echo "المستخدم المختبر: {$user->name} (ID: {$user->id})\n";
echo "Creator ID: {$user->creatorId()}\n\n";

// فحص جميع المنتجات
echo "=== جميع المنتجات ===\n";
$allProducts = ProductService::select('id', 'name', 'sku', 'created_by', 'type', 'sale_price', 'purchase_price')
    ->orderBy('id', 'desc')
    ->limit(20)
    ->get();

foreach ($allProducts as $product) {
    echo "ID: {$product->id} | Name: {$product->name} | SKU: {$product->sku} | Created By: {$product->created_by} | Type: {$product->type}\n";
}

echo "\n=== المنتجات للمستخدم الحالي ===\n";
$userProducts = ProductService::where('created_by', $user->creatorId())
    ->select('id', 'name', 'sku', 'created_by', 'type', 'sale_price', 'purchase_price')
    ->orderBy('id', 'desc')
    ->limit(10)
    ->get();

foreach ($userProducts as $product) {
    echo "ID: {$product->id} | Name: {$product->name} | SKU: {$product->sku} | Created By: {$product->created_by}\n";
}

echo "\n=== فحص created_by values ===\n";
$createdByValues = ProductService::select('created_by')
    ->groupBy('created_by')
    ->get()
    ->pluck('created_by');

foreach ($createdByValues as $createdBy) {
    $count = ProductService::where('created_by', $createdBy)->count();
    echo "Created By: {$createdBy} | Count: {$count}\n";
}

echo "\n=== اختبار دالة getallproducts ===\n";
// محاكاة تسجيل الدخول
Auth::login($user);

try {
    $products = ProductService::getallproducts()->limit(5)->get();
    echo "عدد المنتجات من getallproducts: " . count($products) . "\n";
    
    foreach ($products as $product) {
        echo "ID: {$product->id} | Name: {$product->name} | SKU: {$product->sku}\n";
    }
} catch (Exception $e) {
    echo "خطأ في getallproducts: " . $e->getMessage() . "\n";
}

echo "\n=== فحص المنتجات المستوردة مقابل المضافة يدوياً ===\n";

// البحث عن أنماط في البيانات
$manualProducts = ProductService::where('created_by', $user->creatorId())
    ->whereNotNull('pro_image')
    ->count();

$importedProducts = ProductService::where('created_by', $user->creatorId())
    ->whereNull('pro_image')
    ->count();

echo "المنتجات مع صور (غالباً يدوية): {$manualProducts}\n";
echo "المنتجات بدون صور (غالباً مستوردة): {$importedProducts}\n";

echo "\n=== انتهى التشخيص ===\n";
