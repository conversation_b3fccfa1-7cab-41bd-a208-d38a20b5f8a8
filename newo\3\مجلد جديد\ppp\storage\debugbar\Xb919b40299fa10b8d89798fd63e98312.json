{"__meta": {"id": "Xb919b40299fa10b8d89798fd63e98312", "datetime": "2025-06-17 14:01:32", "utime": **********.354761, "method": "GET", "uri": "/customer/check/warehouse?customer_id=6&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750168891.73959, "end": **********.354786, "duration": 0.6151959896087646, "duration_str": "615ms", "measures": [{"label": "Booting", "start": 1750168891.73959, "relative_start": 0, "end": **********.270762, "relative_end": **********.270762, "duration": 0.5311720371246338, "duration_str": "531ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.270773, "relative_start": 0.5311830043792725, "end": **********.354788, "relative_end": 2.1457672119140625e-06, "duration": 0.0840151309967041, "duration_str": "84.02ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46234432, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01992, "accumulated_duration_str": "19.92ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.30898, "duration": 0.0184, "duration_str": "18.4ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 92.369}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.339541, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 92.369, "width_percent": 4.568}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.344869, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 96.938, "width_percent": 3.062}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-1783227034 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1783227034\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1547671041 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1547671041\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1621034731 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1621034731\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-427784691 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjJwUEZVQ3Q0VVdVY1l0RzFrMmlHMnc9PSIsInZhbHVlIjoiMEhhbE1oUE1DYmszOGhYeTB1MUhiMkI0dkY3bThndjdRTTN0Ymc3MFlOSmZXazU3d3RWVE5SeWlxYXVQclphYkVHZ1lhb0FSdG56dWhoODE5Vm92blN2ZDRIMjlLRzZEdmJtM1ZlWlZTdGVBeXhpMy9KMjhiZ3hQbnFKczdZcnhUMmxKeVhWcGtFamhBQmM2dzF4TmtLd2kvT1Z6VGdLK0llcHM4YzhudjRtU1V1SzhpNWNqZjRHSFVMOVlER2JEYXRvT2N6c1RKalRVL3RSVWU0eENDdm9UbzBvM1grQmRhTDNrM0tGV3lIaGZHV0VOR2tDLzJMYUswWUNUeElhYVFobld2ZWdCWkVabmVRT2RwWFpHd2pJYWg3Y1ZRVHdEYllKano2Wm1kVnVCWW05WnpGWmpzVHpCZTZ6UFVSYy83WFJFVzFKNXpGSnl3U3gvei9vT1NjSjdvYzZSckh2a2JhaW5XQjg4K3VXK0NHditLT2VtcStmUDBMSDRkdG5acXNLNWpVMkRGeGtkZFI2L0JjUjJhL0NWQkJnMXB5TnhEVGhFSUE3YmIwa1dTbVMrR25WSmNFWGlkVHplWWVINjNmdktmTWtDaVBTN3RRbThzLzRqVUs3Zmsvc2k0SmMyZ3lYN0U3aXpqUjRNaTc0YVk0VzE5aVhoZXVOcmE0bXgiLCJtYWMiOiIxOTY4OWMwMWNlYTIzMTQ3ZjExMTRjYmYzYjgzNjAxZmI3ZjExZDNjZDYxZDFkMmYwNjU3NDFhMDY2Y2Y3YzU5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImxrNDR2VmJ0QmpiTHpUU01RSERyaWc9PSIsInZhbHVlIjoidmxJOE1oMnFoT2g2T1M0c3hpcy8wUDJ1My9RcG5Fa0NNSlBGT3JXemlhNG5idEZKWGVDbStRai9mWEV1aXk2aFZEZGVEU2J4cXVjY1VCLzc3cFhzczVEbTRPQWNid21QeEliMk1SOEFCSkV5RlRCNGYzWEtqV0tlNUUzdzNLRitweHA4d3p5VjMyLzNvM3liaDVRSHNNQTlCaWovWXg0c1ZnMDhZaDVQQ1BJRFlZaTUxeWloc0dsVFlWVEJSdEpVMWRZTzc0ckZ4RjJYVWh0cnNLTk0vQ3JCOUdZck94em1xZmRrY0lvK2tvQ3lKM0tQTmhYdGFQeXBtM09ucmJsTVl3ZlRXM3htYjBDWndsTXRiMXVSY3dIK3Z3MGUyWnFHRHR0Q2VBOVlsRVFBMDNmMmhaYTFpUlIyMzZxNVMwMkx2MmVGS1JaS2dvbzVhZXFxQlFyRm1STWJCQkkzY2owcUc3Ly9tQTBmemEzOXA1NW1zM1Mwd1d2RTJEVzlnNnRRNTh3ZlhSYmdOYkZKK1ZiQ2FQRCsxcTdDbld4djJQaEtpb3Y1Ui9uaGNJQkZmZllvMFNOaXBKUktEKzUzZC9YT0JBYTlIVzYvYmIyWldKanc2N1M2cTZYK0p4b1BweFRvSzNWb1QrN1BILzJmSU53TjdIbkQ4cVVhS2VjSDVuQk8iLCJtYWMiOiJlY2NjMGYyYjVmYzQ4ZGM3MDJiZGRiYjgzNGZiMTRkNDliNTQ5ODM3ZjJkMjgwNGU2MzQ5NzI1NWQ2MjQzMjVmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-427784691\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-298364098 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-298364098\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-73579357 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:01:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im1DWkJleU9vUHNQV2NqcVd1aDllU3c9PSIsInZhbHVlIjoiNW9WM1BWSUhpaVBVUFJhTzBNbGQ1TkJqV2ZsR2c2a0NtWmd1aUxZdXFHTU40WDRRay8vdUMwNDduZXE1UWRjMkczNEt2Vllhd0Fla1BZNlZmb1p3K1g4WmtJWnlDc0xsWnJFcDNCdjdEMVJPUjNUSkw4MUhEZmYyMlQxUitNMEdKNVNmN0hZb3FVRDBXVmx2cnl1MXNrdjVKUTF1eVdtNkpwaVFrUWovRk5qamIremhWSlltWjdzVnFRTlJ2Q2tkbnQwNXVHZG5zVFNGazI5ZTl4VU0rZ1ExamI5QTAyemRpNDIxWWRFRWlzc041R2hwYjhPekhNRWhUWUh4SHJaOFBPTEF4Y0l4M1dKL1cvay9ud0FEWFFSUGRhMHZ3OS9FNG5mUlFkVkVSWSs4T3Q4bnl5elpicG93NmFxdEpNb2RmMjBjK2ZuNEx2b3dOZjZleENrZkJ3Nk5lbytmcWttS1l4cmtNTUZiSzZXSTU1R05odnFxRUJnSkJ3MHdpbzRNUEViZmRoMi81UXFjck9LdXMwNHZBeHNSL1RyOW1TVFJPd1JrSmJiMVNhSDhsY2Vpck5uNThUaHc5ZFNaVFh0TFhEZWViaFRNcFJmTjNqRXF0dTlCaU0ycVN5YTQ5T1UyQzZxZ3poekFTRkZnSy9laFpLYTNnbUY1Z2tjV1RpOWkiLCJtYWMiOiIxYTJhY2FjNTFiZTdhMzYyMjgyMmI4ZTM2MGVjZjZlNjY2MDJkYTVlOWZiNzBiNjI3NTkyMzAwOTEyODE5ZTJiIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:01:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImRJYVBOOXFxcm5aNTVvZlU4VkltZXc9PSIsInZhbHVlIjoiQmhUczFjTnBad2E2UEpGWHBneldYaGRIWlpPd1dIU0k0Vi83RHJubHBUMGIzUW5KUzVpYnVMQ3A3RlNuMXA3bjQ1aXhleERuQTk3YktQR21pTG1USkRNb0dCbXFqV1JTSlI1UVhWRVErU2c5emljS1I2T3RRL0ZBdnpPK3E0NWF2V3B0aFo0VHpwZUcwa3hKbFZhaFdUMVFHcXFxUzhsV1F1QkRYc3NQMlU3L0ZsRHUzOXBBRGRVMnNIcEY4MVV4QTA4aEpVTEtBQlF1WFFCZWlML3NDS1BJa2I5Wm83UHhocW5sbFV5YytJNHhqRDFhWWlPNHluUWJvenFuUW5WS0VMMmlTc2VFbFhKb2p3aXdDSFB1b2xEOHdCdkZZZzJTL2tnUjVRWHVKeW9kVlFvaldkMHZRMERObVgwbGVRUjNEYkdzRjhJWStRdmZnb1l2TFBnaEwwU3EzTmhGeThRVm1XblByaTF1aHFyWEVwbTg2VUJRUFc3Tkh6QlRqQnFFay9jdVFPUXF3VE9NWmZPN0NhRURUeXhXOFNRNlR2WkpwR0pmanFSdkxVUEpVK2QycWxLOER6SjhxWlRsdzZHek5VZlB5Ry9XU0l5WFZIVTZxRFNWZFZJSEU1eUFSc3pLWW5PODIvV0M3eGV6TkZRT24ySWIrU0Nzc1Bha1g0cDIiLCJtYWMiOiJkN2MxNzU4ZDIzYTRhODA2YzhjY2EwMWMxZmEyZTlmY2Y5NjQ4OGIyZTMxYjIxYzcwNDc5ZDg5ZjhlZTIwYzZmIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:01:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im1DWkJleU9vUHNQV2NqcVd1aDllU3c9PSIsInZhbHVlIjoiNW9WM1BWSUhpaVBVUFJhTzBNbGQ1TkJqV2ZsR2c2a0NtWmd1aUxZdXFHTU40WDRRay8vdUMwNDduZXE1UWRjMkczNEt2Vllhd0Fla1BZNlZmb1p3K1g4WmtJWnlDc0xsWnJFcDNCdjdEMVJPUjNUSkw4MUhEZmYyMlQxUitNMEdKNVNmN0hZb3FVRDBXVmx2cnl1MXNrdjVKUTF1eVdtNkpwaVFrUWovRk5qamIremhWSlltWjdzVnFRTlJ2Q2tkbnQwNXVHZG5zVFNGazI5ZTl4VU0rZ1ExamI5QTAyemRpNDIxWWRFRWlzc041R2hwYjhPekhNRWhUWUh4SHJaOFBPTEF4Y0l4M1dKL1cvay9ud0FEWFFSUGRhMHZ3OS9FNG5mUlFkVkVSWSs4T3Q4bnl5elpicG93NmFxdEpNb2RmMjBjK2ZuNEx2b3dOZjZleENrZkJ3Nk5lbytmcWttS1l4cmtNTUZiSzZXSTU1R05odnFxRUJnSkJ3MHdpbzRNUEViZmRoMi81UXFjck9LdXMwNHZBeHNSL1RyOW1TVFJPd1JrSmJiMVNhSDhsY2Vpck5uNThUaHc5ZFNaVFh0TFhEZWViaFRNcFJmTjNqRXF0dTlCaU0ycVN5YTQ5T1UyQzZxZ3poekFTRkZnSy9laFpLYTNnbUY1Z2tjV1RpOWkiLCJtYWMiOiIxYTJhY2FjNTFiZTdhMzYyMjgyMmI4ZTM2MGVjZjZlNjY2MDJkYTVlOWZiNzBiNjI3NTkyMzAwOTEyODE5ZTJiIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:01:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImRJYVBOOXFxcm5aNTVvZlU4VkltZXc9PSIsInZhbHVlIjoiQmhUczFjTnBad2E2UEpGWHBneldYaGRIWlpPd1dIU0k0Vi83RHJubHBUMGIzUW5KUzVpYnVMQ3A3RlNuMXA3bjQ1aXhleERuQTk3YktQR21pTG1USkRNb0dCbXFqV1JTSlI1UVhWRVErU2c5emljS1I2T3RRL0ZBdnpPK3E0NWF2V3B0aFo0VHpwZUcwa3hKbFZhaFdUMVFHcXFxUzhsV1F1QkRYc3NQMlU3L0ZsRHUzOXBBRGRVMnNIcEY4MVV4QTA4aEpVTEtBQlF1WFFCZWlML3NDS1BJa2I5Wm83UHhocW5sbFV5YytJNHhqRDFhWWlPNHluUWJvenFuUW5WS0VMMmlTc2VFbFhKb2p3aXdDSFB1b2xEOHdCdkZZZzJTL2tnUjVRWHVKeW9kVlFvaldkMHZRMERObVgwbGVRUjNEYkdzRjhJWStRdmZnb1l2TFBnaEwwU3EzTmhGeThRVm1XblByaTF1aHFyWEVwbTg2VUJRUFc3Tkh6QlRqQnFFay9jdVFPUXF3VE9NWmZPN0NhRURUeXhXOFNRNlR2WkpwR0pmanFSdkxVUEpVK2QycWxLOER6SjhxWlRsdzZHek5VZlB5Ry9XU0l5WFZIVTZxRFNWZFZJSEU1eUFSc3pLWW5PODIvV0M3eGV6TkZRT24ySWIrU0Nzc1Bha1g0cDIiLCJtYWMiOiJkN2MxNzU4ZDIzYTRhODA2YzhjY2EwMWMxZmEyZTlmY2Y5NjQ4OGIyZTMxYjIxYzcwNDc5ZDg5ZjhlZTIwYzZmIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:01:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-73579357\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1754494397 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1754494397\", {\"maxDepth\":0})</script>\n"}}