{"__meta": {"id": "X87e3806a36c40f1fe618c6e7bc6f36a9", "datetime": "2025-06-17 14:36:04", "utime": **********.435917, "method": "GET", "uri": "/customer/check/warehouse?customer_id=10&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750170963.633818, "end": **********.43594, "duration": 0.8021221160888672, "duration_str": "802ms", "measures": [{"label": "Booting", "start": 1750170963.633818, "relative_start": 0, "end": **********.314775, "relative_end": **********.314775, "duration": 0.6809570789337158, "duration_str": "681ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.314787, "relative_start": 0.6809689998626709, "end": **********.435942, "relative_end": 1.9073486328125e-06, "duration": 0.1211550235748291, "duration_str": "121ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46219624, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.023979999999999998, "accumulated_duration_str": "23.98ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.376947, "duration": 0.02166, "duration_str": "21.66ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 90.325}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.415332, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 90.325, "width_percent": 4.796}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.423468, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "kdmkjkqknb", "start_percent": 95.121, "width_percent": 4.879}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-1443408047 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1443408047\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-504095113 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-504095113\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-471345410 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-471345410\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-910403964 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlBIZENMSFNZVW9TL0hsY2tIeDBzUHc9PSIsInZhbHVlIjoiZ2dmMDRjbmNzVlNLNkEzN0FSRFRvLzl2Q0RMYXJwU3A5R3RwMUk0dmFQS3IrLysrK2ZrYm1NbndPMUJDMHFqTnFzZVI0Z2wvR3habENyZVNXWHBCT2FPWjdqdHBuMzhQZFViRHhtb2ptWXRoZUxtN3VUSm5tcEh6RDUyQStVb3RCa0VMUTZDeGVteWF4VXVJNHlkWWZkREt6SStxeSsvUjNEV0M1VUlPZzZhdTlPeS91dXhoV3VYclZVREVTaENITG5KcHBxa01RT3NHUU8vcnZFcGZOK0wxUitpQ2kzUmtOT2dOU1piT3JvczFEc1NibFV5QUhqYTNCTnRBWHArRDYvNDltR0RlMUdQTWFnRytuOGo2SU1HT3RIcnJrSVBlaERCMFpYanB4V2E2RGxOUUU3all3WjJPRnVzVHIwbG5PcHJmRm5zcW9XdG1qOERXcXMwc1Q2ZUttR2RSRHVwd3VoMGcxWFREU1dmTFdDT3R0ZmJjaUovemtwRzd2NGNzdkdKRXl4MUtPV25rSE5TRDNWNDZFQldqTXhZTVg5b05DTSt6Y2JIdjBoVEdIYmdsb05UVjlDaUFYVi9waEpjSkRsQm1UQTRtaUtXdWUxMHRsV2h5TUhFTXR1SXMwcjdKK3ZXTmwrYkJScFF5MXVicE5SQURxay9qNytVblpUdVciLCJtYWMiOiIwNDkzZDc1MGMyZGFmNzAyMGM0NjJjZDM1OGJlYzc2MmQ1N2RhOGUwNDMzMDJkZjk2NWZlY2YyNDAwOGY5ODIxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InRpMW83RjZPK1A2SEV2NHRsa3dKenc9PSIsInZhbHVlIjoiNU5zMlQ4bXpWN0F3WWVLWEZBMkUyZlFuRkNUQmt6ZWwxWHNwQ1lvRkxEblZSb2k1MllUUkpzRjUzVlljUUplYUd4ZURmbGo4cWhJZUg4VW0yZUtRSzVsbDhORVF0Z05IaWduaGhoTklsK0xNOGNMc2hlOUorbGNtRkxIVlM3VmRkVTF5UitFbG1uRTVJZFN2N3ZYN0w4WnpkOEhQR2RCckxlQmtoanlHK0ZES3NFSU44NFBKU3FoRXN6eFNOMnZ1TEo5TFpuU1A3dUROek5mS2xPZ3NIeW1NcVRoL2pqNEtodG5QaCtKbGV2OW5iZ0hGU1dhQTVmMGhaeVI0N01oTEJkTCttTTFJK2NINUh0bS95eVNNa1JOUndXMTlnS3plSHh1SmxrYS9NekNldEZSRjZFY21zRUczMXNBK0FNaWt2bG5udDdMVlk1U1d2MWV1V0F2YnEzMXdHYnU5K1FhR0E4RUhtTXR6ZFR1NUFBTUdGaWtOYlNEOTl2eTNKdVNSTWVCNUZnV1RKcWNISk5IV0JjT09EU1Q2STVRSGI0K25vUHgyRitnZmpmV1RRdEJ5a3F5ZVJUYU9VNmEzUEZ3dkZsc1dMSjR0N2FJc2hVL1Q2aVhNTE9ybENSR3BNZXdGZUhSc29DTGlKR0sxVjZVS2V2bTVsaUg5VnBEMitwdC8iLCJtYWMiOiJhNjU5YWE2YTExODdiNGQ2NDJkYmIwYWMxY2M4MmYwZDhkZDI5ZWYyNDg1MDM1MGEzMjkyZmMxM2RlYjExMTZlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-910403964\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1653013066 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1653013066\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1657547449 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:36:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZXMGQ3dXdVVVZMaU9sUWVYUWFlWHc9PSIsInZhbHVlIjoiZjNoRHY0aU42UHVkd3NtQk02cWpHY0NyZWNwNFU0SFM1RjFiNWVXK0RaQlV0Q3JFSk16UXpwWlBEbmdYVUJ6UkhhYzFNbXFzMFhtL0E2cUo0cVliTGsxZ3didEVHMWRodlUwSHh3WlNOSFNtb0I1M243TGdkdVljWlZ6a3MyM3FoeUVRUWlNUmRTWWQ3RmF6MzJkTzFGdFNlOEF3WmtheEVhaytlOCtaa0lvYWk4UWhJQ1VEcTRqcXpiUzluN0RlVjZPTkJLdHFzTzQxeUt1WThNZDdMK0prSVNpaWdLUHNDaFZwV0xwa2cvZjJRb1N1cXNNakRlbUZmbkwxZVNpcU9qU1NPaThUdGdwZVdyYVgwMFkvRjFXN1RCZm5pMjZ6L2QvaU5HMzA4SXR1U2ROdGE5MWRtZGRxUmJwY3YwcnZDb2U4cFVBaG1Uck5RemtoRytMa2lHNElpZXM5UnpzRW1VeSs3aXJyTWIwMDgrK1Q2Yi9FcmQ2ckdKbWZsZFgyWUFubVA5MnlSV21vc3BsOEZpcWFrL3d1YlFoSmRTZG5DUjBObTgvSVlMbERWWXFyRU1lRm95NmNwTmlWSU5FWDlZdUNXT2VmcXVrS2Q2ZkxnaHNTVnpzK3NSK211a2lmbUVRYXdKSHd5cjErL2x3d3hndlhNRVVWSVZJOExrbm0iLCJtYWMiOiI0N2Y2MDdhNDc1NmQwNTQ0YzU3ZDY1ODMyMjQ1MjZiMmIyZjZkNjlkZWQ3NmFlNjE5ZmE3OTAxNzNiNTZmMmU2IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:36:04 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InphWWh6ODBRenNSQnMrSVpNakVNL0E9PSIsInZhbHVlIjoidENyMTlmalQ4Wk9HTURaYndxWjFzbDhLM2hoT2xJdUcyREdsQlNYdXVnc08wVFU3bnlYb1N6YWhGQXNnU0xGQkxucG1yU1JqRHNZSmJCR3l4UC9kRU43MnVJSUZlTU5ZZEhZSTZaVjZ0VzdneHY0VERRZDV6a0JuQXZlOXlYTnFtcjJlTElPWmJQazlZeTlKR1J4d01kV2djM3RKMHRSakJzekFwYkdEdFFQOTlLUkQ0QzUzcERzNmpKbWdHejZCbzFGT2JrUHNaOVhMOFFFVlJCWG8zSkdFcWZZOUxVNE5TMFVWMWt3Yy8xWEZtUmJBZjY3ZEhCdXF2Z2tqcExyQUt4RWR4Z3hNSVdWUTVYd0w3emVBalVXWGJZVnAvRGtUZ2d2Y2NBU014bWVILzdCaTExNFM3RTNwL0JaTTN6Ylo5U2xybHpTUXQ5ZmU2MkdGRmFnK1lHTFlXcjhhN0NWMnRuN1p2bzhoeWI1Q2MySUZvU0JxVTJnRXYyNW4yK0FvY1UvSUgxRVFwaHJFN1ptc0xhaEpVQUxITFg2SUhKMllHYlJvVEQ1TUYyT3ZQK29nckM5ODAwMk1FdmtkVGlyeVVuNjE5Y1ZBMnZCUnY1SjR1eXZYL1JZUXBVTERWeUxaWDg5bUxjU3N1R1lGRzdRNUtEM01VLzhkbnI1WkxacHYiLCJtYWMiOiI0NDhmNTdiZTBhMTIxMTZjNGI4NDhhMTU2N2UwNzRlOGNiZjYzOTRlNzEzNzA2NDJmOWMzMzVmZWNjZTgwNWI0IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:36:04 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZXMGQ3dXdVVVZMaU9sUWVYUWFlWHc9PSIsInZhbHVlIjoiZjNoRHY0aU42UHVkd3NtQk02cWpHY0NyZWNwNFU0SFM1RjFiNWVXK0RaQlV0Q3JFSk16UXpwWlBEbmdYVUJ6UkhhYzFNbXFzMFhtL0E2cUo0cVliTGsxZ3didEVHMWRodlUwSHh3WlNOSFNtb0I1M243TGdkdVljWlZ6a3MyM3FoeUVRUWlNUmRTWWQ3RmF6MzJkTzFGdFNlOEF3WmtheEVhaytlOCtaa0lvYWk4UWhJQ1VEcTRqcXpiUzluN0RlVjZPTkJLdHFzTzQxeUt1WThNZDdMK0prSVNpaWdLUHNDaFZwV0xwa2cvZjJRb1N1cXNNakRlbUZmbkwxZVNpcU9qU1NPaThUdGdwZVdyYVgwMFkvRjFXN1RCZm5pMjZ6L2QvaU5HMzA4SXR1U2ROdGE5MWRtZGRxUmJwY3YwcnZDb2U4cFVBaG1Uck5RemtoRytMa2lHNElpZXM5UnpzRW1VeSs3aXJyTWIwMDgrK1Q2Yi9FcmQ2ckdKbWZsZFgyWUFubVA5MnlSV21vc3BsOEZpcWFrL3d1YlFoSmRTZG5DUjBObTgvSVlMbERWWXFyRU1lRm95NmNwTmlWSU5FWDlZdUNXT2VmcXVrS2Q2ZkxnaHNTVnpzK3NSK211a2lmbUVRYXdKSHd5cjErL2x3d3hndlhNRVVWSVZJOExrbm0iLCJtYWMiOiI0N2Y2MDdhNDc1NmQwNTQ0YzU3ZDY1ODMyMjQ1MjZiMmIyZjZkNjlkZWQ3NmFlNjE5ZmE3OTAxNzNiNTZmMmU2IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:36:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InphWWh6ODBRenNSQnMrSVpNakVNL0E9PSIsInZhbHVlIjoidENyMTlmalQ4Wk9HTURaYndxWjFzbDhLM2hoT2xJdUcyREdsQlNYdXVnc08wVFU3bnlYb1N6YWhGQXNnU0xGQkxucG1yU1JqRHNZSmJCR3l4UC9kRU43MnVJSUZlTU5ZZEhZSTZaVjZ0VzdneHY0VERRZDV6a0JuQXZlOXlYTnFtcjJlTElPWmJQazlZeTlKR1J4d01kV2djM3RKMHRSakJzekFwYkdEdFFQOTlLUkQ0QzUzcERzNmpKbWdHejZCbzFGT2JrUHNaOVhMOFFFVlJCWG8zSkdFcWZZOUxVNE5TMFVWMWt3Yy8xWEZtUmJBZjY3ZEhCdXF2Z2tqcExyQUt4RWR4Z3hNSVdWUTVYd0w3emVBalVXWGJZVnAvRGtUZ2d2Y2NBU014bWVILzdCaTExNFM3RTNwL0JaTTN6Ylo5U2xybHpTUXQ5ZmU2MkdGRmFnK1lHTFlXcjhhN0NWMnRuN1p2bzhoeWI1Q2MySUZvU0JxVTJnRXYyNW4yK0FvY1UvSUgxRVFwaHJFN1ptc0xhaEpVQUxITFg2SUhKMllHYlJvVEQ1TUYyT3ZQK29nckM5ODAwMk1FdmtkVGlyeVVuNjE5Y1ZBMnZCUnY1SjR1eXZYL1JZUXBVTERWeUxaWDg5bUxjU3N1R1lGRzdRNUtEM01VLzhkbnI1WkxacHYiLCJtYWMiOiI0NDhmNTdiZTBhMTIxMTZjNGI4NDhhMTU2N2UwNzRlOGNiZjYzOTRlNzEzNzA2NDJmOWMzMzVmZWNjZTgwNWI0IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:36:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1657547449\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1642332295 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1642332295\", {\"maxDepth\":0})</script>\n"}}