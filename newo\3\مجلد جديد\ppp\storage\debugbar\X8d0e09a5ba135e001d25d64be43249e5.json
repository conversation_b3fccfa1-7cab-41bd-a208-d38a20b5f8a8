{"__meta": {"id": "X8d0e09a5ba135e001d25d64be43249e5", "datetime": "2025-06-17 14:39:20", "utime": **********.656129, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750171159.793866, "end": **********.656159, "duration": 0.862293004989624, "duration_str": "862ms", "measures": [{"label": "Booting", "start": 1750171159.793866, "relative_start": 0, "end": **********.520407, "relative_end": **********.520407, "duration": 0.7265410423278809, "duration_str": "727ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.520429, "relative_start": 0.7265629768371582, "end": **********.656162, "relative_end": 3.0994415283203125e-06, "duration": 0.13573312759399414, "duration_str": "136ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46131008, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0075, "accumulated_duration_str": "7.5ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.592387, "duration": 0.00511, "duration_str": "5.11ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 68.133}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.620706, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 68.133, "width_percent": 13.733}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.637489, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 81.867, "width_percent": 18.133}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6", "_previous": "array:1 [\n  \"url\" => \"http://localhost/inventory-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1399307322 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1399307322\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1544703843 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1544703843\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-776899382 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-776899382\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-349040136 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=3pzjoc%7C1750171148552%7C7%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlpYV3hOcmY3a2JJQjNQcnlqeUZmVWc9PSIsInZhbHVlIjoiQ0dZSXMxUDJXYUU1dklDaVpEVjJpdFhIQXlxODBramJFSjAzdDMxZzZZeUxNeFdEK0x2NFRlT3R2dHY4SGx5dGpCZ3U5cUM4TmxyODFUTDRTUnRhSzUzeFdLVmN4ZXREU1M3TFV2VjlsMGxpaXNoSlZLd01vaGNWZzc5eitMQ1FiWTluREJEQStEdGY1cVpSYUhya05KaHBzYmtseUVBdXpkTGFMc1Q2YkNPZVdxbHlOdnMxT1NoYUNJU0ZPOVJvb1FNVHpyMkpsNkpNZHNuci9sOHAxdXU5UFVBaXBSaWZ1VFFkWHJvY3lpbU93dVBDNFYydTRvdmhCdmJmZGNJUWl4M0w5NzZ2aENkQzcyWDVBY25OTmwwdExOdDFMVmRtSjlpcDRGMGtPQUJzWHRwYTFjU3QxbEhpM3ZHcitxWGptYmczb3YwWS9MT2I4UTJxaTdmMk5WM0xVTzhHcmdZNlpZcmxaSjNudWpDazBScDFCY0RreW1xZ3YzZjkzQzM4RU0zYklzZFBLbVN6Z2tlVmxhejlDRWNVUUxtN29OTXJKM2RaeWllTkRtbUhTNlZrTHpFN2hQYk05TWtiK0o4Q1I0UG5hYUI2bUNFQ2NvNER5Z1FqT2F4OGhhdWE4clNoWGNUZ0lMc3JEVXVrOXZkUVhTbldTL3dpcHNUVFRHSW0iLCJtYWMiOiI0ZGRkOTVkYmRjZTM4ODgwNWQ2NDlmNWRiYzA2NDY1ZDIzYzIzN2Q5OGMxOTc2MjM0YmY0M2FjYTI4NmI4ZTMyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImNERERaajZoOE5wOE1mSU5QKzRxVmc9PSIsInZhbHVlIjoic3VLcFQydHBJTnNBUlBFOHY5QnJ2UmRQRENDTnhtUlRURzYzMGtDVHM2QVltQmdXVFc0a21RSDdtMld0eEJqeldqUkF0YnpOOEgyRE5RZVVSMjlxVTJ2YTJPUTlxdzUvV2Jyb3VPektwdmtTbDdlNkdqT1hCZlBSWXdqZ3BlNnYrNjV1QUtDMlhsZUMxYXhXUUdtc0NvaTh0M0h0NFBBR2tOcWVEMUFrWHJyMDIwSVZLTlM5MzlSVlNmbUF6clhQTkdRMW5PdzdTblJ0MzhhcjJRVktQTzZJUWpjdmNnWkZGTkgvYjZUeGFWZ2RLM3JXK0Y3QWhYbkNSRk0wdHI0bTRxdEVyME1aWFBlRDViVXRNZ2lra0YzWCs5K0VTSnNkTkREM3dBRXZKWHhra0RqUFFKYVMyRmtvTUd5WXpZVkV5YlNkeGdNdFp5QWxkWjFxcmtZaDd2ZVkxNlJ3Y2E2R1piYTJKcGFVU2NKZVNvWnJFZ1VJR0ZXZkJ6K0pPdmxxejUxcHZLaTExSlBMN0orWXlRSmFmZW1mcEdpQ3dIbkNCVkYrc2xIQTBOUUdHUi9BcEx0aE55enNkdlJPT0QvZmtodFVKUGlWZysyd091a0FTMGppbW5uL21SKzdaL01YTUcwRnowMUNqQWViS3o2WE5pSTVuQzNCOGY1RmtpODIiLCJtYWMiOiI5MzUwOWFjODUwMTc0OTk4MzcxMDRiMWZlZjI0NTc4M2JjZDA4NjI4YzMyNDEyNjgwOTg1NGMxMjdhMDdmMmQ2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-349040136\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1244281750 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HxnOnSuhAAPTfN2VS4q58slgJoytwtHYqaX0oLoW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1244281750\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:39:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjNiQzRBWGZlSE01bUpUTWN3Q1RWL2c9PSIsInZhbHVlIjoiQ0t3WU1WN3VGQm1MOFNOZE9Ha3lxT0ZoUjFwTGd4T3gveHRhMEkwYWhiR3VjTGNMbGdnVWxYVVd1V05Ya0ZVcHZEYXJrMXJTNkJBSnNDKzJ5NnJWMzRFaGQwVGdyZzNQOHNKcllPaFZKUG5XQnovc2J4eEY0Z2o0bW9CWnIxd3FMbi92Z1N3UXFRNVVRSWpMWnRDZHJqaTRaaVpzbkI3eVF6L25hc0wxTzkyOHloaVozT21kRGcxZjliMUVHcmI2ZnhWTnNCT0tBcG5SMHdQbG9lM3Qwa2h2d1BTNEZGaEZhUFl1a1pvdHNBaWJscHlBaDBkMks4bEs0MzhPMUcycCtZYXdmZkRPVEFqSFNUL0lITHViejF6c1ZXTUp2SkU4M2pJUmVlZlpMMHRubU1lQ1RMK1Q2anR1NEVBazE2ZFRIc09xeXVSZmxLMGJyZjAzMnBMcXpxZ0piZ2x2RDhjQTFGbHROTEttRnh0dDAxUVdWdHdjUnB3NjhSN0VuKytObFZRVXFVYTgzalV2NlVTdjFkaUZSb09hbWNybTAxT0NYZ1NvM3ZpRndtdGZjN3FGRWxsQlVwT2loYU1xVjNkbCtRb3BkWitPTzZjTkx5RHVMQVVxeUhmSmQ4Qi9zMGJJeVFUZktlZHhBQi9wOWlvSFNIY3RpNUlGWVNwMWRHd3AiLCJtYWMiOiI2MjZmOGE5MTA4NTE3MjAwOTFjMWE0OGIzYjNiM2I2OGI1NGY3NTEzZTdmMjVmMGZiNDhkNGVhN2JiZWYxZDNhIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:39:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjI4VFI4OVo1eElZaWZ0QVB4OHlrbXc9PSIsInZhbHVlIjoieHJrNmtKeDkzZ0FTeUszcGN6bXlreWVuZUdRL3AxM0hzbDRTWnN5blY0VU9rd2VMRjc3aFQyVktoNVluOG5uRlNTRGxrK3FnSkVMS1lnT3RrUy9RUGR0NnhLSTM3cEtvK0U5SkU1Z0dOdWhNSGpJanZ5dVNvN2tpeGtjaFRlMFJXeFFsdDZlSVZLOGZ0Y3IyNjBRNDg3SlplZ0p5NGxlTHBLS016b2RZeG9RRFFxYlVyclFGak9KQjNPQ1EwT1ExMm1OK3paUEtYNk9RYnhvMzBSQjNUTkZSNGMvRXJSaXlyUXlobllxdnRiU2tUdmN1RXcrTDdoeDBCOUN6NExsc0kxazdrcmZ6QUtOaGhBVGluZ2hPb1dzck0vbVl1Vy9sbkgxT3JNMG1QQVNVR3BJZlJBVmljYU1tTkhXV2o5N0NQVGI5WWtxdEg1cHV3UUE3YjlzMEZ3bGttM2cxb1NEbzZDWDJUaGkzd29UTi80WEY4UjVQZ1BLOHhJZ3RTakFZeXAyWGlEZFhuUHYyZ1JsNUp0Vmw5d25oZUZpOXZ0MGFKQXZRUmp6RzVJcjB4UG9UMVllY2x0Nk05dHl3WVd4TmVxbWJxZ01mVWFkdmsyKy9wR1ptWk1NZTNWeUhqOFc2SnFMbG5ROFlITlBYRElDcEZXSEhsem5wQ1ZXYldvSlMiLCJtYWMiOiI2MjUxOWM1N2RhMmM3ZmMxMTIxNzA3MmNhOGViYzY5NTBjZjNmZmFmNGJiZTE4MWY3OTNkNDliMGIxYWI1Zjg0IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:39:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjNiQzRBWGZlSE01bUpUTWN3Q1RWL2c9PSIsInZhbHVlIjoiQ0t3WU1WN3VGQm1MOFNOZE9Ha3lxT0ZoUjFwTGd4T3gveHRhMEkwYWhiR3VjTGNMbGdnVWxYVVd1V05Ya0ZVcHZEYXJrMXJTNkJBSnNDKzJ5NnJWMzRFaGQwVGdyZzNQOHNKcllPaFZKUG5XQnovc2J4eEY0Z2o0bW9CWnIxd3FMbi92Z1N3UXFRNVVRSWpMWnRDZHJqaTRaaVpzbkI3eVF6L25hc0wxTzkyOHloaVozT21kRGcxZjliMUVHcmI2ZnhWTnNCT0tBcG5SMHdQbG9lM3Qwa2h2d1BTNEZGaEZhUFl1a1pvdHNBaWJscHlBaDBkMks4bEs0MzhPMUcycCtZYXdmZkRPVEFqSFNUL0lITHViejF6c1ZXTUp2SkU4M2pJUmVlZlpMMHRubU1lQ1RMK1Q2anR1NEVBazE2ZFRIc09xeXVSZmxLMGJyZjAzMnBMcXpxZ0piZ2x2RDhjQTFGbHROTEttRnh0dDAxUVdWdHdjUnB3NjhSN0VuKytObFZRVXFVYTgzalV2NlVTdjFkaUZSb09hbWNybTAxT0NYZ1NvM3ZpRndtdGZjN3FGRWxsQlVwT2loYU1xVjNkbCtRb3BkWitPTzZjTkx5RHVMQVVxeUhmSmQ4Qi9zMGJJeVFUZktlZHhBQi9wOWlvSFNIY3RpNUlGWVNwMWRHd3AiLCJtYWMiOiI2MjZmOGE5MTA4NTE3MjAwOTFjMWE0OGIzYjNiM2I2OGI1NGY3NTEzZTdmMjVmMGZiNDhkNGVhN2JiZWYxZDNhIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:39:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjI4VFI4OVo1eElZaWZ0QVB4OHlrbXc9PSIsInZhbHVlIjoieHJrNmtKeDkzZ0FTeUszcGN6bXlreWVuZUdRL3AxM0hzbDRTWnN5blY0VU9rd2VMRjc3aFQyVktoNVluOG5uRlNTRGxrK3FnSkVMS1lnT3RrUy9RUGR0NnhLSTM3cEtvK0U5SkU1Z0dOdWhNSGpJanZ5dVNvN2tpeGtjaFRlMFJXeFFsdDZlSVZLOGZ0Y3IyNjBRNDg3SlplZ0p5NGxlTHBLS016b2RZeG9RRFFxYlVyclFGak9KQjNPQ1EwT1ExMm1OK3paUEtYNk9RYnhvMzBSQjNUTkZSNGMvRXJSaXlyUXlobllxdnRiU2tUdmN1RXcrTDdoeDBCOUN6NExsc0kxazdrcmZ6QUtOaGhBVGluZ2hPb1dzck0vbVl1Vy9sbkgxT3JNMG1QQVNVR3BJZlJBVmljYU1tTkhXV2o5N0NQVGI5WWtxdEg1cHV3UUE3YjlzMEZ3bGttM2cxb1NEbzZDWDJUaGkzd29UTi80WEY4UjVQZ1BLOHhJZ3RTakFZeXAyWGlEZFhuUHYyZ1JsNUp0Vmw5d25oZUZpOXZ0MGFKQXZRUmp6RzVJcjB4UG9UMVllY2x0Nk05dHl3WVd4TmVxbWJxZ01mVWFkdmsyKy9wR1ptWk1NZTNWeUhqOFc2SnFMbG5ROFlITlBYRElDcEZXSEhsem5wQ1ZXYldvSlMiLCJtYWMiOiI2MjUxOWM1N2RhMmM3ZmMxMTIxNzA3MmNhOGViYzY5NTBjZjNmZmFmNGJiZTE4MWY3OTNkNDliMGIxYWI1Zjg0IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:39:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1119082840 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1119082840\", {\"maxDepth\":0})</script>\n"}}