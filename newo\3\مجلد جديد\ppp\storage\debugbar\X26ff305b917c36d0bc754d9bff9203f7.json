{"__meta": {"id": "X26ff305b917c36d0bc754d9bff9203f7", "datetime": "2025-06-17 14:01:29", "utime": **********.365599, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750168888.743992, "end": **********.365639, "duration": 0.6216468811035156, "duration_str": "622ms", "measures": [{"label": "Booting", "start": 1750168888.743992, "relative_start": 0, "end": **********.233929, "relative_end": **********.233929, "duration": 0.48993682861328125, "duration_str": "490ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.233944, "relative_start": 0.48995184898376465, "end": **********.365642, "relative_end": 3.0994415283203125e-06, "duration": 0.1316981315612793, "duration_str": "132ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49205496, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.02587, "accumulated_duration_str": "25.87ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.280363, "duration": 0.02172, "duration_str": "21.72ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 83.958}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.315094, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 83.958, "width_percent": 2.938}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.337925, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 86.896, "width_percent": 2.783}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.34132, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 89.679, "width_percent": 2.513}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.348388, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 92.192, "width_percent": 5.334}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.354248, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 97.526, "width_percent": 2.474}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1971371373 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1971371373\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.346901, "xdebug_link": null}]}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1586171929 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1586171929\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-478847485 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-478847485\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-19776868 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-19776868\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1579248484 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InFCa2JDZU9FTldrU2NhdUhzaTlzc3c9PSIsInZhbHVlIjoicmlmbkNUVGVwOFk3bTNlckVOdjcvQitZOU5OWGhJT0FDS2pKOWNzZFczTExNaGJZOFRoaVZiU1FxSk5Qc1lETy9aRDhLNExma0NjTHo1dm1IcVV0Sng0U28vMk1QbkoybzQ1ditONFNGd1RQSFN4UnBCL1Z0VmdsUDA0c1J5b0xROTZKNTJsV0ZEY29nQ1diQUNSVG5ualdyZDJxWHdxaGJSSytMSGxZU1I4ZDg3RDZTTHYzajNTUWszVGZBNmZtOHJEbzd4cVVqbXJtdzJidjljQXgrcjhHcU84RWZ1T1phdlZidXdtNjFpMTZZVlhvT2JwRWJLenphdmFJV0k3U1BwdTA2Q01qRFFFU1RTdnpjVEZNREViU2xtTTNEOFFmbXJWeDk1dU1jbDVxR1NZcExPVWE0bVV3UFBpKzBtdDNyR0kzbXRmR0V2VE1JY0oyUUJ0Slp3YktoTlBBZ0ZxNXI4MGVmWUNJVnk1WHd5emx6RWZBZjZUanB5M2xJUnZGOXJCejMvK3hlaUhLd2h1TmJhOFB1c1pVOGduYVpTb2dhaHVLelhrc2hLb3Y0Ylo4cE1BWkNVUnZyMmh5cXd0TWNVYnd2ekdybUVzTWJoQ0dIZ081RmcwQXo3T1hNc2xKa2RXVGJJWHVGL2JkSmxrdUZYUkxaTG1SdWhHWHljczYiLCJtYWMiOiI1ZjJlYmI3YzBmZGZhNzZkMDExNGRhZWFlMDQ3Mzc2Y2U3YWVlYmM3NjQxMTc3OGI2MDBjYTBhODYzMDdjYzk0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjdBU2tOQks4OWUwYk9TelJHQlBSY0E9PSIsInZhbHVlIjoiTm9lOVYyVWtONm4vYUVwNUU4UHJIK3JHamNMdTlxTU04bFd5NTMraGpwZXFCSnNMYktKR290NkNJWEQ1QmxuS0crVEF5VEZSaFJSS05PWXduNFF3R1pMcXBGQ1ZnbUxyNTV2Tkl1Z0lGZVE4SFRETTdTTmdTWjBMQmx5WkpqQ0c3bVk1cUZGQmlSTklib2dBRlhxVEFzcy92TEpNenVoSGRvYXlmMHIrUGZiT0tsaUVHODlaQjFXZFRHeWI3QXdXcUc5Q0VMajVFS0JldGNNb2h5OTNZR1BUNnpLRmwrTW1rNmFZTkZNZ0E3dkhidGkrL3lwck5SNkU5VG8vNkVRYVZKN0c5STArV1BRR00xZFR5a0dQYVlKUzg2bWtzcXdJZEdnV3RNbFh4OW01WkNlWGc5U1hDZUl1QnYzVlVKc3B1c0hZZmhyYVBUdWc4UlpzUWVneFUrUndzc1Q4NXMzMTg1enZteFNmMWtqTWxOZFNydXdRTFUxczZMY3JaejVIMi8zUjJRTHRKbWZ4K3puWVNTKzJEMW52UXI2ZHZoK1pxTi9SM0kyU2lmTmU2Q1hFUE9GMjJ5VlBxa0IzQ2JtZWdoQUVnUVp3bDJlRE0wL0pIQlRVMVh3SVY5cmZXbEtYb2VDNWFobDdJVEo5TWZGRjl3ZTZmOHlkejE4SndDMHgiLCJtYWMiOiJjYWEwNDg0ODM4NDhiMGVmMDk4ZTVlMDgzNjk2NGZiM2VmMGM0ZWZhZDMxMDY5M2MzZWUzMGY3N2FlMzk5Y2IwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1579248484\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1650121969 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1650121969\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-382510545 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:01:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik1RUGVYeDdoY3RYckgxaDJtay91OGc9PSIsInZhbHVlIjoiZkxqQmZ1cmIrdEIvNGk3TDRDb3UzL3ZaQ2lzc2Ircy9GR1o0Tk9nSktxT1grUmdLY3NXdTVEa1lYb2ZQblZzVTUyellTUlM0TDdUSkQ5aEI0aWowdTZwWnc1V2U4ZmIvQzQ2eU5KbE9raXE2ZzBwSGNkUHQxZndLbHVRM2VvSlVLMDY4WGc1QUF3WThqR1pDdVFYVzlZUEVGdWJPRVdXaFlpMGIzOGFNa0NqWWUrTy9taGVnMllHZWt1TXN5VEdJSUNkTGYxWkFHVTVDYnJHa1ZJODVJdWlsOWpZMmF2aUhER1Vwc1RuMkJmWklVSWMvYVMyaU5sYUVRaDVwVm9nRzdOc3pJdFdUeU5BNm16SmhCdnNMSmZ1bHE1bkNYL2hYZGRxUU5Vc29xS2lDVDRBaUJEdzJtSVdTR1Z5MmpBaHMvRFRST1l6Z0pjY0k3YmljR2t0VUtKdGlSQU9rQzR2eSt6SmJiT0NOaTZDVDNaQ3BMSlNTckNVYmFnM0w2d0hPeUkxbGZkRmdlWFNWblRmcjltN05OY1orYVczWUJwVlkyUVdqMklSb2I1MHRMUDVObmFhY1hoTFIrR0R4S2hBTGFUVENKZlpyV3E4bytzc1hMdmg3NW53YUVrVWx4Y0JTcU9ZbmVZTGh4WDBqc1VrSUY0L3ZSb3EzMEVDdDhGNjciLCJtYWMiOiIyNTQxODZlNGZkNDBmMjhmODQzMjgxZTk2NjFhZjc1NGNmZmYyZDhiOWIzOWY4OWJlM2ZmMGIzNDVmZDI3ODE5IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:01:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImQ4M014UEcxS0lVZ2xoYktVMjUydUE9PSIsInZhbHVlIjoicjhWVHFBemtjS3hsOGV1ME56UU92WTM5ZjVOak9UQmVZbVVaRGswY003cWV0c0xMN2w2VUtMY1hOZ1UzUXhDckUxZTZaT1dXS29pdm1QbXpNbEZoZDlKTTZHV2NtVWRiWklmRW9KWlovOVhWSkFDT3QzNlhKbFJFNmlaQ2s1Z05wWlhkMW0vM3hHdVhaYWV5YTVNNWZRZHRtMHdVNjRsc0VlNzgzMmZiblNtd1NWNzhPT1dmTDIvdU5CMVhLcGE1aDFnU2dFbU9pbEU2STZFT3dWNFZuaWU0bGtDZjBRNmNZcTZSdmM0R1IrN2ZoN3BJclJ5YzJNdjh4YW9TelBPTFhIVlR0MzlaMUpZS3NTRlpld3p1dmUvRnhady9rMmM4VGVXWVkwWWVoVi9HdUdLa2NHc0c4Ni8yYThSWHdBR3hRNlZ2c2V0K205Y2dWMnZobzE5YVdZS3lSS3dBejc3ZFVKaTErM0ZFcnJVSE1oWFNWRmV1UXBRRHhyRkh2eEFhMU0rc1dvdDQzdWR1NVM3clovOGZaWmpOZ1hhU2krZGZ2RkpuNS94Tzd0akE0TDRvKzZrSGRFU1pIQ3g3OWZmU1YrSVdOTGVLeDlKdDlIMkdiMWM0bU1SdEp2dzZLRDJSS01tY1VKbWdVRkZnc1hTc2F0NFg0TEo4V1M3cXpaNVoiLCJtYWMiOiI0MjQ0MjRiYzYyZmUxMDEzMjQ0YWU5ZjlhNDY0ZjFjOTIxY2FjNGVhMmQ1MDVjMzliMDhlYjlmNDYxMzRlNGYwIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:01:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik1RUGVYeDdoY3RYckgxaDJtay91OGc9PSIsInZhbHVlIjoiZkxqQmZ1cmIrdEIvNGk3TDRDb3UzL3ZaQ2lzc2Ircy9GR1o0Tk9nSktxT1grUmdLY3NXdTVEa1lYb2ZQblZzVTUyellTUlM0TDdUSkQ5aEI0aWowdTZwWnc1V2U4ZmIvQzQ2eU5KbE9raXE2ZzBwSGNkUHQxZndLbHVRM2VvSlVLMDY4WGc1QUF3WThqR1pDdVFYVzlZUEVGdWJPRVdXaFlpMGIzOGFNa0NqWWUrTy9taGVnMllHZWt1TXN5VEdJSUNkTGYxWkFHVTVDYnJHa1ZJODVJdWlsOWpZMmF2aUhER1Vwc1RuMkJmWklVSWMvYVMyaU5sYUVRaDVwVm9nRzdOc3pJdFdUeU5BNm16SmhCdnNMSmZ1bHE1bkNYL2hYZGRxUU5Vc29xS2lDVDRBaUJEdzJtSVdTR1Z5MmpBaHMvRFRST1l6Z0pjY0k3YmljR2t0VUtKdGlSQU9rQzR2eSt6SmJiT0NOaTZDVDNaQ3BMSlNTckNVYmFnM0w2d0hPeUkxbGZkRmdlWFNWblRmcjltN05OY1orYVczWUJwVlkyUVdqMklSb2I1MHRMUDVObmFhY1hoTFIrR0R4S2hBTGFUVENKZlpyV3E4bytzc1hMdmg3NW53YUVrVWx4Y0JTcU9ZbmVZTGh4WDBqc1VrSUY0L3ZSb3EzMEVDdDhGNjciLCJtYWMiOiIyNTQxODZlNGZkNDBmMjhmODQzMjgxZTk2NjFhZjc1NGNmZmYyZDhiOWIzOWY4OWJlM2ZmMGIzNDVmZDI3ODE5IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:01:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImQ4M014UEcxS0lVZ2xoYktVMjUydUE9PSIsInZhbHVlIjoicjhWVHFBemtjS3hsOGV1ME56UU92WTM5ZjVOak9UQmVZbVVaRGswY003cWV0c0xMN2w2VUtMY1hOZ1UzUXhDckUxZTZaT1dXS29pdm1QbXpNbEZoZDlKTTZHV2NtVWRiWklmRW9KWlovOVhWSkFDT3QzNlhKbFJFNmlaQ2s1Z05wWlhkMW0vM3hHdVhaYWV5YTVNNWZRZHRtMHdVNjRsc0VlNzgzMmZiblNtd1NWNzhPT1dmTDIvdU5CMVhLcGE1aDFnU2dFbU9pbEU2STZFT3dWNFZuaWU0bGtDZjBRNmNZcTZSdmM0R1IrN2ZoN3BJclJ5YzJNdjh4YW9TelBPTFhIVlR0MzlaMUpZS3NTRlpld3p1dmUvRnhady9rMmM4VGVXWVkwWWVoVi9HdUdLa2NHc0c4Ni8yYThSWHdBR3hRNlZ2c2V0K205Y2dWMnZobzE5YVdZS3lSS3dBejc3ZFVKaTErM0ZFcnJVSE1oWFNWRmV1UXBRRHhyRkh2eEFhMU0rc1dvdDQzdWR1NVM3clovOGZaWmpOZ1hhU2krZGZ2RkpuNS94Tzd0akE0TDRvKzZrSGRFU1pIQ3g3OWZmU1YrSVdOTGVLeDlKdDlIMkdiMWM0bU1SdEp2dzZLRDJSS01tY1VKbWdVRkZnc1hTc2F0NFg0TEo4V1M3cXpaNVoiLCJtYWMiOiI0MjQ0MjRiYzYyZmUxMDEzMjQ0YWU5ZjlhNDY0ZjFjOTIxY2FjNGVhMmQ1MDVjMzliMDhlYjlmNDYxMzRlNGYwIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:01:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-382510545\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-37926920 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-37926920\", {\"maxDepth\":0})</script>\n"}}