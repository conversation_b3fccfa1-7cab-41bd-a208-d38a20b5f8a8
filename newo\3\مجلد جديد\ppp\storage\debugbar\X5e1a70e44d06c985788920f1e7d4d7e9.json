{"__meta": {"id": "X5e1a70e44d06c985788920f1e7d4d7e9", "datetime": "2025-06-17 14:51:01", "utime": **********.533334, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750171860.86298, "end": **********.533358, "duration": 0.6703782081604004, "duration_str": "670ms", "measures": [{"label": "Booting", "start": 1750171860.86298, "relative_start": 0, "end": **********.433216, "relative_end": **********.433216, "duration": 0.5702362060546875, "duration_str": "570ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.43323, "relative_start": 0.5702500343322754, "end": **********.53336, "relative_end": 1.9073486328125e-06, "duration": 0.10013008117675781, "duration_str": "100ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46094584, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.020810000000000002, "accumulated_duration_str": "20.81ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.472839, "duration": 0.01925, "duration_str": "19.25ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 92.504}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.5090039, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 92.504, "width_percent": 3.508}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5213652, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 96.012, "width_percent": 3.988}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6", "_previous": "array:1 [\n  \"url\" => \"http://localhost/inventory-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-198053480 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-198053480\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-260556024 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-260556024\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1248998713 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1248998713\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1881964540 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2032 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; PHPSESSID=o1mek9etnn9ejsik46u9hjcosg; _clsk=17t60l2%7C1750171852691%7C6%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InVvS2RZYmFram1obHJ2cGtLOTUrVmc9PSIsInZhbHVlIjoiVll6WlZFRW1VajJOMkhOVk5pTmNxRTVvOEVIT3hGa1Y4elgxL3FzWW5oRVZLdkxyaThMRDN4anpWR1Y4VDNGZUhhOVp0RXh6Vi8rclRtWFFZTitsNzhRRS80VnhaK3JiUXRPc1FqZ3hxVmh0KzlmeS9iWmRCT3ZpM2pnblB5Y1FacW9kZXlmeEpHTUlZZ1BrRlZNaGtnRVh4dlBLbXZlWVUwbkNjR05tek9jQkZ6aGx2Wjk1Z0taVGUzeXBTbk11OVFZWHpvQVkrSml6WkZGS2RabjVRaWl0RmxRSVN3dGlSSFdSdzBLeHFpbEY3MllJVVBRbWsyb1VyMUM2NklBSWI5YkNpb3VKTVlTemluZnRlN0Z0cTBKSE9QTFJtWGYxRDZYNTJlZHFBQ2ZXVEozOEJHY1NZRTN1N20vRkFJa2RveVlWNzNUV0YxMGE1N1ZxUTNJTnprYVBmSW5kWk1HNGdnTDZFOSt0SVpyb3JRZ1k2cVdkWHFyRVZVeUZYZ0ovQ1pVNTNKTUt3bXFjTE44aEVxQUFWaVFQZEt1Q2U0TnpRZ2NtTjdCL3QxQ2ZDVisyU2xWdFYrTml2U3ZmZEN5REcvWEU3a2RtM3AvQlVndzVqTU5mdHdidGZzbDlCcTIwSU5TZzNrTVpubm1Qd3NabHVaWGl1NVdBZFd1aTVYNjgiLCJtYWMiOiJiY2Y0ZjJiMzg2ODM1NGY5ZGViMDlhZWNjZDY0YTgzZDM2ODkzNGFjY2JmZjdlMzAyMzhkZDliZTAxMjI2ZjI4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ik9SQW42NVordjlPblB6amlNbXpqM2c9PSIsInZhbHVlIjoiTVNrR2hnWlN3Nko0aEk1YjRMcHFSb1AwRnoxSUw2ci82dlkwVEJQWDA3VUVhNWJsNjJUbW1lWTZPL2Z6eXVRdG5ZUFdVZGhTd1BBZkk5TjFXQU15c2RMKzlGSjFMS3QrdXVKdGkwTGIxdyt5cGp6ZHhoMm54dWkrNGZDNkoyWVNkTFJ3eTlEaGdrYmlaLzRVT09lVFczMUp3d1cwR2xGYjNvUHh5VHZWaU9CZlRoU1Q1M2Y4QVQ4MGZUWHpmdUFOWE5rNmpiQmU1WG96Y1BIbW5ZREUzcHhFcDJra1MxM1pCdENieU1xa1o5TEpSTDNMcXM4b2NwcUNscldmMXpqajVTVDFWRTBqZzlvMnZmUzNwcnF0QlV2WFBCSlArUHltL1N2VkNGb2t2YnA3cGM5S3o0OEN5eGpBS25IMkJ0MitlYm95WXBRY05pMTh2K05aL3hlcFMzYnQ4a3dmdTdoNzlvNVJFVUxBZVNQUXBaTmJSaW9Ea1JzYlNKS1prenBFWUNac0ZmK1dId05ualZTVlhnVWpUSnBMMStiL1FwandnSlo2R2tnSnhHU3ZOcWtMLzBKbGJrdEJFV2g1c2UybW1ZdUJXL3lRMW1GTmhtNVRCRlVGUWFwOWVoTEg5OXNxcWk4WXM0NXVaSWQ0bUtvWFJsL1F3cE9xK0hNQWVJNWMiLCJtYWMiOiIwODE5NGUyNTFlNjFlMGJkN2NlNTg2N2M5MDM3YWZiZTQwNDY3MDhhYmMwNDhlY2ViNDc3NGExMWY3MzJkODkyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1881964540\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2036751032 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>PHPSESSID</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HxnOnSuhAAPTfN2VS4q58slgJoytwtHYqaX0oLoW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2036751032\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1712624277 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:51:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ijg1MUpzb3hYM3dDNkZpalZxR2IwS1E9PSIsInZhbHVlIjoiUENZTWJ4UFRTS3ZwSXhwdzBPVWNJeU9HZUV2bGJ4ZDFYVk9MTi9WYzd5NzlsenFRb3h0UnpZL282Z1hxSHdvNnJZUFNUWFViei81dVkxUTRCYnd3MnUwLzA1OGRjNGN5MG42ZkYzNlRCYmNFSmFZVlpkYXZmOUZzNEM0ZlcrL0dXZzczOGFkcDk2aU9KSGxDRXpwQUtMUitlejg1UlBXaXRjaEdaMmhqSUpiZXRuMzBheW14R2l5dDAvM0tuRENhbUJVYlFrUW5TVVlkbk9GanArUktVOGtDQWNPWjJySTNqaXRiQlh6d00rM01XcHozdGw3aEQzb3hrVVRyWWJocDR2TThpT2twWmF2RHRyaTJXclF1dWhhM3pmNXJFeXh1cXZJeWxMSk5yTWltQ3JsQUZWQXMvZmU4MTI5aGp0b3dGMnhnRWRrWmR3dUVjNGJhTWtwTTRYdllPdURMS0pNMEtKV3FuejJGN3VFMlpjVkZQQ1kxeUs4MVcrYmw5dkJqNm1QMUpnMXgxbHNUbGdMc0d3a29aU1RnL0g5bndSbjdzQUFHSkRGeWQrM1QycW8xcHNudUUwWlQrQStPZXh3OHdpTUZaZlRzMkpxc1RSTzVIcG9CWlQyK1lLYzhOM1ZCZWtjWGRuQlNheWVZWjE2S01PLzZxNkhOc2xlRTNWeTYiLCJtYWMiOiJjZjFjMzY4NGQ4NjM2NmM5ZDk4NTVjZWE4NGRhZGE5ODk2ZDg4ZWQ5M2ZlODdlOTQ2N2MwN2YzYjgwZDM2ZDAwIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:51:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImhuRXpwYU12WnhieVhwVE1xb0YwZlE9PSIsInZhbHVlIjoiV1RiT1NtQ0ZQQzR5Y2VzMUFaTElIckVWdEVuOSthVFJ3MGhFRjFvZGlJWHBMRVhqS2w2MnFzSVJLRm1rMG5kUmtGa1JBU2puZHRObTVXdXV2eTRYbzg2Wi9ieG1Va2hWdXoyOSt3QnN0Wkc5VzNjb0JZdC92UTJHU1lHZzBkejMzY21aa3Boa0IzeGZPZ2RERmhSOGRubVd0c1hXZnFQN1FVbTlTZHY0Qmh0enNmM29JbkdYWVBsTTZ4ZDJZYTArdnkzNEtHNllJY2IwSy9GcUZhaVNYYy9oY21HemNyTmtTd0lub2ZIeHdLMWg1T1AxcTVRTEJpZ3lqT2RmeW9jVzFaTlZHRGdhN296V0sxNTNaSmVCSVk4Um04UHVoWmk3U1JnYjdiM3g3RGN3dXRWdVUydGJsK3ovOURGSFRseEt5UHJKKzRUaDcxQWxLOUxUaUxpNjRibjJ3NHdIQi9zd0hxL3lkRG8ybC9jWWxETXdlQkNvLzIrbnNWbUgvT3Q5K0FseDRZQzVJR2J1TFJ2RWtma0hEaHB5cEhUdTN3YjNWMVlmVlBrY2I5YVpHZys0bDRFeXljWEp6ait3MTQ3Z0txMHR5M2hyblBTSDVxYnljY2tVUjhPZ2ozSU5WalZXTytCaHdKRk9CeGFMUlcvakVVT3JJVFVFdnRvVGVPc1ciLCJtYWMiOiJlMTIwYzFhZTNhYTMzOGFkYjkyOTJlYjc2YjM2NGRkMDQ3YjVlMTZkOTgwOWI4NzFhYWI2ZDVkNTQxOTczNTM0IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:51:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ijg1MUpzb3hYM3dDNkZpalZxR2IwS1E9PSIsInZhbHVlIjoiUENZTWJ4UFRTS3ZwSXhwdzBPVWNJeU9HZUV2bGJ4ZDFYVk9MTi9WYzd5NzlsenFRb3h0UnpZL282Z1hxSHdvNnJZUFNUWFViei81dVkxUTRCYnd3MnUwLzA1OGRjNGN5MG42ZkYzNlRCYmNFSmFZVlpkYXZmOUZzNEM0ZlcrL0dXZzczOGFkcDk2aU9KSGxDRXpwQUtMUitlejg1UlBXaXRjaEdaMmhqSUpiZXRuMzBheW14R2l5dDAvM0tuRENhbUJVYlFrUW5TVVlkbk9GanArUktVOGtDQWNPWjJySTNqaXRiQlh6d00rM01XcHozdGw3aEQzb3hrVVRyWWJocDR2TThpT2twWmF2RHRyaTJXclF1dWhhM3pmNXJFeXh1cXZJeWxMSk5yTWltQ3JsQUZWQXMvZmU4MTI5aGp0b3dGMnhnRWRrWmR3dUVjNGJhTWtwTTRYdllPdURMS0pNMEtKV3FuejJGN3VFMlpjVkZQQ1kxeUs4MVcrYmw5dkJqNm1QMUpnMXgxbHNUbGdMc0d3a29aU1RnL0g5bndSbjdzQUFHSkRGeWQrM1QycW8xcHNudUUwWlQrQStPZXh3OHdpTUZaZlRzMkpxc1RSTzVIcG9CWlQyK1lLYzhOM1ZCZWtjWGRuQlNheWVZWjE2S01PLzZxNkhOc2xlRTNWeTYiLCJtYWMiOiJjZjFjMzY4NGQ4NjM2NmM5ZDk4NTVjZWE4NGRhZGE5ODk2ZDg4ZWQ5M2ZlODdlOTQ2N2MwN2YzYjgwZDM2ZDAwIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:51:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImhuRXpwYU12WnhieVhwVE1xb0YwZlE9PSIsInZhbHVlIjoiV1RiT1NtQ0ZQQzR5Y2VzMUFaTElIckVWdEVuOSthVFJ3MGhFRjFvZGlJWHBMRVhqS2w2MnFzSVJLRm1rMG5kUmtGa1JBU2puZHRObTVXdXV2eTRYbzg2Wi9ieG1Va2hWdXoyOSt3QnN0Wkc5VzNjb0JZdC92UTJHU1lHZzBkejMzY21aa3Boa0IzeGZPZ2RERmhSOGRubVd0c1hXZnFQN1FVbTlTZHY0Qmh0enNmM29JbkdYWVBsTTZ4ZDJZYTArdnkzNEtHNllJY2IwSy9GcUZhaVNYYy9oY21HemNyTmtTd0lub2ZIeHdLMWg1T1AxcTVRTEJpZ3lqT2RmeW9jVzFaTlZHRGdhN296V0sxNTNaSmVCSVk4Um04UHVoWmk3U1JnYjdiM3g3RGN3dXRWdVUydGJsK3ovOURGSFRseEt5UHJKKzRUaDcxQWxLOUxUaUxpNjRibjJ3NHdIQi9zd0hxL3lkRG8ybC9jWWxETXdlQkNvLzIrbnNWbUgvT3Q5K0FseDRZQzVJR2J1TFJ2RWtma0hEaHB5cEhUdTN3YjNWMVlmVlBrY2I5YVpHZys0bDRFeXljWEp6ait3MTQ3Z0txMHR5M2hyblBTSDVxYnljY2tVUjhPZ2ozSU5WalZXTytCaHdKRk9CeGFMUlcvakVVT3JJVFVFdnRvVGVPc1ciLCJtYWMiOiJlMTIwYzFhZTNhYTMzOGFkYjkyOTJlYjc2YjM2NGRkMDQ3YjVlMTZkOTgwOWI4NzFhYWI2ZDVkNTQxOTczNTM0IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:51:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1712624277\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1177416607 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1177416607\", {\"maxDepth\":0})</script>\n"}}