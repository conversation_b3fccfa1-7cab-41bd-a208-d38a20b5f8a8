{"__meta": {"id": "Xba2172fa1c55ac7fba690affa85b55a8", "datetime": "2025-06-17 14:43:21", "utime": **********.833376, "method": "GET", "uri": "/customer/check/delivery?customer_id=10", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.263136, "end": **********.8334, "duration": 0.5702641010284424, "duration_str": "570ms", "measures": [{"label": "Booting", "start": **********.263136, "relative_start": 0, "end": **********.760087, "relative_end": **********.760087, "duration": 0.4969511032104492, "duration_str": "497ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.760111, "relative_start": 0.4969751834869385, "end": **********.833403, "relative_end": 3.0994415283203125e-06, "duration": 0.07329201698303223, "duration_str": "73.29ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44978400, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.015629999999999998, "accumulated_duration_str": "15.63ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.802692, "duration": 0.01461, "duration_str": "14.61ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.474}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.823159, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 93.474, "width_percent": 6.526}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  2045 => array:9 [\n    \"name\" => \"علي\"\n    \"quantity\" => 1\n    \"price\" => \"8.00\"\n    \"id\" => \"2045\"\n    \"tax\" => 0\n    \"subtotal\" => 8.0\n    \"originalquantity\" => 3\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1184486938 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1184486938\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1801468594 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImQ1RzhvdUZVajJTZGhTbjZpOGUvZFE9PSIsInZhbHVlIjoiWG5DSVMrY3FLcnJLcXJCc0pKSDE4UUxrOC9qQXlpU1IrVzBpeUI2aDhnY3Q0cVpiZnlScWo1dDZrUHU0VWR3YW1PS2lJU0ZVVFlBSmJzd0dHNEhvWXRkR1hDb2Iyc0lPVUJDY3FCT01hNEYzeS9pQ2xQc0dOVTVEbHgvRmptM2U4UU9aSXpESXN3WURwbWliMEdYQVEvRG9IZEV3UmNMNFdGd2MrUGF6TktiMDVjb1Z1clJRQ3ZXU2hTSzA5dlRUY211NXNiS1NSdHZ6WEZad25mSDh3OFlKMlVjTWRnVEZOakZwOUhQcUlJbXRwNWxia1BPZGpzNDF5Z0N6elRIVncrZ0trN1RTMCtWSGRPdDlFRVhBNEhqSEVRSmpmRWk4YkRSQytBLytEUFlKcjFpOThUTHZPVDRFcjNIZFA0M041RER1QmFmRjB5M3FMbHVoWDBKRXVocTgvV1l5a1EvY0toeFZtVzh0Wm55NDc3aVBPY3ZpY0svREkyUG5OQ2lUczV2YWlHeGlTMUxlVFlBOVpVR3ZxTWFqNXRWc2ljYmJ4M2lRV2JhRndHUXEvS2g3YkN1TVgzMHl2eU53S1VKc2V0TitrUi9CZG9FckM2NkdqamtUKzZJd2dUZjVRUlBTdHBvdEllc1FKZ09PZGFiTU4xR3VzbEdWczdQbk5EVXQiLCJtYWMiOiI4N2Q2NjQ4NzBhNWU3YWM5MDkyMjE2MzFiNTU3OTU1NjE2MTY2NjgxMGY4NzNjMjQ4Y2M0ODRhZDk3YTA0MzM4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjJ2VjhYUXFBcWZSNmV3OWZ1S3pFOXc9PSIsInZhbHVlIjoiOE5iTmhhVjRaMjllYTkwL2JVanlnajVKS0s4OTBxdUh0dlBFVFF6U0RYMmpJQ3JUT25CekxueXo0YzdLUHNQUG1pM0VzRm93N2J3VVh4RGZCTjEvM2pUZks2QnYvVnVrREtwMEIwWlgzNkl5MCszMU9odE5wWmQ4R3ZqS21vZnhYU1I1bm1BMS9xdE5OQ1NWZ3BhLzFRWU5iODBTRnlVRmwrcHVaMGhkUy9NVUplUC92cmgrY2RTWDVxZUc2T0VKUmxKTE5nT1NzaVJwbDBtdzVNcEMvcERZR0NQK1N4MTJ0aTB3Nk8xRTVDaVZSN0V5eldGK09jTWpRTm8zS2Q2UnNoQ1diV3pPNTRXeVpSWnVTeHYwRzd5amw1L1V0aithZnhjRUluSiszRTJhWWczZWlqNW9hS000em8xMS9mWFdZZmFuemloVVB1RlN5SEljVFA1U2FnR1JUaTh6eHNmOE1UeisrZmowVTJWNFdzVmYvRjgzN21QaHVCYURNaWUwSW1OVTNrWW9pV3BEY1hxMDJhOEJwUHBoSG50Yk9yLytMaENqajUvT1NHa0xWTUlnUkpYcDZ3enRYU3k4dDc0LzgvOWczMGNNZWZ1cW16ajBQamdaUm95b2xTWlBFaE5Qa2tIanJLdVViRC9kdndBMnBOVkFSY3h2ejNtdFBNSzgiLCJtYWMiOiJmMGM1NTMxNGQwZDAzNWVmZWU4NzY2ZjBkOTYyNTYwZGU3Njk1NzA0N2E5MjYwNTYzNWVkZGJhM2ViOTMyMzVkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1801468594\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1824723901 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1824723901\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1376587237 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:43:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjN1emhMT3hIMjZrNGZUOWRjaWV2QlE9PSIsInZhbHVlIjoiK3Bwb3d5MFkwa2V3L1dhTmRETHF2WTZsK2xTM1QrSU94WS80VW15Z1VQdExKZlg2WjZ3ZEJMY3NZRWx6WW84UU9IQ0tNZ0F0ajE4dmR6Tm91ZWs5andWN04rNFcrOFcyTnJHRFVxNU9nN3AzMW13OGFVWktCUk1TSjZPMHdBSkl5NTU1NVB3QitqRjcyYnh5VVF0RXZvRHVhMDlGb1VzOFlicG4yRU5mdkhLSGQzVlI3NDd2Q3Z6TFNyUDA2RXdmNnlrQVZpcnpHNHlDVUFRTE4zTlFGN2c2QU9pYm84ZzNmSXJBYzNHLytrR2h1UVVuQVYrSzM5YkZnc2dSV20zYXllL0dNSm11L2lQV2V0bU1lL3lsQUc2bnRLb2JIMC93cnBPLzhSeWJsVERkT2tEc3VvOHFpMm1xazZRMldnL3RmT2ZqUi9vYXZyaEFvdnRPeFczZ2FJbU5ZVXd2eWgvWHBPc2lDL2hTWVIxdWNlYXNUcENFV0lKNWZXbFlzeXVYSFJVOWdIUHFSRHNVT1A3bmEvNzcrb3l5UU4rNTYzNFROZXdPM2RRbTk3dzJjOUJMNmdWZXYzQjRWdGVDWEpGeFFiUXU0RWJYK1V4Yi9KZjRwYThLa21jckhXbE1rSjdQS1NJYmYvQ0RGaDhFNTF3ZGN3RkIvVlMvVFp3My9RbFYiLCJtYWMiOiJlMGQ3MDgyYTc1ZmNmYjE2YzZkYzNkMDRmMjQ2NGQ1MTZkNWE3OWNhNTdjNjYxOWFlOGQ3M2E3YzFhZGFmOTdmIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:43:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjJQc0NoMkxXV2dobVkvbElXbzN6ZHc9PSIsInZhbHVlIjoidkhQOXVtZEE5TkZJZ1dSWjM0NmZLaWxqU29Kb0pJL0p4V25WbXFJdjZUN09kaWV5TXFnb0U4Ti9mckNJTkFNYXpNcGJZSDl1VXhpc3JHRXcwdmhqbDIwK1k0Q0Zsb1FGeC9JblVXTzhiK1FacGMyVjNtS296Q2o3RDF1dUFJOXgyZHlzQnhJT0R0b0gyY0ZvdG4xOTFnMXpiYkRYMGpqYmVZZ0FvMXlZWW53MnZMb3RzVEF5azdQK1dEb3NkVk92V0tqL2xKR1YxU0puSlJSUUNwamxyTGVlWEFEWGNUWXlIN2Y4WVpVU3dVRTBwQnNyY3h1RlR2SkxWdDlhZi93V0M2cElEbHh2ZjFnakpYTkttUzRKYzkzNVFuNmdhMUNIK0M1R1BmcGl5WkxLT043L0Q4ejBtcGFGMStuVVFLNTNWRnlmZXFsajZ6bE00WXNFYnZuWHEwMUEzSWhHbmhRNWZ1UmlSSlVSOC9oTWtGenZUNnhwR1JGendhTGNNOG9rd1h6Y0l2R3BRUFM1TVIxK080ODNhZ3pEZ0pRWWREVGtXbVVRYmhLZmxBZWpHVmwxbGE5OGR6R2duOFVCVzcrWlZsTElzbnc3TkpvYjZ5R2JEOTlNQk91ajF4cE5BcnU4S2ZZOGdsdzFUbkF4S3MzanBMVnZkeVJaem8zVE9Xc2giLCJtYWMiOiIzNWIwY2ZkMWJkY2QyMGFkNjBiYzA0ZjVkMTRlZjQ2NjA3MGYwYmU2MGY4MThhNGY2ZTUyZWNmZmMxNjYwOTk1IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:43:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjN1emhMT3hIMjZrNGZUOWRjaWV2QlE9PSIsInZhbHVlIjoiK3Bwb3d5MFkwa2V3L1dhTmRETHF2WTZsK2xTM1QrSU94WS80VW15Z1VQdExKZlg2WjZ3ZEJMY3NZRWx6WW84UU9IQ0tNZ0F0ajE4dmR6Tm91ZWs5andWN04rNFcrOFcyTnJHRFVxNU9nN3AzMW13OGFVWktCUk1TSjZPMHdBSkl5NTU1NVB3QitqRjcyYnh5VVF0RXZvRHVhMDlGb1VzOFlicG4yRU5mdkhLSGQzVlI3NDd2Q3Z6TFNyUDA2RXdmNnlrQVZpcnpHNHlDVUFRTE4zTlFGN2c2QU9pYm84ZzNmSXJBYzNHLytrR2h1UVVuQVYrSzM5YkZnc2dSV20zYXllL0dNSm11L2lQV2V0bU1lL3lsQUc2bnRLb2JIMC93cnBPLzhSeWJsVERkT2tEc3VvOHFpMm1xazZRMldnL3RmT2ZqUi9vYXZyaEFvdnRPeFczZ2FJbU5ZVXd2eWgvWHBPc2lDL2hTWVIxdWNlYXNUcENFV0lKNWZXbFlzeXVYSFJVOWdIUHFSRHNVT1A3bmEvNzcrb3l5UU4rNTYzNFROZXdPM2RRbTk3dzJjOUJMNmdWZXYzQjRWdGVDWEpGeFFiUXU0RWJYK1V4Yi9KZjRwYThLa21jckhXbE1rSjdQS1NJYmYvQ0RGaDhFNTF3ZGN3RkIvVlMvVFp3My9RbFYiLCJtYWMiOiJlMGQ3MDgyYTc1ZmNmYjE2YzZkYzNkMDRmMjQ2NGQ1MTZkNWE3OWNhNTdjNjYxOWFlOGQ3M2E3YzFhZGFmOTdmIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:43:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjJQc0NoMkxXV2dobVkvbElXbzN6ZHc9PSIsInZhbHVlIjoidkhQOXVtZEE5TkZJZ1dSWjM0NmZLaWxqU29Kb0pJL0p4V25WbXFJdjZUN09kaWV5TXFnb0U4Ti9mckNJTkFNYXpNcGJZSDl1VXhpc3JHRXcwdmhqbDIwK1k0Q0Zsb1FGeC9JblVXTzhiK1FacGMyVjNtS296Q2o3RDF1dUFJOXgyZHlzQnhJT0R0b0gyY0ZvdG4xOTFnMXpiYkRYMGpqYmVZZ0FvMXlZWW53MnZMb3RzVEF5azdQK1dEb3NkVk92V0tqL2xKR1YxU0puSlJSUUNwamxyTGVlWEFEWGNUWXlIN2Y4WVpVU3dVRTBwQnNyY3h1RlR2SkxWdDlhZi93V0M2cElEbHh2ZjFnakpYTkttUzRKYzkzNVFuNmdhMUNIK0M1R1BmcGl5WkxLT043L0Q4ejBtcGFGMStuVVFLNTNWRnlmZXFsajZ6bE00WXNFYnZuWHEwMUEzSWhHbmhRNWZ1UmlSSlVSOC9oTWtGenZUNnhwR1JGendhTGNNOG9rd1h6Y0l2R3BRUFM1TVIxK080ODNhZ3pEZ0pRWWREVGtXbVVRYmhLZmxBZWpHVmwxbGE5OGR6R2duOFVCVzcrWlZsTElzbnc3TkpvYjZ5R2JEOTlNQk91ajF4cE5BcnU4S2ZZOGdsdzFUbkF4S3MzanBMVnZkeVJaem8zVE9Xc2giLCJtYWMiOiIzNWIwY2ZkMWJkY2QyMGFkNjBiYzA0ZjVkMTRlZjQ2NjA3MGYwYmU2MGY4MThhNGY2ZTUyZWNmZmMxNjYwOTk1IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:43:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1376587237\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2045</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1593;&#1604;&#1610;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2045</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>8.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>3</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}