<!-- Stats Update -->
<script>
    // تحديث الإحصائيات
    $(document).ready(function() {
        <?php if(isset($products) && isset($warehouse)): ?>
            var totalProducts = <?php echo e(count($products)); ?>;
            var lowStockCount = <?php echo e($products->filter(function($product) { return isset($product->min_quantity) && $product->warehouse_quantity < $product->min_quantity && $product->warehouse_quantity > 0; })->count()); ?>;
            var outOfStockCount = <?php echo e($products->filter(function($product) { return $product->warehouse_quantity == 0; })->count()); ?>;
            var normalStockCount = totalProducts - lowStockCount - outOfStockCount;

            $('.total-products').text(totalProducts);
            $('.low-stock-count').text(lowStockCount);
            $('.normal-stock').text(normalStockCount);
            $('.out-of-stock-count').text(outOfStockCount);
            $('.current-warehouse').text('<?php echo e($warehouse->name); ?>');
        <?php endif; ?>
    });
</script>

<div class="table-responsive">
    <table class="table datatable">
        <thead>
            <tr>
                <th><?php echo e(__('الصورة')); ?></th>
                <th><?php echo e(__('المنتج')); ?></th>
                <th><?php echo e(__('الرمز التعريفي')); ?></th>
                <th><?php echo e(__('سعر البيع')); ?></th>
                <th><?php echo e(__('الضريبة')); ?></th>
                <th class="text-center"><?php echo e(__('الكمية الحالية')); ?></th>
                <th class="text-center"><?php echo e(__('الحد الأدنى للكمية')); ?></th>
                <th><?php echo e(__('الفئة')); ?></th>
                <th><?php echo e(__('الوحدة')); ?></th>
                <th><?php echo e(__('الحالة')); ?></th>
            </tr>
        </thead>
        <tbody>
            <?php if(isset($products) && count($products) > 0): ?>
                <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php
                        $isLowStock = isset($product->min_quantity) && $product->warehouse_quantity < $product->min_quantity;
                    ?>
                    <tr class="font-style <?php echo e($isLowStock ? 'low-stock' : ''); ?>">
                        <td class="text-center">
                            <div class="product-image-container">
                                <?php if(!empty($product->pro_image)): ?>
                                    <img src="<?php echo e(\App\Models\Utility::get_file('uploads/pro_image/'.$product->pro_image)); ?>"
                                         alt="<?php echo e($product->name); ?>"
                                         class="product-image rounded shadow-sm clickable-image"
                                         title="<?php echo e(__('انقر لتكبير الصورة')); ?>"
                                         data-bs-toggle="modal"
                                         data-bs-target="#imageModal"
                                         data-image-src="<?php echo e(\App\Models\Utility::get_file('uploads/pro_image/'.$product->pro_image)); ?>"
                                         data-product-name="<?php echo e($product->name); ?>"
                                         onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                    <div class="default-product-image rounded shadow-sm d-flex align-items-center justify-content-center" style="display: none;">
                                        <span class="text-muted fw-bold"><?php echo e(strtoupper(substr($product->name, 0, 2))); ?></span>
                                    </div>
                                <?php else: ?>
                                    <div class="default-product-image rounded shadow-sm d-flex align-items-center justify-content-center" title="<?php echo e($product->name); ?>">
                                        <span class="text-muted fw-bold"><?php echo e(strtoupper(substr($product->name, 0, 2))); ?></span>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td class="editable-product-name editable-cell" data-product-id="<?php echo e($product->id); ?>" data-field="name">
                            <div class="d-flex align-items-center">
                                <div>
                                    <h6 class="mb-0 editable-text"><?php echo e($product->name); ?></h6>
                                    <?php if(!empty($product->category)): ?>
                                        <small class="text-muted"><?php echo e($product->category->name); ?></small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </td>
                        <td class="editable-sku editable-cell" data-product-id="<?php echo e($product->id); ?>" data-field="sku">
                            <span class="badge bg-light text-dark editable-text"><?php echo e($product->sku); ?></span>
                        </td>
                        <td class="editable-price editable-cell" data-product-id="<?php echo e($product->id); ?>" data-field="sale_price">
                            <span class="editable-text"><?php echo e(\Auth::user()->priceFormat($product->sale_price)); ?></span>
                        </td>
                        <td class="editable-tax editable-cell" data-product-id="<?php echo e($product->id); ?>" data-field="tax_id" data-current-tax="<?php echo e($product->tax_id); ?>">
                            <?php if(!empty($product->tax_id)): ?>
                                <?php
                                    $itemTaxes = [];
                                    $getTaxData = \App\Models\Utility::getTaxData();
                                    $totalTaxRate = 0;

                                    foreach (explode(',', $product->tax_id) as $tax) {
                                        if(isset($getTaxData[$tax])) {
                                            $itemTax['name'] = $getTaxData[$tax]['name'];
                                            $itemTax['rate'] = $getTaxData[$tax]['rate'];
                                            $totalTaxRate += $getTaxData[$tax]['rate'];
                                            $itemTaxes[] = $itemTax;
                                        }
                                    }
                                ?>

                                <?php if(count($itemTaxes) > 0): ?>
                                    <div class="tax-container editable-text">
                                        <?php $__currentLoopData = $itemTaxes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tax): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <span class="badge bg-primary-light text-primary me-1 mb-1">
                                                <?php echo e($tax['name']); ?> (<?php echo e($tax['rate']); ?>%)
                                            </span>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <div class="text-muted small mt-1">
                                            <?php echo e(__('إجمالي')); ?>: <?php echo e($totalTaxRate); ?>%
                                        </div>
                                    </div>
                                <?php else: ?>
                                    <span class="text-muted editable-text">-</span>
                                <?php endif; ?>
                            <?php else: ?>
                                <span class="text-muted editable-text">-</span>
                            <?php endif; ?>
                        </td>
                        <td class="text-center editable-quantity editable-cell"
                            data-id="<?php echo e($product->warehouse_product_id); ?>"
                            data-product-id="<?php echo e($product->id); ?>"
                            data-warehouse-id="<?php echo e($warehouse->id); ?>">
                            <?php if($product->warehouse_quantity > 0): ?>
                                <span class="fw-bold text-success"><?php echo e($product->warehouse_quantity); ?></span>
                            <?php else: ?>
                                <span class="fw-bold text-muted"><?php echo e($product->warehouse_quantity); ?></span>
                                <small class="d-block text-info">
                                    <i class="fas fa-plus-circle me-1"></i><?php echo e(__('انقر لإضافة كمية')); ?>

                                </small>
                            <?php endif; ?>
                        </td>
                        <td class="text-center editable-min-quantity editable-cell"
                            data-product-id="<?php echo e($product->id); ?>"
                            data-warehouse-id="<?php echo e($warehouse->id); ?>">
                            <span class="fw-bold"><?php echo e($product->min_quantity ?? 0); ?></span>
                        </td>
                        <td class="editable-category editable-cell" data-product-id="<?php echo e($product->id); ?>" data-field="category_id" data-current-category="<?php echo e($product->category_id); ?>">
                            <?php if(!empty($product->category)): ?>
                                <span class="badge bg-info-light text-info editable-text"><?php echo e($product->category->name); ?></span>
                            <?php else: ?>
                                <span class="text-muted editable-text">-</span>
                            <?php endif; ?>
                        </td>
                        <td class="editable-unit editable-cell" data-product-id="<?php echo e($product->id); ?>" data-field="unit_id" data-current-unit="<?php echo e($product->unit_id); ?>">
                            <?php if(!empty($product->unit)): ?>
                                <span class="badge bg-secondary-light text-secondary editable-text"><?php echo e($product->unit->name); ?></span>
                            <?php else: ?>
                                <span class="text-muted editable-text">-</span>
                            <?php endif; ?>
                        </td>
                        <td class="stock-status">
                            <?php if($product->warehouse_quantity == 0): ?>
                                <span class="badge badge-custom-warning">
                                    <i class="fas fa-box-open me-1"></i> <?php echo e(__('غير متوفر')); ?>

                                </span>
                            <?php elseif($isLowStock): ?>
                                <span class="badge badge-custom-danger">
                                    <i class="fas fa-exclamation-triangle me-1"></i> <?php echo e(__('منخفض')); ?>

                                </span>
                            <?php else: ?>
                                <span class="badge badge-custom-success">
                                    <i class="fas fa-check-circle me-1"></i> <?php echo e(__('طبيعي')); ?>

                                </span>
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php else: ?>
                <tr>
                    <td colspan="10" class="text-center py-5">
                        <div class="empty-state">
                            <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                            <h5><?php echo e(__('لا توجد منتجات في هذا المستودع')); ?></h5>
                            <p class="text-muted"><?php echo e(__('لم يتم العثور على منتجات في هذا المستودع. يرجى اختيار مستودع آخر أو إضافة منتجات.')); ?></p>
                        </div>
                    </td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>
</div>

<!-- Modal لعرض الصورة المكبرة -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalLabel"><?php echo e(__('صورة المنتج')); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" alt="" class="img-fluid rounded shadow">
                <p id="modalProductName" class="mt-3 text-muted"></p>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        if ($.fn.dataTable.isDataTable('.datatable')) {
            $('.datatable').DataTable().destroy();
        }

        $('.datatable').DataTable({
            dom: '<"d-flex justify-content-between align-items-center mb-3"<"d-flex align-items-center"l><"d-flex"f>>rtip',
            buttons: [
                'copy', 'csv', 'excel', 'pdf', 'print'
            ],
            language: {
                search: "<?php echo e(__('بحث')); ?>",
                lengthMenu: "<?php echo e(__('عرض _MENU_ سجلات في الصفحة')); ?>",
                zeroRecords: "<?php echo e(__('لم يتم العثور على سجلات مطابقة')); ?>",
                info: "<?php echo e(__('عرض صفحة _PAGE_ من _PAGES_')); ?>",
                infoEmpty: "<?php echo e(__('لا توجد سجلات متاحة')); ?>",
                infoFiltered: "<?php echo e(__('(تمت تصفية _MAX_ من إجمالي السجلات)')); ?>",
                paginate: {
                    first: "<?php echo e(__('الأول')); ?>",
                    last: "<?php echo e(__('الأخير')); ?>",
                    next: "<?php echo e(__('التالي')); ?>",
                    previous: "<?php echo e(__('السابق')); ?>"
                }
            },
            "order": [],
            "columnDefs": [
                { "orderable": false, "targets": [0, 4, 9] }, // عمود الصورة والضريبة والحالة غير قابلين للترتيب
                { "width": "80px", "targets": [0] }, // عرض ثابت لعمود الصورة
                { "width": "150px", "targets": [4] }, // عرض ثابت لعمود الضريبة
                { "className": "text-center", "targets": [0, 5, 6, 9] } // محاذاة وسط لأعمدة معينة
            ],
            "autoWidth": false,
            "responsive": true,
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "الكل"]],
            "initComplete": function() {
                // إضافة أزرار التصدير
                var exportButtons = $('<div class="export-buttons ms-2"></div>');

                var excelBtn = $('<button class="btn btn-sm btn-custom-secondary me-1" title="تصدير إلى Excel"><i class="fas fa-file-excel"></i></button>');
                excelBtn.on('click', function() {
                    $('.buttons-excel').click();
                });

                var pdfBtn = $('<button class="btn btn-sm btn-custom-secondary me-1" title="تصدير إلى PDF"><i class="fas fa-file-pdf"></i></button>');
                pdfBtn.on('click', function() {
                    $('.buttons-pdf').click();
                });

                var printBtn = $('<button class="btn btn-sm btn-custom-secondary" title="طباعة"><i class="fas fa-print"></i></button>');
                printBtn.on('click', function() {
                    $('.buttons-print').click();
                });

                exportButtons.append(excelBtn).append(pdfBtn).append(printBtn);
                $('.dataTables_filter').append(exportButtons);

                // تحسين مظهر البحث
                $('.dataTables_filter input').addClass('custom-input');
                $('.dataTables_filter input').attr('placeholder', '<?php echo e(__("بحث...")); ?>');
                $('.dataTables_filter label').contents().filter(function() {
                    return this.nodeType === 3;
                }).remove();

                // تحسين مظهر عدد السجلات
                $('.dataTables_length select').addClass('custom-select');
            }
        });

        // تأثيرات حركية للجدول
        $('.datatable tbody tr').each(function(index) {
            $(this).css('animation-delay', (index * 0.05) + 's');
            $(this).addClass('animate__animated animate__fadeIn');
        });

        // تفعيل Modal الصورة - استخدام event delegation
        $(document).on('click', '.clickable-image', function() {
            var imageSrc = $(this).data('image-src');
            var productName = $(this).data('product-name');

            $('#modalImage').attr('src', imageSrc);
            $('#modalImage').attr('alt', productName);
            $('#modalProductName').text(productName);
        });
    });
</script>

<style>
    .bg-info-light {
        background-color: rgba(23, 162, 184, 0.1);
    }
    .text-info {
        color: #17a2b8;
    }
    .bg-secondary-light {
        background-color: rgba(108, 117, 125, 0.1);
    }
    .text-secondary {
        color: #6c757d;
    }
    .avatar-sm {
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .empty-state {
        padding: 30px;
        text-align: center;
    }
    .animate__animated {
        animation-duration: 0.5s;
    }
    .animate__fadeIn {
        animation-name: fadeIn;
    }
    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    .export-buttons {
        display: inline-flex;
        align-items: center;
    }
    .badge-custom-warning {
        background-color: rgba(255, 171, 0, 0.1);
        color: #ffab00;
        font-weight: 500;
        padding: 5px 10px;
        border-radius: 6px;
    }
    .product-image-container {
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .product-image {
        width: 50px;
        height: 50px;
        object-fit: cover;
        border: 2px solid #e0e6ed;
        transition: all 0.3s ease;
    }
    .product-image:hover {
        transform: scale(1.1);
        border-color: #6fd943;
        box-shadow: 0 4px 8px rgba(111, 217, 67, 0.3);
    }
    .clickable-image {
        cursor: pointer;
    }
    .clickable-image:hover {
        opacity: 0.8;
    }
    .default-product-image {
        width: 50px;
        height: 50px;
        background-color: #f8f9fd;
        border: 2px solid #e0e6ed;
        color: #6c757d;
        font-size: 12px;
        font-weight: bold;
    }
    .default-product-image:hover {
        background-color: #e0e6ed;
        border-color: #6fd943;
    }
    .tax-container {
        max-width: 150px;
    }
    .bg-primary-light {
        background-color: rgba(80, 143, 244, 0.1);
    }
    .text-primary {
        color: #508ff4 !important;
    }
    .tax-container .badge {
        font-size: 10px;
        padding: 3px 6px;
        border-radius: 4px;
        font-weight: 500;
    }
    .tax-container .small {
        font-size: 10px;
        font-weight: 600;
    }
    .editable-cell {
        cursor: pointer;
        position: relative;
        transition: all 0.3s ease;
    }
    .editable-cell:hover {
        background-color: rgba(111, 217, 67, 0.1);
        border-radius: 4px;
    }
    .editable-cell:hover::after {
        content: "✏️";
        position: absolute;
        top: 2px;
        right: 2px;
        font-size: 10px;
        opacity: 0.7;
    }
    .editable-text {
        display: inline-block;
        min-width: 50px;
        min-height: 20px;
    }
    .editing {
        background-color: rgba(111, 217, 67, 0.2) !important;
    }
    .bg-info-light {
        background-color: rgba(23, 162, 184, 0.1);
    }
    .text-info {
        color: #17a2b8 !important;
    }
    .bg-secondary-light {
        background-color: rgba(108, 117, 125, 0.1);
    }
    .text-secondary {
        color: #6c757d !important;
    }
</style>
<?php /**PATH C:\laragon\www\to\newo\3\مجلد جديد\ppp\resources\views/company_operations/inventory_management/products_table.blade.php ENDPATH**/ ?>