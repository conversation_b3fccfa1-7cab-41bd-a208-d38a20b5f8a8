{"__meta": {"id": "X47060a672170e126edf8aafcfb3d3d67", "datetime": "2025-06-17 14:01:33", "utime": **********.060183, "method": "GET", "uri": "/customer/check/delivery?customer_id=6", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750168892.365336, "end": **********.060209, "duration": 0.6948730945587158, "duration_str": "695ms", "measures": [{"label": "Booting", "start": 1750168892.365336, "relative_start": 0, "end": 1750168892.984345, "relative_end": 1750168892.984345, "duration": 0.6190090179443359, "duration_str": "619ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750168892.984358, "relative_start": 0.6190221309661865, "end": **********.060212, "relative_end": 2.86102294921875e-06, "duration": 0.07585382461547852, "duration_str": "75.85ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44976936, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01989, "accumulated_duration_str": "19.89ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.0255702, "duration": 0.01925, "duration_str": "19.25ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 96.782}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0501502, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 96.782, "width_percent": 3.218}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-391774709 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-391774709\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1277384081 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1277384081\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1257616994 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1257616994\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-591743327 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im1DWkJleU9vUHNQV2NqcVd1aDllU3c9PSIsInZhbHVlIjoiNW9WM1BWSUhpaVBVUFJhTzBNbGQ1TkJqV2ZsR2c2a0NtWmd1aUxZdXFHTU40WDRRay8vdUMwNDduZXE1UWRjMkczNEt2Vllhd0Fla1BZNlZmb1p3K1g4WmtJWnlDc0xsWnJFcDNCdjdEMVJPUjNUSkw4MUhEZmYyMlQxUitNMEdKNVNmN0hZb3FVRDBXVmx2cnl1MXNrdjVKUTF1eVdtNkpwaVFrUWovRk5qamIremhWSlltWjdzVnFRTlJ2Q2tkbnQwNXVHZG5zVFNGazI5ZTl4VU0rZ1ExamI5QTAyemRpNDIxWWRFRWlzc041R2hwYjhPekhNRWhUWUh4SHJaOFBPTEF4Y0l4M1dKL1cvay9ud0FEWFFSUGRhMHZ3OS9FNG5mUlFkVkVSWSs4T3Q4bnl5elpicG93NmFxdEpNb2RmMjBjK2ZuNEx2b3dOZjZleENrZkJ3Nk5lbytmcWttS1l4cmtNTUZiSzZXSTU1R05odnFxRUJnSkJ3MHdpbzRNUEViZmRoMi81UXFjck9LdXMwNHZBeHNSL1RyOW1TVFJPd1JrSmJiMVNhSDhsY2Vpck5uNThUaHc5ZFNaVFh0TFhEZWViaFRNcFJmTjNqRXF0dTlCaU0ycVN5YTQ5T1UyQzZxZ3poekFTRkZnSy9laFpLYTNnbUY1Z2tjV1RpOWkiLCJtYWMiOiIxYTJhY2FjNTFiZTdhMzYyMjgyMmI4ZTM2MGVjZjZlNjY2MDJkYTVlOWZiNzBiNjI3NTkyMzAwOTEyODE5ZTJiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImRJYVBOOXFxcm5aNTVvZlU4VkltZXc9PSIsInZhbHVlIjoiQmhUczFjTnBad2E2UEpGWHBneldYaGRIWlpPd1dIU0k0Vi83RHJubHBUMGIzUW5KUzVpYnVMQ3A3RlNuMXA3bjQ1aXhleERuQTk3YktQR21pTG1USkRNb0dCbXFqV1JTSlI1UVhWRVErU2c5emljS1I2T3RRL0ZBdnpPK3E0NWF2V3B0aFo0VHpwZUcwa3hKbFZhaFdUMVFHcXFxUzhsV1F1QkRYc3NQMlU3L0ZsRHUzOXBBRGRVMnNIcEY4MVV4QTA4aEpVTEtBQlF1WFFCZWlML3NDS1BJa2I5Wm83UHhocW5sbFV5YytJNHhqRDFhWWlPNHluUWJvenFuUW5WS0VMMmlTc2VFbFhKb2p3aXdDSFB1b2xEOHdCdkZZZzJTL2tnUjVRWHVKeW9kVlFvaldkMHZRMERObVgwbGVRUjNEYkdzRjhJWStRdmZnb1l2TFBnaEwwU3EzTmhGeThRVm1XblByaTF1aHFyWEVwbTg2VUJRUFc3Tkh6QlRqQnFFay9jdVFPUXF3VE9NWmZPN0NhRURUeXhXOFNRNlR2WkpwR0pmanFSdkxVUEpVK2QycWxLOER6SjhxWlRsdzZHek5VZlB5Ry9XU0l5WFZIVTZxRFNWZFZJSEU1eUFSc3pLWW5PODIvV0M3eGV6TkZRT24ySWIrU0Nzc1Bha1g0cDIiLCJtYWMiOiJkN2MxNzU4ZDIzYTRhODA2YzhjY2EwMWMxZmEyZTlmY2Y5NjQ4OGIyZTMxYjIxYzcwNDc5ZDg5ZjhlZTIwYzZmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-591743327\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1782461428 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1782461428\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:01:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkFCcW55aWtOU0ZoOHRVaVZJYWx5clE9PSIsInZhbHVlIjoiSnM2U0lpVld4bVFMY3NRVDJoOU4wVHFyZTFiU1lwMHdWeGUvSTJReC90YWxTYk0zR2llUVIrVSthRGFleGdRZ3k2cXhDSDVsZ202VUlzRVFZS1pwRGV2UUt1b2QwTklOQ00wRlRrOGpuSzUwMTBJNkkyNk5DNkJ5cUsvanhOcFpqQm5hT1NYWDVydloyZjg3UUJGRHIxWHdxK3FFMm9hYTFMWVNBVGlvU0MvbXpmeVdmeW1lL1loUm1NdVZ3ejNGdjIvVnRKNjFGM3lGVXZ5QmRxK0N2Ymw0QXVHMW1FdnRoWDQzeEx4alYxRXBmUG9DQnR5L2JWU1JTVndxaE9hVEw2MTNtbStWOS81QU50STdBM3YwRno3c01lR1EzVnJCU09xc0xXMWFCR2lkOUdXNU9DdFBNdkovV3k0b3NvOWRlcmZwdDB4MEhpRktJb1d5Qk9zUkVIRjRZREZzeE40dXlmemNFNDRVRFZLRkI4V05Uc0prR25iTFRmUVdwbDVieVJEZ1QyZkRucDZsSmdFT1c4WnU4ZTR6aENHSUhySmw0T1gwSXA2Z0FHYitoejgzOXNpZlNJSHcxZ25YbVlxMlY5cXRLY0EreWRCanZqbTdhWDk2OTQzRkFBV0xCVUdNc2swb0hZRDhEdlF3b0VPUENMU3ZETzA2cGFpRjB3TTMiLCJtYWMiOiI1MzZjN2RlN2FkOGNlOWZiOTczNzc1OGQwZmZlOWZkY2MwZjZjZmUwN2M1ZWY0YjI4MGZkNmI5MTllNjU4N2M0IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:01:33 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InFrUWhCRnI0SHBQYXlUMk1oektjUUE9PSIsInZhbHVlIjoiS1Uxc1lpZ0w0MFFucXlOU0hwWlZudjU2a2pCUVBmU2oxZnpvRGZpUVJaQ0NPV0FFTHc2QjhWWjFwb3dvQ01MaTBGWVl5TGNOQ1FyTVR1V0MyRG90eGlsSWFxclZwZnMyQnNYcXllZE10cS9SZHlwM0tkalFqc05RbGE0MmhBM2V3dDdYTU51b3VrdiszRUN0d2JIR3R1UFJ1d3NweDh1bDdiMHJjcjUyUkdiQStwOHRKN3AzOFhkbGJNbjZ5NmR5Q0N1eWJmcjUyK2N4VlJhcUVaRHRmY3kyK1pyd3JTQW9YZEFodm1CaUFyVUZ5WGFQVkNaOXNXUExTeUZLRjlTN1hkUTh3ZW0rVmVTVnBkemRPYitwQVdEcHhhYzVCSlVYUXVOZ210Z3R0YWZyM2dZZ1hqdzVKeEVLbU1ndGFUdnArMXltRm5jTzg5SElPZnRpQytFbnV0Zm5yMUFvZ0NUejNMcHpjNnpxbFlFYWNSYjUyNkpPcW9hLzZrcVhObmkvNm5SZmR1MGZEYUNOUTRxenBzdUwwY2Q2eGsyakNlb3hZNVlzdWJxOHM2cktPalR6VFA2NkdvYUtMb0tuMkZHb3h1NTB2MlFBTXhjZHB3MjZlWnEvNkRHMjMvUmhFT0NMYVpGZU1SYzd0UEdYdlRkNzlOQWV4VnhqNjFMNFdNZWEiLCJtYWMiOiJjMDg0OTM2OGQ3YTE4NjgzMDNkZTM1YjM0MzI4OWNmMmYzYWI3MWVhYTdiZTAzYTYzZmNhOTRhY2U3ODNkMGRmIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:01:33 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkFCcW55aWtOU0ZoOHRVaVZJYWx5clE9PSIsInZhbHVlIjoiSnM2U0lpVld4bVFMY3NRVDJoOU4wVHFyZTFiU1lwMHdWeGUvSTJReC90YWxTYk0zR2llUVIrVSthRGFleGdRZ3k2cXhDSDVsZ202VUlzRVFZS1pwRGV2UUt1b2QwTklOQ00wRlRrOGpuSzUwMTBJNkkyNk5DNkJ5cUsvanhOcFpqQm5hT1NYWDVydloyZjg3UUJGRHIxWHdxK3FFMm9hYTFMWVNBVGlvU0MvbXpmeVdmeW1lL1loUm1NdVZ3ejNGdjIvVnRKNjFGM3lGVXZ5QmRxK0N2Ymw0QXVHMW1FdnRoWDQzeEx4alYxRXBmUG9DQnR5L2JWU1JTVndxaE9hVEw2MTNtbStWOS81QU50STdBM3YwRno3c01lR1EzVnJCU09xc0xXMWFCR2lkOUdXNU9DdFBNdkovV3k0b3NvOWRlcmZwdDB4MEhpRktJb1d5Qk9zUkVIRjRZREZzeE40dXlmemNFNDRVRFZLRkI4V05Uc0prR25iTFRmUVdwbDVieVJEZ1QyZkRucDZsSmdFT1c4WnU4ZTR6aENHSUhySmw0T1gwSXA2Z0FHYitoejgzOXNpZlNJSHcxZ25YbVlxMlY5cXRLY0EreWRCanZqbTdhWDk2OTQzRkFBV0xCVUdNc2swb0hZRDhEdlF3b0VPUENMU3ZETzA2cGFpRjB3TTMiLCJtYWMiOiI1MzZjN2RlN2FkOGNlOWZiOTczNzc1OGQwZmZlOWZkY2MwZjZjZmUwN2M1ZWY0YjI4MGZkNmI5MTllNjU4N2M0IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:01:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InFrUWhCRnI0SHBQYXlUMk1oektjUUE9PSIsInZhbHVlIjoiS1Uxc1lpZ0w0MFFucXlOU0hwWlZudjU2a2pCUVBmU2oxZnpvRGZpUVJaQ0NPV0FFTHc2QjhWWjFwb3dvQ01MaTBGWVl5TGNOQ1FyTVR1V0MyRG90eGlsSWFxclZwZnMyQnNYcXllZE10cS9SZHlwM0tkalFqc05RbGE0MmhBM2V3dDdYTU51b3VrdiszRUN0d2JIR3R1UFJ1d3NweDh1bDdiMHJjcjUyUkdiQStwOHRKN3AzOFhkbGJNbjZ5NmR5Q0N1eWJmcjUyK2N4VlJhcUVaRHRmY3kyK1pyd3JTQW9YZEFodm1CaUFyVUZ5WGFQVkNaOXNXUExTeUZLRjlTN1hkUTh3ZW0rVmVTVnBkemRPYitwQVdEcHhhYzVCSlVYUXVOZ210Z3R0YWZyM2dZZ1hqdzVKeEVLbU1ndGFUdnArMXltRm5jTzg5SElPZnRpQytFbnV0Zm5yMUFvZ0NUejNMcHpjNnpxbFlFYWNSYjUyNkpPcW9hLzZrcVhObmkvNm5SZmR1MGZEYUNOUTRxenBzdUwwY2Q2eGsyakNlb3hZNVlzdWJxOHM2cktPalR6VFA2NkdvYUtMb0tuMkZHb3h1NTB2MlFBTXhjZHB3MjZlWnEvNkRHMjMvUmhFT0NMYVpGZU1SYzd0UEdYdlRkNzlOQWV4VnhqNjFMNFdNZWEiLCJtYWMiOiJjMDg0OTM2OGQ3YTE4NjgzMDNkZTM1YjM0MzI4OWNmMmYzYWI3MWVhYTdiZTAzYTYzZmNhOTRhY2U3ODNkMGRmIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:01:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1364595246 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1364595246\", {\"maxDepth\":0})</script>\n"}}