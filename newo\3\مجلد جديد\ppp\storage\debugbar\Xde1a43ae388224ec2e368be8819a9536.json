{"__meta": {"id": "Xde1a43ae388224ec2e368be8819a9536", "datetime": "2025-06-17 14:51:38", "utime": **********.885126, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.284369, "end": **********.885164, "duration": 0.6007950305938721, "duration_str": "601ms", "measures": [{"label": "Booting", "start": **********.284369, "relative_start": 0, "end": **********.794256, "relative_end": **********.794256, "duration": 0.5098869800567627, "duration_str": "510ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.79427, "relative_start": 0.5099010467529297, "end": **********.885168, "relative_end": 4.0531158447265625e-06, "duration": 0.09089803695678711, "duration_str": "90.9ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46094632, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.015569999999999999, "accumulated_duration_str": "15.57ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8359601, "duration": 0.013609999999999999, "duration_str": "13.61ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 87.412}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.862025, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 87.412, "width_percent": 6.294}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8725202, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.706, "width_percent": 6.294}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-663226261 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-663226261\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-676905681 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-676905681\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-767705833 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-767705833\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1947060984 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2032 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; PHPSESSID=o1mek9etnn9ejsik46u9hjcosg; _clsk=17t60l2%7C1750171884533%7C8%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImJYTGFKckRLYWNLM25YdDBsbmVOZHc9PSIsInZhbHVlIjoiN0pielRBTDZqNDBrYzNmSDR2TjExTU40WW53RFM1Z3o4SlFVTVF2Yjh1b3dnN2FhUVZnOHVtZ2FlTmVlMXBBTkJrNFdpa2FJS2s4VWFJUC9pNU9Zdmo4Lzdibk02UGVTZWNmYUxBbngwQ1U4K3BnckdXdWpsalhKNEhtVlBIZXdtQS81bjVKakpBMEZRN0NDTlRMTWVrZmo0VTZqOVRCVTJZam5BWUYxVjhZeU45a2dXcEI1S0xMK2tVS0c3SGFITURoUEFRZVMwRmk5WHdwL2RBSlAvZ1ZFQzBRYVM1OC82eFl0ZWVVNllhcDNPS1JYTWRCSUxBbDBFM2g2SGY0RytJSDR5UmozRlhzUFZJVjRyckU5bDBrUFJtcjlkaFBaOENIY1BoUmtlOGZJTXdXVjJGQzhncGYveWxEa1hoLzhYLytQb0dXWkVPWDY1eXVLaXdiMjloU1AvQXJrMklRaE1KcmxmTCtmNU4xcmxaNU9zeWIxaktMSjh4cTNFRlFIMTRScnIzZFVaaThKY1dacFVZOWZKSXJJZ01uWHZoL0VldXpxUnN6Z0pXYm5nYnlMWGdyNHMzK04rQTduNlhtRDB4cnZ5MVk0WkNZQ2FZY1hta05GQU1LUldtNEtBbnhrSjVqL1ZnTmZudXZVSDI1VEcxZzlQbk1LcUs0aEdoazgiLCJtYWMiOiI3YjI4MTAwZmVjMGJkNWYzMGFlYWJjMGViZDE3NmY3MzZmMzY5OTZjMTM5N2QxMWM3ODVlZmNlYmVhM2Y2YTliIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlZ2Rjh5V3FtSUcvVW9OWHVKSTBaYkE9PSIsInZhbHVlIjoibmtuQjR0WkFHUStZd2k2SGJrM0c5dmRXTS9tQStJcU9LM2JNV1lDSjhxYXZ6dFNtb1VYOGwzUVNyRzNsVFdhaU1YQUJzRDV1VERzc1llTzIyWEpQK2YvdXlMZHQ4RHBYaXkrNjgwZWtOYUtnZXhkV09CSGZqZElVdUZhWkp1MktHWHlsSmNsMXlHYWIrSkxadWwyejBXWXFaZG5ERnM4UHNDV3UvbEI5U2hka24yVDlNbitDQnkzYjFQajVaZXF1dzVNQ25OUDZSMWlMSkVOaHhHNkZ1VXA4OUxqSWlkSENqaUp6TTJZZTcrTUJNZnBXbCswY0E3YkNpbXFDZVRtNGtTUkc1bWZIZDBCdlhnU09zSzJNZ080VmlSVG5ybUFNV2hzdDY0eGJ3RTRNYWk5blRNaFZVb2M0TkVOMWZXa2gvZkJLMlMzSTF0cHFBTytZeHYwemZDQVh0TytGZlZySkdveEdJNHE5ZkJnWEhVM3d0UDJjMlVZM0FtRlRVam1TQU1ZZHE4SEF6ampsUTgvc3pFMXlldUgvdVNNZEw3M29NVE03V1NDVitPNUhXSHlrSWJLU2V1ek5EMEI5dGhKQUJGTW5TZkJLNUdGbTR6dTA4VTRLUzAyanBsTmtaQ0FBUTRkQ0xldGoyejZadVpmQ0xWektaVHIvazdxQXNGcGkiLCJtYWMiOiJmMzgwNGNhYjdkY2M3OWQ1M2MxZWNiNWU0YmE3YjM1OGY4M2ZlMDViMTgzMDdmNmNhN2NhYWViMGE1MGU2MGJlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1947060984\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>PHPSESSID</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HxnOnSuhAAPTfN2VS4q58slgJoytwtHYqaX0oLoW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1404928270 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:51:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im43QnhWVkRBSmNKcCt5bHBOWWRJOFE9PSIsInZhbHVlIjoieDJLWXVlakN4OEFXVFo5akRLZW1hekxIMXlUd1MwQVRhd0xya2Y2SHYrNXVnN0RmNnlWbGRFelV4cExiNXBCcVRqdUErck93VzFKVlBWVlpDcXE1dnJoZXBGVjIzdHpTcEI2Z1d2dHRCeEE4cjc5a0NMbXFrdmRRcU0vZWdsQ0JHb0hOVnZxbkMveVgxWjV6NFErU3RKbVBHNlJmVUVxRG0wZWZpNnYxRzZkZ3cxdGk1RHhzK3EvRk1wRWhiSE9TVXoyUzFPY3F1L3dYV29OU054Y2xNa0JYMHg2UDlqbEZ5YWNwZ0FuQTFTTFhhOGZIN092YUhwNXptUnpyeG1NVmQzQXpJa2FPNEJrVmlZTEhla29UeCtkME0yb28ySmljMkdrMDFLcXBiS2VyMm9iUGpIUFVkVVB0Z1g2cEtPamJPN1gxQUlTWUtPdjNFNFlqUnprSzM0SU1rMlUxUDNLa1JVakN1TllKaWx4QVJ0Q21YVnE0aENRb1V5bzliOXBFcWJZbm5wMmNoSGY2cmZrVWgreDd3RFRRVjdmRm9LU0I5Y3UwK1p1cmVrOEJ4TCtyRkhUTjFrRFg1THdubWlzYTFyZkpyRGlqeVA0Sk81MkVMUmVBNUNPMWVpZXE0aFZUeTlOa0E4Ti9iSTRIcHBZM1J2S1RwZjZyU3hsSy9GbGwiLCJtYWMiOiIyMjlmYWNmZGM2MjIzNTI4MDVhNzIzYWFkM2Y2OTAzYzc5Yjg0ZGIxNjBhNmUzMGYwZTExODMwN2Y4OTIyYWUxIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:51:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImFaLzVSeVdsbmpuV1R4ZTc3Qnk2akE9PSIsInZhbHVlIjoiUWdjQkhSczV3UDdzSGxUb0JZUG50SkM5V1dWWXM3U3hKbFVkVHNGMTZlb3RLQ2NyZjRkSHN3OGVHWml0OUVYWEtMblEwRUZxbU5SbFNuS2VKUkJqYUZGWVZOWVFIeEFYaTJIV1d5RTBFYVdRRklJb2psWHExVG0zRGJZajhMci81TkRBR05wTVBQdW5iVW13dVltKzZCRXlySzVNaWZhWEpxaTI2d0ZLVXZkNlZkcDU2aDhYTFJUbEpuQ0l3ZFZWS0FXR05VNWtCU3BRWGFJTnRSTHNPRGcwQUs1V3Y1enNRbUJBVGhxdlQwb2dOT3Q1VWJldGxmVy9RS2h3em9HUnpPZkhTbHExd2VUOTBsd214bHRwMUgzd0pHZWFSVVcyQnltTGREKzVHdXdzSGJabkN2WkV2Z3dGVDBDNU9DVkQvOHp4UE5sOUtrT2ZaWTdkbERQWHRvMkl3c2R2M2hpR2RrZmN1eWdwektadVNGSW8wNVo2ZUcyRVU2MFhzTUtiOHJJVjNGWU52a1FhbTcwOTJKMDQvakhScDMwUGZoci9DMTh6QmI2VDJVTnRGZjBCSWU3WUIvdGlSRTNrckp6aHhvODFLTHFnTEZyU3p2Ym91Mm1ld2tnWHIzakcvSnVBMVRFR0N3bFEvYUVUR3FndHB5dHpkY2Vya2t3aFhnTW0iLCJtYWMiOiI1NGQ5MTgwNjliNTQyOTE2YjI0ZWM5Y2VmMmQwMDA4MjY1Y2IxNzllMDEzMmQ3N2RmMTJmNDk3NzFmNTcxMTUyIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:51:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im43QnhWVkRBSmNKcCt5bHBOWWRJOFE9PSIsInZhbHVlIjoieDJLWXVlakN4OEFXVFo5akRLZW1hekxIMXlUd1MwQVRhd0xya2Y2SHYrNXVnN0RmNnlWbGRFelV4cExiNXBCcVRqdUErck93VzFKVlBWVlpDcXE1dnJoZXBGVjIzdHpTcEI2Z1d2dHRCeEE4cjc5a0NMbXFrdmRRcU0vZWdsQ0JHb0hOVnZxbkMveVgxWjV6NFErU3RKbVBHNlJmVUVxRG0wZWZpNnYxRzZkZ3cxdGk1RHhzK3EvRk1wRWhiSE9TVXoyUzFPY3F1L3dYV29OU054Y2xNa0JYMHg2UDlqbEZ5YWNwZ0FuQTFTTFhhOGZIN092YUhwNXptUnpyeG1NVmQzQXpJa2FPNEJrVmlZTEhla29UeCtkME0yb28ySmljMkdrMDFLcXBiS2VyMm9iUGpIUFVkVVB0Z1g2cEtPamJPN1gxQUlTWUtPdjNFNFlqUnprSzM0SU1rMlUxUDNLa1JVakN1TllKaWx4QVJ0Q21YVnE0aENRb1V5bzliOXBFcWJZbm5wMmNoSGY2cmZrVWgreDd3RFRRVjdmRm9LU0I5Y3UwK1p1cmVrOEJ4TCtyRkhUTjFrRFg1THdubWlzYTFyZkpyRGlqeVA0Sk81MkVMUmVBNUNPMWVpZXE0aFZUeTlOa0E4Ti9iSTRIcHBZM1J2S1RwZjZyU3hsSy9GbGwiLCJtYWMiOiIyMjlmYWNmZGM2MjIzNTI4MDVhNzIzYWFkM2Y2OTAzYzc5Yjg0ZGIxNjBhNmUzMGYwZTExODMwN2Y4OTIyYWUxIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:51:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImFaLzVSeVdsbmpuV1R4ZTc3Qnk2akE9PSIsInZhbHVlIjoiUWdjQkhSczV3UDdzSGxUb0JZUG50SkM5V1dWWXM3U3hKbFVkVHNGMTZlb3RLQ2NyZjRkSHN3OGVHWml0OUVYWEtMblEwRUZxbU5SbFNuS2VKUkJqYUZGWVZOWVFIeEFYaTJIV1d5RTBFYVdRRklJb2psWHExVG0zRGJZajhMci81TkRBR05wTVBQdW5iVW13dVltKzZCRXlySzVNaWZhWEpxaTI2d0ZLVXZkNlZkcDU2aDhYTFJUbEpuQ0l3ZFZWS0FXR05VNWtCU3BRWGFJTnRSTHNPRGcwQUs1V3Y1enNRbUJBVGhxdlQwb2dOT3Q1VWJldGxmVy9RS2h3em9HUnpPZkhTbHExd2VUOTBsd214bHRwMUgzd0pHZWFSVVcyQnltTGREKzVHdXdzSGJabkN2WkV2Z3dGVDBDNU9DVkQvOHp4UE5sOUtrT2ZaWTdkbERQWHRvMkl3c2R2M2hpR2RrZmN1eWdwektadVNGSW8wNVo2ZUcyRVU2MFhzTUtiOHJJVjNGWU52a1FhbTcwOTJKMDQvakhScDMwUGZoci9DMTh6QmI2VDJVTnRGZjBCSWU3WUIvdGlSRTNrckp6aHhvODFLTHFnTEZyU3p2Ym91Mm1ld2tnWHIzakcvSnVBMVRFR0N3bFEvYUVUR3FndHB5dHpkY2Vya2t3aFhnTW0iLCJtYWMiOiI1NGQ5MTgwNjliNTQyOTE2YjI0ZWM5Y2VmMmQwMDA4MjY1Y2IxNzllMDEzMmQ3N2RmMTJmNDk3NzFmNTcxMTUyIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:51:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1404928270\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-18586791 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-18586791\", {\"maxDepth\":0})</script>\n"}}