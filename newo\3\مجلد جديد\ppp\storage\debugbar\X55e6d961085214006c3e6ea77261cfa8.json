{"__meta": {"id": "X55e6d961085214006c3e6ea77261cfa8", "datetime": "2025-06-17 14:54:09", "utime": **********.753204, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.057324, "end": **********.753234, "duration": 0.6959099769592285, "duration_str": "696ms", "measures": [{"label": "Booting", "start": **********.057324, "relative_start": 0, "end": **********.64807, "relative_end": **********.64807, "duration": 0.5907461643218994, "duration_str": "591ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.648088, "relative_start": 0.590764045715332, "end": **********.753236, "relative_end": 2.1457672119140625e-06, "duration": 0.1051480770111084, "duration_str": "105ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46326352, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1663\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1663-1673</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01058, "accumulated_duration_str": "10.58ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.711925, "duration": 0.009640000000000001, "duration_str": "9.64ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 91.115}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.737907, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 91.115, "width_percent": 8.885}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-162046607 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-162046607\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-71038562 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-71038562\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-490197137 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-490197137\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1408577310 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkF6a1ppZDhlSE8raXljaEp5WXIxUGc9PSIsInZhbHVlIjoiaWV6cFBpaFlvRkNrc2hkb3B5NG1VZlNXMTFjK3FTRFRVeHJuVTl1MUpkcGJaM3lKa1cwVGVaRzR6d3lXVmpWZ1FZYUg0Z1R6WFFJWmJvclRWWVZpMUExVEYwUHBaMk1nMkpONWVkb05RNll3dUJKVHcrWkxra2RneXM2ZytXWWpUdk1PVlFkUExGTVlFYUtwdXo4RnJBdndJYUs0MU0wWkQ0bit1U0pIODZ2bjFwZjhNOHh0amgxWHFzSlNUYURCWWJ4NTdNWGl1NUNiQis4UHF4ZHdPWW4yQWhiZVhLVnl5WVYvSU53alhqVkpKaXp2YjM4T1BZa0FQdmdWaW00TTVmSFozZU1lUHNEYkZ5N1k1cVlGSzlCVDd0djhXSzBzc2UxZFFrOWdBTlE5ajlXUWh2YUJ0aG5ZUUFFNHlIVUhKTzF5ZEU3WThiYWFNcldKVUU4UFNMTysyTG9sTjNDSkljZTVmZk1NZlcwV2Q3dmtIeStqSy8rV0wxbFlIbTdsb3JyNUo5OG44RElad2IvSUtEU1lSakNjN2VIaW5nZzMrSmw1TmpabFF0WTVmL21jMGU0MjFPU1dYRUxoWjgxRFRmamVtVEo2ZjlNdGwrbG1VYWFmOEVHRlBVdi91Q0NVUlg4N0hVVDU4YUFQdytjRlVFRm03YnR6V3BIbGxFcWQiLCJtYWMiOiIxOTcwYTU4ODU2MjAyNzM1ZTQ4MWQyYWUyMGE0NTgxNTAwZGYxZDQzN2VhZGUwNGQ1ZTQxZmY4Mzg2NTE1NDBkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImFJRHpwZmZYWGFUcG01STJqNC9LQWc9PSIsInZhbHVlIjoiRzk0Q0FGa1BFbHM0WHdObjNHei9weC9ycDNBNGdYdURLYTBkOGJ4QzdGRkp0bUF6RGtZWXp1czlDQ2lqUm03amh4SjkyMHYvRlhNRFJjK3dldmNTeFVFRzFVdVRwOWRxajEwMEJQa0dUMWhmeGRBREEzTWZmT21YaStaT1FiS3JDUkRaeHFxNk5EMVMvY0hLRVRydE50L2t4M2NJTkNjaGQyVmdoNnZ2cXJOZmpZcmhCQ2JPR1BaVnkvL1orRWxYZFFydHI2TnZETm1udHNWby9PNGNQNEhBODlsalJhMlNwYVlva29zQVFWeFUrWmdwcHpvWnlkYm4xeGpyYkpWQkVjZ0cvYXp6T0dvcTExSG15OUdWaTFVK01yeitKMVM0dUxtdmg5TEttdzNYTkxtMHpaL01yUGhpaGxyTzZrOXFtQmZ1Q05HQkxKU1lPMFdRdGVDSnlvMjlIazJwWTI1YjZxdWl0WFlPQWhQa2ppeE1BWForRFMxT3BKLzB5dk1zVUlZNVFZT0xWZDBEMkxCRGgvMUJWN1JQLzA2RkpGSDUwVGE5ZzVMWVVnVVhxWk0xZTJvRjRjM3dJcnFBdlVDMk92SENkNG9jTUlGTHJ1Qit0RU5ReW5nS3lYdnVEdnpMZkZKZGdtUmIxQTFkMjVLWkprNHhDdmtTN0RXQXphRnAiLCJtYWMiOiIyMjdjYTQzOWY3ZjUyYzVhMzk0ZjcyNGM4MTM5Yjg4Y2QzYmY2ODFlNGYxYzllYTZlODY5ODlmMmU1MzIzNjE0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1408577310\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1009576998 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1009576998\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1285354035 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:54:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InZmYjV2UFhZUEJVS3BEQmhvdDhBbkE9PSIsInZhbHVlIjoiK2VwQVdkQ0R0cmRMMTNETXdFTldlUWFaZW91YUlVRnZ4VjROYlMzcXU3Y2pCY1N2RGRWQlZOcEtTQStRSzUrY3Nib2pyTnRnQmdXK05NLzlyaGhpemRaNHh0bS9BZzd2bmNPZGhPUVRlQlFUOFl5MWZNQzRDQUVlT3E4Z09weWNaNmhuVFlNUmxwRmdvL1o3WXhNREFQM1dtUVFyRkFiSzJkdXJMSkxiaG5xTmVSL3d5Y3RZY1pVZ0dFZCtyZWh0Vi93aDQ5eXRNNWsrYkkvV3JxOTNuNWRsY0lLWUtEU3FSMHlBd2I0Z3R2a0dhazQ5Mm03YTlqcTJvNFZPeUcraDJlcnhuYVdGaWNlaGZSMi94aDBQS0xvc2RKU0JxWk1Qd3EwaVVqWXhwNjJmZXduRHk2VU11a0JyN2hNQm92OGRXNnBUTHVPd0p3SWx5aS9HK1dqTllSd2pNUWNTNzl6K2JXcUpzUThSTEF2VjVGbnRrbHlkQVhMZXFZTU40TUxSZWMyR2VYbEJLYmNSMzF3L1VPWlZLRS9ZL0VNVkJPUXJvNERvTWFIdGpiSWFxNkRKTTJFay9aNUlDUUEyU2gra1A3aUJ4OWREM3h1bzRhZWYzS2RQL0ZnTUxTQlQyTHVuNTdPb1B0YmhSbEVLcVZkTVV3VVI2Qy9TVXNWc29VVW8iLCJtYWMiOiI5NmYxZGFjMTg3MzM0OTI5ZGY1MzQ2YjYwNmM4M2VkOTBkMzlhY2JiMTBhMTZjYzI5NzJhNzEyYzU2YmJmZWNkIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:54:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IllqMmpXRWNNb2ZYaW1NOHQ5QXg4YXc9PSIsInZhbHVlIjoiT29PdkFYY2RGcjZUSjlzMW5DSDRzQkR5QllnZTUrWmFBdThGUklncHhKdTZpQUw5aG03cFpFaDg5M3hDQW9jeEZHTUIrU1kxQ29wZForbWFERWdGaS94UUhWMWxobjRodU1Yd2NNRk5pdGI5SGRLNWJXcDVheU1YTFhlYmlKVlhraHdNUGFoMzJYL1gzTU9ZSTB2TlZIT2pRb2VFS3M0bjk1ZWZsU0ZCSjJuRnlwamRGdm5WeGVIdHFWd1VmdDBmZDBSSjlHV25IRTRmL3FPRDlEajhmeGVmb1l0NGpaVHQ1RjRsYkM5c2NWM0kyRDRiZW9yaXZQdkNSQXJCLys0a3BNZEJ3Q3R2MkFmczRQMHZWeWYwekI2bzdCVDU2aVdSN1ZLL1hpWjU1a2hFV0dLNFdTMzdyRTRNRC9ISVpjb0txQmc2cGYrNCtwdnZRSWk4Z3hvTnBSU1cxanNLWW5hMzZiRHB3aUlaSEx3M1hEeVdKRXlIUXR4NGNaYVdzWHEvRHVUZXdtRzkyRXp1MCtkWE5LTm1EZ2o1dnRYUDdXcDJvcGxxZ0p6QzhhbjdwMHpPdGIrQ3EzWVU2ZTlQY2EyQ3NzZTdaTHduQ21qZHN2eGhZNWJmdHhLMlQ1TVJ5WnRQQkhwczU4ajlyYVJEa0xPMGZLVDQwVjB0cTR3b2tEVisiLCJtYWMiOiJmMWY5Y2Q4NDNkMjQ5NjU5NzJlOGJlNGJlMThlYzIxYjljZGU4MTY0YTNkOThmZDMwODFjM2Q5NDYyYzM4ZmExIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:54:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InZmYjV2UFhZUEJVS3BEQmhvdDhBbkE9PSIsInZhbHVlIjoiK2VwQVdkQ0R0cmRMMTNETXdFTldlUWFaZW91YUlVRnZ4VjROYlMzcXU3Y2pCY1N2RGRWQlZOcEtTQStRSzUrY3Nib2pyTnRnQmdXK05NLzlyaGhpemRaNHh0bS9BZzd2bmNPZGhPUVRlQlFUOFl5MWZNQzRDQUVlT3E4Z09weWNaNmhuVFlNUmxwRmdvL1o3WXhNREFQM1dtUVFyRkFiSzJkdXJMSkxiaG5xTmVSL3d5Y3RZY1pVZ0dFZCtyZWh0Vi93aDQ5eXRNNWsrYkkvV3JxOTNuNWRsY0lLWUtEU3FSMHlBd2I0Z3R2a0dhazQ5Mm03YTlqcTJvNFZPeUcraDJlcnhuYVdGaWNlaGZSMi94aDBQS0xvc2RKU0JxWk1Qd3EwaVVqWXhwNjJmZXduRHk2VU11a0JyN2hNQm92OGRXNnBUTHVPd0p3SWx5aS9HK1dqTllSd2pNUWNTNzl6K2JXcUpzUThSTEF2VjVGbnRrbHlkQVhMZXFZTU40TUxSZWMyR2VYbEJLYmNSMzF3L1VPWlZLRS9ZL0VNVkJPUXJvNERvTWFIdGpiSWFxNkRKTTJFay9aNUlDUUEyU2gra1A3aUJ4OWREM3h1bzRhZWYzS2RQL0ZnTUxTQlQyTHVuNTdPb1B0YmhSbEVLcVZkTVV3VVI2Qy9TVXNWc29VVW8iLCJtYWMiOiI5NmYxZGFjMTg3MzM0OTI5ZGY1MzQ2YjYwNmM4M2VkOTBkMzlhY2JiMTBhMTZjYzI5NzJhNzEyYzU2YmJmZWNkIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:54:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IllqMmpXRWNNb2ZYaW1NOHQ5QXg4YXc9PSIsInZhbHVlIjoiT29PdkFYY2RGcjZUSjlzMW5DSDRzQkR5QllnZTUrWmFBdThGUklncHhKdTZpQUw5aG03cFpFaDg5M3hDQW9jeEZHTUIrU1kxQ29wZForbWFERWdGaS94UUhWMWxobjRodU1Yd2NNRk5pdGI5SGRLNWJXcDVheU1YTFhlYmlKVlhraHdNUGFoMzJYL1gzTU9ZSTB2TlZIT2pRb2VFS3M0bjk1ZWZsU0ZCSjJuRnlwamRGdm5WeGVIdHFWd1VmdDBmZDBSSjlHV25IRTRmL3FPRDlEajhmeGVmb1l0NGpaVHQ1RjRsYkM5c2NWM0kyRDRiZW9yaXZQdkNSQXJCLys0a3BNZEJ3Q3R2MkFmczRQMHZWeWYwekI2bzdCVDU2aVdSN1ZLL1hpWjU1a2hFV0dLNFdTMzdyRTRNRC9ISVpjb0txQmc2cGYrNCtwdnZRSWk4Z3hvTnBSU1cxanNLWW5hMzZiRHB3aUlaSEx3M1hEeVdKRXlIUXR4NGNaYVdzWHEvRHVUZXdtRzkyRXp1MCtkWE5LTm1EZ2o1dnRYUDdXcDJvcGxxZ0p6QzhhbjdwMHpPdGIrQ3EzWVU2ZTlQY2EyQ3NzZTdaTHduQ21qZHN2eGhZNWJmdHhLMlQ1TVJ5WnRQQkhwczU4ajlyYVJEa0xPMGZLVDQwVjB0cTR3b2tEVisiLCJtYWMiOiJmMWY5Y2Q4NDNkMjQ5NjU5NzJlOGJlNGJlMThlYzIxYjljZGU4MTY0YTNkOThmZDMwODFjM2Q5NDYyYzM4ZmExIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:54:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1285354035\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1860328964 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1860328964\", {\"maxDepth\":0})</script>\n"}}