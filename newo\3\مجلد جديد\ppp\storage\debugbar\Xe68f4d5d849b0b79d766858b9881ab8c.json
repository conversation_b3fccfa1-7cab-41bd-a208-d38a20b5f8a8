{"__meta": {"id": "Xe68f4d5d849b0b79d766858b9881ab8c", "datetime": "2025-06-17 15:02:23", "utime": **********.321309, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750172542.534047, "end": **********.32134, "duration": 0.7872931957244873, "duration_str": "787ms", "measures": [{"label": "Booting", "start": 1750172542.534047, "relative_start": 0, "end": **********.213642, "relative_end": **********.213642, "duration": 0.6795949935913086, "duration_str": "680ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.213657, "relative_start": 0.679610013961792, "end": **********.321344, "relative_end": 3.814697265625e-06, "duration": 0.10768699645996094, "duration_str": "108ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46326352, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1663\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1663-1673</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02168, "accumulated_duration_str": "21.68ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.266371, "duration": 0.02068, "duration_str": "20.68ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 95.387}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.304261, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 95.387, "width_percent": 4.613}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-263945167 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-263945167\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1845355307 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1845355307\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1706062083 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1706062083\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1760037789 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Inl0a1pvQ3ZGQWYzN2VZajlFaEZUNUE9PSIsInZhbHVlIjoiWWNUVmtqZTg0aG1PY0twb01Wdjc1VXVzd0VTNEF4SE5VbFVKZ056WDhKb2Z6RXRJOHJUUEdFc1A4NFdDbngwZ0J6RHFVQTVpMktzdDdwTFRLRG9NVnVCSGx0aWhQUnQvZk11d0g2eWRkeEx2dW16OGdmMFZLNUtqUXBzandmemMxS0MyK3Z6QTNXb2N2dFhTcjFCOUhZRnpnaWZMdTJUaTBBTURxekU0ZVJZaFdRL2JLVXVIbHZhUzc4NmVWRHgwWEtWdXJ0YWp5T1JvcWlnTG0zLzgwR21hOCtVcnN1NU84RkJyaFpHR3FqZWkxdjVQRW92OWpBWjFDR3E1ZEQ2dUNIQnlLVE1kaUhaOGRFbEZVakpGK3VjNUIwVllWUWZjenFDblVselBiOGpQYUJrNXJxVFdPRUFnOVBMQzFEVzd5eE1NbE5LSEJrTEo0TFZ6VmwxQzJxRnUrRWx5UEozbi9IamVLYjdTL0YvdHZIVk1OZGVDMDR3WnlxTEg3b28vZmpMaGhjdS91dkt4N0p1aHptVUx5UjNvamNTMnBqZTBzaXlrSkpiZlhhaTNvVllyaUNDRVd0ZXJseEZPT2k5RTdINGQyUmF6OXFrRTJZWVFVUW9FWHh3MkVWMlRPRWlDMVpKOU5JczU0anJvdE5tdnQrWnJzaU9zUkFobUVJU3QiLCJtYWMiOiJhYzZjNjdkYWI2NWU5ZGZiMTU2OGMyMWJjOTZkYTM2YTliNTBmMWExMzZjODVlOTZmYTQ4NmI2ZmQwMjhhMzMxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlBzekE3Y21PSng0YjdlMGtueVczV0E9PSIsInZhbHVlIjoiOFhFSE5JNjl2L0NnQkF2Z2s4cnl3MWhJS3ZONWlpZW5BSE41SEkweVA1T3VHU0ZpajlyeHlxczJUMzVKSS9JaU5Vb0krbHVsR2VSaUo1SWFMcDF3WkFPTFVaTEZqSVRLSHVQVGJjQ2NIbDVSZkprMzZCSUwrVWFsSzgvTHp2RHc1VFR6UmQ0SGdPS0ZnT1AxZDV4Y3hEbVBZOG5yMy9KTFZXQ3RET0lZL1ljRnpoOGtrRUhPMkR2ZVFrTm4yck1LTEFDcEhTL25FYUhubXJxSUxBc01oL3V5MlIvcjZNS3ZtZDFNTVQrRUswa0pYSXczRFlUSWZ6N2t6QmVnN2QrYWw5ZDl2VDRLRWhzaGF6dk5kblNsdlBkVUFaN2R5MWZMUmo1KzNmeVBDdVVhY3lSTTdET05ML1pPUnJiTTdWM3RqQXBYUTR6M0NSN2VQNWtkTEpScmRPcFEzVmQrY2FJOFVyQ2JXMzkvemZOZlYxdmxabzZRSm5FQlBlMFJudU82Zkhsb3UrRjZpOWU2dE8xOFF0bUNGY2c0OEpmZUd0djk0VnpqSjNKUCtoZk1ycjdLQlZvTzkyNUkyUUFPMXRxbVF3RVgyeFhXb2kwanFVR0xvdURRa3VsekdoUjloUFNRcVRLSmlMNldxNEFRQ1FRNjZyeXVMQUhWM05MNFhZOUgiLCJtYWMiOiJiMDAyMTM0NmQ3YjhhY2IzNWY4ZGM1ZmM3NDVjNDY1MjkzOTc0NTYyNjA1N2U3ZTkwNDA1ZWE3MjlkNjZjNjlmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1760037789\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1313097717 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1313097717\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1844820206 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:02:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InpRekUvSnZCNnZXTnV4M09paVNrcnc9PSIsInZhbHVlIjoiVnE1bjEvc2tmRnErV0MwQ1VMSnZDTGFzSVo3Y1A5K0w0L0lXQWx3YmF0VnpFcndaK0J0RnRneTgzOFZUaFNyL3FyaHEzOHQ3ZTZPSGJYYWJKeWNHaXd1amhJNEx2emwzb2xuZC9kbE9oNFJFOVpwT28xZUhlMXJSUktCRWsyRzFIZ1Y3ZGNLOTRhKzNCY0pLTnFHOElkcExtSVBQTWQyT0hIMDRtZEhzS2l6NHVsUnpseEtiRTZQUkZmYmJXVEs0NFFrUGpzQlRCcmdBZ0xIUmJuNVliMHMzZ2RzS0tUelRicU9LYlY5cXFkVTJoemxyZzBpeCtZdHE1K3dIR0VRYmh2ZTkrNE5pY3lua214cmZscXRUejJ6SHRKWktMOWxrcklUbXcrbzZMcVhSRG56MVJ3c2NaUlF4NjJxdmxWVkQyVHdLbWtXRVF6Zm5IWnNQaC9Mbk15NDJ4aXozaUxiLy9DR2VsaGxONkpvNnd4aHlyb3hxV1djaThodXpBc0REREpOajQxdS82c2VWTWlBaU5JU2FRWWlUK0JzSC9yUjN1L1J6cjIyYkU3TW5ZcU0xbDh5eGhLakhIT09TOWx1eUhGTEN6Um0zdHRBNElITlRyektDcGp3cm5lc3MzRU8yUlFKdUFoN3JsQWxYQnZVdUxnYXh6QXRmcEQrdXRFdDYiLCJtYWMiOiI0YzlmZmUxMzY5ZTE5Y2E1NGUwZTlmZDM4ODIwYmE0MjY2Mjk4M2RlMDIzYjRhY2I5ZWEwMTMzNmQxNmJiZDJkIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:02:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IktqTS9LZDBmcDhKeWpZYnpUNzM1d2c9PSIsInZhbHVlIjoiOXhpYkpRVGFJWmZ4VTBDUm8ydGU2N013cm5STU54dDIwaVV1SWxrMFR4Y2ZEOGI3MGU1c2s0NS9sV1NTT0QvcVR1aWVZV0UvcEZRcXJ2S2syNDZCaU5JV1NmNFZNdUpWcUJmWUc4ZEF4aGRNM01lY2VTbnhkNjh5OWhEb0Y1Q3hJamRkK3Z3MWZwWitxRlJvdUZiUldqa1ZIWlp1bk1XcDViOGg4WWc4S1JlbmRrMTNPNm03UlVuTHRZWXppRGdRVTZBdmVuWVhCQWJmSFFpZnpGbU1GbCs1NUxUaUtzcHc5a2IzTUpRU1c1YnB1RGNWekZoQUlVTGIybWhsclVwRUNkUjJQZnE0OVpMQldhWXFMYjRlTjh5LzE2b2RudUdZS2VGZC9LOEVpUmU5NkgyZEw1azJURUxrdks5dTFKbUdoZENLTXpFREV0eW5EUUNqR2xVMTlXNDBkRTJuR05BTzIvbmp6R25jcHVXWHJiUXJRMnp3a3Z6bDF1V1JiSndrZDRDVk16RG1ocGtSQytNalNXQWtRY1VGMzN3cEdtVzk5dUxndHVOK0JkWUMwQStUN2J2L3BkejZlTXFJdTZaWUhacEhzUFhCM1F2VXErTStpL290QnBYbVZaYmpkTVRzRG54MUhxNjh0c3RiYUxRUHpFMHhzR3FQcFhIb1lRdE0iLCJtYWMiOiJhNGU5YmQ4NjI4NDU0MTEyNjU1NDgxOGQ4MDUxNjlkYzE0ZTUwZDU1MWE5ZjdjMmZkMWJmMTQ5ZjE0NGRjZjNiIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:02:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InpRekUvSnZCNnZXTnV4M09paVNrcnc9PSIsInZhbHVlIjoiVnE1bjEvc2tmRnErV0MwQ1VMSnZDTGFzSVo3Y1A5K0w0L0lXQWx3YmF0VnpFcndaK0J0RnRneTgzOFZUaFNyL3FyaHEzOHQ3ZTZPSGJYYWJKeWNHaXd1amhJNEx2emwzb2xuZC9kbE9oNFJFOVpwT28xZUhlMXJSUktCRWsyRzFIZ1Y3ZGNLOTRhKzNCY0pLTnFHOElkcExtSVBQTWQyT0hIMDRtZEhzS2l6NHVsUnpseEtiRTZQUkZmYmJXVEs0NFFrUGpzQlRCcmdBZ0xIUmJuNVliMHMzZ2RzS0tUelRicU9LYlY5cXFkVTJoemxyZzBpeCtZdHE1K3dIR0VRYmh2ZTkrNE5pY3lua214cmZscXRUejJ6SHRKWktMOWxrcklUbXcrbzZMcVhSRG56MVJ3c2NaUlF4NjJxdmxWVkQyVHdLbWtXRVF6Zm5IWnNQaC9Mbk15NDJ4aXozaUxiLy9DR2VsaGxONkpvNnd4aHlyb3hxV1djaThodXpBc0REREpOajQxdS82c2VWTWlBaU5JU2FRWWlUK0JzSC9yUjN1L1J6cjIyYkU3TW5ZcU0xbDh5eGhLakhIT09TOWx1eUhGTEN6Um0zdHRBNElITlRyektDcGp3cm5lc3MzRU8yUlFKdUFoN3JsQWxYQnZVdUxnYXh6QXRmcEQrdXRFdDYiLCJtYWMiOiI0YzlmZmUxMzY5ZTE5Y2E1NGUwZTlmZDM4ODIwYmE0MjY2Mjk4M2RlMDIzYjRhY2I5ZWEwMTMzNmQxNmJiZDJkIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:02:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IktqTS9LZDBmcDhKeWpZYnpUNzM1d2c9PSIsInZhbHVlIjoiOXhpYkpRVGFJWmZ4VTBDUm8ydGU2N013cm5STU54dDIwaVV1SWxrMFR4Y2ZEOGI3MGU1c2s0NS9sV1NTT0QvcVR1aWVZV0UvcEZRcXJ2S2syNDZCaU5JV1NmNFZNdUpWcUJmWUc4ZEF4aGRNM01lY2VTbnhkNjh5OWhEb0Y1Q3hJamRkK3Z3MWZwWitxRlJvdUZiUldqa1ZIWlp1bk1XcDViOGg4WWc4S1JlbmRrMTNPNm03UlVuTHRZWXppRGdRVTZBdmVuWVhCQWJmSFFpZnpGbU1GbCs1NUxUaUtzcHc5a2IzTUpRU1c1YnB1RGNWekZoQUlVTGIybWhsclVwRUNkUjJQZnE0OVpMQldhWXFMYjRlTjh5LzE2b2RudUdZS2VGZC9LOEVpUmU5NkgyZEw1azJURUxrdks5dTFKbUdoZENLTXpFREV0eW5EUUNqR2xVMTlXNDBkRTJuR05BTzIvbmp6R25jcHVXWHJiUXJRMnp3a3Z6bDF1V1JiSndrZDRDVk16RG1ocGtSQytNalNXQWtRY1VGMzN3cEdtVzk5dUxndHVOK0JkWUMwQStUN2J2L3BkejZlTXFJdTZaWUhacEhzUFhCM1F2VXErTStpL290QnBYbVZaYmpkTVRzRG54MUhxNjh0c3RiYUxRUHpFMHhzR3FQcFhIb1lRdE0iLCJtYWMiOiJhNGU5YmQ4NjI4NDU0MTEyNjU1NDgxOGQ4MDUxNjlkYzE0ZTUwZDU1MWE5ZjdjMmZkMWJmMTQ5ZjE0NGRjZjNiIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:02:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1844820206\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1875364809 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1875364809\", {\"maxDepth\":0})</script>\n"}}