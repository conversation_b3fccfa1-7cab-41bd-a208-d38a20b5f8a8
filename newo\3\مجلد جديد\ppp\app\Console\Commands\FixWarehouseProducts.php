<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\ProductService;
use App\Models\WarehouseProduct;
use App\Models\Warehouse;
use App\Models\User;

class FixWarehouseProducts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:warehouse-products {--user-id= : ID of the user} {--warehouse-id= : ID of the warehouse} {--dry-run : Show what would be changed without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix warehouse products relationships for POS to work properly';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('🔍 فحص علاقات المنتجات والمستودعات...');
        
        $userId = $this->option('user-id');
        $warehouseId = $this->option('warehouse-id');
        $dryRun = $this->option('dry-run');
        
        // إذا لم يتم تحديد معرف المستخدم، اختر المستخدم الأول
        if (!$userId) {
            $user = User::first();
            if (!$user) {
                $this->error('❌ لا يوجد مستخدمين في النظام');
                return 1;
            }
            $userId = $user->id;
        } else {
            $user = User::find($userId);
            if (!$user) {
                $this->error("❌ المستخدم بالمعرف {$userId} غير موجود");
                return 1;
            }
        }
        
        $this->info("📝 المستخدم: {$user->name} (ID: {$userId})");
        $creatorId = $user->creatorId();
        
        // إذا لم يتم تحديد المستودع، اختر المستودع الأول
        if (!$warehouseId) {
            $warehouse = Warehouse::where('created_by', $creatorId)->first();
            if (!$warehouse) {
                $this->error('❌ لا يوجد مستودعات للمستخدم');
                return 1;
            }
            $warehouseId = $warehouse->id;
        } else {
            $warehouse = Warehouse::find($warehouseId);
            if (!$warehouse) {
                $this->error("❌ المستودع بالمعرف {$warehouseId} غير موجود");
                return 1;
            }
        }
        
        $this->info("📦 المستودع: {$warehouse->name} (ID: {$warehouseId})");
        
        // فحص المنتجات
        $userProducts = ProductService::where('created_by', $creatorId)
            ->where('type', 'product')
            ->get();
            
        $this->info("📊 إجمالي منتجات المستخدم: " . $userProducts->count());
        
        // فحص المنتجات الموجودة في المستودع
        $warehouseProductIds = WarehouseProduct::where('warehouse_id', $warehouseId)
            ->pluck('product_id')
            ->toArray();
            
        $this->info("📦 المنتجات الموجودة في المستودع: " . count($warehouseProductIds));
        
        // العثور على المنتجات المفقودة من المستودع
        $missingProducts = $userProducts->whereNotIn('id', $warehouseProductIds);
        
        $this->info("❌ المنتجات المفقودة من المستودع: " . $missingProducts->count());
        
        if ($missingProducts->count() > 0) {
            $this->table(
                ['ID', 'Name', 'SKU', 'Sale Price'],
                $missingProducts->map(function($product) {
                    return [
                        $product->id,
                        substr($product->name, 0, 30) . (strlen($product->name) > 30 ? '...' : ''),
                        $product->sku,
                        $product->sale_price
                    ];
                })->toArray()
            );
            
            if ($dryRun) {
                $this->warn("🔍 وضع المعاينة: لن يتم إجراء أي تغييرات");
                $this->info("💡 لتطبيق التغييرات، قم بتشغيل الأمر بدون --dry-run");
                return 0;
            }
            
            if ($this->confirm("هل تريد إضافة هذه المنتجات إلى المستودع؟")) {
                $added = 0;
                foreach ($missingProducts as $product) {
                    try {
                        WarehouseProduct::create([
                            'warehouse_id' => $warehouseId,
                            'product_id' => $product->id,
                            'quantity' => $product->quantity ?? 0,
                            'created_by' => $creatorId,
                        ]);
                        
                        $added++;
                        $this->line("✅ تم إضافة المنتج: {$product->name} (ID: {$product->id})");
                    } catch (\Exception $e) {
                        $this->error("❌ فشل في إضافة المنتج: {$product->name} - {$e->getMessage()}");
                    }
                }
                
                $this->info("🎉 تم إضافة {$added} منتج إلى المستودع");
            }
        } else {
            $this->info("✅ جميع المنتجات موجودة في المستودع");
        }
        
        // فحص المنتجات الزائدة في المستودع (منتجات لا تنتمي للمستخدم)
        $warehouseProducts = WarehouseProduct::where('warehouse_id', $warehouseId)->get();
        $orphanedWarehouseProducts = $warehouseProducts->filter(function($wp) use ($userProducts) {
            return !$userProducts->contains('id', $wp->product_id);
        });
        
        if ($orphanedWarehouseProducts->count() > 0) {
            $this->warn("⚠️  منتجات في المستودع لا تنتمي للمستخدم: " . $orphanedWarehouseProducts->count());
            
            foreach ($orphanedWarehouseProducts as $wp) {
                $product = ProductService::find($wp->product_id);
                $productName = $product ? $product->name : 'Unknown';
                $this->line("- ID: {$wp->product_id} | Name: {$productName}");
            }
        }
        
        $this->info("✅ انتهى فحص المستودعات");
        return 0;
    }
}
