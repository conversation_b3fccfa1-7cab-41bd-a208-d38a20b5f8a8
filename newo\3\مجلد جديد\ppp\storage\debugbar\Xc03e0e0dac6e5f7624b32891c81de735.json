{"__meta": {"id": "Xc03e0e0dac6e5f7624b32891c81de735", "datetime": "2025-06-17 14:50:41", "utime": **********.998787, "method": "GET", "uri": "/search-products?search=*************&cat_id=0&war_id=8&session_key=pos&type=sku", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 1, "messages": [{"message": "[14:50:41] LOG.info: searchProducts debug {\n    \"warehouse_id\": \"8\",\n    \"products_in_warehouse\": 4,\n    \"products_found\": 0,\n    \"search_term\": \"*************\",\n    \"category_id\": \"0\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.991324, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.411255, "end": **********.998811, "duration": 0.5875561237335205, "duration_str": "588ms", "measures": [{"label": "Booting", "start": **********.411255, "relative_start": 0, "end": **********.876444, "relative_end": **********.876444, "duration": 0.46518921852111816, "duration_str": "465ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.876456, "relative_start": 0.46520113945007324, "end": **********.998813, "relative_end": 1.9073486328125e-06, "duration": 0.12235689163208008, "duration_str": "122ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49416848, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "search.products", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1224\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1224-1336</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.017820000000000003, "accumulated_duration_str": "17.82ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.92466, "duration": 0.01209, "duration_str": "12.09ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 67.845}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.947978, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 67.845, "width_percent": 6.79}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.9708111, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 74.635, "width_percent": 7.52}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.974958, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 82.155, "width_percent": 6.004}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8'", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1236}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.981966, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1236", "source": "app/Http/Controllers/ProductServiceController.php:1236", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1236", "ajax": false, "filename": "ProductServiceController.php", "line": "1236"}, "connection": "ty", "start_percent": 88.159, "width_percent": 4.882}, {"sql": "select `product_services`.*, `c`.`name` as `categoryname` from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15 and `product_services`.`id` in (3, 5, 6, 7) and `product_services`.`sku` LIKE '%*************%' order by `product_services`.`id` desc", "type": "query", "params": [], "bindings": ["product", "15", "3", "5", "6", "7", "%*************%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1260}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.986888, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1260", "source": "app/Http/Controllers/ProductServiceController.php:1260", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1260", "ajax": false, "filename": "ProductServiceController.php", "line": "1260"}, "connection": "ty", "start_percent": 93.042, "width_percent": 6.958}]}, "models": {"data": {"App\\Models\\WarehouseProduct": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FWarehouseProduct.php&line=1", "ajax": false, "filename": "WarehouseProduct.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 6, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1063296841 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1063296841\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.981127, "xdebug_link": null}]}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/search-products", "status_code": "<pre class=sf-dump id=sf-dump-1473399845 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1473399845\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1044668413 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  \"<span class=sf-dump-key>cat_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>war_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1044668413\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-521632982 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-521632982\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkthbUQ0TFlYT3o1NDVKOHI0NWFET2c9PSIsInZhbHVlIjoiUG5Qb2JGVWZaS1R6Qlh0Qld5Ym51cFNxNW4zSmRialFoVnBmS3c0UE1zbldEVjhrc3hWalNEMzRCZWxlOVVpR1UrY3BqaWVoOVo5V09VZkk3aFpzbkdaejQwQjhkTExxOHZaVFM0ZDBXSit5T0l6TDQ0RzcwelEvVGdtb0pMcTJuVDRXTm9DNDR1dTlFYVVFa1gyaURBQXFaUVV0Rm1tVjFlS3FoVVBkcnA1Q2ZrQStOL1FvZmRweFZHQ1VGcmVWTFNxYUpyRWE4eVdLd0NRWXd3YXk5cjhoWFo5NXpvK09jcC8zcTFqb2g2UW5MQmRJY1FFM2dpTE1QeVlxcUFMU2N3cksxa2ZpMDBYVThJSWJtcVpZMHgvWDRmZ2YyVzRTWjdLb3hXeXl2UGFaczZlOTB5by9YSUU0bE5YR3JlYnF2eTVMS3VaajJvZlR2K1pDQnRNVEsvcndCbks0OVhPMnA2cnNQMWFnTEUyNG1YTkNtU3REM2xjWndQTW9SS0FBTFd0czlCRGhUOGdlUlRnbzVjYmpGUlZGME14WXdTR0lHVWhlVDhUcTZTdm9hb3pvS3JYVTlhdDVDMTJKTXBic1BKN3pibk5qUUxmSS9lNWRUbWMyV1c0UGRLdndsOEFiTlFIVmx5Z2ZuODQzaWpYSGFSdGE3LytmaGhHMC9QbnkiLCJtYWMiOiI5ZTljNGYwZTUwZjU4ZGI2OTQ4N2Y1NDA1YzBmNDI4NWFjZmVkMDIxMDUwMjIyOTM2ZjdlMzQ5YmFhNDQxNGM4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlNGSC9FL0hvVmJWUHkwVnY1NGpQdkE9PSIsInZhbHVlIjoiMzdGMGg5dUNXR3NHSldDQkJRTkxoVTFvQll2dmF4NUhldjB1MkNIcjN4Q042Q05iWmJBYlYwVVpCckZjM0hIRzhlTitpU1R4SDU3TmtIK3dPbVFYeHJOcTNCeVUyT3ExY0VpMWJDZXN2THBYTmtnOGh2S25zbFVpSmdxMlBkejd5UEl1K2tpRGYvK205d0ZKa3M5bTdhaEw1YlhUY2piSHBQQW5jNzRKL0NHVDVsMExZajlCejFIMXdZZ0N1T0FTb0lydy9icG12OGRleFFEbG1vTW9ZdWcrQmNVMENUWnZKMlA5MHFNSlFseUpycm5MUysyMUVXbnRkTmN0K3d4RE50YkNiRkdlZWlMTUh5UldJNGcwWmNaWXo0SVcxdW4vR0tVT3JtQk95M2kvMTl4enM0RmpTZ2YwMlNtZ2hOd3l4R3dzcTVYeUZCcVBOZWFZYnRRTGw0UVhhYUp6Qi9nRy93MnE1aWZoamhTL09rSW9QbjZ4SmhSQ2pTVjR4aFE0TWdYVUZEeGNNQXhDYTNOWXFUNm5sZWZBYXlMclo0cVptNUMvbDhOVmJ2WFp4V1BxQjNmejVlRlpLWG0wS3U0akNWM29SWXoxSTFXb1NEU3RyMXJSS2J2UkxQRGdKajhjVkVqNzMxNy9EeXdCZzVrYzJCOVVsTjlHRDlEdHh3U0QiLCJtYWMiOiIxY2FjYWYyMmU5NzUzOTM5NDc1MjMwZDg0MTQ5OTU2ZGI3ODMyNjI4ZGYxNTFmNDY2ZmZmMTA0Y2U2MjNlMTVkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-323460557 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:50:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImEwTXhnMm0ySTYrOW1MVXppdTdzaWc9PSIsInZhbHVlIjoiSUxtSXRDbFJsdVFkQ3FBZWtGMEtNU3FhWGwwVFNQL2NtNmtuT3dlNStPaDJZYmxUaWVYQjVaU3pRRGkwcDdNTVJGVlU0RDl2RDhDSU9PSGFSWVFUMENvM3FlQ0NKcEVRdTBiY2lmRkNGZEMvNkhzb1NuOEhiTHNUSW45ZmpoTEd2QnBlbDdqK20rV1ZkRm0zOHY5dmdvWjdkRlkyanFOeE93QWZHd00vTGhIdXlzd01jcFNHbmpFd3hWT0NDV3phaWYydFlKb1Jmd2hwVzFsMFB3V3lKY1owNkJRdUdNMFdPV1VFaDhLUklYRUFEZlg1a1lSU3pMMWJLbjhMQUxJckgwd3pZTXkxZVNiTXlMUm54NTgxL0YyRTZsSmJBMy9kTVhrRDZCK0J1OU1LK1ZNeHBINDNKZGxYeTlnMXQ0L3BKQTJlNHJXRm4vdldLd0xoQk5CUUUzbXkyM2c3YkxMMHZmd0dtU0l5UmJWRWxKVFhieVlCVlBpKzlodElNSDdJL0FSTTk3d0lhWXlIZ2NGaEI2MVVzNkY1VjJrZ0pSNXc5aGcvMlE4N2ZuOFlsR2xjbjJHZTRPVnNpTHMrbXZjU0g1WDFPTytrMWFsVExVNzlDakF6YVhyb2duNExrUmdUdFkrM2w2NjlWZTJQQ1ZkT3VGWTg3Rk91RllWL3dXdG8iLCJtYWMiOiI2N2JmYTYyMjBiN2MxMzQ4YzRiZWYyMzhiYjcxZWM3M2ZhOGZmMWIxOGM3NzFkY2ZjYTQ2ZGQ2OTExZmEwNmNjIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:50:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik9mWmVkTmlBaUpQSGhsQ05VSlJqOEE9PSIsInZhbHVlIjoiaVJVdzUvakVoS3RkVE52cjdIUGpQRVZleHZuZSsrUmtNWFRPNUZsdHk3czVXQWtud2lJcHZFekUwQ01EeUNWTVV4b0hkbEpkbnNjQytVMm5YcjlGK0djdHMvQTFYSjF1VXFjUVREMXV3bHJGS0NDaTJ4ZzNXdldzNDE4c21BZTd4SzJkalJ1NVJlQzRYb1JOWGRpYmt3eEtBeUgvQkcvSXFrUXBPQ20zSitGUnZYYVFiQ05EM2pOait1bTRmUElYL1JvbTVPYVVjeGhkRWZUWWl0RFlQekhyVzJTQUZ0OUJ2a1FPdG8rQlZnc3BUQmxSUUs4YTNCV1oxMDQ0NksxR0N2T3NwUjBsYkVSUnBET0Y0TWFHWEpFUTFiUkV3SUVwd2t5WUNGZWY2Nm1PNDI4MmJPT2syb1NMN3RwYndBbzFQQ0JzOFZHNExEVlhVNTdnQ3RaalhWSElxUzBPNU9MN2drLzBTeVlGMDZOVzFWS3Nid3EwNWxEOXdpclNnOTEremNDcmcyY0h2bDl5cUllU3o0TnpjcUp6WHlNKzhFdFkzRDB3TXNjVkIzYlJUWUxXOW1hSCs0VjlYeXVML3kvVmxoNGFwTmh1elRudTJxV3J0TE1velBMQXVrQ1JHa2ZQUHlZM3VTSjRSald5SFFrMXRZOFY4NllnSFBBbHlIQXYiLCJtYWMiOiJjZDM2NDY2NGYyZTc2MGQ0ZmEwNDE5NDI2Yzk2YWM1NjEyMGNiMTYxZDFkMmY3NTgxY2FlYTM4Yjk1NDliOGY3IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:50:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImEwTXhnMm0ySTYrOW1MVXppdTdzaWc9PSIsInZhbHVlIjoiSUxtSXRDbFJsdVFkQ3FBZWtGMEtNU3FhWGwwVFNQL2NtNmtuT3dlNStPaDJZYmxUaWVYQjVaU3pRRGkwcDdNTVJGVlU0RDl2RDhDSU9PSGFSWVFUMENvM3FlQ0NKcEVRdTBiY2lmRkNGZEMvNkhzb1NuOEhiTHNUSW45ZmpoTEd2QnBlbDdqK20rV1ZkRm0zOHY5dmdvWjdkRlkyanFOeE93QWZHd00vTGhIdXlzd01jcFNHbmpFd3hWT0NDV3phaWYydFlKb1Jmd2hwVzFsMFB3V3lKY1owNkJRdUdNMFdPV1VFaDhLUklYRUFEZlg1a1lSU3pMMWJLbjhMQUxJckgwd3pZTXkxZVNiTXlMUm54NTgxL0YyRTZsSmJBMy9kTVhrRDZCK0J1OU1LK1ZNeHBINDNKZGxYeTlnMXQ0L3BKQTJlNHJXRm4vdldLd0xoQk5CUUUzbXkyM2c3YkxMMHZmd0dtU0l5UmJWRWxKVFhieVlCVlBpKzlodElNSDdJL0FSTTk3d0lhWXlIZ2NGaEI2MVVzNkY1VjJrZ0pSNXc5aGcvMlE4N2ZuOFlsR2xjbjJHZTRPVnNpTHMrbXZjU0g1WDFPTytrMWFsVExVNzlDakF6YVhyb2duNExrUmdUdFkrM2w2NjlWZTJQQ1ZkT3VGWTg3Rk91RllWL3dXdG8iLCJtYWMiOiI2N2JmYTYyMjBiN2MxMzQ4YzRiZWYyMzhiYjcxZWM3M2ZhOGZmMWIxOGM3NzFkY2ZjYTQ2ZGQ2OTExZmEwNmNjIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:50:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik9mWmVkTmlBaUpQSGhsQ05VSlJqOEE9PSIsInZhbHVlIjoiaVJVdzUvakVoS3RkVE52cjdIUGpQRVZleHZuZSsrUmtNWFRPNUZsdHk3czVXQWtud2lJcHZFekUwQ01EeUNWTVV4b0hkbEpkbnNjQytVMm5YcjlGK0djdHMvQTFYSjF1VXFjUVREMXV3bHJGS0NDaTJ4ZzNXdldzNDE4c21BZTd4SzJkalJ1NVJlQzRYb1JOWGRpYmt3eEtBeUgvQkcvSXFrUXBPQ20zSitGUnZYYVFiQ05EM2pOait1bTRmUElYL1JvbTVPYVVjeGhkRWZUWWl0RFlQekhyVzJTQUZ0OUJ2a1FPdG8rQlZnc3BUQmxSUUs4YTNCV1oxMDQ0NksxR0N2T3NwUjBsYkVSUnBET0Y0TWFHWEpFUTFiUkV3SUVwd2t5WUNGZWY2Nm1PNDI4MmJPT2syb1NMN3RwYndBbzFQQ0JzOFZHNExEVlhVNTdnQ3RaalhWSElxUzBPNU9MN2drLzBTeVlGMDZOVzFWS3Nid3EwNWxEOXdpclNnOTEremNDcmcyY0h2bDl5cUllU3o0TnpjcUp6WHlNKzhFdFkzRDB3TXNjVkIzYlJUWUxXOW1hSCs0VjlYeXVML3kvVmxoNGFwTmh1elRudTJxV3J0TE1velBMQXVrQ1JHa2ZQUHlZM3VTSjRSald5SFFrMXRZOFY4NllnSFBBbHlIQXYiLCJtYWMiOiJjZDM2NDY2NGYyZTc2MGQ0ZmEwNDE5NDI2Yzk2YWM1NjEyMGNiMTYxZDFkMmY3NTgxY2FlYTM4Yjk1NDliOGY3IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:50:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-323460557\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-773880375 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-773880375\", {\"maxDepth\":0})</script>\n"}}