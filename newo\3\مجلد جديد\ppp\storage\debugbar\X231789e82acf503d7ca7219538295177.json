{"__meta": {"id": "X231789e82acf503d7ca7219538295177", "datetime": "2025-06-17 14:23:12", "utime": **********.079719, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750170191.303723, "end": **********.079758, "duration": 0.7760348320007324, "duration_str": "776ms", "measures": [{"label": "Booting", "start": 1750170191.303723, "relative_start": 0, "end": 1750170191.945361, "relative_end": 1750170191.945361, "duration": 0.6416378021240234, "duration_str": "642ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750170191.945381, "relative_start": 0.641657829284668, "end": **********.079762, "relative_end": 4.0531158447265625e-06, "duration": 0.13438105583190918, "duration_str": "134ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46130976, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.03085, "accumulated_duration_str": "30.85ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.005899, "duration": 0.02736, "duration_str": "27.36ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 88.687}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.049654, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 88.687, "width_percent": 4.862}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.061949, "duration": 0.00199, "duration_str": "1.99ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 93.549, "width_percent": 6.451}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-2088946577 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2088946577\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2017373544 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2017373544\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-75218131 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-75218131\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1134712479 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://localhost/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=3pzjoc%7C1750170159624%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlJydUFvTFJveStEQWFkVHdxMXZ1bmc9PSIsInZhbHVlIjoiSjdXT2ZzZzlER1RCSi9lZHVZWmJZSmc2Uk1EcHI3empzUFFNWTlmeDljTHhSRmtyNVlCQ0FjZlRtMzdmaUVuazdsRXlzdWk1MUx4NlVxamVLOTNQWjRoVzF5SDhpVEVqKzRITFZ5RVV4bXF4Q0xpY3EzS3NjRW1xL3pDbXd6a2JxZTFPZ2xQQnhZZ3pjdG16Mmp0RnJjWHRmbjRSLzZTVERNYktwWm91L3RuOVFjSUlaa3RNMkhwcFRITlQ2QXdHU2tGYU5zRkVackxudUlIald4UnFMVmh2b0IvVUl4V0V0dWljdWhSRzFVVlcxWmtBeEJmZElUZU9WTGFlQlNGcVR3cXRObXFJTDd2YTd5anlXUjJrV3BPUHc5ZlBCckNBcGZFQTEvdHFEK2ZnelRNVVU4NVZkeHM3KzRJOW04eVlvUUNpSFJ0c3ZWWWoxTXlTVTR3ODkxMHBPUUZDRHB0SGlWZlo5UmNCalE5Y1k5ODdQS0NpNjBXQ0t5T3dJWVFSNDlVUVhNMDBTMW9EUGFPWk1BWUNXaTJyYTdsSFZETlljRnlMekphYVNFa1lZbU1HYlBpOWV4RnBWNE44Z0ZaTUJtNndZSW0xZW9UeEFoTjlENEdzYXdQQk1UTVJSOWUxOVV2bURwUi9Yd25LUW5sT0VCS2FSdC83RDVxS3M5ZEEiLCJtYWMiOiJlY2ViNzBlZmI5OGYzNmJiN2I3YjAyOGM0M2VmZmYyYTg4YmFjZGI2NTgzYjUxNDE1OWZjODVhZjk5MTlkNDliIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlQ3TWE2Tkduc0l0cEp3WmFEYjN3MlE9PSIsInZhbHVlIjoiWmRPcmgxVVRGZTc3U25ucDRGVTlqZVp5T2xVbEdZeVVFOXpDMEp2QjNnRTNzY0FmSHhlazZLRTJDczI3WlQvR0k2bkkrN0lIOUR0cEJPSzlnSjhKaDZvWnJhOGFkVnFkVFl5cVQxQXFuQmtzS2RtTk4yZ3JvT2R5Snl3WW5LSzRGTjZIVkV1VkpBQlJ6ZTFUaUdBSHN0Y05FZlpKVDRmOVFIOWpKbXJSZk03UDc3OGhiREFwYkJWZUx6N0VIVjBwTUdnalRsa1BaVEFtaFBzcC9KSG1MMVdCNlpMVStFKzUwRkpmNHhvdzJvYkh6SU1Da0J6Z1BIZUVMa2F6MWZhei9oYmE0STZibjFDUDM1L3VOTmtjazc5VHFWUVpkaVVtQ21NTE9iSHg2bEswcU9wMHVRUzhGZUFteCtBVmNwdGJVMVphZmRvSjgyZWREK29OMHl2bzdJRG94dWFzRWRYWVV4S1dacVpsa3Yxb28vSGgyMzZCVnplMWt6NGQ2RWlSaWNhOThONFNYMTl3b2NaOE1Uc0ozRVFFcmYxSGJkQ3VGZEJzU1hLcThYdGh3WGt1RVNFVEdUbGUvUGJjTkVUOVR1elpXTmZwVXZxcE9DZElOZkdtNVNMUDVIY2lxbndsSWxPd2hpd1ViNjVaL2NtZmtDSTBrczVNWG1UTnlNYSsiLCJtYWMiOiI0YjZmYWNhMWE4YzU2OGM5YmZkNDk3MjEwMjI5MmNlZmIxOTQ0NGViODQwZGMxNzg4OWU4NGRhZTQzZjQyM2JkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1134712479\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HxnOnSuhAAPTfN2VS4q58slgJoytwtHYqaX0oLoW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1981152820 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:23:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkVnTHpSeEZrMTZhMmg2R0wvamZIbGc9PSIsInZhbHVlIjoiZzVjeHYxOHFnY2xBUmltVU1ONHNWUlhiQm1OdlpKcG8zYWgrTnZwUk1DUU5yWEliVXYycVM0bHRycW55Uk1UMStRTHVBVmJxQys5cDJ2MjBTSkJIakw1WVNRS0tsTmlsWXVEMWZwcUlkNUZkYm1SSzl3OUJOMjlqOWtQbWN6ZHU2OEU5d2VkKzRXY1hadGxTTi9oQXc3VjdNNGRnV3FtT3llNUJqcUltMWNCdEEyWFpTT0NpUWdpNElEaDRzbk14cVVwYnpkRkFQWGhQQVVsaWFPUU5KdHhtRDBaSDcrbE9PSzFtYzJNamZ5Uy9GV3RRVld5c09WWGNSY0xXTVhXTXB1ajZlS0NtcVd0cUJCQi8zK0pJbU82MXgwbVpjeW9uQVhLMjlHUkFZVjR0cExHb29JTmdBN1BOaFFNU0l0RkpOVFdRN1R6NnIyVW1jZzRoZ3NEaG16QXVsUDhYSit4UTlxamdLZ1JOWGgrWS9KaEQzNGFrakJabFJCRzdDYVcrMitEdjY0RExVaGZSam1xd1g3bm9hbDhxN3l2NEM1Ry9DbGRmTTN2V2tqM1QzN0V6clFjRDFGb2IwUW9GVE9td21xdXUra3hPRFpjUnNlczl2SHo4NkpYT1JkbFByYzZ5MXQ5UjMrKzRnQVFSa3lRSS96Q3p1THBKZWVYaDlzUnAiLCJtYWMiOiJmNmM5OWI5NTQ3MGVkZWExYWRlYzRhZmRjMzNjMmE2MGNiZTRhOTI1ZTM0YWExYzQxYjk4ODFhNTFkY2I1OWQ4IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:23:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik9VUWRLeEFHM29mdSt4VEtORldNU0E9PSIsInZhbHVlIjoiaGlqb052NFFWSlY4cVdhZldhMGJJc085Ry9sVEJlbjFtalJUTGRQME9TZ1hERUtMcGZ5Si80QXpDcGZ2QTZTU0NRQ1c1N04vczV1NEZoMmxNRTRaTmFPVE1hbFBDVHE4K1k3dWRmMENMMHQzbTVleVQrK2IvczBXNERRQk8xRzVoTVpUeWtveDVjbFRnOWJIWGs0eDM1VnNabjRLeUJkM0pSRklkcTcveFFWb3ZKMTJQbUd1ZTJFdW5uZHlvR0kwZW8yMHNLMkx5cjA3cnFJcTJWTWsrV3lFYkV5RG1aNHJORVM2WVFqemdZVFozS3NIM2MwMXlWZzZwMFJsT0ZPRm16VTkyNjY5SVpWK3Ard3ZlVEsvTFEyRFc3NDRwQ0EzMUs2UWpBVzVPR21RUzhRQTlaV3hzdWViVTg1d0RUb0wzUWJEcnQza2tKMUZWSHA4YnRUOHdoeXd2R3dvQ1lkMWxEamJKUnZlZGVHTHJzMXZtT3hxNmFlM3ZSS2RkRGxaeitUWjNjeXlIMW01QWRRZnZFZGNTZ0w0YjhiRlF5bDJzSmR0MDFzU29LMmVsZkpVRDFnclVKS2pQdVhMTWRHZWVtdEZFS1k2aFBveVZ1bXUwd1lLeGx1bUFwSVRFQlJvTEszazkzM3VaZEN6TEs5bFErRVJ2cG15S0FiQlQyaGgiLCJtYWMiOiJhOGJiNWZlODlhYjQwNTg2Nzc1MjVjNzkwM2VkZmFjZDc0NzZhMGJkYTViODQxY2NiMWM5MjcyNDdhZDllMjVmIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:23:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkVnTHpSeEZrMTZhMmg2R0wvamZIbGc9PSIsInZhbHVlIjoiZzVjeHYxOHFnY2xBUmltVU1ONHNWUlhiQm1OdlpKcG8zYWgrTnZwUk1DUU5yWEliVXYycVM0bHRycW55Uk1UMStRTHVBVmJxQys5cDJ2MjBTSkJIakw1WVNRS0tsTmlsWXVEMWZwcUlkNUZkYm1SSzl3OUJOMjlqOWtQbWN6ZHU2OEU5d2VkKzRXY1hadGxTTi9oQXc3VjdNNGRnV3FtT3llNUJqcUltMWNCdEEyWFpTT0NpUWdpNElEaDRzbk14cVVwYnpkRkFQWGhQQVVsaWFPUU5KdHhtRDBaSDcrbE9PSzFtYzJNamZ5Uy9GV3RRVld5c09WWGNSY0xXTVhXTXB1ajZlS0NtcVd0cUJCQi8zK0pJbU82MXgwbVpjeW9uQVhLMjlHUkFZVjR0cExHb29JTmdBN1BOaFFNU0l0RkpOVFdRN1R6NnIyVW1jZzRoZ3NEaG16QXVsUDhYSit4UTlxamdLZ1JOWGgrWS9KaEQzNGFrakJabFJCRzdDYVcrMitEdjY0RExVaGZSam1xd1g3bm9hbDhxN3l2NEM1Ry9DbGRmTTN2V2tqM1QzN0V6clFjRDFGb2IwUW9GVE9td21xdXUra3hPRFpjUnNlczl2SHo4NkpYT1JkbFByYzZ5MXQ5UjMrKzRnQVFSa3lRSS96Q3p1THBKZWVYaDlzUnAiLCJtYWMiOiJmNmM5OWI5NTQ3MGVkZWExYWRlYzRhZmRjMzNjMmE2MGNiZTRhOTI1ZTM0YWExYzQxYjk4ODFhNTFkY2I1OWQ4IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:23:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik9VUWRLeEFHM29mdSt4VEtORldNU0E9PSIsInZhbHVlIjoiaGlqb052NFFWSlY4cVdhZldhMGJJc085Ry9sVEJlbjFtalJUTGRQME9TZ1hERUtMcGZ5Si80QXpDcGZ2QTZTU0NRQ1c1N04vczV1NEZoMmxNRTRaTmFPVE1hbFBDVHE4K1k3dWRmMENMMHQzbTVleVQrK2IvczBXNERRQk8xRzVoTVpUeWtveDVjbFRnOWJIWGs0eDM1VnNabjRLeUJkM0pSRklkcTcveFFWb3ZKMTJQbUd1ZTJFdW5uZHlvR0kwZW8yMHNLMkx5cjA3cnFJcTJWTWsrV3lFYkV5RG1aNHJORVM2WVFqemdZVFozS3NIM2MwMXlWZzZwMFJsT0ZPRm16VTkyNjY5SVpWK3Ard3ZlVEsvTFEyRFc3NDRwQ0EzMUs2UWpBVzVPR21RUzhRQTlaV3hzdWViVTg1d0RUb0wzUWJEcnQza2tKMUZWSHA4YnRUOHdoeXd2R3dvQ1lkMWxEamJKUnZlZGVHTHJzMXZtT3hxNmFlM3ZSS2RkRGxaeitUWjNjeXlIMW01QWRRZnZFZGNTZ0w0YjhiRlF5bDJzSmR0MDFzU29LMmVsZkpVRDFnclVKS2pQdVhMTWRHZWVtdEZFS1k2aFBveVZ1bXUwd1lLeGx1bUFwSVRFQlJvTEszazkzM3VaZEN6TEs5bFErRVJ2cG15S0FiQlQyaGgiLCJtYWMiOiJhOGJiNWZlODlhYjQwNTg2Nzc1MjVjNzkwM2VkZmFjZDc0NzZhMGJkYTViODQxY2NiMWM5MjcyNDdhZDllMjVmIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:23:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1981152820\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1123389449 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1123389449\", {\"maxDepth\":0})</script>\n"}}