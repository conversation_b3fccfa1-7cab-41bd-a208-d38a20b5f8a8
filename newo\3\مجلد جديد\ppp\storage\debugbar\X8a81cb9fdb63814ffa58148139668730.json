{"__meta": {"id": "X8a81cb9fdb63814ffa58148139668730", "datetime": "2025-06-17 14:01:08", "utime": **********.473946, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750168867.76471, "end": **********.473974, "duration": 0.7092640399932861, "duration_str": "709ms", "measures": [{"label": "Booting", "start": 1750168867.76471, "relative_start": 0, "end": **********.376467, "relative_end": **********.376467, "duration": 0.6117570400238037, "duration_str": "612ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.376478, "relative_start": 0.6117680072784424, "end": **********.473977, "relative_end": 3.0994415283203125e-06, "duration": 0.09749913215637207, "duration_str": "97.5ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46091016, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02485, "accumulated_duration_str": "24.85ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.413714, "duration": 0.02268, "duration_str": "22.68ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 91.268}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.44862, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 91.268, "width_percent": 4.024}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.459444, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.292, "width_percent": 4.708}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-38876686 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168503247%7C8%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InhKcXd4dTAyMnp4aE9sbWNLN2NUQ1E9PSIsInZhbHVlIjoib3dDTE1NcWVsY1FkWUQyY0lvRFNNQm9vb2N2Z1FjYU1BNmhQNXJVQmFkUU83RldXUmg0TERKV3ArLzR0emNIRWhIdk03Qi9hODVUbEhFbkk4UjROWC9CQ1EyeWlqVzJNSDF2L1h2bktWY0FQamxHQWwxMldYTTZGNWlPOHZBa1VlcUZzZys4WFZ2dDhoYm1CMEtiN1BvdllNekdGQnFUWDdaa29VbUFPNExrV0FEdm5hYVYrZnlMNXNnVEdDK1QvaUc5UW51VExWT2JmRkZ6NFZSMnNXTG5GYWtVbGRGMk1iU2thTDQ5VDlMd2EyQUttaWpsZGJLRG5Ic1dBNHBhNFU2OHZEMXlhS1JFNi9rWk1IVWR4L1Q3VDRYT2hmUndHbjJKa1hpSFl6UmdNVUxNRWNsRHh4aFMxZ0FHTSticHQ2eEVLS1ZrekZnUGYzN1Jxb3NDR0pVQWhLRWJYb3FxQlV0Y2tiNjZvVXlpZHNFa1k5K0poYXBJRVYxZVhHaEU2NE9NR25aaGtaYkp2a0UzZHpvT0pGQkNVK0RRTXB4TDgwN0tGcnZXdU85MS85WjRoVGhKSkVVcEs2K1ByY24wTTJ3c3gzV1AwQnF5MTMxeE5vOHFEWUt3Ui96ZHhwcllNNUdXN29vK1prQkNrQTBkNjBPS2FuWHpEd2tBei9qaW4iLCJtYWMiOiI5MWQ1NDBlNjJhN2I0YTJmNGE5MjBhNThlMzk2ZmM1ZjgzNzJmZmFiMTRlNDc3OTZlZTFiM2Y2NjQyMmFmMmY5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkhyYWZ1TVk3cEtPNmZPSjNoRUJwcGc9PSIsInZhbHVlIjoiczNYeTVyei92aHg3OEE1MGN0QUc1aE9KMXNFWndoZXJMTDl6YzgySC9RTGswdnBXblVxSDdJeXJDbEY2OC82SGtkUVE4SzArbi9USUNEVGpYclBDV0R2R3JHbHpvaWEyaUIyeW10MkpQdUhpR1JYWW9sd05GaElkSEl1NGRKYldjQ3o4WGRQSjY2RnUzTndTazBZRzM5aTRkVVpCcGpTTkdLMkVqTVdlM2ZzMExQWEhpaEJJN3c0bXg0NnE2TFIrcHZWZG05NVZzSEZCZnBrVklCU0lEdTh3VHczMC9DTG1mNjJIZXpzTGwzREk2dkRjV0FCQmF3MjNzU3BHOTQ3OVhVRk5GMjJIRll3d3JrNzg3QUFJaXRjUjBNRXhKSXV2a1c4UE5KNktVKzltYUN6NDgxUk44QWsrd3oxbVl6a3I4WXplc09yNk96YlB6V1MzS0JZem5YM3VES0lYYXNKYnBGY3VnMDFkeTJVd2dNWFBCclJRV3BnYW1aMC9KaHp1OXJvMzJNcjZTVGZZMllVTjVONE9MWVdmZEdrZmdBN2NEQjEyN0kxM2tvanJzcmIxZDNPQTZGTk5KSVlmdWc1SGYvbWhhWFU0ZGdkdTI1WGtWVWhnclNwNXVVNWxYQTZPMXlDbEE2UFZYOGxsbS9QaUIrZkJ2d3loa0NyeG4yL0QiLCJtYWMiOiI0MWI2MGNlZGJiNjhmMGUyZWNkNmMxN2NhOGEwOTY2ZDY5YmE0MTEwNjBmMTBjOTE4YmYwZTI3MzVjZmRhMjY2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-38876686\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1805061729 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1805061729\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1504637566 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:01:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjNCN2JTNFBlWm5HTlBVR1B3eVlzd0E9PSIsInZhbHVlIjoiTGx0NU1QMlNPcjBHVks3UUxNdW8wTHVhZ0hWUVprZ29tWlp1alZDMjkycXdnb2s5aElZY0R3UEh5dlczc1lISURLckVvWHdsK2Z3VUxZVSs3TGhNTGRIY2V5UHVvallQT0l2VUFCa0ZpUC95SFJTa1RqVStLMCs4SjIzRjMraDd2NmhQZDRYSnlteVhMSDRqQS9Qd2tlTDlvSE9tUU9VaUZ0NjZSVHVXc1lCanI2VDlSdVdjdVFNNXljTmJSSXlNa0dzVy9WeEFJei9oTXpveUtCRiswZWR1THA1dTBFZ2NkcjRzMCtZVE4wVGV0TjhwV3NYQnY1R3dtRWhOLzQwaE5RbGVqbEtkTGZNOTFCOElvR0xxZCtDQzRETGZsSURQM3YwUUhPNUpuNDZmMTRjMFJTVnAwMTM3cmU3NzdNY3dlWk9Bb08zUGtmWGMyeklOcnlUMU4ydVowN2FHcTE1TFRETDVsWHpSZncrOGxKUlphQys0a3hjR3VVS3lZL2ZpTFg3NFlWQVhoZXFScTFXVldYZGJUQWdoV0RkbXRYWS9MdXM2S3YzeE4zd2IybVhvbDhVRjIvRzlteGVtWW5IZ3NQb0FuNTZVSjE5YmpQc240dEFrd0V3VjM3SU9EYm1QREZKRFA5WlRWU2lrWDY2UTBhTmtvL0dBRThqczRIcUQiLCJtYWMiOiJjZmUzN2IxZDg3NjA1YjAzMjliNDNiNTg3ZTE3NDgxMDkzNTgzOTM0ZWQyYjAwZWRiMjc5ZWUzNDJhYzJhOGU3IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:01:08 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjYvaGFBNnVhUlczRm44TWFxcG16b3c9PSIsInZhbHVlIjoibXZwWjNVVkRCYzlPK2VxMGhBOU9NQy9za0JHUDFmL3R5L29uY2svY3VreG15WS9IdkJyUmdmWlkyQUVtK1d1U3p0TzdEbjJ3YlJTQ1M0QmUvMWxMWG84bEFSODNmMEU3Q0loSWZCamxPaVd6ZXF6c2w3SWxTOHRNc1RYTzBWRVRFUlZYRjlheEpWZzJaOWFMbXdTUXMrUWtaZS93RU1nU015T0NFczhWQmlSRDlHM0FHTk9SSTNKSXZQOEhBNzU5ZWw3RkRFKzAxOE9VVEtUTUJYT1ZyV1FtVU1ma291UGZxTGNOWFk1TGNOZ3MwdVhTZVJkYnFaVzN1RjlzK3dtVit5dkJsa1MwUkpONzJkTUF1MTBmNVErUkNQaDdrbllxbk0veGlnZFBWNlBPMjlKSTlES1N3ckprdnVQSExjUlh2eC9odENLOFJqclYxOTFjWEpHeHpUV2FwR1pxYi8yNzFBVmZFMk4vamlWSVlmWXBhd2FRdmFvT3Rkdi9RdmJyUzlZMkJiVUhiRjk3Vi9hamhHcTI3NnRSVk9SOEphN2dDdWlxaDFQd2dvWTZVdVM0QktYeEZOeVh1d0w4N0QxeGRYS2NiL0ljd0ZXQ3dscnMvMXZSVldIWUFNd2ovSURuYytYRVhMNWs2RnhlL2tqT3pvNk5NbEdSQ05TV1NUQ2ciLCJtYWMiOiIyMWEwNGZlY2EwMDlhZDRlZmNiNzMzYTQ3NWIxZmZjMGY5ZDY4YTUxNzg0Zjk0MzdkOWE5NGY1MTViODhlZTIwIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:01:08 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjNCN2JTNFBlWm5HTlBVR1B3eVlzd0E9PSIsInZhbHVlIjoiTGx0NU1QMlNPcjBHVks3UUxNdW8wTHVhZ0hWUVprZ29tWlp1alZDMjkycXdnb2s5aElZY0R3UEh5dlczc1lISURLckVvWHdsK2Z3VUxZVSs3TGhNTGRIY2V5UHVvallQT0l2VUFCa0ZpUC95SFJTa1RqVStLMCs4SjIzRjMraDd2NmhQZDRYSnlteVhMSDRqQS9Qd2tlTDlvSE9tUU9VaUZ0NjZSVHVXc1lCanI2VDlSdVdjdVFNNXljTmJSSXlNa0dzVy9WeEFJei9oTXpveUtCRiswZWR1THA1dTBFZ2NkcjRzMCtZVE4wVGV0TjhwV3NYQnY1R3dtRWhOLzQwaE5RbGVqbEtkTGZNOTFCOElvR0xxZCtDQzRETGZsSURQM3YwUUhPNUpuNDZmMTRjMFJTVnAwMTM3cmU3NzdNY3dlWk9Bb08zUGtmWGMyeklOcnlUMU4ydVowN2FHcTE1TFRETDVsWHpSZncrOGxKUlphQys0a3hjR3VVS3lZL2ZpTFg3NFlWQVhoZXFScTFXVldYZGJUQWdoV0RkbXRYWS9MdXM2S3YzeE4zd2IybVhvbDhVRjIvRzlteGVtWW5IZ3NQb0FuNTZVSjE5YmpQc240dEFrd0V3VjM3SU9EYm1QREZKRFA5WlRWU2lrWDY2UTBhTmtvL0dBRThqczRIcUQiLCJtYWMiOiJjZmUzN2IxZDg3NjA1YjAzMjliNDNiNTg3ZTE3NDgxMDkzNTgzOTM0ZWQyYjAwZWRiMjc5ZWUzNDJhYzJhOGU3IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:01:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjYvaGFBNnVhUlczRm44TWFxcG16b3c9PSIsInZhbHVlIjoibXZwWjNVVkRCYzlPK2VxMGhBOU9NQy9za0JHUDFmL3R5L29uY2svY3VreG15WS9IdkJyUmdmWlkyQUVtK1d1U3p0TzdEbjJ3YlJTQ1M0QmUvMWxMWG84bEFSODNmMEU3Q0loSWZCamxPaVd6ZXF6c2w3SWxTOHRNc1RYTzBWRVRFUlZYRjlheEpWZzJaOWFMbXdTUXMrUWtaZS93RU1nU015T0NFczhWQmlSRDlHM0FHTk9SSTNKSXZQOEhBNzU5ZWw3RkRFKzAxOE9VVEtUTUJYT1ZyV1FtVU1ma291UGZxTGNOWFk1TGNOZ3MwdVhTZVJkYnFaVzN1RjlzK3dtVit5dkJsa1MwUkpONzJkTUF1MTBmNVErUkNQaDdrbllxbk0veGlnZFBWNlBPMjlKSTlES1N3ckprdnVQSExjUlh2eC9odENLOFJqclYxOTFjWEpHeHpUV2FwR1pxYi8yNzFBVmZFMk4vamlWSVlmWXBhd2FRdmFvT3Rkdi9RdmJyUzlZMkJiVUhiRjk3Vi9hamhHcTI3NnRSVk9SOEphN2dDdWlxaDFQd2dvWTZVdVM0QktYeEZOeVh1d0w4N0QxeGRYS2NiL0ljd0ZXQ3dscnMvMXZSVldIWUFNd2ovSURuYytYRVhMNWs2RnhlL2tqT3pvNk5NbEdSQ05TV1NUQ2ciLCJtYWMiOiIyMWEwNGZlY2EwMDlhZDRlZmNiNzMzYTQ3NWIxZmZjMGY5ZDY4YTUxNzg0Zjk0MzdkOWE5NGY1MTViODhlZTIwIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:01:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1504637566\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}