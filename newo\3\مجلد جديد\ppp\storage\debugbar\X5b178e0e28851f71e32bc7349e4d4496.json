{"__meta": {"id": "X5b178e0e28851f71e32bc7349e4d4496", "datetime": "2025-06-17 14:57:11", "utime": **********.609256, "method": "GET", "uri": "/add-to-cart/8/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750172230.968037, "end": **********.609274, "duration": 0.6412370204925537, "duration_str": "641ms", "measures": [{"label": "Booting", "start": 1750172230.968037, "relative_start": 0, "end": **********.47349, "relative_end": **********.47349, "duration": 0.5054531097412109, "duration_str": "505ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.473502, "relative_start": 0.505465030670166, "end": **********.609276, "relative_end": 2.1457672119140625e-06, "duration": 0.1357741355895996, "duration_str": "136ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49699272, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1338\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1338-1571</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.01968, "accumulated_duration_str": "19.68ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.523846, "duration": 0.013380000000000001, "duration_str": "13.38ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 67.988}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.5489528, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 67.988, "width_percent": 6.504}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.573205, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 74.492, "width_percent": 6.453}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.577449, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 80.945, "width_percent": 4.929}, {"sql": "select * from `product_services` where `product_services`.`id` = '8' limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1342}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.584609, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1342", "source": "app/Http/Controllers/ProductServiceController.php:1342", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1342", "ajax": false, "filename": "ProductServiceController.php", "line": "1342"}, "connection": "ty", "start_percent": 85.874, "width_percent": 5.03}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 8 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["8", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1346}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.592421, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "ty", "start_percent": 90.904, "width_percent": 4.675}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1415}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.596248, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "ty", "start_percent": 95.579, "width_percent": 4.421}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 16,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1569244492 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1569244492\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.58321, "xdebug_link": null}]}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  8 => array:9 [\n    \"name\" => \"tea-شاهي\"\n    \"quantity\" => 1\n    \"price\" => \"2.00\"\n    \"id\" => \"8\"\n    \"tax\" => 0\n    \"subtotal\" => 2.0\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/8/pos", "status_code": "<pre class=sf-dump id=sf-dump-1970779114 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1970779114\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1787469204 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1787469204\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1482023765 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InlxZm9wWThxaXd0cEIzZ2tUQ2xNQlE9PSIsInZhbHVlIjoiNHFJc2o4eHFFTHYxVmEzOHcxdFN6d0p0NVZSZWt4dkhsRkJ1MFkxMjJGU1lKOTh4S3ZuR3J1bVo2a1FYdllucHorQ3ZZRkoxaEpKZ21HaWRtWHdzUXVNNEpqSVo2OWRQSXc5UzdsdWZJWldFeHFFYjhsSXNTV2o4VU9qS3RaTEtyakpidkVVQ0JWMFFVR1BicnRoUzdCS2M4dzlQVDRFKzlkejFlVjVRT2Y3Vy9xNThrb3ZkNVRjT1NyKzduMTdWd0NqYVptNnRQbU92a2JJSUUzVWpNUm9nNGdWMTV3NGt1a1pZMGJ1dE5LOVZsd2F1bFJTL1c0VUtPeTdSd2YyZitUS3hFQTZNQ1pUMzlzaVZQUjdHUVZSK21KN2E1YmtRTW5BLzJMWW94bFI1V00rWlJyVTh0czFoQm5OaTZ0cEZObVlhRVdmekcvQnV1NkEzRUtVdUExdkRtd3pVQ0ZkR3hlc1FPZk5CK3lqWVNFUHVLYXZheDRTYzk1YkszK3F5NEZHQWoxQ3Q1VS9SbmVuVjFhcXhkTW5Ec3pLM2Nwbm81OHJ6d0xkNmNOYnFmUlRNTHRUNmo1S1RYNktQRzd3RHhqQzJ1Zk5pVjNsRm9PN2xkWThKdDFMcVNjcU1yU0dzODNxdVZWemJTcWNIYWZuZ1RSUHlITWRCU001ZXMzQTYiLCJtYWMiOiI4NDRhYWRiYmI2MDMxYmQyNmIzYzk1NTVlMmUyM2M4MmYyNmNlZjcyZTQ2MDg5NmY2NjU2Mzk0OTNjZmY3ZDNiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImJuZ1VqOXdTdTlQSHdSc3NFL05ZYlE9PSIsInZhbHVlIjoiU24xTFBpUHJRaWRmTHUvVmxQUnBHSGhJOFliTGR0OVZaWXRxMDlQc2dhM1VCSUlyT2UxdXc0T2dQZWFBYkE2aEplNkphWmdLeUtMQUpBREI2ZTR0dzhESHFDVkhYbFlFcTN5SXoxcEYrbnJ3VHZ1dVZobVRJUjJ3aHhsWjRNRy9vQzNDYi9wVC9IM0loN2NJMjNQbVIxUlhDZUhZdVRwdzF6a3huMzVhY1VBdFVqUGRldC9KdUpBaEQ0bHFQN094Q3B2ZmhhbTU3Q2JjQ2dkTW5kT1lCKzJWUmNjTTd2NE1DdWkvTFpJTXVHRXlDK25uZCtZUlpWOEVLUm9zcWpmMnhwSGFIdEs5NzJWS01ReEhCZVFOUUxZZ01NeitFcnpUZFdpSU8wcmVrcDA4V1JTZ29RV0dwYnhMZFRvZ1FVQVJYNnMwSnYxSU93YUk0ZEZwSmdkbWxaRHFnOU8vZUlsWmE5QkJtdlh0aFVNVnNCVGJtS2Y4ZXRVeStIOHU4TWViZWQ2b1BHd2hYdmpzY0llanRuZE9OVTMrdS93OU1OZnNzdlV1bUtSTGdqSjZST0NXYTBIeDhjaWMzVXBhUWpuOWdVYXpMTFFlMmlzc0lXbnEyQlZIYUpGQWtnaVNKYUJ0VVZGbnl0T1BjUDllSEk0SzIzaUlMVzVoQldtbkhnYVAiLCJtYWMiOiI0ODNjZTZkY2ZjNjM1MjBmMTU0NzgwZTMzMWEzNTU5NmY1NzdmYjFkODJmOGRlOTVjZTY0MWNiMzk3YzgxM2YwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1482023765\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-166924594 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-166924594\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-517235066 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:57:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjdyTXBudFJvU0ZuMUVnd214b1FidFE9PSIsInZhbHVlIjoicjF6RC9hMS9ab20xa2Q2cmJ5LzJTbFFaZ0pic3JDZFFFTVg5bllCSnRuSWVHZkNzZFhCcjFuenBjVnZ1Vis0UGZ1emRqRHNrWkFTcVMzakFzTjV4cE1mM2ovQmVEK1NsWlZWTFRtZnhNN3ZxZlBJVGxDNEpabEozZVdkMGhRcHRlejF2eGNJdkhyeE5XMk1jMWI5UlFkVk5TUFQ5cVlRSVhOZnpYeCtOKys2MjN6anZWNEJEckI1Nk44NHVCbGpCOUlGb3FNTlprbExOZlJBZThiSTE2dXBQemJ2OVluRXVDR3ZRajhiRE9VQjl4VlVhY1dKWjQ2dWtHYVpRUU5aR3Zaei9DQ3ZOeDRiMms4eTQ5Qld2NlZ2SHNXbnFUQ2dkMUNYVnZNRVJtdkI0bXFzY1o3Zzc0bTVOb1pjWUw0S3ZHZVVhanNYSUJOQmdjUmJnN1czNXN0dWJmemdNTDFPWHhNcW56a0srbVRHQXBBdGZUVGpvOFRURWwzT3Q0Zm5JdFkvNTJHRTAxMGxjSElWVUsvY1VqQkRYQlBBMnJxbnpvUnozM0Jjd1kzaDRsSjRhbWl4WitXZUo2dE9zbnd5Y2FiR0REYTc0QkxwMGdCdDRRdlA1NzVLa0ZBc2dJanJVck5PM3M4dUpwODV1SmtLalcrZXZvS3FZRkx0aWl4NWYiLCJtYWMiOiI4M2ExZTBhOWFhNzQxN2Q5ZDhmYzAzYmY4OGRhNGE4MjY3ODEwNDcxOWE4ZjBhYjg5NjdkOTNlYWY4NjBlNmQxIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:57:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ii9JT0g0WDAxUzNBbEt6Kytua3dMTFE9PSIsInZhbHVlIjoiTnNBTXFmbGY0NkxMdXV0Q3NVeDkxaFF6NVpKQWN2d3U2N3lDc25IelgxTzN4dFQvb05uTE5tMnZWdFZwbm9uK05yM29hZUpJOWtPQkNyM3N5VUVkRUk4SDhUNTVnQmFLMUFpUjIwZlY1SWFUSGthd0pqSjZ2VzB4TmlERGlGMmRSKzdTbXdrbWJieG9kMHE5ZU1nWUp4MCtpL0FFeld2VTFWemtMS2RtZ1Rwb3A5Nm5xN0pWNXg4b3pvRjh4bjRpdHJzU0Yvb2hHOEVzMnd2S0ZiR0FySzlRcEVETGk0dUpqMWhYaXcwM1MyelNjZmJicTBHcFVFNXB1K1pHU2tXaTJ6SmMvLzhDaTdQQ3M3QWJiRCtZMGdZbG40Skl1aWUwNy9UdGRrQXNaOEtnWlU3KzEwemtPcDFWSmZDeWhPeXM3YXQ5dWZ0RExiT0dpbmxtVkJxOHNVN1R3Z2wwUkt4Z3UvcFVISFhDNURGaktMN01RdVVDcXZPYkl1UmcwUG1PZ0FrWU80SDNBdHdNNW1aWkxGWUoxVlVMaEpLZC9BRnpFSWQwNC9BYWlYRy9HQlJBeS9lN0htYndoNnRiQ3NRSXVQOG9lZUhnUUg2Q0hzaUhrd1VLQ095Tm9TOVNsWVJzVng1aUY1aWdRRmgyeldKUDRGczNycE9RZTJTYXdIQlIiLCJtYWMiOiI3NjZiNzc3YTU3MTU1MzBlZTc4YjIzOThhYzhkNTE2MGI3NTVhMTM2NDQwMTQ1NDJiMjU0ZWUyZjJjMDc0NmIyIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:57:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjdyTXBudFJvU0ZuMUVnd214b1FidFE9PSIsInZhbHVlIjoicjF6RC9hMS9ab20xa2Q2cmJ5LzJTbFFaZ0pic3JDZFFFTVg5bllCSnRuSWVHZkNzZFhCcjFuenBjVnZ1Vis0UGZ1emRqRHNrWkFTcVMzakFzTjV4cE1mM2ovQmVEK1NsWlZWTFRtZnhNN3ZxZlBJVGxDNEpabEozZVdkMGhRcHRlejF2eGNJdkhyeE5XMk1jMWI5UlFkVk5TUFQ5cVlRSVhOZnpYeCtOKys2MjN6anZWNEJEckI1Nk44NHVCbGpCOUlGb3FNTlprbExOZlJBZThiSTE2dXBQemJ2OVluRXVDR3ZRajhiRE9VQjl4VlVhY1dKWjQ2dWtHYVpRUU5aR3Zaei9DQ3ZOeDRiMms4eTQ5Qld2NlZ2SHNXbnFUQ2dkMUNYVnZNRVJtdkI0bXFzY1o3Zzc0bTVOb1pjWUw0S3ZHZVVhanNYSUJOQmdjUmJnN1czNXN0dWJmemdNTDFPWHhNcW56a0srbVRHQXBBdGZUVGpvOFRURWwzT3Q0Zm5JdFkvNTJHRTAxMGxjSElWVUsvY1VqQkRYQlBBMnJxbnpvUnozM0Jjd1kzaDRsSjRhbWl4WitXZUo2dE9zbnd5Y2FiR0REYTc0QkxwMGdCdDRRdlA1NzVLa0ZBc2dJanJVck5PM3M4dUpwODV1SmtLalcrZXZvS3FZRkx0aWl4NWYiLCJtYWMiOiI4M2ExZTBhOWFhNzQxN2Q5ZDhmYzAzYmY4OGRhNGE4MjY3ODEwNDcxOWE4ZjBhYjg5NjdkOTNlYWY4NjBlNmQxIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:57:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ii9JT0g0WDAxUzNBbEt6Kytua3dMTFE9PSIsInZhbHVlIjoiTnNBTXFmbGY0NkxMdXV0Q3NVeDkxaFF6NVpKQWN2d3U2N3lDc25IelgxTzN4dFQvb05uTE5tMnZWdFZwbm9uK05yM29hZUpJOWtPQkNyM3N5VUVkRUk4SDhUNTVnQmFLMUFpUjIwZlY1SWFUSGthd0pqSjZ2VzB4TmlERGlGMmRSKzdTbXdrbWJieG9kMHE5ZU1nWUp4MCtpL0FFeld2VTFWemtMS2RtZ1Rwb3A5Nm5xN0pWNXg4b3pvRjh4bjRpdHJzU0Yvb2hHOEVzMnd2S0ZiR0FySzlRcEVETGk0dUpqMWhYaXcwM1MyelNjZmJicTBHcFVFNXB1K1pHU2tXaTJ6SmMvLzhDaTdQQ3M3QWJiRCtZMGdZbG40Skl1aWUwNy9UdGRrQXNaOEtnWlU3KzEwemtPcDFWSmZDeWhPeXM3YXQ5dWZ0RExiT0dpbmxtVkJxOHNVN1R3Z2wwUkt4Z3UvcFVISFhDNURGaktMN01RdVVDcXZPYkl1UmcwUG1PZ0FrWU80SDNBdHdNNW1aWkxGWUoxVlVMaEpLZC9BRnpFSWQwNC9BYWlYRy9HQlJBeS9lN0htYndoNnRiQ3NRSXVQOG9lZUhnUUg2Q0hzaUhrd1VLQ095Tm9TOVNsWVJzVng1aUY1aWdRRmgyeldKUDRGczNycE9RZTJTYXdIQlIiLCJtYWMiOiI3NjZiNzc3YTU3MTU1MzBlZTc4YjIzOThhYzhkNTE2MGI3NTVhMTM2NDQwMTQ1NDJiMjU0ZWUyZjJjMDc0NmIyIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:57:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-517235066\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1852084282 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>8</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">tea-&#1588;&#1575;&#1607;&#1610;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>8</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1852084282\", {\"maxDepth\":0})</script>\n"}}