{"__meta": {"id": "Xd60bfdb6f9fbf3fdffca0aaa70d4a829", "datetime": "2025-06-17 14:25:55", "utime": **********.209233, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750170353.844691, "end": **********.209271, "duration": 1.3645799160003662, "duration_str": "1.36s", "measures": [{"label": "Booting", "start": 1750170353.844691, "relative_start": 0, "end": 1750170354.993179, "relative_end": 1750170354.993179, "duration": 1.1484880447387695, "duration_str": "1.15s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750170354.993221, "relative_start": 1.1485300064086914, "end": **********.209276, "relative_end": 5.0067901611328125e-06, "duration": 0.21605491638183594, "duration_str": "216ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46131024, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01402, "accumulated_duration_str": "14.02ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.124368, "duration": 0.00926, "duration_str": "9.26ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 66.049}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.156373, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 66.049, "width_percent": 13.909}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.176286, "duration": 0.00281, "duration_str": "2.81ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 79.957, "width_percent": 20.043}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-816403320 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-816403320\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1634724031 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1634724031\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-647773224 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-647773224\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-719672451 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=3pzjoc%7C1750170198935%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ijdwb3ZJVTdzSXNWbnMzZGNFUkVnN3c9PSIsInZhbHVlIjoiL0dNNk5LWms0eDVRaEJlQ2tkUUE5VkxSbnBzL0w0ak45T3N0QktqNVF0NlllL0gyZnFYelkxZGo0Ym9wMEY4N1cwS3VyS0JBQ3NXWFA5YWIxTDFpUG9NUVdiMndYWU5qNW9ubDV3RTFjL0hJa0w4QTlnU0JkY2RZTEZWN0xuK1c2RDFkdlNZcFNjMHdFYmUydjZrMzJTdGdoVk5uTklKSU95SENYN3FXTDlBbWIxeDlTc1NBRWcxOElKSy9LZVpPRTlvS0tVbXB2TnpLV3g0L1ZWZ1N5MC9BUWJsUHdtUFpsaTdteVMzWitFdUVxNzE5T2ZlVEVjWVJ0NXpOd1RMMENWaWxveTJsNDVybDd6SmtCREN3UG1xVmN1bG1VQXNNbWkybk1XaW11dHFJK2dHTGJMdEZ1L29tUU8zRndQV3AwSVdsajg5WFN1NGVURENmMnFUbEVGTEVuM3FtdkRSbklJR1hqbUQ4VUU4L3hsWDJvSDcwSnhRVmpNVkRaVHBDL0lFM3ErRjlpVm52dzA1a2Z0S1h4Mm1MMDhVdnZuWDJrNXpLeWdFVjdNb042TXFTYkpYUmRMRlpGbnp5M015amkzeCtxdlRHMGZRdXBWaVdZc3k0bU9aZlNqMG9ZU0VYNEIyQnF5TWpLbTgyOExMSlZzSmoxeVQ4S0tDMU1VVzUiLCJtYWMiOiJkN2YxZGFmNzM5YzgwM2MyNGI2YzEzNzMwYjQ1ODFhM2YxNTk5ZDFjNjJhMDY5NzE5MmRiY2MzNzhlMzJlMzI4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ilo5WHY3SzhnMzd1enRQR0dwdSs0WkE9PSIsInZhbHVlIjoicXFpN3ppU0FHTkoxRitjT1hIWHVHYm1ic2tBbkxQUSs1aGRQL2NockdBVHo0M2hTei8yODhqcVR6SnRYQVZoaDlKVnhUMk5md3cxZkhWcTY2SWZpVkFvQnRlTk5qYUloKzJHaDNMT3RUS29xOHlHeUplcDlYMWNrWVp5d28vRUl5THZXejFNRGpVYlFvRFBRTXBJMHorWGR5eTRydnQrUGRDenJ2QkYrbzF2RENKb09UM2FXcFdjVTVvWTFzdWw4Y1pST01IWmdTQmpLZTB5dlhLS3FBZHh3bTBrdXhjWE05OXNFakxidXdqaHBjdVZxOGhQY3ZId3I4MWFyVmN2dHdKSWk3Smhwelo0a1I5bXJ1OUhEZWtmTWFacW1ySDh1SEVteWdKa0RiOXRtZmdpY1drVVh0NUROTTZOUGJvMnpFRTVpRW5KaGJnSGJWN0ZGUmR1cU9ybnJiVXRrT2diUW5ZeVRVM0FuVHIzN1A2dTFXcjkrU1Y4NEdWUkZlWmdBWVp3L3cwcE5aN29Ed3I5WnJTM0Z0dElmZ21pbE9EZlVHYnRDSFpadWpZS2RBZ1BKK1J2Y2pYZkIvSW9jb0VrRTRHdW5VZk9FZFJYWmJuYnlPYWtRSjdMNGRwOVVtK0pJbktNSHVlb0s2YitxVFFIYk9XU2ZLMHhRWW5FVTB4TVciLCJtYWMiOiJlZTZiMDI2MGJlMjU1Y2QzOTMyODY1NTA2YWI4NzdlNWMwNmQ5NWZkNTc3NWNkMWQ0NDc5YmE4ODI5NDVlYzY5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-719672451\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HxnOnSuhAAPTfN2VS4q58slgJoytwtHYqaX0oLoW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1941805385 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:25:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkJPQUVxdm0xbDJaUlNJNVd1SEZFN2c9PSIsInZhbHVlIjoiUzBNZitWa0luMjhwbVU4cVhWRnEvQlVPTEN4Yyt1OFFwNWYyWFdGVGRRUU9iaHJ3SHRmN0dTWldkQkhPRDh3dnZvQU1YZjhmZUI0UHkvcXdQR1E1aTZjSUIwR1N6RHFMOVo2VG5nSVZvNEFjREdFS3VCSkpoUFdqSUFoZVpISXFHN1JjK1BHcEFRQ3hIdmd5Y2NUT3RTVk1TenNSTTcvcGRQV3kwNmlBNnd1YVMxdm5ORjJWRjZ1YmNqaVVsdlh4VGo3aWMvTGIzQWx1UHF3Um1lYzhWNWVXaDdhNmwwL1h1SUlvaHNtOFV2Y3JQRUNQUWtqSWNndFc3Mmc5M1lPTHpQdVgzUEt4dVM3MXNtMzJFWU5tbnZjMGFHM2tWc2hVS3VEQ05OQkFKMGNBRHFIZlBDZXNtNE95ZExITitjeFZkYnNKSGFpN3VndTc0NUVYQnQxYzdoRE5GQ24xUHkyVnk2Z0MzNzdIK2E4TTZqQU12NHFhS09RVWxHU0d3MHc4UGt4OHFQek55QzJQVXJpak92d2cxdHFNWmkyb2hKQno2b01WZFk1M1VPTTNtcUxPNkxuVWxHSnhWTE1udkZFR2ZWYXFMWnRiT2VkSFU5Zm1SbUhSRFFPZGNQOXpBTCtVQjFMMGxVUFl5OGFHck95ZFpaN3NPZ3cxUnJJSTFiQTciLCJtYWMiOiI0YTE3OWNhNmJjODJlNWE5NzZkZTQ2YmYzOGYxODgzYTYzY2UxNGY2NmI2ZGEwZGMzNmJmZGFhMzAxMDdlMjgzIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:25:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkZjbkhESGEzQ0FDVTBSdWV0YUpvR3c9PSIsInZhbHVlIjoiOE42bUlLSHZvYkVZUlQ4VTNXU2JzK0h0NEszaEJ3Z3NGcTg4Um5SbjRCWHE1MGsyLzEzTkFNYjdjR3dkemlzNlFTQVFLRWhiNjloQVR5VHQwYTZxaEJMSzlsS3l2MjlzRmJIQndoMzVtNElzQ3ZGd085MVoraitTeDlrV2Q2RXFoYlZ4N1FKLzQ4YWVic0lyejRWNWJUVXAra1RSNjNqZGQ4N2lTZDlNcEgzbkpCeDQyQi9pVlZ0Y0kxU3FoRklhamFNbVd2Z2pGNWpkNjhHZE5YbWZTU3UyZE54dTQvYWk0MEkzNFRaeFVtVk5jb0ZLcjRSbjhtU0RmdmJlalZJZmxoQkpzc3VRNmR5b29XQkluSUJUSGVUdkJHN3VNVmJVdXU5elowZHlRMkpCNHJoVkZocDZCSWovN3A2a3ZQOEZpdTZEYTBzWS91alR3MVNzLzVRTGtjd25PeE9SRzNLMXJMYytTcHZLNHdrZFE2Z1VnVkJhWFdkQkhIK1FWTVZDOCtScE9wcUhuYnFCV0VmajRwNWpKVGlLOUtMMVl0N1FISzN4eFlVWStiZnlBdjl3MTB6VW5hWDdPaXhtMFhRK01PQ052UVlvWlBtNXhpRUNJQ2hkMy94WUJGZDk5Zm01L2s0SXVHRjdxZXNxaGRPTVI4NzlHSUcvVkpzWHFNODEiLCJtYWMiOiIzMjNmNjdhNzVlMzBjMjAwZWQ3MjY2ODYzMzZhMDk5MzI5YmEzMTkxMDJiZjdkYWM1ZThkMTNmOTUyZGNiZmI2IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:25:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkJPQUVxdm0xbDJaUlNJNVd1SEZFN2c9PSIsInZhbHVlIjoiUzBNZitWa0luMjhwbVU4cVhWRnEvQlVPTEN4Yyt1OFFwNWYyWFdGVGRRUU9iaHJ3SHRmN0dTWldkQkhPRDh3dnZvQU1YZjhmZUI0UHkvcXdQR1E1aTZjSUIwR1N6RHFMOVo2VG5nSVZvNEFjREdFS3VCSkpoUFdqSUFoZVpISXFHN1JjK1BHcEFRQ3hIdmd5Y2NUT3RTVk1TenNSTTcvcGRQV3kwNmlBNnd1YVMxdm5ORjJWRjZ1YmNqaVVsdlh4VGo3aWMvTGIzQWx1UHF3Um1lYzhWNWVXaDdhNmwwL1h1SUlvaHNtOFV2Y3JQRUNQUWtqSWNndFc3Mmc5M1lPTHpQdVgzUEt4dVM3MXNtMzJFWU5tbnZjMGFHM2tWc2hVS3VEQ05OQkFKMGNBRHFIZlBDZXNtNE95ZExITitjeFZkYnNKSGFpN3VndTc0NUVYQnQxYzdoRE5GQ24xUHkyVnk2Z0MzNzdIK2E4TTZqQU12NHFhS09RVWxHU0d3MHc4UGt4OHFQek55QzJQVXJpak92d2cxdHFNWmkyb2hKQno2b01WZFk1M1VPTTNtcUxPNkxuVWxHSnhWTE1udkZFR2ZWYXFMWnRiT2VkSFU5Zm1SbUhSRFFPZGNQOXpBTCtVQjFMMGxVUFl5OGFHck95ZFpaN3NPZ3cxUnJJSTFiQTciLCJtYWMiOiI0YTE3OWNhNmJjODJlNWE5NzZkZTQ2YmYzOGYxODgzYTYzY2UxNGY2NmI2ZGEwZGMzNmJmZGFhMzAxMDdlMjgzIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:25:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkZjbkhESGEzQ0FDVTBSdWV0YUpvR3c9PSIsInZhbHVlIjoiOE42bUlLSHZvYkVZUlQ4VTNXU2JzK0h0NEszaEJ3Z3NGcTg4Um5SbjRCWHE1MGsyLzEzTkFNYjdjR3dkemlzNlFTQVFLRWhiNjloQVR5VHQwYTZxaEJMSzlsS3l2MjlzRmJIQndoMzVtNElzQ3ZGd085MVoraitTeDlrV2Q2RXFoYlZ4N1FKLzQ4YWVic0lyejRWNWJUVXAra1RSNjNqZGQ4N2lTZDlNcEgzbkpCeDQyQi9pVlZ0Y0kxU3FoRklhamFNbVd2Z2pGNWpkNjhHZE5YbWZTU3UyZE54dTQvYWk0MEkzNFRaeFVtVk5jb0ZLcjRSbjhtU0RmdmJlalZJZmxoQkpzc3VRNmR5b29XQkluSUJUSGVUdkJHN3VNVmJVdXU5elowZHlRMkpCNHJoVkZocDZCSWovN3A2a3ZQOEZpdTZEYTBzWS91alR3MVNzLzVRTGtjd25PeE9SRzNLMXJMYytTcHZLNHdrZFE2Z1VnVkJhWFdkQkhIK1FWTVZDOCtScE9wcUhuYnFCV0VmajRwNWpKVGlLOUtMMVl0N1FISzN4eFlVWStiZnlBdjl3MTB6VW5hWDdPaXhtMFhRK01PQ052UVlvWlBtNXhpRUNJQ2hkMy94WUJGZDk5Zm01L2s0SXVHRjdxZXNxaGRPTVI4NzlHSUcvVkpzWHFNODEiLCJtYWMiOiIzMjNmNjdhNzVlMzBjMjAwZWQ3MjY2ODYzMzZhMDk5MzI5YmEzMTkxMDJiZjdkYWM1ZThkMTNmOTUyZGNiZmI2IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:25:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1941805385\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-132467873 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-132467873\", {\"maxDepth\":0})</script>\n"}}