{"__meta": {"id": "X1c814545306dc5e79fad0d19f808152e", "datetime": "2025-06-17 14:54:16", "utime": **********.889558, "method": "GET", "uri": "/customer/check/delivery?customer_id=6", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.322489, "end": **********.889583, "duration": 0.567094087600708, "duration_str": "567ms", "measures": [{"label": "Booting", "start": **********.322489, "relative_start": 0, "end": **********.810787, "relative_end": **********.810787, "duration": 0.4882979393005371, "duration_str": "488ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.810799, "relative_start": 0.4883098602294922, "end": **********.889586, "relative_end": 2.86102294921875e-06, "duration": 0.07878708839416504, "duration_str": "78.79ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44978352, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02351, "accumulated_duration_str": "23.51ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.851263, "duration": 0.02221, "duration_str": "22.21ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 94.47}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8784678, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 94.47, "width_percent": 5.53}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  8 => array:9 [\n    \"name\" => \"tea-شاهي\"\n    \"quantity\" => 1\n    \"price\" => \"2.00\"\n    \"id\" => \"8\"\n    \"tax\" => 0\n    \"subtotal\" => 2.0\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1057124616 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1057124616\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkEyMmpncTJlVDlkQWpWbVFxU3J3dEE9PSIsInZhbHVlIjoiTERBaDJVNGp4c3BkdGVsbXVMRzRwY3lzZ1l5SklBTTBIY0Q3Mkp1NmpWU3Y4N2hyT1Fqb1NEQnJHRzdWdVRLUlQwYlN6VXUrcGo2dTFudEQreTdjalB3MUZJR0paNUpGbVc2UGRXT1RVcGdZQld1ZzQ5ZEdqdnQ0NDBNb0tSY0VEZnc1d0I5SkJwSGdZOFFISUovTk9TalV0R3FGb3RpVEpoMm9waUpudXYvcHl1elQxTTgvTVQ4bC9PREl2OHFPU3JLYXBuNWo5Ky91b1VDK3BvT3pEalhEb2YrWWdTTzhSSWt4aDBpRjlGYnVBTk1kR3NVOGU1L2pJUmVhTm5MN05IdXhCTkMrRjhrU1lteVBpWXR0STVvRFlCU1g3bjF0WmZLZ3VFWlNxVEdYeDhhTmd5MHFSbnZKaTJJcXdrMmZYNkNYUTZDeXpETWorZGxua3hQcEszeVNVeG01TFJrZ1VxU1NiRFcycHVZdWlPYWNsb0hLNDkyZjBMbWpoMndtSnkyeTRNTlptL0JjdTFJdHdPb0RlSUc4WVR6Qk1Fbkh6blpiK216N09EdGtXeCtGUlE5bndheHprK25MUk56Q2RTTEMxMFVNTnpjOG0wWUdUTTlMcGJZK3ZXSmFWZGpLYS81QUJtUW9RODVXQklYQmdGemUzK2hoQ09wbVJjRDIiLCJtYWMiOiI4NDdmMmZkYmY5Mjk4MDFiY2Q3NjdjOTljNzhhYTlmMDE4ZTY5MzVjMzk3MmEzNDMxMGExODdiMThmOTAwMmM5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InpVUXRLWDYyc0VvcjEycjl4RlgzMVE9PSIsInZhbHVlIjoiS2VWQlEyVUNDN3hvb2o2a1AzSEszdTI0bWRqVWNTMzRzdFUvanMwOXI1Vm9XRXdpeUZLbndnVGZVRXZWUG5uR01LMGJkTnFJQUE0UnB2dm1ySmRIZE1pazdkZEJXa0RvYTl1elR6SnFkNHFOc2lJTnR1cWYzRVF6U3FYQ0x4WkFnVVYzc2dBK2E2b2phaUFqaFAvRmJSRHZPOUIxVHF3Rkp5cUJEZGd5TDBuY2NyZ2dVTnBROGt3eElUbHAveUp3T1hSYTdiR2FnRVZXMktoRXk1VHUvdEtKb1QzVE5zOGx6VDQyS1RvTWI5QmtkLy9TcmxlVDRyWkY1Zy9tV3BIejg5WFpyUy9sL0k5UEtsRHIyeUdEVmF1QVBqenlIcnVwZUpNYXp0Tk5jUHk5aERzR2I3UG5iYmtrQStaNkdxbXNUR3NxYkpZdjNHNzh5eVM5S2dPb05KTWtmZ1IzMlk0TzllMFozaWVPdE9XZEFxQjZZcUFQM2hLWDRpMlNiSURhV1BOWk5VZHJhaTRPVXhvejltT0M0aWFxckhxZjdKSlYzTE1XY1llNHErRVIycWR6S3Q3ZlFGTmErZ2tjbENGWkxLQmkzN0dBVVVCY2dhcnNwK2tXc2d6a1BUVlZlNUE1R05KdlNKN2xEaTZMZWJsTFg3OVBKdlpBRW9XRlpMZXoiLCJtYWMiOiJhMTM5YzY0NmRkZGM2MzU0ZTRkMmNiZDhkNzhmZjVhNDE0MTY2ZWJiMmU0Y2Q1M2VkYzE3Y2UyNTMyNWExZTQzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1806756379 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1806756379\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1520090213 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:54:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImJzcVNSeEV0SUZCZ2Fad1hXeTdtZXc9PSIsInZhbHVlIjoiWGFwUWRiQ2xOcG9GaExnb1M5ditDR1RUNjJ5dkdVbGt3djhBYjQyNXlvRlMrRzV6bkt0ZnZBZTI1YjJ3UVZOa3R1V3JHUE5HeUJKMmJxdjBYYXd3M0hBU1U1WkVHSEkwUElHUHdtMi9mNFlDanFpQldrbWtBTllTSm8zWVZNeDdEbWQyWHZWaDQwMTVJVloxMDArZ3k5bEdCRTVaQ2JCL0VpVml6WFJxZjJObSswenZRbnlndGpMRGNWVklEZW9NMU4ybWN3dUMzRnptQ1I5UkhrN01TRWdScDNwSmlsYkRlamlPRzN4QjdSbDNqbUVib3dPbytmRGNseFRsa0VHSnpqOS9yN1ptOEZmcTVvRXFYcFpVc245Vlk4OTRZZk9ISHFVeGFBTFFpWWNxZGQ1RVZtYjd2aWxDNDF4RGVsR1JOb1diaWtxT0k3Q2Q3ZVZhek9sVjc5S3hlcFRNbTN4NTFsVHZ5TW4zS2dSZ2pOWEo2THBMeTlaRVorNThpUkc4OXlqbTV6WTNNU3BoNDBodkloZGtLbTVxTitoMVZkeW00dnF3TmdIbFNLWDBmdlBsaHE5S1hzb2o5TWVUcVBORmw3azI0eFAwbHdpeUJXY1l3VUUzTUIrSXhrb0dhK093aUkvTk15WWszbzZsdmphN2lCNkh4Ni83WjFUQlZBVkciLCJtYWMiOiJjYzg3ZTlhZjhhYWU3OGE5Y2NhZWU5OThhNGNiZmZhNGNlNDVjZDcwMmQ4Y2ZjNzcyZWJmMzgyYjg1ZjBlOTY0IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:54:16 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImlMckRLYTYrVkgyQmhEQ0poekdQZHc9PSIsInZhbHVlIjoiVHgwWG55Tk9xTERSY2lJMmI5SXM0RnFiazFHSXFkUEU1QjYyVDg4ejk2VnFMQlA1K3I1VE1VdUlSeFRZUGtBRVVZbU8rYkYvQ1pjU2NIOWY1UU1VSy81WFVtSi8veUpwaGRMM2h6dXpYVUlMWDk3c012OGQ5QjR0WTFmS0Q4RlhON0hyaUZWbGprNTErQ1c5MjhmN1owSkR2WUlUMzBmQ0ZvKzhmMEEzUzAxV2Z5NnNiUzZ5TGRKdTliZEZWWnVKeGZMZldEWlVta2ZqbjA2bzFFL0txVHdibDl6dE5wczIxU2tpUmtHL3hnd3A2d2VvYlQyZDcwREZiTlQrT21mZW05U1lMWWx3aVhNR3dBdURqMEd0SkI1NE9iNG01WGk5SXQ2MzlleE14Q09vMGlNMVVSaWR1NGVRUXFkVUVYckYwQlgzVUJtaEMwTE5CRmNTM0IydVJpeHFkRFVxTHUxOURlSmI2clZXQXNHNXN3ODFHSUVNNExQQXVsOXN0T2ZXdzBhT2pwZXBET3FwR2NCQWN1eCtjU3pJQVliL3ArZGVDNnoxUkJRVEJjWlRXUGMxbUdBTEVOcFdOR3c0b0RqOVd4UVZtclY4ZXVaQXNHMUxKMFArTWx2azNKZ3NxSUxwTGpaL1ZHbmdEYnJtZ3prWEsxWWJ5TG9TbmFlUm9JUVkiLCJtYWMiOiI3ZTYyODVjNTVjYTBlNzIyZGUwZmU5MDQ5YWU2M2UzN2JiNDNiMjg0ODU4ZTdjNzViNjhiYTU4YTgwZWQ1NWVjIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:54:16 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImJzcVNSeEV0SUZCZ2Fad1hXeTdtZXc9PSIsInZhbHVlIjoiWGFwUWRiQ2xOcG9GaExnb1M5ditDR1RUNjJ5dkdVbGt3djhBYjQyNXlvRlMrRzV6bkt0ZnZBZTI1YjJ3UVZOa3R1V3JHUE5HeUJKMmJxdjBYYXd3M0hBU1U1WkVHSEkwUElHUHdtMi9mNFlDanFpQldrbWtBTllTSm8zWVZNeDdEbWQyWHZWaDQwMTVJVloxMDArZ3k5bEdCRTVaQ2JCL0VpVml6WFJxZjJObSswenZRbnlndGpMRGNWVklEZW9NMU4ybWN3dUMzRnptQ1I5UkhrN01TRWdScDNwSmlsYkRlamlPRzN4QjdSbDNqbUVib3dPbytmRGNseFRsa0VHSnpqOS9yN1ptOEZmcTVvRXFYcFpVc245Vlk4OTRZZk9ISHFVeGFBTFFpWWNxZGQ1RVZtYjd2aWxDNDF4RGVsR1JOb1diaWtxT0k3Q2Q3ZVZhek9sVjc5S3hlcFRNbTN4NTFsVHZ5TW4zS2dSZ2pOWEo2THBMeTlaRVorNThpUkc4OXlqbTV6WTNNU3BoNDBodkloZGtLbTVxTitoMVZkeW00dnF3TmdIbFNLWDBmdlBsaHE5S1hzb2o5TWVUcVBORmw3azI0eFAwbHdpeUJXY1l3VUUzTUIrSXhrb0dhK093aUkvTk15WWszbzZsdmphN2lCNkh4Ni83WjFUQlZBVkciLCJtYWMiOiJjYzg3ZTlhZjhhYWU3OGE5Y2NhZWU5OThhNGNiZmZhNGNlNDVjZDcwMmQ4Y2ZjNzcyZWJmMzgyYjg1ZjBlOTY0IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:54:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImlMckRLYTYrVkgyQmhEQ0poekdQZHc9PSIsInZhbHVlIjoiVHgwWG55Tk9xTERSY2lJMmI5SXM0RnFiazFHSXFkUEU1QjYyVDg4ejk2VnFMQlA1K3I1VE1VdUlSeFRZUGtBRVVZbU8rYkYvQ1pjU2NIOWY1UU1VSy81WFVtSi8veUpwaGRMM2h6dXpYVUlMWDk3c012OGQ5QjR0WTFmS0Q4RlhON0hyaUZWbGprNTErQ1c5MjhmN1owSkR2WUlUMzBmQ0ZvKzhmMEEzUzAxV2Z5NnNiUzZ5TGRKdTliZEZWWnVKeGZMZldEWlVta2ZqbjA2bzFFL0txVHdibDl6dE5wczIxU2tpUmtHL3hnd3A2d2VvYlQyZDcwREZiTlQrT21mZW05U1lMWWx3aVhNR3dBdURqMEd0SkI1NE9iNG01WGk5SXQ2MzlleE14Q09vMGlNMVVSaWR1NGVRUXFkVUVYckYwQlgzVUJtaEMwTE5CRmNTM0IydVJpeHFkRFVxTHUxOURlSmI2clZXQXNHNXN3ODFHSUVNNExQQXVsOXN0T2ZXdzBhT2pwZXBET3FwR2NCQWN1eCtjU3pJQVliL3ArZGVDNnoxUkJRVEJjWlRXUGMxbUdBTEVOcFdOR3c0b0RqOVd4UVZtclY4ZXVaQXNHMUxKMFArTWx2azNKZ3NxSUxwTGpaL1ZHbmdEYnJtZ3prWEsxWWJ5TG9TbmFlUm9JUVkiLCJtYWMiOiI3ZTYyODVjNTVjYTBlNzIyZGUwZmU5MDQ5YWU2M2UzN2JiNDNiMjg0ODU4ZTdjNzViNjhiYTU4YTgwZWQ1NWVjIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:54:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1520090213\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1790116376 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>8</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">tea-&#1588;&#1575;&#1607;&#1610;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>8</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1790116376\", {\"maxDepth\":0})</script>\n"}}