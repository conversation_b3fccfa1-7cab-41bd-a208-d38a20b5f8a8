{"__meta": {"id": "X4e61651d31f3abfe78be641f30222089", "datetime": "2025-06-17 14:34:05", "utime": **********.204239, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750170844.427948, "end": **********.204266, "duration": 0.7763180732727051, "duration_str": "776ms", "measures": [{"label": "Booting", "start": 1750170844.427948, "relative_start": 0, "end": **********.105223, "relative_end": **********.105223, "duration": 0.6772749423980713, "duration_str": "677ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.105236, "relative_start": 0.6772880554199219, "end": **********.204268, "relative_end": 1.9073486328125e-06, "duration": 0.09903192520141602, "duration_str": "99.03ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46118280, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.007519999999999999, "accumulated_duration_str": "7.52ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.157939, "duration": 0.0047599999999999995, "duration_str": "4.76ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 63.298}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.1780179, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 63.298, "width_percent": 15.16}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.1908329, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 78.457, "width_percent": 21.543}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6", "_previous": "array:1 [\n  \"url\" => \"http://localhost/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-754257425 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-754257425\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1852578974 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1852578974\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1880402001 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1880402001\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2036202430 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://localhost/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; XSRF-TOKEN=eyJpdiI6IjlKU2dzWlZxdE5Pcmt6Q3VQNmQzQWc9PSIsInZhbHVlIjoiZ3N3Uk0vb1ZQVnQ4TVRUQU5vczNrVUg5UlU4NC9ndkJzbVZUMFA5VUZ5amF2c2lIeUJiYm9GdGZ6WllPU1I0c3A1dlAyV3VySHBadTkrT1YrM0tWWnY3eUlGZjUzeVRvQzF2NFJKVERvZ3diRmF5cVg5STBiOHJrTzJLRGJabDgyUGVBQnFMditpUUFoVlhnYmhzOUlXTjhkemF4Z0lnVytkVk5Xa0VCNkRaay8zb3pka1ZpSFlvYjh5S0xLVGNSRElQR1Z3VlB4M3k2OG1zZ3VGWnJDaWl4TzIzdkxZSzV1K3RmV3h4b2pmUHVBN3o2NHA3Y0IvaCtxM1RGSjVUeFdyUDdmM2xaOXRpT3g0U01NU2JiWVl4VUdLRGMvZHJOT1loTVoxZjRBQXMxUlk2aTh6azM3ZFAzSTgrVUtrY01WRXErbTd0ZzU0TTV6R2ltUDNHUkIwVmlmM0RMeWh4L1l0elg3Z1psc29aNTVmMk5Kc1laYVpVcDVIOFJ3U1pPR0c1cHV1djdJOUg3eWloSVRkbWFXZ0V3Q016enU4T2pzaUlyd21vbm81MVYzR2tCN1hLMk9KMkNsZnhKQ2h3dCtDZUNtV3k0WE1va3g2QlQ1U1FCZVBYZW43QWFaaEVnZ2N6OUZNMHlIeWZoQ3pqYnhMcnF5ZHpkdmc4eHJITEciLCJtYWMiOiJjNGI2YWMwMmQ5NmQyMGU0ODgwNGU3Y2FlMTY0N2E1ZjU4NzhjNDJkYWZiZjEwM2JhMTU2NTZkYmZhMjk4MDJkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImFXblVTZ1lqSWE3N25oMDI3Z3psUVE9PSIsInZhbHVlIjoiVmpMNWY1b2NDS0J4WmtZTFQ3b2ZJQTZPM2VlbkdabCtiakFVbEF6ZExITFNQRG9xa0VsUnpKVnlzcW15RXpIT0xNOTBxeWxhNVZna05ad2R5TWZmZFkyOHdFbUhCYzk0ZzkyTUxzTzNzUHhSTmlRdjR3cTdrSXhPRG5md3FlNTVZTEhaUi8vNHNLS2hPdU5qanRYMFFYUWxYK0NyMUdHaVI5MjFWaGhaZ2U1TFFROTFMSDNaOTFXN3dHTWhBODBTR0NRWDk1RmlGR0JjVStmSUREMDlpOVltNWhmOWlITjBTMUZtTUxtcHZDNjNrQm56U0wrVnM4aUxNeUNIWkM1b3F1blc5dEdvbzJPMHFMRmVUZzg3L0p3TlhrRmlJMjFrZjJEM1p6bk1ZblRLRVNuSXZMOG5wUFRUZ01NUUF0Ym1uSWJ0aVFkWUIrRHE2QlFMTHdQZFRjSGJrZWd2NDJMWXEvdWUwMHIwT2xqTkxiZFRvaDBKRkxZRi8weGlVMlRrWmhXa20wcnl0ZVFFd1Y5bGw0T1VjOUdBRkwxUGovRTlTVTdNMk5TakxVY0hiN2tIVVRnOGwrZ0p3UVJsYmdkb0dFUzlyYzFOdU4zYnc2dGJzZHVQK1hhRVN6azBhQkZQYVFzKzJTSnVxRDZ3dmVpbTZXRW1KeStqNEIrc0FUbWciLCJtYWMiOiI4NWJkMzBiOGUyOGZhZmUzNDM2NjQ3YzU2NGIzMDEzMGQ1OTkwZGFmMTMyNzdmMzAzNTY5NDRjMWY3ZDc4YzE3IiwidGFnIjoiIn0%3D; _clsk=3pzjoc%7C1750170827229%7C5%7C1%7Cl.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2036202430\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HxnOnSuhAAPTfN2VS4q58slgJoytwtHYqaX0oLoW</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1199819116 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:34:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InNlek5Jc1I1ODliQ3pJY2xjeVJkV1E9PSIsInZhbHVlIjoiMzFMRzJuZGNkZzhnQjQxclhjUmVaQytWeElsdFdBb0NCcnV4TmowSTFQc3A5Nk9ST1ZHQnVybHFjWDFxUVh3bUtFSTE2OUJzQ09qSktSWU1TVytLUFU5OUd2ZEN2SWU3a2JBYjkvYitPKzZqMVlnd3B4Ui9ycE1vRDNqNkJXdDZ6cEhnL01PejJHUjJQNUtMZ1p5R2RNM3Z0dU5IWFF6K0Eyd3d3Ym5WSTZUakw0bUZzdk8yTXFNdjhSV3JQQ2IzeWlzQ3pLM3VVUURMclhXcVdZd3VqeG05NzRSTkwra1ZzTGJuNXpPZTNvang4NFFHYVlRQWdGU2JEaGxpclE0VEtONFBvN2JENG94dFlRMnhlZTVKeWNXTXlhY0JhQXpvaEU5aXg4NDFpRXRER3BqSUZJNjlxM2NESURGWFVRS3dINERneDB4MldFOHk1TVB2YU44UVNXUHZGOGp3L0Y2aUNXNFRGRkVnZ1djTkg5SkpBODFDOFJSajhqNFB1dUQ4b2s5UFlJNkVvcGlodlZXVHdSQnE0Z0ZHSDI1enlGYjhPd2Q1TzRkU214UU00L3pxK3FKVXFzSU14ZDlDUDdYT05xdDVqNExZOWg5c2FGdm5yUEcvVXdOdVRoQ3MyQzZYaTlBRnlxdnlkMVV1YW12ODJSV3l3QmZkMUdJazBFOU0iLCJtYWMiOiJkMDE1YTE5NDhjYzVlMWRjNGJhMDM4OWJlY2NjYmRkYmYwMzk5Mzg4ZWQ1YmZhMjM2ZmExZTE2YWQ2OGIyMzllIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:34:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlJ0WVB1aUtyMjZVdUFPWG5aa2twckE9PSIsInZhbHVlIjoiV2l3WDRyTVZsajZEY1JiaVcxVlVPaVRrQmRaRmc1VGlrRWNVNDlNTkpaaWhRWkwwckhsQjdGS1N3QTB0NDl2clNGMmhkZExnQ3dEbkpzeWpIOG1LLzVGN3lBM2lkUXd0Q0JTYU8yVFV0Umtjbit2UmQ4Qmd6ejRURExwK3dmYUo2ekRxNU5wTHp3QTF0OEwxUHBISzBkUkxialpjUTdJdkZFczNPYm5uS1BobzdmSC9vai9YQlBld0l3c3l1c2plY25sb0YwSGJLblk5cWNvbEh6RXluNVhJc0YrM2JCYWs5U0xCZW9EYWZHKy9mbENuQ0xIWmJnUEJQSU90RHA0TGx4RTdXdHVYeGVSYXBEa3NGQm80TmE2RFlmc2xUVTZyZllNa1JiKzBNbklRaVFreHVjcVQrY3JHdjcwRHVxMndmUkVMSHNtN2JiSmRpK09Ua3FhOWpNbER3Y0RaVlRmTlloRXlQdjdObFdsOXl4M1hHZHNjeTd2elg3UllGV1l0Vmdrc0Z2a1B5azNMRHJwMExmTFhxcWY1WGxGMjBzdS9NRXJPcVhveWF2V2xER0JiQUpZWTZ6ZWozazhyY1dkaEg5Tm5nVUt0NHlhdmVmUUpHZVFJcktuSi8zbGNSNWtuQ1dIbkNId21rYmlVYUJIYWw5T1pGY24wdTdRcHVJME4iLCJtYWMiOiIzZjk4NmI5YjI2MGExOGFmY2Q1NGRkYWU1Y2RmMWZhZGUwNTA4ZTRiM2VjNGY5YmIyZmUzNzNmZTIzNTA5M2NjIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:34:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InNlek5Jc1I1ODliQ3pJY2xjeVJkV1E9PSIsInZhbHVlIjoiMzFMRzJuZGNkZzhnQjQxclhjUmVaQytWeElsdFdBb0NCcnV4TmowSTFQc3A5Nk9ST1ZHQnVybHFjWDFxUVh3bUtFSTE2OUJzQ09qSktSWU1TVytLUFU5OUd2ZEN2SWU3a2JBYjkvYitPKzZqMVlnd3B4Ui9ycE1vRDNqNkJXdDZ6cEhnL01PejJHUjJQNUtMZ1p5R2RNM3Z0dU5IWFF6K0Eyd3d3Ym5WSTZUakw0bUZzdk8yTXFNdjhSV3JQQ2IzeWlzQ3pLM3VVUURMclhXcVdZd3VqeG05NzRSTkwra1ZzTGJuNXpPZTNvang4NFFHYVlRQWdGU2JEaGxpclE0VEtONFBvN2JENG94dFlRMnhlZTVKeWNXTXlhY0JhQXpvaEU5aXg4NDFpRXRER3BqSUZJNjlxM2NESURGWFVRS3dINERneDB4MldFOHk1TVB2YU44UVNXUHZGOGp3L0Y2aUNXNFRGRkVnZ1djTkg5SkpBODFDOFJSajhqNFB1dUQ4b2s5UFlJNkVvcGlodlZXVHdSQnE0Z0ZHSDI1enlGYjhPd2Q1TzRkU214UU00L3pxK3FKVXFzSU14ZDlDUDdYT05xdDVqNExZOWg5c2FGdm5yUEcvVXdOdVRoQ3MyQzZYaTlBRnlxdnlkMVV1YW12ODJSV3l3QmZkMUdJazBFOU0iLCJtYWMiOiJkMDE1YTE5NDhjYzVlMWRjNGJhMDM4OWJlY2NjYmRkYmYwMzk5Mzg4ZWQ1YmZhMjM2ZmExZTE2YWQ2OGIyMzllIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:34:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlJ0WVB1aUtyMjZVdUFPWG5aa2twckE9PSIsInZhbHVlIjoiV2l3WDRyTVZsajZEY1JiaVcxVlVPaVRrQmRaRmc1VGlrRWNVNDlNTkpaaWhRWkwwckhsQjdGS1N3QTB0NDl2clNGMmhkZExnQ3dEbkpzeWpIOG1LLzVGN3lBM2lkUXd0Q0JTYU8yVFV0Umtjbit2UmQ4Qmd6ejRURExwK3dmYUo2ekRxNU5wTHp3QTF0OEwxUHBISzBkUkxialpjUTdJdkZFczNPYm5uS1BobzdmSC9vai9YQlBld0l3c3l1c2plY25sb0YwSGJLblk5cWNvbEh6RXluNVhJc0YrM2JCYWs5U0xCZW9EYWZHKy9mbENuQ0xIWmJnUEJQSU90RHA0TGx4RTdXdHVYeGVSYXBEa3NGQm80TmE2RFlmc2xUVTZyZllNa1JiKzBNbklRaVFreHVjcVQrY3JHdjcwRHVxMndmUkVMSHNtN2JiSmRpK09Ua3FhOWpNbER3Y0RaVlRmTlloRXlQdjdObFdsOXl4M1hHZHNjeTd2elg3UllGV1l0Vmdrc0Z2a1B5azNMRHJwMExmTFhxcWY1WGxGMjBzdS9NRXJPcVhveWF2V2xER0JiQUpZWTZ6ZWozazhyY1dkaEg5Tm5nVUt0NHlhdmVmUUpHZVFJcktuSi8zbGNSNWtuQ1dIbkNId21rYmlVYUJIYWw5T1pGY24wdTdRcHVJME4iLCJtYWMiOiIzZjk4NmI5YjI2MGExOGFmY2Q1NGRkYWU1Y2RmMWZhZGUwNTA4ZTRiM2VjNGY5YmIyZmUzNzNmZTIzNTA5M2NjIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:34:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1199819116\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1487420011 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://localhost/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1487420011\", {\"maxDepth\":0})</script>\n"}}