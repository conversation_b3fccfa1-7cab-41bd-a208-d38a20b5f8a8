{"__meta": {"id": "Xe4de8313f4dfc02aa201842e7a522b30", "datetime": "2025-06-17 15:10:19", "utime": **********.182625, "method": "GET", "uri": "/pos-payment-type?vc_name=6&user_id=&warehouse_name=8&quotation_id=0", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750173018.487089, "end": **********.182661, "duration": 0.6955721378326416, "duration_str": "696ms", "measures": [{"label": "Booting", "start": 1750173018.487089, "relative_start": 0, "end": **********.049144, "relative_end": **********.049144, "duration": 0.5620551109313965, "duration_str": "562ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.049168, "relative_start": 0.5620791912078857, "end": **********.182665, "relative_end": 4.0531158447265625e-06, "duration": 0.13349699974060059, "duration_str": "133ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49397408, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET pos-payment-type", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@posBillType", "namespace": null, "prefix": "", "where": [], "as": "pos.billtype", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1496\" onclick=\"\">app/Http/Controllers/PosController.php:1496-1604</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.021330000000000002, "accumulated_duration_str": "21.33ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.104818, "duration": 0.01821, "duration_str": "18.21ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 85.373}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.13698, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 85.373, "width_percent": 3.797}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.160711, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 89.17, "width_percent": 4.641}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.164858, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.812, "width_percent": 6.188}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-8579686 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-8579686\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.171704, "xdebug_link": null}]}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/pos-payment-type", "status_code": "<pre class=sf-dump id=sf-dump-1328174816 data-indent-pad=\"  \"><span class=sf-dump-num>404</span>\n</pre><script>Sfdump(\"sf-dump-1328174816\", {\"maxDepth\":0})</script>\n", "status_text": "Not Found", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-499624111 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>vc_name</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>warehouse_name</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>quotation_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-499624111\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1126298147 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1126298147\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1075772780 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjYrMStFMlhRaVdxeTdRUmI3ZkY1cVE9PSIsInZhbHVlIjoiSXF2Y1RHaDVrNUpPUUZucjdRNkdlclpYRE1PN0VCMXFPc0JDUEozeStQRDI4WnM5TjlrUngwQVlRcEJhT1FjTWlNcUQyWmlmNmpqZ25JMXF4TkE5RU43RlVjRW5iaGFNWUdlajg1M3dUdE5pV1MvRTlWUkxuWGZkamMwd1FDTVphWWEwMWxVbnBXTWZIVmpCS0JSNEZvd2NwRGpLWkdzZWdVN3JaWHBLVlN6QjJadnVhMVprTnhTWmJNOUsrbVBxL0NPS1plZU4wN1RXY3RiTUFxa0M0OHZqSk5CUVdpZkFMNkFrRGpKOWZlMXpvd3lXKzFnaTZ2cms1V3FxUlNmWGcydm4yTjJsa0JkWDNHVVlKNFBSekU2cnFxV09QZ2d1dkJFZGcvQk9mTVZIdzlFSDlqbUVjLzBmSFk0c1RyZFRGZnFqNHJkRlpoenVzTHhMWjNqZEE3cVE5Wk55UzlvMFdVWG5KZ01yMXI4MzB1RzYrKzFuS0RJTE81QXBxdThlbm5vcWJVMHZ1TlFpRXFnQ3RZOUsyT2Viak14Y3JUeDJKRzFNZnpISjJENkFOQjJlYW9CSE5POHNQWnRjT3daMm4rdHpzR3hHSWhCcFlOVW9TTXB5RGU4SEtuY0daN3ZsUGk2Z1pRaWtiVTgrenFmRVcrNzdlY0hZWVoyaXFQcHkiLCJtYWMiOiJkMDMxZWVlNTcxNjRhYmE5NTkwNDc2M2Q0ZTk1MWQ5YTc5MTFjZmFmNTkwMDQzNzlkOGVlNDY1NDE0YmNhNWQ4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImxSSFE3K3lma3l5TTZ3YkNaYmFRNGc9PSIsInZhbHVlIjoiYWtpelc1VUZZci9ZSWpPYnluSGZzc2h2V3RONVBsZTFsc2dPZzNYWHdrWVF0aWF0SlJkenNFdHMxYm52S2IwS1B3SlZvZEVRdXcyR1IraldvbjlIVEJQcUpnYU5Nd0FzNk0yVDV3OTNxTXZFWW1vLzMwZ1dMNjhqQjM0SG16eERRQi9EWllVZVUyRVR2a2lDMzhhRjkxSHQrZDVFdXpmT0l1elprNDFta3B0VFBtWHQ1VkNvdys5d3k4V3ovSHdTdGdhaEtWeXcyN3d4Y3g0OVczMi9QdkM3TEtTS2pNK0dtYW52WHpCVGNwM25pbGhvQVc5VlRIOXJvL0czTFc4bHNzNWxLUDdNbkdDNTVVMGZJZGlKOURWTi9ySXQ0OFNNK3NrVU5sVmdoM09Ma2Nmc0YzcERlUnFIZ2NEV1VPK2FWNnZYSTVLMnU3Zkh1MFdzWkdWVzY0eXJiTHQxcXVjNlptMUZnYTFaMjdwQVAwSk5FZ0N3ejhQOUtkZmFKMkhDVmU2cld6bnA0dUNHdXRnSjd3K0drQk0wMUdwWjNLNER0Y0pSSmU3WDZjRGZUR2ZFeEdBWFBkSmwwZGZMRGlwK0N6bzhCVWNLMWt3UmkzQ3BjS0MrYkU0ajNtamdiV1p0WUg5NlE3M2diMzU2eGdRSkNIYjl2N05DamtqU2I0TTgiLCJtYWMiOiJhZjIwMDMxNDA5YjM0MGUyMjkzNmZlNTc3YTI2YTQ4MDdkM2Q2MDEzMWQ4MThmMDhkZjhlMzI4NTU5NjYxMjRjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1075772780\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1007603144 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1007603144\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-221849052 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:10:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkVnU1VWWm5DUnUwL3BoMitFbUhYclE9PSIsInZhbHVlIjoiSC8xN1FHbUhFTk1MZytUVXJUcGRuTjkzZldZRUx4Q3JodlFyem1YbnJtU283SXg2dG15cHhQWnhPdUtYMSt4YW9QOXF5dFBsaGsrYmRzaFhjMzFwUSs5SFAvd1RWaEJQMUl5Zm1OYy9RUnBHOVBFdEhYTlVNNVdET2p6Z1ovbGdpQ21peHZwZnF6SURVR1dqUFJWRGdSNzI5eXgvcnlNYmJtTFFJaHI1RjV4YkZWa056SXZTWXJqRFh0NFRINGJRQmh6aUNhbWYrbFpLR1Fpd21LNFlwTDFFM0lLeUYrYjlTM0lBWG91Ym9tc2kvbXlTbCtkVCttdEpabG1PTUcyYzdESHdCTThoZE1COGtyT29kajJZMC9pOUdvSUJjSG5YNmYrdklxUGIwQmdFcm56UW0xYlA3dHZUaEpzWWtBOHBKdVhpak40aHBpOHpvYWRMT2tLbFBSSEpJVFFFYlNkeElyTzlTWTZtdUJPd2tqemRwdk05RS9vWHhqZ0F6TGo1SmVJQkZaUjRpdFBjNnIrRHQ4bVBrN2NDUjlGQXVyZzFtSW9lYml1VC9xOEtVTmlLcGVMMk00NS9MZVJ4NzZ6VENQazV6OWlMbkl0QUZ6cE5BcUdESDlBZ0dCeU50Z01CdURvc3E1Wk5XcExiVmFQQzJXLzdnd253QVdscTkwaDkiLCJtYWMiOiI1ODM5MmY2NTgxZjhkNDNhNjEyZWRhNGZlNjIxMzc3ODc1Yjg1ZmI2NDNmMGM1ODI5YTE1NmEzZWQxZDg2NWMxIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:10:19 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlNGRVh6WFNxemxZd0ZWUW85SHE0RUE9PSIsInZhbHVlIjoiUDZpTUhuclRNMzI1MXl4UHRYcW1mUjFmTEdteE1Pc0h2NkUyMkVObks3c0ZueFFmMmoybTdXQjJ1WWVxTUEwUTB5bG9wMUJxNEQ2bHNwa3NQQlJ2TnZueE83S2hHbEhDa2Y5VEtTRm85ZTZkSkhmN3lpV0xidVpUbGFZOURGcEpYVGxaNDUyUG1jQ2lJbW05d01CMGJXamp1WTFLeHBSNXFLcVZwYTlQc3BXTTRFbDRWeWl2aWFuVTRxQzZ2OXlkTXpRaGdjOHFlVDNjMWVlWk9lUUtiSTdxM0ZKNmdxWFBFV0lCOTA0OFRaaDdMUG4yK0dhWC9aSjVNYVBtdzN4UzlvM0pnUCtDSHh5SmNFSHhuQmV0Q3VCV2dVK0ROaG1CbEo5NnJUNW5BOE9oR2NaMm85TFY0aGhEdURBSEtLMFVVRjBTNEhBVjl4NHlYNUZ5WGNhV0RjM0dMeDhMSG10U1owbVZVUDdFaFVSTlNhUXhQcSs1Z3BpajBSNzZuek5IaDMxcWUrVHFrYVo2R3RNSDBJWXl0TVQzcHNCVERsQkR5QlMxcUZjTTloRys2MUNFM0dOOU9FRnpEd3NmaHBuVmI3UmQzMEIrZWNTRHJnOWd3SW1rbmFjdzFWVTMrNUJsTG9JT2MyKzhkTWNpbVdLNmtmQlIvalpFcVVFOXUrU2kiLCJtYWMiOiJkMTdlZjNlNDJiNDA5ZTAyYjI5Y2YxZTEzMjEwNTc4ZDRhNTViNWFmNWMyNGI0MTI1ZmEyZTJmNDRmODc5YmM2IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:10:19 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkVnU1VWWm5DUnUwL3BoMitFbUhYclE9PSIsInZhbHVlIjoiSC8xN1FHbUhFTk1MZytUVXJUcGRuTjkzZldZRUx4Q3JodlFyem1YbnJtU283SXg2dG15cHhQWnhPdUtYMSt4YW9QOXF5dFBsaGsrYmRzaFhjMzFwUSs5SFAvd1RWaEJQMUl5Zm1OYy9RUnBHOVBFdEhYTlVNNVdET2p6Z1ovbGdpQ21peHZwZnF6SURVR1dqUFJWRGdSNzI5eXgvcnlNYmJtTFFJaHI1RjV4YkZWa056SXZTWXJqRFh0NFRINGJRQmh6aUNhbWYrbFpLR1Fpd21LNFlwTDFFM0lLeUYrYjlTM0lBWG91Ym9tc2kvbXlTbCtkVCttdEpabG1PTUcyYzdESHdCTThoZE1COGtyT29kajJZMC9pOUdvSUJjSG5YNmYrdklxUGIwQmdFcm56UW0xYlA3dHZUaEpzWWtBOHBKdVhpak40aHBpOHpvYWRMT2tLbFBSSEpJVFFFYlNkeElyTzlTWTZtdUJPd2tqemRwdk05RS9vWHhqZ0F6TGo1SmVJQkZaUjRpdFBjNnIrRHQ4bVBrN2NDUjlGQXVyZzFtSW9lYml1VC9xOEtVTmlLcGVMMk00NS9MZVJ4NzZ6VENQazV6OWlMbkl0QUZ6cE5BcUdESDlBZ0dCeU50Z01CdURvc3E1Wk5XcExiVmFQQzJXLzdnd253QVdscTkwaDkiLCJtYWMiOiI1ODM5MmY2NTgxZjhkNDNhNjEyZWRhNGZlNjIxMzc3ODc1Yjg1ZmI2NDNmMGM1ODI5YTE1NmEzZWQxZDg2NWMxIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:10:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlNGRVh6WFNxemxZd0ZWUW85SHE0RUE9PSIsInZhbHVlIjoiUDZpTUhuclRNMzI1MXl4UHRYcW1mUjFmTEdteE1Pc0h2NkUyMkVObks3c0ZueFFmMmoybTdXQjJ1WWVxTUEwUTB5bG9wMUJxNEQ2bHNwa3NQQlJ2TnZueE83S2hHbEhDa2Y5VEtTRm85ZTZkSkhmN3lpV0xidVpUbGFZOURGcEpYVGxaNDUyUG1jQ2lJbW05d01CMGJXamp1WTFLeHBSNXFLcVZwYTlQc3BXTTRFbDRWeWl2aWFuVTRxQzZ2OXlkTXpRaGdjOHFlVDNjMWVlWk9lUUtiSTdxM0ZKNmdxWFBFV0lCOTA0OFRaaDdMUG4yK0dhWC9aSjVNYVBtdzN4UzlvM0pnUCtDSHh5SmNFSHhuQmV0Q3VCV2dVK0ROaG1CbEo5NnJUNW5BOE9oR2NaMm85TFY0aGhEdURBSEtLMFVVRjBTNEhBVjl4NHlYNUZ5WGNhV0RjM0dMeDhMSG10U1owbVZVUDdFaFVSTlNhUXhQcSs1Z3BpajBSNzZuek5IaDMxcWUrVHFrYVo2R3RNSDBJWXl0TVQzcHNCVERsQkR5QlMxcUZjTTloRys2MUNFM0dOOU9FRnpEd3NmaHBuVmI3UmQzMEIrZWNTRHJnOWd3SW1rbmFjdzFWVTMrNUJsTG9JT2MyKzhkTWNpbVdLNmtmQlIvalpFcVVFOXUrU2kiLCJtYWMiOiJkMTdlZjNlNDJiNDA5ZTAyYjI5Y2YxZTEzMjEwNTc4ZDRhNTViNWFmNWMyNGI0MTI1ZmEyZTJmNDRmODc5YmM2IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:10:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-221849052\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2145936450 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2145936450\", {\"maxDepth\":0})</script>\n"}}