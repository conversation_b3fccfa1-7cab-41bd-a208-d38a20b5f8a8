{"__meta": {"id": "Xd63e7a205000ef2c8be2d351ef03ed62", "datetime": "2025-06-17 14:57:10", "utime": **********.640406, "method": "GET", "uri": "/customer/check/warehouse?customer_id=6&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750172229.986966, "end": **********.640428, "duration": 0.6534621715545654, "duration_str": "653ms", "measures": [{"label": "Booting", "start": 1750172229.986966, "relative_start": 0, "end": **********.543811, "relative_end": **********.543811, "duration": 0.5568451881408691, "duration_str": "557ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.543823, "relative_start": 0.5568571090698242, "end": **********.640431, "relative_end": 2.86102294921875e-06, "duration": 0.09660792350769043, "duration_str": "96.61ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46219568, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.019409999999999997, "accumulated_duration_str": "19.41ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5906901, "duration": 0.01714, "duration_str": "17.14ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 88.305}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.6224759, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 88.305, "width_percent": 6.749}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.629118, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 95.054, "width_percent": 4.946}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-131973952 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-131973952\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-893152162 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-893152162\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-943638697 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-943638697\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1074218735 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im5raGwrMzFLZTRKRDdOQTlTeTdjZ3c9PSIsInZhbHVlIjoiMzc4N05HckhvKytyZ3BVK0xKazhNWFc3TGd1VWpUUTdad1NPKzhObzF4NklTMFpHSTF0bS9JMHhkMjFmZTNySW5nM09leExUWjA2NW1PK1dQa0dJRXE5aFk0dnRnemJQbnVwVjZjUWZiWHZhRXdLZFo1WG1pclYwM2lGTjM2STJJTTd5V1lKMnhDSUF1QkJGQ2dhZUFTQms5aEhvaDQ4MzZIOGJvRnVwNDdlQWJrZ0craExyOU1udXg2OUhwb1lFRHlLSlR5VWlsQmV2K0ZFV1NhcVhyVllLTTMrQzdlaC9hcmkrKytwcDNDdmZSdDEwS3I5ZzRCdUNMcFlUYW05VU5mcCtlMDZpVlc2UHZ6SGxPc2I4QXRIVmJMT1pGQm9nZmxvd2x6Z2VuUy9zYWVHdSt1SEJOK084VFBUa3huOERidEJDM3AzbndLNTFHa0llSzJzSVNrdS9vSlVtWnNibWwvU0lub3d3Qlh2ZGdCN1FER25IVWZWS1BrYXMyQlJ3OGpKOERUVktteVU0RlFKUC9FWWl6STBNTzBST3JFUUZ6QU1YQWRFRGRWT1RXZ25ybk1OQ0ZnR1RGWHg2WWpnY2hqQTExQkFHMFI3djkrMGdBa2lVREZzVTJkd3p0TFA3bWFtOWN4RHN6bFd4VDR4LzNubkxsN21KN1g4eVdSbGEiLCJtYWMiOiIxOTY2NzJiMzhlMWVkMzk3Mzc3MzM5NjJkOWRkMzUyODg0YWQxOTQ2ODVlODQyMzFmMGUxZWZhZWQ2YTc5NWM2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkZWKzZRbWlFVjIyUW5BQzRLNUxISEE9PSIsInZhbHVlIjoiRkoxQlRyZzJ2bzZwRk52dVkyUWxpemtVNDF4R25TalV5ZnRjWURoYXp4SzZHMm9KOTNmcHlRVXoxazYyUm1DT2NVK2NxU1BrLzRWZGloVTJyTUtWK0JyT3E0bk1qN0lrWDdZMWpJMEF1NHFBNWZVbmxKQTM0T3F4VVhuZ2NIT2NZYUM1eG9NdGdEUGVuNlJBNTM2ektkWGZoTWZuVXBLUWZZNHJlc3RoR1NmRHhCT2tzdU9TcTREV1p1Q2lML0NCQTgrSzU3K2RyZ29QdWJJTU1jOGdiSzhPcUdjRGVBZ09qZnJEOFZEUUwrS1FrZDlWdXFJZUxHb3M4ZzBzUVdUMWhlaTdJQks5VjdIM1RuZUk3R25ybE9lc21uMjFVTVNvTWtLbUZFcGU4RmFEMWNnUTE1MWl4REE2RHZvZ1NLekxDRDR1N29uR1BlWS8xaklMTExlU1Fyb3g0SEIrZkt3N2YreVRiUWl5dWppQ241Z01FaVpnL0ZUMU1BMjRsZzR5NHRzVVpzYU1Wa1AwVXpwckFnZUNEalFlV3JVdUMybEVKWkRNdUltTHVtU25lREF0c2l5RFVWTEZaNGFoQ1U2SnlRcVkxTERSL01ZblllZUMxdndaQjFUYlBkZEsxVHFOaUhIYUx4UmdrY2lFcUQ1TjMrejFoMlNFL2tJU0VQSXUiLCJtYWMiOiIwY2YwOWFlOTA2NzkzYjRmNmRlMTQzNjA0ZWEyM2U0NDIwZmY1ZjBlMTc2MzQ3MGZkMDM4MzhlNDgyMzZhOWIxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1074218735\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-23116307 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-23116307\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1272322425 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:57:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InlxZm9wWThxaXd0cEIzZ2tUQ2xNQlE9PSIsInZhbHVlIjoiNHFJc2o4eHFFTHYxVmEzOHcxdFN6d0p0NVZSZWt4dkhsRkJ1MFkxMjJGU1lKOTh4S3ZuR3J1bVo2a1FYdllucHorQ3ZZRkoxaEpKZ21HaWRtWHdzUXVNNEpqSVo2OWRQSXc5UzdsdWZJWldFeHFFYjhsSXNTV2o4VU9qS3RaTEtyakpidkVVQ0JWMFFVR1BicnRoUzdCS2M4dzlQVDRFKzlkejFlVjVRT2Y3Vy9xNThrb3ZkNVRjT1NyKzduMTdWd0NqYVptNnRQbU92a2JJSUUzVWpNUm9nNGdWMTV3NGt1a1pZMGJ1dE5LOVZsd2F1bFJTL1c0VUtPeTdSd2YyZitUS3hFQTZNQ1pUMzlzaVZQUjdHUVZSK21KN2E1YmtRTW5BLzJMWW94bFI1V00rWlJyVTh0czFoQm5OaTZ0cEZObVlhRVdmekcvQnV1NkEzRUtVdUExdkRtd3pVQ0ZkR3hlc1FPZk5CK3lqWVNFUHVLYXZheDRTYzk1YkszK3F5NEZHQWoxQ3Q1VS9SbmVuVjFhcXhkTW5Ec3pLM2Nwbm81OHJ6d0xkNmNOYnFmUlRNTHRUNmo1S1RYNktQRzd3RHhqQzJ1Zk5pVjNsRm9PN2xkWThKdDFMcVNjcU1yU0dzODNxdVZWemJTcWNIYWZuZ1RSUHlITWRCU001ZXMzQTYiLCJtYWMiOiI4NDRhYWRiYmI2MDMxYmQyNmIzYzk1NTVlMmUyM2M4MmYyNmNlZjcyZTQ2MDg5NmY2NjU2Mzk0OTNjZmY3ZDNiIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:57:10 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImJuZ1VqOXdTdTlQSHdSc3NFL05ZYlE9PSIsInZhbHVlIjoiU24xTFBpUHJRaWRmTHUvVmxQUnBHSGhJOFliTGR0OVZaWXRxMDlQc2dhM1VCSUlyT2UxdXc0T2dQZWFBYkE2aEplNkphWmdLeUtMQUpBREI2ZTR0dzhESHFDVkhYbFlFcTN5SXoxcEYrbnJ3VHZ1dVZobVRJUjJ3aHhsWjRNRy9vQzNDYi9wVC9IM0loN2NJMjNQbVIxUlhDZUhZdVRwdzF6a3huMzVhY1VBdFVqUGRldC9KdUpBaEQ0bHFQN094Q3B2ZmhhbTU3Q2JjQ2dkTW5kT1lCKzJWUmNjTTd2NE1DdWkvTFpJTXVHRXlDK25uZCtZUlpWOEVLUm9zcWpmMnhwSGFIdEs5NzJWS01ReEhCZVFOUUxZZ01NeitFcnpUZFdpSU8wcmVrcDA4V1JTZ29RV0dwYnhMZFRvZ1FVQVJYNnMwSnYxSU93YUk0ZEZwSmdkbWxaRHFnOU8vZUlsWmE5QkJtdlh0aFVNVnNCVGJtS2Y4ZXRVeStIOHU4TWViZWQ2b1BHd2hYdmpzY0llanRuZE9OVTMrdS93OU1OZnNzdlV1bUtSTGdqSjZST0NXYTBIeDhjaWMzVXBhUWpuOWdVYXpMTFFlMmlzc0lXbnEyQlZIYUpGQWtnaVNKYUJ0VVZGbnl0T1BjUDllSEk0SzIzaUlMVzVoQldtbkhnYVAiLCJtYWMiOiI0ODNjZTZkY2ZjNjM1MjBmMTU0NzgwZTMzMWEzNTU5NmY1NzdmYjFkODJmOGRlOTVjZTY0MWNiMzk3YzgxM2YwIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:57:10 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InlxZm9wWThxaXd0cEIzZ2tUQ2xNQlE9PSIsInZhbHVlIjoiNHFJc2o4eHFFTHYxVmEzOHcxdFN6d0p0NVZSZWt4dkhsRkJ1MFkxMjJGU1lKOTh4S3ZuR3J1bVo2a1FYdllucHorQ3ZZRkoxaEpKZ21HaWRtWHdzUXVNNEpqSVo2OWRQSXc5UzdsdWZJWldFeHFFYjhsSXNTV2o4VU9qS3RaTEtyakpidkVVQ0JWMFFVR1BicnRoUzdCS2M4dzlQVDRFKzlkejFlVjVRT2Y3Vy9xNThrb3ZkNVRjT1NyKzduMTdWd0NqYVptNnRQbU92a2JJSUUzVWpNUm9nNGdWMTV3NGt1a1pZMGJ1dE5LOVZsd2F1bFJTL1c0VUtPeTdSd2YyZitUS3hFQTZNQ1pUMzlzaVZQUjdHUVZSK21KN2E1YmtRTW5BLzJMWW94bFI1V00rWlJyVTh0czFoQm5OaTZ0cEZObVlhRVdmekcvQnV1NkEzRUtVdUExdkRtd3pVQ0ZkR3hlc1FPZk5CK3lqWVNFUHVLYXZheDRTYzk1YkszK3F5NEZHQWoxQ3Q1VS9SbmVuVjFhcXhkTW5Ec3pLM2Nwbm81OHJ6d0xkNmNOYnFmUlRNTHRUNmo1S1RYNktQRzd3RHhqQzJ1Zk5pVjNsRm9PN2xkWThKdDFMcVNjcU1yU0dzODNxdVZWemJTcWNIYWZuZ1RSUHlITWRCU001ZXMzQTYiLCJtYWMiOiI4NDRhYWRiYmI2MDMxYmQyNmIzYzk1NTVlMmUyM2M4MmYyNmNlZjcyZTQ2MDg5NmY2NjU2Mzk0OTNjZmY3ZDNiIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:57:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImJuZ1VqOXdTdTlQSHdSc3NFL05ZYlE9PSIsInZhbHVlIjoiU24xTFBpUHJRaWRmTHUvVmxQUnBHSGhJOFliTGR0OVZaWXRxMDlQc2dhM1VCSUlyT2UxdXc0T2dQZWFBYkE2aEplNkphWmdLeUtMQUpBREI2ZTR0dzhESHFDVkhYbFlFcTN5SXoxcEYrbnJ3VHZ1dVZobVRJUjJ3aHhsWjRNRy9vQzNDYi9wVC9IM0loN2NJMjNQbVIxUlhDZUhZdVRwdzF6a3huMzVhY1VBdFVqUGRldC9KdUpBaEQ0bHFQN094Q3B2ZmhhbTU3Q2JjQ2dkTW5kT1lCKzJWUmNjTTd2NE1DdWkvTFpJTXVHRXlDK25uZCtZUlpWOEVLUm9zcWpmMnhwSGFIdEs5NzJWS01ReEhCZVFOUUxZZ01NeitFcnpUZFdpSU8wcmVrcDA4V1JTZ29RV0dwYnhMZFRvZ1FVQVJYNnMwSnYxSU93YUk0ZEZwSmdkbWxaRHFnOU8vZUlsWmE5QkJtdlh0aFVNVnNCVGJtS2Y4ZXRVeStIOHU4TWViZWQ2b1BHd2hYdmpzY0llanRuZE9OVTMrdS93OU1OZnNzdlV1bUtSTGdqSjZST0NXYTBIeDhjaWMzVXBhUWpuOWdVYXpMTFFlMmlzc0lXbnEyQlZIYUpGQWtnaVNKYUJ0VVZGbnl0T1BjUDllSEk0SzIzaUlMVzVoQldtbkhnYVAiLCJtYWMiOiI0ODNjZTZkY2ZjNjM1MjBmMTU0NzgwZTMzMWEzNTU5NmY1NzdmYjFkODJmOGRlOTVjZTY0MWNiMzk3YzgxM2YwIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:57:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1272322425\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1829524628 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1829524628\", {\"maxDepth\":0})</script>\n"}}