{"__meta": {"id": "X5612f8aea33364a6985437412ab7e6b2", "datetime": "2025-06-17 14:53:33", "utime": **********.869498, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.113581, "end": **********.869524, "duration": 0.7559430599212646, "duration_str": "756ms", "measures": [{"label": "Booting", "start": **********.113581, "relative_start": 0, "end": **********.74297, "relative_end": **********.74297, "duration": 0.6293890476226807, "duration_str": "629ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.742987, "relative_start": 0.6294059753417969, "end": **********.869527, "relative_end": 3.0994415283203125e-06, "duration": 0.1265401840209961, "duration_str": "127ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46092424, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.03559, "accumulated_duration_str": "35.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7930481, "duration": 0.033159999999999995, "duration_str": "33.16ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 93.172}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.8439138, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 93.172, "width_percent": 3.147}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8547719, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 96.319, "width_percent": 3.681}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6", "_previous": "array:1 [\n  \"url\" => \"http://localhost/inventory-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1027764533 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1027764533\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1192449251 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1192449251\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-991729762 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-991729762\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2032 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; PHPSESSID=o1mek9etnn9ejsik46u9hjcosg; _clsk=17t60l2%7C1750171898702%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjJselZITks0WHdFalBjRU5qRFdlZEE9PSIsInZhbHVlIjoiN0NiK1pGS1hVR0Z2TlkzeXR1L1BwOEVQSzJGZkF6L0t4S3h1ZmhkalZHaXd3Z0pTbkhpTllHK0k4aFZtSWpyblVVcEI0VnRGR2F2elMzb01VeVpCNUdxQVViS0xSS1FNWUt0d2p3SXZleXFTMEJMd0k1cC9BVWhUUWVWdSt5dFpVSE9DNGwyQ1I5MWpWL0NmczUvWEgzNDNpZkhHUktTQk1HeUd6KzBMSHpOTTIyYkNKUVZraWxJM25Mc0Zwem1XWndESkx2cGcrdkR6MkFkb3Qvcmc4bVh4MmdSei9mNGZ0OHFLNXZaK1pYYkM5ZFhkS3M5Tm5ERjJ3Zi82cnpMM25rUjZnQ3NBRldZK3ZuV0hMTEt0MkkyVWs0cTVMc01Rbm5xNlA2SXR2VXVFVmw1czBVcUZ1WnExcVFLcXg0L1ZNNHNxNWxBeWRncXA4WndoN09qNmhXYnB4WGFsWDZoeTB0YlAvQld3cDFFQ3RDVEsweHFTR3RUNkVpYWxXTHAwUzM4TDBkVFlpTHZ4aEF1L2llYldYaGY2Y0lPZ0J5RU9WQXY5TGJjZnJlenpKZ3N4YW9ZSG9TYVRjV3F4STNqWS9NOTRIQXBUL0p6OWxRSnhZV2V4RzRMb0FKMDhFL0VFaDZEOGtTVTRmQjhQdWNhZHI2QXJhVWZrS0VvdmIrSUkiLCJtYWMiOiI4NDZkODE1YWZmNGMxNWM5MmJlMTZlMzc1NGYxMGUyZmY5MTUzYWE0MzQzYzA5OTUxNGMwMzAxM2JiMjM3ODU5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImxKU0VkQ3VwWkplcFB3ajc3NVlVcEE9PSIsInZhbHVlIjoiU2JQbW5Bd241Rklvbks2enAxaXFPRlJTdGtYQjlucEJrWmV2cTZTNHRqTFN1QkVZUm5OaTl1NER5MXdYL1Bwa1FreXRsblVic3BjMWNhV3N3NzkvTUZyZCtOOFVqNHRGeXA0aFRUSGdLZnBac3VFUUp5K0RhRkJ0K2tMT21MeCtCV0wwbmlHQVlFd2lSMnJEOGVnVy9ib094Z3hLcFBOVU9GclRlOCsxb2daZEoxY0VTc2xqYmtCV1JkeE0wTHJ1dXpmb09rR3EzUUdBZ01BNDEzOURFbmFISUE1VTNaVUxPRlE5ZXlPMFZuRnBjeXFZWlpCY1AzUGliZWJhL1R1aXhpa2FkNXBWdmVvcHRaZktMZkRra0RycUVFR1h3ck9HanZCV3hiYzhCZFh1TDFrUXpaWlVZTUhIaDV4MXltbVRSeUF3SEVNOEFQbFhja1B2THpSQ2RhNTBmdzQwTUdoVTE5eFNacnZ3bFFkWnBmS2pNNXZqNTVmOTdvTWlUVmZUNTA3UXhYVGZYeDhjMkZUMWpnaTVoTXI4eHhya3NZQWNqQmlBZWRyN1g2V0ZLUkg1cGNxVStsSWt6cm90aG5wYmNvNmhuNVlhV282RlMwMkVhcFh0ejE4U0NDOERuMnRHSWp4OTBaOUFLeDlRa3JBSXEvc3dYZW1ybTRoZ2p4Q2EiLCJtYWMiOiIwZjEwNjYzMDMwNmYwZDFiNGFjZjRmZmJmNGI0Y2UzMDdhYzAzMGNlNDZhOTU4YWY5YmViM2MwMDE1MjE0MTUxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>PHPSESSID</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HxnOnSuhAAPTfN2VS4q58slgJoytwtHYqaX0oLoW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:53:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkpsUXFuWU85S2o5UnRmYnV2MlZrdGc9PSIsInZhbHVlIjoieWwxTXB4andKb3h0czlqekt5SEhCSmpPZWxTMG1kSlhlaVdMdm1rdTBXNkVrditVdldwTFl6LzdCTmhPTnlaRkZodFQ3NTA3WHdYMGo3YkMvR3RWRHUrdW9DeDkxdWN6dndTTXhra1UrR2VlMTlxSXRKRjIyZ1JDbkx1WHVpSkpQM0taN2F2clJ4Z3VSK0dobnZMK045V25Jcjk0K0Zmc05JSk5LSXNwWnpSditxSEgvK1BsMnd3ajhYeU1sVVpicmt6VERUWFNFWjBsZHZ2TGYwaGJyVGdPLzM4WlhJbitTWU54bkVOdGkvMGkxZU1QU2lWTTJYWHA1MzRmdno3V0pMdDdOa21rQldaK0xFcTIrcGN6dEV1ai9xYi9QVTRERlBnMjlNcHprQUMrc01VMUpwVUYzamVyUTZaT0RIamI4bzh4M0EzOSsyK2NjcnpNbUJZaDJkZ0ZJOGI3VVgrVFNxRXlqNXZ0MFoxOXpaLzNTbTMwR2VzUmpURm9UTTl3ZWRFcU9ETWNGcDN6SWlaYkJyL2RUOEcwMHFza3g2a2xBTzB4em5tckxXSnZLamdrTXBkMm5OcmRpVUp3Q21MdnZ2NDllYW9zUFJzNEp5VjhjNWk1bVI3OWNMVk5tU1F1VWlkRElUTmNpcnZIRThETHZDT2RJSUZGZ24vUzFOWmkiLCJtYWMiOiJiZjYzYjI0MmY5YzRjMDYxM2RmODE3NjVjYTNjZmU0MzRhM2NlYjNhM2JhNWE2ZTllNjc2OGM5NTVmZGYzODczIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:53:33 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlpvdUtIZkpJdHZYNFErTW5nMG9iY3c9PSIsInZhbHVlIjoieVpIV2RXWU8wdjN0WGRqVTJHRjNVZDQrSDhIMDNxSjgxSXlGMUYvYXhiVjZjUUk0MUVUM0FWK1JBMEQ5QWJvWnU4OGNrRTJkWHl2a21qeTZ5V1dVeTlVQ1VBbFFPcG13ejVNZVhWWDNKazMzM1FZcXJtbTVXOUk5M0cwUmVxUDdXK0hZdmc1UnNGWkFpNkNTYUVTNVU2OWNYbDgxMWs2LzdWekVvUkEzZ3JwRUk1aGQ0SGNoSHVYT2hyV2VHVDMzcmh4NjFNRDk5a1hXcU82UDVNM09oOCtJRU83OS80Y0tKeUVKQUNqV0JJZC90NXM5aVRySi84TmlyOUh2SlFtWVVSUVJkdFMyZkx3Q3p5NkIwdjlCcm1ySEszRXVWcm1wbFliMitGd3dqRk5GN2c5bVVNaHFXcFh3d2ZWdk4rVmY3WkdTWnphUTIvT1NianhoL0I1eGZEcWJuZDZTMzcvOWtZVWtYTTVUcyt3VFppYWdBc1NTTFpDOEVNckxROVZVNC9ObUV4WEFaS2FmUFl4dmVIVGNRZTRjM1dHdGp6ZXBydnVOVnBwQlhXUzNwVkEyaGxVNEdyZUpZaGt3Q0dva1R1N0gySjNKWlRHaVFFa1NhMTRSQUlERUhXRE5vWGxJU0tqVnhoam5GL2N4bU5wdk1pQjlTZ0txMk9UNitUM1IiLCJtYWMiOiI1NWZiMjIxNzMxMWUzMDc4YTUxMmFkMGFiMTM2Y2IzMWI4YWM1ZWJiN2JiMTBkODkyMTAyYjE2YzM4ZWI0NWQyIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:53:33 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkpsUXFuWU85S2o5UnRmYnV2MlZrdGc9PSIsInZhbHVlIjoieWwxTXB4andKb3h0czlqekt5SEhCSmpPZWxTMG1kSlhlaVdMdm1rdTBXNkVrditVdldwTFl6LzdCTmhPTnlaRkZodFQ3NTA3WHdYMGo3YkMvR3RWRHUrdW9DeDkxdWN6dndTTXhra1UrR2VlMTlxSXRKRjIyZ1JDbkx1WHVpSkpQM0taN2F2clJ4Z3VSK0dobnZMK045V25Jcjk0K0Zmc05JSk5LSXNwWnpSditxSEgvK1BsMnd3ajhYeU1sVVpicmt6VERUWFNFWjBsZHZ2TGYwaGJyVGdPLzM4WlhJbitTWU54bkVOdGkvMGkxZU1QU2lWTTJYWHA1MzRmdno3V0pMdDdOa21rQldaK0xFcTIrcGN6dEV1ai9xYi9QVTRERlBnMjlNcHprQUMrc01VMUpwVUYzamVyUTZaT0RIamI4bzh4M0EzOSsyK2NjcnpNbUJZaDJkZ0ZJOGI3VVgrVFNxRXlqNXZ0MFoxOXpaLzNTbTMwR2VzUmpURm9UTTl3ZWRFcU9ETWNGcDN6SWlaYkJyL2RUOEcwMHFza3g2a2xBTzB4em5tckxXSnZLamdrTXBkMm5OcmRpVUp3Q21MdnZ2NDllYW9zUFJzNEp5VjhjNWk1bVI3OWNMVk5tU1F1VWlkRElUTmNpcnZIRThETHZDT2RJSUZGZ24vUzFOWmkiLCJtYWMiOiJiZjYzYjI0MmY5YzRjMDYxM2RmODE3NjVjYTNjZmU0MzRhM2NlYjNhM2JhNWE2ZTllNjc2OGM5NTVmZGYzODczIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:53:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlpvdUtIZkpJdHZYNFErTW5nMG9iY3c9PSIsInZhbHVlIjoieVpIV2RXWU8wdjN0WGRqVTJHRjNVZDQrSDhIMDNxSjgxSXlGMUYvYXhiVjZjUUk0MUVUM0FWK1JBMEQ5QWJvWnU4OGNrRTJkWHl2a21qeTZ5V1dVeTlVQ1VBbFFPcG13ejVNZVhWWDNKazMzM1FZcXJtbTVXOUk5M0cwUmVxUDdXK0hZdmc1UnNGWkFpNkNTYUVTNVU2OWNYbDgxMWs2LzdWekVvUkEzZ3JwRUk1aGQ0SGNoSHVYT2hyV2VHVDMzcmh4NjFNRDk5a1hXcU82UDVNM09oOCtJRU83OS80Y0tKeUVKQUNqV0JJZC90NXM5aVRySi84TmlyOUh2SlFtWVVSUVJkdFMyZkx3Q3p5NkIwdjlCcm1ySEszRXVWcm1wbFliMitGd3dqRk5GN2c5bVVNaHFXcFh3d2ZWdk4rVmY3WkdTWnphUTIvT1NianhoL0I1eGZEcWJuZDZTMzcvOWtZVWtYTTVUcyt3VFppYWdBc1NTTFpDOEVNckxROVZVNC9ObUV4WEFaS2FmUFl4dmVIVGNRZTRjM1dHdGp6ZXBydnVOVnBwQlhXUzNwVkEyaGxVNEdyZUpZaGt3Q0dva1R1N0gySjNKWlRHaVFFa1NhMTRSQUlERUhXRE5vWGxJU0tqVnhoam5GL2N4bU5wdk1pQjlTZ0txMk9UNitUM1IiLCJtYWMiOiI1NWZiMjIxNzMxMWUzMDc4YTUxMmFkMGFiMTM2Y2IzMWI4YWM1ZWJiN2JiMTBkODkyMTAyYjE2YzM4ZWI0NWQyIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:53:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1205940194 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1205940194\", {\"maxDepth\":0})</script>\n"}}