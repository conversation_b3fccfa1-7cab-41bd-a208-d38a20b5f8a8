{"__meta": {"id": "X78a2417c609bdcc59639957f72c33d0a", "datetime": "2025-06-17 14:56:37", "utime": **********.970073, "method": "GET", "uri": "/customer/check/warehouse?customer_id=6&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.411189, "end": **********.970102, "duration": 0.558912992477417, "duration_str": "559ms", "measures": [{"label": "Booting", "start": **********.411189, "relative_start": 0, "end": **********.885747, "relative_end": **********.885747, "duration": 0.47455787658691406, "duration_str": "475ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.885757, "relative_start": 0.47456789016723633, "end": **********.970105, "relative_end": 2.86102294921875e-06, "duration": 0.08434796333312988, "duration_str": "84.35ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46234432, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01642, "accumulated_duration_str": "16.42ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.927614, "duration": 0.01391, "duration_str": "13.91ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 84.714}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.953912, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 84.714, "width_percent": 7.369}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9594738, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 92.083, "width_percent": 7.917}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-1395601857 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1395601857\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-953514756 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-953514756\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-356686520 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-356686520\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1369611577 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im9EdnNhTVR4eXVQUUdoZDdTTEJmRkE9PSIsInZhbHVlIjoidmVQbEZpOEN2b016cC8xangvK1JWSlFWaC9FWGRZb0hXa1g1OGhsNVRxd0MzNlRiUHlKVFZGRjJWWk50MFRCVWgwU0haQXpQdHZFQjl4MmNVREd6SXhPVWRNYUluMEg4TGZ4ZXpsaXFuWkZ2SU41RzRRMi82dnNVTnJqWkZNdlpJQzVrbjRSUTlYS1I5eGUybThZTjhoMXZnWk94YUVsTWtlZ3VKM0J3ZlRNNTA5ei9pem8yRHllSldqd2FTU2V3eVE3MnViSjhTbFg2aTJBVEVvbmpYa1NsSnFlYS9vWDQwdDZDeTJMTjlKRktUWk5lMVM4R3QxY3kwWnRoa0R1Y2QrMzhwbDExR2pIbVlGMjd1Q0l5T1RFWnptVVRoNWNhZ2IxWWFMMkU1eDJ3MWtNOGFPWi9LY1RMT1JqUmpEaE4xWUJjdUdVNElIUjZDSGpwbm5GWkxiRGhHY3FMaXVEM3VLaWhRSXFEQ01vYVljZ1BOQ29ZUEtHalN3T1hOR0NCNlV0R1l0SjMwMERWTWpDSkNhY2VJYTk4d3FFbzM1WkExekJ5ZDJUSzZsR2w1SFQ2bmtISzE0Y3p6cUM3NndCV1NqcHVOQzFXeDRiQlFYUVkvSG8vN3ZnR2dDaHZkclVQYnVMYzBZVEJMRzJ5cGNHWEZUWDFsTWM1cHBBc1p4VmIiLCJtYWMiOiI1NmNkZTI4Y2ZkNzBkZTUwNWMwYzBkODJlZDhhZTkwMmU4ZTJkYzQxOGIyYmM1ZDVhZTRkZGEwNmY0NWRjMTU4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Im5iRWc3T3UzbFBmS200Zk5iY29hamc9PSIsInZhbHVlIjoibWkxeWhKeUoxRzZVSjlqVHdJZVc4UFBGNm1LRFZ5MWhCVy81ZW5Xc0FKZTJUUzhDUEd5bVZWdHpvYUphd0VoS2dCcjFwRU5PdnIvSXVZOEpoNjFRdEhXa3AwL1p0ZmtRRG9VNWRtU1NRbkExTkdEUHkxS1hIYisvRWEzdGRaek9TRVgrUDNYbTZsMExIYitYV2diQkxaVENtdGdybXpGV0FyeWRKYzB3TXYrbmNWM0toSHJnVkdOWk1FWUQ4V1cwb1QrQ0Rna2Y1RzV5dnhpTWl4NWJOZXN4RkVpc0RUejlEQWVFalI0QmR4LzNOZHg3QlhCL0lqc0RuTWc2cUJmZ2RKWDh5VWoyOTZpUllQMFRXTnFEL1NhUjZoaFA2bk8vR2g1NnJsWWZ0MGw1MkNXQjJtWGVKRjh2MEQzMEhFZDlkRWJUdmxLajhNaUtPTDlHdHllcXA2bEU1blRRZ2tqbnRkZ2xCcXlzanZDeTR3bXRMcXFLcEp2T0xJL2sxb3paYVkxMFNqQ0JIZ0pxWjNVbnZtZlZabEJBK3FKWG1pblVnNHFJUXVheDlZam0yeVB5bVZESGdxU3BXZ01tYVVKZDVRY21nendHbTl3NUhWM2dlcmdUWlU2N0lEbm1LT3ZFZG5ZeFVOajAwbU8xd1plY1U4RC95MUNob09pTWRxOFMiLCJtYWMiOiI3ZjFhNzA2YTQwNzE2YmU1MDJkNDFkYmQ0NDA5ZDMxMmYyNTNmMDRjZjY0ZjUwMjZmYmZlMGUxYzQxZTQ3MmM4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1369611577\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1053168299 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1053168299\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:56:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InFzRmlVNTBWOGZ4NEQxdCtLK2FOMEE9PSIsInZhbHVlIjoiOGd6bGxyMGhtU1d1eWs0dktUZEEyREhMR1NiV1ZVUWJFMXBZemNsazJJb25SZnMwcUNnZ2p2SlN0ZThnTVd3SVd0RWxMekNPb2Y0TWJFWlRXdG5OVUJrbkJxQTdOTGxza01zaHdBN2JVajB5Vmg3UHJUMXViSDVWdkdhREdRcXlKVnZ2MkQrWG5ya1JLZWtlcVRXbzlhSXVzeEJGUDhqTnhNcER3WFZ4R0l4NjVZKy9PMkhqRjMvN3ovVlVxWm9LM3VwSmdkUzB5b0hQR0FkTS84MlRJK1N4OEpwMG1RNmJNSzZ0SmpoU01EYjNRWGhGS2U2dGpBYWVYbXJBUC84dGk1ZWpFc0YvcTQ3R1VGcXM5am1TQ3JwN3UzZEFpdWlzdVFrUWpnVFAyTzhrTjVCUkk3V016L2dLcFNadDM2VkphcGJrclpjRnVTUGtaajJLTXlCdWhMblVQaER6WURDb09kRnZMZW9tMlZlcW52U1RuM1VMVU9PM2pkVXl2S08wSlBTTm9qOEFYZ0hTN2lrSHFjb1dHa0ZiWHU0Q1hKR1RXRWJBMmRTakhWeHpxd1lqZjZCQ1BmVjVYZ2hIOTA0dnRvd2hZTy9oM2p6a0NJZytNbDE4NkxLMlJzU1d3RnhYdWFuTFFzVjBLNU9rVHdBWVI3L09LbUFzV3Q4MW1EOW0iLCJtYWMiOiIwZTZjY2Y4OWYzOTNhNDJiYzIwMDRmYmY0NTNiNWMyODhjZDUxZmQ0ZDhmYTNiNjc5NjQ4YmJiZDEwOGYyOGJiIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:56:37 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImhrUGxKMGJwVTF2RW1TcnRiSUpPR2c9PSIsInZhbHVlIjoieUgvQndTMklRRjhMRU1EQm45cDV4N1VmRTFLclI5R3M1d2VsRXNFa3J6aW10RG9jS0lDZnJBMCttVzFteUFKS3IwU2FmTy9tN2xtT280SEVxTUFZbmpOTTZvMldKMUIvbER3MnhSVWN0ZUFla1FUQzN0RmQvUTkrUDdESU1icVFRT2l5Sk5rQ1VTTEVJWi9HdWhBd1pKMTVVdFczQWNLYkI1cDVWK241RXYwcDVEbEFuZ3BYTm1INHdCN1RTRWgzbCs0MEZuR1hsUm9nZUlHV2JlYkwxZU4vUHRmdmxNWUxFYUw5YW80cFZwZnlFenQ3dExNL1pFd3dPNlJ6c0trSy9EWG00N0V1UFJEaHVaNm9mOUY4Y0VXUXY4SGM5VE4vdndzelRsZ1NWL0I0SkU5dEJJa1Z5VUVGOTdqWlNRTFJzRFhMLzl3N0FIRjF0TFoxZC9MZ0l5a0lDTFV4bzRUVFN5RjhtL1MyME9YOHVNcVE5dGZ3UnlubldENHVPL21YVkZJK3hxVTNFUWE0WHlLRk1EQnNwZ1F4VW9wcU1tc2E4R29mY0s3K3hZc3ZkcUw3N0hTYnJYNGdud0ZrN3ZvSkIzb3pMaXJHL2Q1YW8wb3lSMC8rVE9VeW9nNVBjV2xVOVRYQ3p0VkxFSXdUcjdxY1F4YnYrTFdlYSs2aDdMMVkiLCJtYWMiOiJkNjkzNjE4ODc4Nzk5ZjVkMDUxMWZkMGFjZDQ2YjVjMTIzNzk0ZDcxN2FmMWU5YmNhYWI4ZWJmNzY3YjU4NTYyIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:56:37 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InFzRmlVNTBWOGZ4NEQxdCtLK2FOMEE9PSIsInZhbHVlIjoiOGd6bGxyMGhtU1d1eWs0dktUZEEyREhMR1NiV1ZVUWJFMXBZemNsazJJb25SZnMwcUNnZ2p2SlN0ZThnTVd3SVd0RWxMekNPb2Y0TWJFWlRXdG5OVUJrbkJxQTdOTGxza01zaHdBN2JVajB5Vmg3UHJUMXViSDVWdkdhREdRcXlKVnZ2MkQrWG5ya1JLZWtlcVRXbzlhSXVzeEJGUDhqTnhNcER3WFZ4R0l4NjVZKy9PMkhqRjMvN3ovVlVxWm9LM3VwSmdkUzB5b0hQR0FkTS84MlRJK1N4OEpwMG1RNmJNSzZ0SmpoU01EYjNRWGhGS2U2dGpBYWVYbXJBUC84dGk1ZWpFc0YvcTQ3R1VGcXM5am1TQ3JwN3UzZEFpdWlzdVFrUWpnVFAyTzhrTjVCUkk3V016L2dLcFNadDM2VkphcGJrclpjRnVTUGtaajJLTXlCdWhMblVQaER6WURDb09kRnZMZW9tMlZlcW52U1RuM1VMVU9PM2pkVXl2S08wSlBTTm9qOEFYZ0hTN2lrSHFjb1dHa0ZiWHU0Q1hKR1RXRWJBMmRTakhWeHpxd1lqZjZCQ1BmVjVYZ2hIOTA0dnRvd2hZTy9oM2p6a0NJZytNbDE4NkxLMlJzU1d3RnhYdWFuTFFzVjBLNU9rVHdBWVI3L09LbUFzV3Q4MW1EOW0iLCJtYWMiOiIwZTZjY2Y4OWYzOTNhNDJiYzIwMDRmYmY0NTNiNWMyODhjZDUxZmQ0ZDhmYTNiNjc5NjQ4YmJiZDEwOGYyOGJiIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:56:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImhrUGxKMGJwVTF2RW1TcnRiSUpPR2c9PSIsInZhbHVlIjoieUgvQndTMklRRjhMRU1EQm45cDV4N1VmRTFLclI5R3M1d2VsRXNFa3J6aW10RG9jS0lDZnJBMCttVzFteUFKS3IwU2FmTy9tN2xtT280SEVxTUFZbmpOTTZvMldKMUIvbER3MnhSVWN0ZUFla1FUQzN0RmQvUTkrUDdESU1icVFRT2l5Sk5rQ1VTTEVJWi9HdWhBd1pKMTVVdFczQWNLYkI1cDVWK241RXYwcDVEbEFuZ3BYTm1INHdCN1RTRWgzbCs0MEZuR1hsUm9nZUlHV2JlYkwxZU4vUHRmdmxNWUxFYUw5YW80cFZwZnlFenQ3dExNL1pFd3dPNlJ6c0trSy9EWG00N0V1UFJEaHVaNm9mOUY4Y0VXUXY4SGM5VE4vdndzelRsZ1NWL0I0SkU5dEJJa1Z5VUVGOTdqWlNRTFJzRFhMLzl3N0FIRjF0TFoxZC9MZ0l5a0lDTFV4bzRUVFN5RjhtL1MyME9YOHVNcVE5dGZ3UnlubldENHVPL21YVkZJK3hxVTNFUWE0WHlLRk1EQnNwZ1F4VW9wcU1tc2E4R29mY0s3K3hZc3ZkcUw3N0hTYnJYNGdud0ZrN3ZvSkIzb3pMaXJHL2Q1YW8wb3lSMC8rVE9VeW9nNVBjV2xVOVRYQ3p0VkxFSXdUcjdxY1F4YnYrTFdlYSs2aDdMMVkiLCJtYWMiOiJkNjkzNjE4ODc4Nzk5ZjVkMDUxMWZkMGFjZDQ2YjVjMTIzNzk0ZDcxN2FmMWU5YmNhYWI4ZWJmNzY3YjU4NTYyIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:56:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-648534065 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-648534065\", {\"maxDepth\":0})</script>\n"}}