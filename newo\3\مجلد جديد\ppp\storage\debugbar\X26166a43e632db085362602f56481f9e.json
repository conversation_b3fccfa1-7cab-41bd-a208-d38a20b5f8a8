{"__meta": {"id": "X26166a43e632db085362602f56481f9e", "datetime": "2025-06-17 14:05:53", "utime": **********.924702, "method": "GET", "uri": "/customer/check/delivery?customer_id=6", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.353588, "end": **********.924725, "duration": 0.5711369514465332, "duration_str": "571ms", "measures": [{"label": "Booting", "start": **********.353588, "relative_start": 0, "end": **********.842035, "relative_end": **********.842035, "duration": 0.4884469509124756, "duration_str": "488ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.842055, "relative_start": 0.4884669780731201, "end": **********.924728, "relative_end": 2.86102294921875e-06, "duration": 0.0826728343963623, "duration_str": "82.67ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44962072, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02133, "accumulated_duration_str": "21.33ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.887258, "duration": 0.019829999999999997, "duration_str": "19.83ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 92.968}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.912367, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 92.968, "width_percent": 7.032}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1092141194 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1092141194\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-520115353 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-520115353\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-648140227 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-648140227\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1216493636 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InVDTnlkbUdoVXJLdnI5OXYxbTR1aFE9PSIsInZhbHVlIjoicjB0dTA2S1JSQlU2U1NSc3ZzUTd0QVhyVnRFY1djSzZ2U29waHRlbWNjbXVYRlBwUUM1b1g5RzFiMldraEhBYWRxSUdnaUR6K2xPWjZ2Tzd2NlhzRk4zc0NWdEU1eDlveDVIZFA3VXg5MldJUDdvaUNET3JnOHFjVk5EdUpTdzkxZzg3RnJmNzByZkw5TDN4MjM0K2ZZeS9RbUtBQkZDS3B6S0diNFF6NVdCazRsUkNzQjlMMW5kbUZCMUhUQkNLeDhqUThCdHdteU5abGVMdTdzaG1CZitKR3BTeEsvMy9GMmlQQno5N2JTMW1qa0R3V1ZlUStheCt4K2dtTkRBNEIzL2hGQkNycVVUdDIxMU1NRE4zdGFlWVNDZFNqWW9UMzU4T1pBaWdIOTZjVHh1cWV2dVJUSjlpRFpMVjZvZmdPSUxEVi92WGF3R2hxTXBaT091dWJGZTQ1bE5WaDQ3L2xod1RoTmZMb0lSUkRqRHA3d1dKRTduWER2d244UXRXUVJwQkxMR01aVXhTNWo3UTlvbmtoeU9qcE0zWTBiZjhzQWFKVzNNamVuRU8ybjVXMkZZRVp5SGFVZHdnU2FNRzROOHJKYm5ZckRURHlNOUoxWEw1Rkhha2RCaE1IL0ZwV1FCKzlXRVR3RWNwVkw3eGhnUWMvV1A3cndaODhPOUMiLCJtYWMiOiJhOGI3Y2UyNTc1MTBmYTFhM2FkNGM5MThlNjZiYjEzODliZWUxNDJkZTNhYzBjNjJkMzc4ZGJjMTYyYjM1OTU2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImxDTUlMOFVaUzJNbmF6a0hXS0pmbWc9PSIsInZhbHVlIjoiWS8rZ0VhaFVBaWtqMVROWDJhMnJrWXZwSUUxMmFXZzhLZ2xBZ1JwTTVtQkRTVmlJblB0VzRkSWJYY05kWFlRN05YUCt2UU1lY0JpM2w4bDRlOFkvTHA1c0psem1SMzRPcGJ2OXBNWVpDajVqREhUWEVKOEgwNWFjT1pRMHl4VDVQOWwySHJzbTdsSnA4QkNwTWMwL21QOWxQVy9MSXhTbTUzSkY2UUFScEJpeXpFUzJPVHErVzF2WHNodkdjcXk2ODFZcDB2TE00Zm9xYkd0Skw0aUFCQ1M4eHhUWlluK0Zja2hFOWJpbTFDUGk5SWd0ZHlpMTRScUs3QzRwZ3RQVDUrQ0d5ZzhsajZlOHFYQmV6U0dXZjV0TkJDanJ1akIzRGhrdkE4b0JORkRyekM1ZnJjQlB0SEgvdkVuZ3ZvdUZzY0hsUjVrbEV5dDMyR3RWN0xqTzRiVmJRV0taSWJKVXNBakZCRFYzZmpzMXIxSkNaNlpBL2Z1NzFKM0tDaUdTUlFMSUdzTFpac2ZQWVRrNGdSR3dDMzVxQnh6dWtjclFMZTZIQzh1VmVESzY2Q05KSDFnbzdMaGJxQ2FvaXhMRjByeEtFZWxicTFicmdvamJvNUg2UHNBNFcrQjdEUkZ6QUFDWHB0TnMvOW16ZHB2bXRlVEZLTmxvZ3Vvei8zRG8iLCJtYWMiOiIzNTU1NGY5MDA0MmVmNDFlYjhlNjk1NmFjNzRjM2I5YWM2ZWNhYmFjZWRlZDIwZTM3NDdjN2M4YzI5NmRiNzEwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1216493636\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-149607371 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-149607371\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1481085097 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:05:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InYrV0I4L2IxQUxMRWRiM2Y4bVUyTHc9PSIsInZhbHVlIjoiRzBHSVd2dUJIQWN2VzJkY0JDcmo4VVB4Q2N4UE1GYThUOWFhMlpnTVM5akVDOThHMExxVTUxc1YySkd4Q2dzVzE1MzZ4bjdoQXlaNGdHWDFGd3JvdEJnT0d0ellDK01jdEZqZWRpZXBoNWF0NzdwcUlOTStvK3FmK3Q2UzR5ZlBFcWwyWTVRaFBDN1E1Y1M4VG1jU0Rmd3BxWGdFZTJJSDNUczdvTFVudzV1YUdYMmtwVG5JZi9XeHJuNFQ5ekFySnA3NVl2clkzVkI4ZFpZRThpZ3ljKzd1SUZab2xZYmMwSFpRMTJWenZzd09Oa1FsbWZzQmNDRkJDTVFPUXZ3a0VtUU5pRkh5OXRYZm5YNlYweGZRQlVkWm1LZUNDMFJ6Y1pYZnRIQVNkcUswTmJqVW8xQUphdzE5VDRSUlAySHlJV2hSSjVpUHlvazBYMXVGQTk1WTFwUlhZMGxvSjRIeXJET0krVlRjbENsaDNPVUtPRCtJZ0UrTEJveVo2K0NKd1hnU2dtTWFISXVCYVducDA3d1Z6V1BnYk84bXYvQ2VWekZXTmt2TEJKWUJoNmpWK1o5QkxmMTlzTGdWamtQN0V4RUNDU0FOME83S0xVNmR0RGlHQ21GRDQ4RmpoQ3hRbmpZWDlSbkVlSithU3JaajYwVzFCU1ppVS9RQ0ZTcFkiLCJtYWMiOiIyNTQzMTlmYTg4Mjg2MzRlYjI5YjExMjQ1NDk5MDMwZjUzZGJlZjFjY2RlYmJiMDYxN2ZhZDRlYzM3NWJhYThiIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:05:53 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImE4VVgrVWhVcUpkc2gyQU5GNEp1QVE9PSIsInZhbHVlIjoiRmlKRkd4czdsc2tSWFJ4VkpBeTNVSURNVnNrV1VGaGYraENLYUhrRWUrS0RxTkIxamk3TnJBMWFpN0tiQVdMbk9UT0lJckhzbnRlUzhYbTBncUw1WS9CTGpZWWtUTUJ4VHoraUw2eDhqMDg4MmFFQVpWNUpwR1lMOW1VcG9QZzNTcjdON3VMY2lpNk0wNGM2Q1FMenhnK2F6aG5PM1IrVForbWVkSnl2K09YWmJFaE5GZ2hwT1JobUVDbmVhWWNlY25UWWg1T1ZrVFpEODBLb2NpUXl6Y0R4eGVUU2FYaTlZeldnMDNaVlA3KzBGZldvRllhOUUvZVBzMHR2MDlVTVBtVitOamgyWHd0cENDeXh1KzZPSC9TSloySXM4a3RaUkFXUExXYUhhT1VFV0NadUZSNUFURnBjeW5ISkl2cGhROU5GUDJXS056cWFyRXRvSzlrNHBiK3MwVE1aNGRsOXJpV0dFL2NyNCtGa0FGSnpZek5TMFJIS1AvVzVmbEo1U3FMUmFOZmpCM1haMGttMzg0a0wyMjJxZnE0VXhlUXRNbVA0YldwcHl1SkczRFQrTDNJb0hxSlNoWUNsZHFtUzlKcEhkNGRSNURFbUtuSW04dThIeVBERGFSb2RLSjJpUHprMG1rb2VadExEZjVKN2pERHJUT1Q5SngzQzJUUHoiLCJtYWMiOiI2NGRkNzkwM2IyMDk1MmQwYWI0ODNlMTViNmM5NGFiZWYwM2QyMWI3ZGQ0MGEyZmRmNDRkM2I0ODlhYzQxMzBkIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:05:53 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InYrV0I4L2IxQUxMRWRiM2Y4bVUyTHc9PSIsInZhbHVlIjoiRzBHSVd2dUJIQWN2VzJkY0JDcmo4VVB4Q2N4UE1GYThUOWFhMlpnTVM5akVDOThHMExxVTUxc1YySkd4Q2dzVzE1MzZ4bjdoQXlaNGdHWDFGd3JvdEJnT0d0ellDK01jdEZqZWRpZXBoNWF0NzdwcUlOTStvK3FmK3Q2UzR5ZlBFcWwyWTVRaFBDN1E1Y1M4VG1jU0Rmd3BxWGdFZTJJSDNUczdvTFVudzV1YUdYMmtwVG5JZi9XeHJuNFQ5ekFySnA3NVl2clkzVkI4ZFpZRThpZ3ljKzd1SUZab2xZYmMwSFpRMTJWenZzd09Oa1FsbWZzQmNDRkJDTVFPUXZ3a0VtUU5pRkh5OXRYZm5YNlYweGZRQlVkWm1LZUNDMFJ6Y1pYZnRIQVNkcUswTmJqVW8xQUphdzE5VDRSUlAySHlJV2hSSjVpUHlvazBYMXVGQTk1WTFwUlhZMGxvSjRIeXJET0krVlRjbENsaDNPVUtPRCtJZ0UrTEJveVo2K0NKd1hnU2dtTWFISXVCYVducDA3d1Z6V1BnYk84bXYvQ2VWekZXTmt2TEJKWUJoNmpWK1o5QkxmMTlzTGdWamtQN0V4RUNDU0FOME83S0xVNmR0RGlHQ21GRDQ4RmpoQ3hRbmpZWDlSbkVlSithU3JaajYwVzFCU1ppVS9RQ0ZTcFkiLCJtYWMiOiIyNTQzMTlmYTg4Mjg2MzRlYjI5YjExMjQ1NDk5MDMwZjUzZGJlZjFjY2RlYmJiMDYxN2ZhZDRlYzM3NWJhYThiIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:05:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImE4VVgrVWhVcUpkc2gyQU5GNEp1QVE9PSIsInZhbHVlIjoiRmlKRkd4czdsc2tSWFJ4VkpBeTNVSURNVnNrV1VGaGYraENLYUhrRWUrS0RxTkIxamk3TnJBMWFpN0tiQVdMbk9UT0lJckhzbnRlUzhYbTBncUw1WS9CTGpZWWtUTUJ4VHoraUw2eDhqMDg4MmFFQVpWNUpwR1lMOW1VcG9QZzNTcjdON3VMY2lpNk0wNGM2Q1FMenhnK2F6aG5PM1IrVForbWVkSnl2K09YWmJFaE5GZ2hwT1JobUVDbmVhWWNlY25UWWg1T1ZrVFpEODBLb2NpUXl6Y0R4eGVUU2FYaTlZeldnMDNaVlA3KzBGZldvRllhOUUvZVBzMHR2MDlVTVBtVitOamgyWHd0cENDeXh1KzZPSC9TSloySXM4a3RaUkFXUExXYUhhT1VFV0NadUZSNUFURnBjeW5ISkl2cGhROU5GUDJXS056cWFyRXRvSzlrNHBiK3MwVE1aNGRsOXJpV0dFL2NyNCtGa0FGSnpZek5TMFJIS1AvVzVmbEo1U3FMUmFOZmpCM1haMGttMzg0a0wyMjJxZnE0VXhlUXRNbVA0YldwcHl1SkczRFQrTDNJb0hxSlNoWUNsZHFtUzlKcEhkNGRSNURFbUtuSW04dThIeVBERGFSb2RLSjJpUHprMG1rb2VadExEZjVKN2pERHJUT1Q5SngzQzJUUHoiLCJtYWMiOiI2NGRkNzkwM2IyMDk1MmQwYWI0ODNlMTViNmM5NGFiZWYwM2QyMWI3ZGQ0MGEyZmRmNDRkM2I0ODlhYzQxMzBkIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:05:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1481085097\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1789249239 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1789249239\", {\"maxDepth\":0})</script>\n"}}