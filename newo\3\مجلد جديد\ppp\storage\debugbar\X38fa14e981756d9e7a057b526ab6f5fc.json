{"__meta": {"id": "X38fa14e981756d9e7a057b526ab6f5fc", "datetime": "2025-06-17 14:33:54", "utime": **********.296819, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750170833.394101, "end": **********.296844, "duration": 0.9027431011199951, "duration_str": "903ms", "measures": [{"label": "Booting", "start": 1750170833.394101, "relative_start": 0, "end": **********.120617, "relative_end": **********.120617, "duration": 0.7265160083770752, "duration_str": "727ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.120634, "relative_start": 0.7265331745147705, "end": **********.296847, "relative_end": 3.0994415283203125e-06, "duration": 0.17621302604675293, "duration_str": "176ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49310352, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.03081, "accumulated_duration_str": "30.81ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.196698, "duration": 0.01829, "duration_str": "18.29ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 59.364}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.234052, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 59.364, "width_percent": 5.161}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.257634, "duration": 0.00237, "duration_str": "2.37ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 64.525, "width_percent": 7.692}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.263459, "duration": 0.00197, "duration_str": "1.97ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 72.217, "width_percent": 6.394}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.2731519, "duration": 0.00426, "duration_str": "4.26ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 78.611, "width_percent": 13.827}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.282244, "duration": 0.00233, "duration_str": "2.33ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 92.438, "width_percent": 7.562}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1539800558 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1539800558\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.27126, "xdebug_link": null}]}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1086032573 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1086032573\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-775314252 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-775314252\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1130995048 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1130995048\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1389421731 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlZ6cjcyd3NjZ2ROemF1STIvelRBNFE9PSIsInZhbHVlIjoiNVQzdC9MQTQ2YmpuWFdlbFIyTjR4dS9Fanl5YjRqWHRqNTF5NUYrbGYzbkJVak9uNWxvcGFpcFBxb2NsUnpveXI2OXZzVjZGUGwwZ0pwUnZCZ0RUY2FlSlc3OGE4eFo4WlYyai84V0pNOXl5NkZDbXlrVjhTNFM0dWJJYUR3Z1M3cW1rb3NGOXlNS1FiaFNjS2laMWpGSXd1MjRGQ2o0Q1pENWFXQ2pUVUJjUXphVHJPNXhEelpWaDJUK053MjFMVThQVmRhVkdwRENWMHpERnZSTDZ2RkxIRTVXTkZsRU04Nys0UmNNc250M2hHMGltUHJIalZQRW05a0FDL1lXRnc2R1JweURrUjE5UGplSm1Udm5EV2MzV0IxOGxMNnFjK0VmTEE3L2QxSlNzSmJvV2w2OGdaQVptajZHT05TWkJFNC9xYm9IcDJYNXVwbkFVc2tMOUIyL0cxOGVLTUtVVXhIM2Q2M3IwYjU5V01mS1NJaUVYK3l2UGhKSnMyaWlUaVVGRUd6RVFnYUxka3JFL3BNc0pzMEQ1ZlRCVDIyaWY2bmxYQktoUE9XZVhHaVlTNGdoQkZZendURlpKNjBkNU1KK1ViUmlSQ2NLN3ZCOXFGWEVHenJoSXhSekVaQktLU2o2dmVnYndSNHdpYm0vRFViaWZXWk51ZXlRNkZoVGkiLCJtYWMiOiIyOWYzYWIyMTc0NjI3MzRiMTdlOGQ3Y2MzYjc5MTY3NzQwZmQwZDk2MmQ0MjMxYjMyNmM4MjYxODJiZjEzYjY3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImtpdERXVzFnUVZwaTQ5U3JvNERBZnc9PSIsInZhbHVlIjoiaVp3QVRvUGV2RElTMElLYm8vQno5RHFERkw3RzVjaWpKd2lNdTM0MFdRVFlqSUlOVlFoU2NKdGU1YXdIWVhHdTFEajA1S3ZwZ2ttNWxXNFhqVEluR1NneExsWWFhcmdlQ1FoUVFyUlRPRE5WL2NOV3o3SjlJbTF5MjJPSCtWT0ppOUhnRkJhNnJuSUhsSEtjbmx5N0l6aVdwbGZBUSs0MWVibm5oMStwRXc1SmZWQW1Uck82VndlNjJ3bCsrQkRQQjRYQmdNZUdzdGxyMjVueXpNUFMyRVVkem13azk2R3haS2dPNUxPeEdzTGwwMGZtZEJURTJ6bjZBSU1GTXpGMTdaNktRVkd0SmpTZ25GRy9idHA5MGt4Rjk1WHV0LzIvMzdobkxBWEZwcDh3cis5amNTWW04WFpMaUdGUWJBRytUUGd4alNYWndhRldOQVV0WjFLYzBVWFgxUjU5VHhob0dpcW1aK3hmMUhyM0paMmJSY0tTSnAzUUVuaHpSeThnRU5EMGMrYUhZdWZvcFNMUmZkUlNnWklYVmFFSkFZcXl2R1Rkd2J1OFQwZThKUFFBV3dLZFFCd2RvdlhvMUNHNjZaMHpSdDNlSWFudmNvNjFIYWM4ZXhNUkVIQ09GcW1HaFEwN2dOTDZoczN5bHhGVTBWQ0RzNEI0dm42NGhaVlciLCJtYWMiOiIxYmRhNzlmNmIwNTdhZDZjMGVkZGE0ZGNmYmZkYWIzMjFmN2M3MTk4ZmI0Yzc5NGRmYzhjOGUwZTZjOTAxYjFjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1389421731\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-527255928 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:33:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBJa3VKZmMzWVMyTFBKVWVzbjNPdkE9PSIsInZhbHVlIjoiQmNOaExrQ0ErNG0zUmdHbDBpL2tnV1FiYXNNeWVtNXh5VzFNVktoY0RiQll2bmNuOEswQno5WWZPVWhPSmRCTDJTeHpYeUZMWE4xblFaZGNNNFVKWTlZVHJwMk9ZZWFMaDh0QVdtM0Z2a3haR254MnloTE94VVBGZjRBU1YxVmVaZnlXV3RFZ2lKOUxpdXU1dVAwZDhEWUUwYnV2SFRhYzBoUmlPNXpCSlNiaGdwVFVvUWRtYkczYnFPNGxQaGYrOUcxVEdUNCtnTStiVDFXK1NrVmRaQzJpWExjazhDT3FOSmc4dXF2ZkVDSWlpd0FZU2cxRGVhMHRBMVNyMmZudUc0aFdwTDNjMlNQMWlaVWxmTjNwWlJWcFdvMU1MalpPSG1UY3lYTkR0VlZEWlhobDh1QXR6SzQrNzQyb0crZGpJU0Z5M2JuZ3IwVHdiYTlzRm1pSUZGZkhobmhYSGVLWWR6S0RXTkZJZEZmaFNucEpNNHZiTThKcG01WE8wWmtJMFpiOGhpU3NRcGxsU2l2MWd0c0U5Y200UDQrYXcwTHc1eHFlVVVXeVozVnB0MVZocGhrTDJvSU9INVkydU9xUmJMcmV5dzUvZjZ0L0FHUWZXTEpmQmFTRzZPTlhENHFTQnNOZWluS09NSGt2MVV2b3d0Q0hJbzRNMER1Q0JXMlIiLCJtYWMiOiI1NDk4ZmU5NGFmYjFkODk5ZDg2MWI2YzQzNDc2N2YxMGZhM2YwMGU0MDQzMDY0NmVjNzI3YTU4NTYzNzFlNWY4IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:33:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InBQUVJxdG9JUTQ1eWdZb1l6cDNGblE9PSIsInZhbHVlIjoidWxscTVqbmpibGxRZ0tFVDJrVDhVK1VadktGVTJaM1JCV2FMVzllcC9uTE5XbnZQdExpQW05VW0wWjRYQTR0cXZ1QXBOd2orTmZWRk1GY2t6K1lKYi8vd3c3ZkdZWW9nMHBtUmpiTFY0V0liK2hEcElGV3Rmc2gydDJ4cWQ3OUtPdTg4QTJsNk1KdCsvb2tpb2l0ajNLQUJRNGYwUnhrOWVObnJZYit5THVwWU5UUkx2YndOZWk2R0ZjeWVwbHhXbXlkZ3Z4RzlUL29ENmxWUWh1MldaL1V3Zk81dTI0ckV1OTkwTWZTU0ZwTW81Y3ViUXcrTWJFZDQ2Ty9DbE95aHdzMjhtK1BHMVJ1TWxRRjEzeXM2c3I3N0VnWkVrT0hZVUc3VUMvZEtkdGRESnVZU1MwY3ZTVU5EUjUrRFBtWHNXMVMxK2g2bTErUTdHeVFqQlYvN3dEMEpSOWNhSWJ0RXBiMmtKWFM5YWd2clc4UDlwNk9zRlM4QTkyN25Tc1Jhc3kybmxOSCtDY0dxQkVVVE9ubThuQmNRQURoalZabXpDK0xVN3hnZjF5WVdQWERLZDU0eEZkRUs5cFVKd3M0VVJxSy9NRUdwOHdQcWJ2eGErVlJKWk5tL3pvUFdiU1lkWkhRV3VWaEQyYVBuRFQvV2RxZlRDNG9vZUIrWHNldzQiLCJtYWMiOiJhYWY0MmQwMzZiYWZkODJiMDk2MWVjYzFhNjBhNTZmOTYwNTc2NGRjODRkZjIzOTkzOTNjMTNjYTUxNDg0ZDBkIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:33:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBJa3VKZmMzWVMyTFBKVWVzbjNPdkE9PSIsInZhbHVlIjoiQmNOaExrQ0ErNG0zUmdHbDBpL2tnV1FiYXNNeWVtNXh5VzFNVktoY0RiQll2bmNuOEswQno5WWZPVWhPSmRCTDJTeHpYeUZMWE4xblFaZGNNNFVKWTlZVHJwMk9ZZWFMaDh0QVdtM0Z2a3haR254MnloTE94VVBGZjRBU1YxVmVaZnlXV3RFZ2lKOUxpdXU1dVAwZDhEWUUwYnV2SFRhYzBoUmlPNXpCSlNiaGdwVFVvUWRtYkczYnFPNGxQaGYrOUcxVEdUNCtnTStiVDFXK1NrVmRaQzJpWExjazhDT3FOSmc4dXF2ZkVDSWlpd0FZU2cxRGVhMHRBMVNyMmZudUc0aFdwTDNjMlNQMWlaVWxmTjNwWlJWcFdvMU1MalpPSG1UY3lYTkR0VlZEWlhobDh1QXR6SzQrNzQyb0crZGpJU0Z5M2JuZ3IwVHdiYTlzRm1pSUZGZkhobmhYSGVLWWR6S0RXTkZJZEZmaFNucEpNNHZiTThKcG01WE8wWmtJMFpiOGhpU3NRcGxsU2l2MWd0c0U5Y200UDQrYXcwTHc1eHFlVVVXeVozVnB0MVZocGhrTDJvSU9INVkydU9xUmJMcmV5dzUvZjZ0L0FHUWZXTEpmQmFTRzZPTlhENHFTQnNOZWluS09NSGt2MVV2b3d0Q0hJbzRNMER1Q0JXMlIiLCJtYWMiOiI1NDk4ZmU5NGFmYjFkODk5ZDg2MWI2YzQzNDc2N2YxMGZhM2YwMGU0MDQzMDY0NmVjNzI3YTU4NTYzNzFlNWY4IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:33:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InBQUVJxdG9JUTQ1eWdZb1l6cDNGblE9PSIsInZhbHVlIjoidWxscTVqbmpibGxRZ0tFVDJrVDhVK1VadktGVTJaM1JCV2FMVzllcC9uTE5XbnZQdExpQW05VW0wWjRYQTR0cXZ1QXBOd2orTmZWRk1GY2t6K1lKYi8vd3c3ZkdZWW9nMHBtUmpiTFY0V0liK2hEcElGV3Rmc2gydDJ4cWQ3OUtPdTg4QTJsNk1KdCsvb2tpb2l0ajNLQUJRNGYwUnhrOWVObnJZYit5THVwWU5UUkx2YndOZWk2R0ZjeWVwbHhXbXlkZ3Z4RzlUL29ENmxWUWh1MldaL1V3Zk81dTI0ckV1OTkwTWZTU0ZwTW81Y3ViUXcrTWJFZDQ2Ty9DbE95aHdzMjhtK1BHMVJ1TWxRRjEzeXM2c3I3N0VnWkVrT0hZVUc3VUMvZEtkdGRESnVZU1MwY3ZTVU5EUjUrRFBtWHNXMVMxK2g2bTErUTdHeVFqQlYvN3dEMEpSOWNhSWJ0RXBiMmtKWFM5YWd2clc4UDlwNk9zRlM4QTkyN25Tc1Jhc3kybmxOSCtDY0dxQkVVVE9ubThuQmNRQURoalZabXpDK0xVN3hnZjF5WVdQWERLZDU0eEZkRUs5cFVKd3M0VVJxSy9NRUdwOHdQcWJ2eGErVlJKWk5tL3pvUFdiU1lkWkhRV3VWaEQyYVBuRFQvV2RxZlRDNG9vZUIrWHNldzQiLCJtYWMiOiJhYWY0MmQwMzZiYWZkODJiMDk2MWVjYzFhNjBhNTZmOTYwNTc2NGRjODRkZjIzOTkzOTNjMTNjYTUxNDg0ZDBkIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:33:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-527255928\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}