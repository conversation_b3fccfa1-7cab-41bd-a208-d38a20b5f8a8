{"__meta": {"id": "X7dd8870387158d6ca6d994315d224ed6", "datetime": "2025-06-17 15:10:25", "utime": **********.421596, "method": "GET", "uri": "/customer/check/warehouse?customer_id=6&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750173024.751636, "end": **********.421619, "duration": 0.66998291015625, "duration_str": "670ms", "measures": [{"label": "Booting", "start": 1750173024.751636, "relative_start": 0, "end": **********.321291, "relative_end": **********.321291, "duration": 0.5696549415588379, "duration_str": "570ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.321307, "relative_start": 0.5696709156036377, "end": **********.421621, "relative_end": 2.1457672119140625e-06, "duration": 0.10031414031982422, "duration_str": "100ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46261800, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01878, "accumulated_duration_str": "18.78ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.370833, "duration": 0.01703, "duration_str": "17.03ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 90.682}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.403264, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 90.682, "width_percent": 4.792}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.408713, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 95.474, "width_percent": 4.526}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-2057701627 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2057701627\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1762487149 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1762487149\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-145071091 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-145071091\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-498902020 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InZMcmJIWVZxK0p4Q0dzaFZuRkFrUUE9PSIsInZhbHVlIjoiVFZqd0x6cXVvNjMxMkdBL0ZQay8za29lUkpINVAvTmFIbm83Uitra3FkdmU2cEl3VEhnM21mcDVOQkU4QWdjNGlGS0Jid2VGNzY1djA1UVhZcXltY05IYncwcmNUN1lGV3Rscyt6cEJJYXN2T0NjVXZiRTRKeEdaeGZ5N0dTQ3BWQjRwUjdDdXlmSWYvN3FiV2Y4eEhvcFdRc0daeXB4LzdXOEg4ZVEwZ1RSWUp6ai80Q3dkUTJkT0VIcDR1UWNKVThUQW9XWUhHcGFmWEZTQnFnVSswbTZ6NEcvdUF2K3QvV2ppVjAzTWZ0RXBqMjJpcXl0MTJFQklUeXVOaFdOSzQzZnpNVlVKUnZjZ2w5LzZFZC93UU9iK3oxaEdrZGNkVmFGWFczV3RycUNyZmZLSzN0MGFvSXVpaXBXdHpET2dxSGtEZDllWExsemREMmlkRHJYaG9sS2x6cEs2UDhBdVBwOGYwbGZFdzQwcllDd2xnbFh1dHFQYkgrbDBlOFRCNkxENVQraXRBbC9KTWd2OXlIakdIMVlFTXhHUDhrbHhzTWpJMTdwTmJVL1FweUpLb3l1YXFwN3J2V2svZ0NQVzlvYzZ1VUpiRlowVjljMWppa3E0QThveWUxR1pnK0pkV0NUZjdySndOWmUvNkhjaHYwTEpVaE94b1RqcS9YYzIiLCJtYWMiOiI5ZDYzM2FiNTgxZWE5ZjIxNDFlOWJjMTUzNDJmODQ2MThiOTM3ZDU2YjI4NmVlNzVkYmI5MDg3NTc0NDNkMzZkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImFjT0Q3SEJlcFBPR0Mvb0dsSWNHOFE9PSIsInZhbHVlIjoicFM2OVpJNmZya2kxdHJXNmF3UXdKMDZidXVIOVViQmdybjRrOFFycVNmK29Gb2xvMlBRVnlmL0dzZ0tDTlVpcVIrWG1QN0dWNDl0YWowSGs5b2tUanJvaE10dm01RFJRQ0N5OFdKVk03VmVDL3ROUlExeUd1NyttKzhyek1leENUZGp3RmpLZ3dwd3FNUEhCN05ZRnZMRnRUUDdXQitwY3dGeUNVWjZBZVJJQ2pabE0reTArNGd1akZselhqbk9qZDJJU3hWQ2hraHZtZ2h0R0xLZE54OTUwSWdESU5iaGxlNC8vajNaZmdRUVJpR21hMjNETmtYd3RTcVArUCtqV2Vod0FsSHhIRkhNeEo0ZzN5SnJ3aU9NUVRhL2lxOFFVWnB5SEVQTnMweU8wK2wzRzloK3NHZW5jdXhCMXZJRVFpZlBwSjhwYVpRSXdKYkl5Qk1Ja2xhSXBkeFFCOElmbHF1RHFCKzhTSkpNM1M1Vy9UYStybllQZE1GMFZlYXJKd0twMVYxbStwNHM5ZVJhSW0xYXQ4ak1FMjVaU1hKb0QrdXFOV2tXczdNMFVicjBBdjhHUFZDZUpxK3FuS0IzcWdwTmFJaStzR0NiUFJTZTBQV2JBZkR3Vkc0U3JLWFNPTDVjajZKamFhVTFQdnVsckZIRU5TNTYwMUdsR3dvQ2siLCJtYWMiOiIzNGNlNzg0ZTMxOGFiYTdjNGM4MGQ1NjU2ODMxYzMzMGFmMjZmNWJmMDAyYmM3OTM5ZTU2Nzc1YmY3YmJhNmVlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-498902020\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-962810532 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-962810532\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-34713147 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:10:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjE4SmpQRmZhSW5tcnZvMWtUYlVkeXc9PSIsInZhbHVlIjoiL3RiVEJlbEtjS3dBdCtqdGp6TXV5aS9wYThyZnZhTElUVFhOQVdJQnk5eWFUV0MvRmRuaHhQVUZESi83MjJVYTd2T2N5Q0VJQXlVald6NDQyMjI4clZHNjNvTm1nL3hDNUJpS1ZKNGpKVzJ3Skw4bElCL1AvQnM2VzVac1NON1VJNXc0a0FXdHRIUklyMTlmby9LRjNuOXRHamdTU3pRbHdnNi9kNGk4Yk1lUGM5b3hEWUZKQVdkcWduY3c3QmI5UHFzVXA4OUluZ0pnbWJmSGJuVkk0alVLbTk0U1JSc1JYWldxTVQ5T2JzdU5hNjNsWkh0eW9Ra2ZXMGJPaVQzNHhkV3JyNjhHTkpRMm4wdVk3WURDQXIySlYrZGlsMzVudUVjVzFpakQ5eG5veFFNTno0WkNvaU5NcVBBTSthWURITHRSZVRDaU9GR0tWdW9QYWV2R3YxVkVOaUFMeVZxbEh3S3RWWlZtcEx2RGFadHg3QnNwamNjemtMKzJQL05ObTRqY2NqY0NjZkcrRzJPQ2pURkN6aHlLRXVkNmtWaFBMR1pjUTdVNlBwdzF1aVZCQTNadVNuMXVrOGZXUFBaR2dQdTVzRzJFcndsdWtEb2NHbjRpVk5hbHM3aGZXQm82UjNXMi9GK2FweVhMVUROYnJWY0phSkFCczhBTlFPM2kiLCJtYWMiOiJhYTA5NDAxZTgyMDg4ODA2YzcyNTU4Y2FkMzgyYzU5N2FmNGFiYzFmNzE2ZTE5YjAxMzI4MTE5ZWMzMjJmZTA2IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:10:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjRjeHlqWTh2RXhqbmJydFVqUURkNVE9PSIsInZhbHVlIjoid25QZitWSkZxaEdlUSs1VFJIQXdiSlJPa0lWeFNFSDdYQmRwZ29obU9ISzhiUVNmSVBYZkZLQVRhK1Q0RFlWUk1kVlJkTEorb254c2VqM1NVNDl6V2FRVDJOVi90TXpDRE5ia0dmT3RpdzRPdmh5VXVFa3l2N2VSSTBCdWg2Zng3RCtQZ0ozZThWMWpQQU90QXdxU0xCa3pSZTlkQ1Q4R3ExNkNmREdFWlJlNGs2V3YwcHVJaG02L1ZSNzh5OGhKMzUvTmlNYUdIcTY1VWl5cVFTZWViM3c5Z2NCRzdQS1RFcVF6OFNsVmcxMXVUMjBZVnJ0QkpCYWpiL0s5dDJiTGhJZUdJdG04M0ZjVmU3MmlEWDZPM2kzVFVyTndNMm9oRE82RFEvZmhuTU84dVVkZnNLblhsSitTMkVwUjJpa2F1ejFYZE9udUJhUkVqWWVCRWFsbWQ1am9vMWp3SGN1RHNXdFd0STBOeHd3TitlU0dvOEtqdzJCNC9wSVAwSWNITVdXakhWQ3hxVkZlQUszNHdZTExDMmlzNmRWNEtUMUtmYzdtTStBUnoyM0xrOE16VHdMZDVRRFFQOXcxdHN3WExuWFRMZnVuWlRSSFdEZUdtN2Q0R0NrMUpBM0VSOUZDM2I1R21XcHV4cUtrTEp0aUpkQmFoZUx4VmRnQ1cwdWYiLCJtYWMiOiI5NzBkNjFjNDlkYWZhYWRiMGQxMDY3MGZhYWVlYzViMzRlNTk4NzM1MmYzZWUxMGMzNjMwM2M0MWM0OGUyMjA2IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:10:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjE4SmpQRmZhSW5tcnZvMWtUYlVkeXc9PSIsInZhbHVlIjoiL3RiVEJlbEtjS3dBdCtqdGp6TXV5aS9wYThyZnZhTElUVFhOQVdJQnk5eWFUV0MvRmRuaHhQVUZESi83MjJVYTd2T2N5Q0VJQXlVald6NDQyMjI4clZHNjNvTm1nL3hDNUJpS1ZKNGpKVzJ3Skw4bElCL1AvQnM2VzVac1NON1VJNXc0a0FXdHRIUklyMTlmby9LRjNuOXRHamdTU3pRbHdnNi9kNGk4Yk1lUGM5b3hEWUZKQVdkcWduY3c3QmI5UHFzVXA4OUluZ0pnbWJmSGJuVkk0alVLbTk0U1JSc1JYWldxTVQ5T2JzdU5hNjNsWkh0eW9Ra2ZXMGJPaVQzNHhkV3JyNjhHTkpRMm4wdVk3WURDQXIySlYrZGlsMzVudUVjVzFpakQ5eG5veFFNTno0WkNvaU5NcVBBTSthWURITHRSZVRDaU9GR0tWdW9QYWV2R3YxVkVOaUFMeVZxbEh3S3RWWlZtcEx2RGFadHg3QnNwamNjemtMKzJQL05ObTRqY2NqY0NjZkcrRzJPQ2pURkN6aHlLRXVkNmtWaFBMR1pjUTdVNlBwdzF1aVZCQTNadVNuMXVrOGZXUFBaR2dQdTVzRzJFcndsdWtEb2NHbjRpVk5hbHM3aGZXQm82UjNXMi9GK2FweVhMVUROYnJWY0phSkFCczhBTlFPM2kiLCJtYWMiOiJhYTA5NDAxZTgyMDg4ODA2YzcyNTU4Y2FkMzgyYzU5N2FmNGFiYzFmNzE2ZTE5YjAxMzI4MTE5ZWMzMjJmZTA2IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:10:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjRjeHlqWTh2RXhqbmJydFVqUURkNVE9PSIsInZhbHVlIjoid25QZitWSkZxaEdlUSs1VFJIQXdiSlJPa0lWeFNFSDdYQmRwZ29obU9ISzhiUVNmSVBYZkZLQVRhK1Q0RFlWUk1kVlJkTEorb254c2VqM1NVNDl6V2FRVDJOVi90TXpDRE5ia0dmT3RpdzRPdmh5VXVFa3l2N2VSSTBCdWg2Zng3RCtQZ0ozZThWMWpQQU90QXdxU0xCa3pSZTlkQ1Q4R3ExNkNmREdFWlJlNGs2V3YwcHVJaG02L1ZSNzh5OGhKMzUvTmlNYUdIcTY1VWl5cVFTZWViM3c5Z2NCRzdQS1RFcVF6OFNsVmcxMXVUMjBZVnJ0QkpCYWpiL0s5dDJiTGhJZUdJdG04M0ZjVmU3MmlEWDZPM2kzVFVyTndNMm9oRE82RFEvZmhuTU84dVVkZnNLblhsSitTMkVwUjJpa2F1ejFYZE9udUJhUkVqWWVCRWFsbWQ1am9vMWp3SGN1RHNXdFd0STBOeHd3TitlU0dvOEtqdzJCNC9wSVAwSWNITVdXakhWQ3hxVkZlQUszNHdZTExDMmlzNmRWNEtUMUtmYzdtTStBUnoyM0xrOE16VHdMZDVRRFFQOXcxdHN3WExuWFRMZnVuWlRSSFdEZUdtN2Q0R0NrMUpBM0VSOUZDM2I1R21XcHV4cUtrTEp0aUpkQmFoZUx4VmRnQ1cwdWYiLCJtYWMiOiI5NzBkNjFjNDlkYWZhYWRiMGQxMDY3MGZhYWVlYzViMzRlNTk4NzM1MmYzZWUxMGMzNjMwM2M0MWM0OGUyMjA2IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:10:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-34713147\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1166949893 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1166949893\", {\"maxDepth\":0})</script>\n"}}