{"__meta": {"id": "Xaa357a3bec87ef07be6e22bdbb8102be", "datetime": "2025-06-17 14:36:22", "utime": **********.760417, "method": "GET", "uri": "/productservice/create", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750170981.832115, "end": **********.76044, "duration": 0.9283251762390137, "duration_str": "928ms", "measures": [{"label": "Booting", "start": 1750170981.832115, "relative_start": 0, "end": **********.537004, "relative_end": **********.537004, "duration": 0.7048890590667725, "duration_str": "705ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.537017, "relative_start": 0.704902172088623, "end": **********.760443, "relative_end": 2.86102294921875e-06, "duration": 0.22342586517333984, "duration_str": "223ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52649272, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 10, "templates": [{"name": "1x productservice.create", "param_count": null, "params": [], "start": **********.729017, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\resources\\views/productservice/create.blade.phpproductservice.create", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fresources%2Fviews%2Fproductservice%2Fcreate.blade.php&line=1", "ajax": false, "filename": "create.blade.php", "line": "?"}, "render_count": 1, "name_original": "productservice.create"}, {"name": "9x components.required", "param_count": null, "params": [], "start": **********.744964, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\resources\\views/components/required.blade.phpcomponents.required", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fresources%2Fviews%2Fcomponents%2Frequired.blade.php&line=1", "ajax": false, "filename": "required.blade.php", "line": "?"}, "render_count": 9, "name_original": "components.required"}]}, "route": {"uri": "GET productservice/create", "middleware": "web, verified, auth, XSS, revalidate", "as": "productservice.create", "controller": "App\\Http\\Controllers\\ProductServiceController@create", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=53\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:53-97</a>"}, "queries": {"nb_statements": 14, "nb_failed_statements": 0, "accumulated_duration": 0.025920000000000002, "accumulated_duration_str": "25.92ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.593137, "duration": 0.01305, "duration_str": "13.05ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 50.347}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.622492, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 50.347, "width_percent": 3.781}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.647442, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 54.128, "width_percent": 2.392}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.652589, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 56.52, "width_percent": 4.09}, {"sql": "select * from `custom_fields` where `created_by` = 15 and `module` = 'product'", "type": "query", "params": [], "bindings": ["15", "product"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceController.php", "line": 56}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.661592, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:56", "source": "app/Http/Controllers/ProductServiceController.php:56", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=56", "ajax": false, "filename": "ProductServiceController.php", "line": "56"}, "connection": "kdmkjkqknb", "start_percent": 60.61, "width_percent": 3.434}, {"sql": "select * from `product_service_categories` where `created_by` = 15 and `type` = 'product & service'", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceController.php", "line": 57}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.668096, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:57", "source": "app/Http/Controllers/ProductServiceController.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=57", "ajax": false, "filename": "ProductServiceController.php", "line": "57"}, "connection": "kdmkjkqknb", "start_percent": 64.043, "width_percent": 4.784}, {"sql": "select * from `product_service_units` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceController.php", "line": 58}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.674643, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:58", "source": "app/Http/Controllers/ProductServiceController.php:58", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=58", "ajax": false, "filename": "ProductServiceController.php", "line": "58"}, "connection": "kdmkjkqknb", "start_percent": 68.827, "width_percent": 2.816}, {"sql": "select * from `taxes` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceController.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6813428, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:59", "source": "app/Http/Controllers/ProductServiceController.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=59", "ajax": false, "filename": "ProductServiceController.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 71.644, "width_percent": 2.894}, {"sql": "select CONCAT(chart_of_accounts.code, \" - \", chart_of_accounts.name) AS code_name, chart_of_accounts.id as id from `chart_of_accounts` left join `chart_of_account_types` on `chart_of_account_types`.`id` = `chart_of_accounts`.`type` where `chart_of_account_types`.`name` = 'income' and `parent` = 0 and `chart_of_accounts`.`created_by` = 15", "type": "query", "params": [], "bindings": ["income", "0", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceController.php", "line": 64}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.687344, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:64", "source": "app/Http/Controllers/ProductServiceController.php:64", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=64", "ajax": false, "filename": "ProductServiceController.php", "line": "64"}, "connection": "kdmkjkqknb", "start_percent": 74.537, "width_percent": 7.369}, {"sql": "select CONCAT(chart_of_accounts.code, \" - \", chart_of_accounts.name) AS code_name,chart_of_accounts.id, chart_of_accounts.code, chart_of_account_parents.account from `chart_of_accounts` left join `chart_of_account_parents` on `chart_of_accounts`.`parent` = `chart_of_account_parents`.`id` left join `chart_of_account_types` on `chart_of_account_types`.`id` = `chart_of_accounts`.`type` where `chart_of_account_types`.`name` = 'income' and `chart_of_accounts`.`parent` != 0 and `chart_of_accounts`.`created_by` = 15", "type": "query", "params": [], "bindings": ["income", "0", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceController.php", "line": 74}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.693601, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:74", "source": "app/Http/Controllers/ProductServiceController.php:74", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=74", "ajax": false, "filename": "ProductServiceController.php", "line": "74"}, "connection": "kdmkjkqknb", "start_percent": 81.906, "width_percent": 5.054}, {"sql": "select CONCAT(chart_of_accounts.code, \" - \", chart_of_accounts.name) AS code_name, chart_of_accounts.id as id from `chart_of_accounts` left join `chart_of_account_types` on `chart_of_account_types`.`id` = `chart_of_accounts`.`type` where `chart_of_account_types`.`name` in ('Expenses', 'Costs of Goods Sold') and `chart_of_accounts`.`created_by` = 15", "type": "query", "params": [], "bindings": ["Expenses", "Costs of Goods Sold", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceController.php", "line": 80}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.701968, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:80", "source": "app/Http/Controllers/ProductServiceController.php:80", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=80", "ajax": false, "filename": "ProductServiceController.php", "line": "80"}, "connection": "kdmkjkqknb", "start_percent": 86.96, "width_percent": 3.897}, {"sql": "select CONCAT(chart_of_accounts.code, \" - \", chart_of_accounts.name) AS code_name,chart_of_accounts.id, chart_of_accounts.code, chart_of_account_parents.account from `chart_of_accounts` left join `chart_of_account_parents` on `chart_of_accounts`.`parent` = `chart_of_account_parents`.`id` left join `chart_of_account_types` on `chart_of_account_types`.`id` = `chart_of_accounts`.`type` where `chart_of_account_types`.`name` in ('Expenses', 'Costs of Goods Sold') and `chart_of_accounts`.`parent` != 0 and `chart_of_accounts`.`created_by` = 15", "type": "query", "params": [], "bindings": ["Expenses", "Costs of Goods Sold", "0", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceController.php", "line": 90}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.708749, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:90", "source": "app/Http/Controllers/ProductServiceController.php:90", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=90", "ajax": false, "filename": "ProductServiceController.php", "line": "90"}, "connection": "kdmkjkqknb", "start_percent": 90.856, "width_percent": 2.816}, {"sql": "select * from `users` where `users`.`id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 5105}, {"index": 21, "namespace": "view", "name": "productservice.create", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\resources\\views/productservice/create.blade.php", "line": 6}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.731193, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "Utility.php:5105", "source": "app/Models/Utility.php:5105", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=5105", "ajax": false, "filename": "Utility.php", "line": "5105"}, "connection": "kdmkjkqknb", "start_percent": 93.673, "width_percent": 2.778}, {"sql": "select * from `plans` where `plans`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 5106}, {"index": 21, "namespace": "view", "name": "productservice.create", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\resources\\views/productservice/create.blade.php", "line": 6}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.7355828, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "Utility.php:5106", "source": "app/Models/Utility.php:5106", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=5106", "ajax": false, "filename": "Utility.php", "line": "5106"}, "connection": "kdmkjkqknb", "start_percent": 96.451, "width_percent": 3.549}]}, "models": {"data": {"App\\Models\\ChartOfAccount": {"value": 67, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FChartOfAccount.php&line=1", "ajax": false, "filename": "ChartOfAccount.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\ProductServiceUnit": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceUnit.php&line=1", "ajax": false, "filename": "ProductServiceUnit.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Plan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FPlan.php&line=1", "ajax": false, "filename": "Plan.php", "line": "?"}}}, "count": 101, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => create product & service,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2038043536 data-indent-pad=\"  \"><span class=sf-dump-note>create product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">create product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2038043536\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.660121, "xdebug_link": null}]}, "session": {"_token": "zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6", "_previous": "array:1 [\n  \"url\" => \"http://localhost/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/productservice/create", "status_code": "<pre class=sf-dump id=sf-dump-73491357 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-73491357\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-71394752 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-71394752\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2085265141 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2085265141\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-278446141 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=3pzjoc%7C1750170827229%7C5%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IndqOGRkSURhdzd2ZWNuOHA3STNZc2c9PSIsInZhbHVlIjoiS01uc1p3aHc5ZStMdzlhWnN6dWJURndyTHcrR1k1ZndCRUV4UnVLUjBQQmxnQWdCUnd1SmltY3l0dGxpMzQydkhWaFBidnNkby95VEJkVXlHMTQ1Qm0wT05Fa1BUbFhFNUs2RS9EbXV3enlFVEM3d0pQam9NWWhZeWNDTjFIdjIvbFVkdnF0Mk9SbFR2clNtcEdKWmV3dnAxMUtnMXQrUzN1cGhQcG5rQTA1ODVMdzNMOW83ZFp0QnNVZm1OeTNHVkFyajg3WmVWaWk5ald1bXNGVFdoaGtrWVZuanhaNERCcHlXUUhEa2MzUjNJbk1VZUQ0YmVhYlZXSEc3bkw5dHNkNGFvcDRqcDNPYktUT0ZKak0welZXUWl3TEVsc3ZEM25JWUFDcnVraWhPTk1XaWhOOEkwMDZNam14bkpoUDdBR1FINFYrczZNSk51QStYVC9oTUtWL0tSUng2NnRJUVZCQ1pLaEtIVmtWM1VQZTJ3aVM5WlpxVHFWOHJWWkwzYWtieGdvMUhacjdMNmthSlNWb013TUV4bFFFR1BXWEErU2pseHM1enFnZ2dFTzVLV3psUDBiRzBFOEZvZDJBNS9jQWNjRDZTeXhLZGFQL2RzYldwYkVtc01rQWlLM05kZlMrSEU5UmRwaWxpMExXak8vbUdtU3ZUUDVmdlQ4WG4iLCJtYWMiOiIzMTQ4NzYzMTA0N2I5YmQ2NjNjOWJmY2YzZDRkZGNlNjI2ZGE1ZWMyOTM3YzhlYzE2Yzg1YTEzZjc4OWZmMWJjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Im9XVUp1V3B1RWMzSjhmZEFzbFRoSlE9PSIsInZhbHVlIjoiaVhNcUVjVjdxYm1oejNBeWJWK2t5NmszN1lEaUpGSVFWR3pBd0JjeDM3bXNJRnFZckV2SHU3SjVnbXBkMHVGaU1EaG1hWEs2SWhVUjRWVVlJODVjZ241OHBCUC9WK1YwZXFhQzhhdEVpNnlNWElKUmU2Y1hJNDBIUWJSWkdsN1NoemQwZDB6TE96SzR0ZnBsMnFlZlc3YWZ2THhLN2dzaUtKRklPTTZCempZQ1ptUnF1K1FYUmU3bmM1cjhDMStxdzVFdjFBbVNoYW1jdU1iMDJ0S0RGaVhzalRnbXVBYnhlMjB5c0xYZmVaNVVXUWVLT0hIcW0rY2FDT0hEUzQvSGQyL2tsY2hVWlZEREVSWGYwU1hVUVFRcEZUaHdFRzhVejV5Z2tYT2dtTnFCajgvVEdXSFRJQmtnZXNUYTcyTE9uVkIwbHA4Zm1adkNvOXhIRUJNMXlEZGZDTlA4M3YxNlFoTlgvTmJrUk5kbEJHY1d2RlhnaXdCSm9LMkhuKzJhc0I2cnlTUWFzU0UwQlhER21JNVA1SVl3RldIeHJrbVl1Zy81bVRSNHR5cnh0V0pzdWFCb1dCeitqUVNJYjB4bU1pUFYyT3l1S2ZYVllscnB3emtuUGdkZU4wNnY0Z25NbkhBSmxnZktXR3owT2ViaXNsam9EOFRJRS9ITTIxZ0IiLCJtYWMiOiJhNzRkNzYwMzVhMjI1YmY5ZDZhMzFhOTQ1OTkyNDcxN2RlMjE1YWJkMjdiMWRmN2YwNmM5OGUwZjMxYTcwYTM3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-278446141\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HxnOnSuhAAPTfN2VS4q58slgJoytwtHYqaX0oLoW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:36:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlRJeFVYNG9JSFNnd3hHSjdKcFRFa2c9PSIsInZhbHVlIjoiZk9scHZVV1pSQ01OK0xEcWJMS0xGUHRhR0FiY2l2N054NDRMV29nd1k2cTNyTmVjd0dVNEpRK3RFeE94SmRnbTdwRzNhMXJuYUtLZTAzamtoYkJUYXBDSjFaeC9NUDFlZWFicllkN3RvaEVxR3dNenIrQzNUSVJCeGNKeHQyOVRXLzdLRUE3MllwL3lES0VPdVNHQW1MQjkxS01lTFZlWTBTa0F6MW16V3FENlBXWnZYNTNRa2x6NytYZ2ViMFNEa2N4Mm1oN0N4RDlLUXJyT3l5eGo0T0NyMVl6V1p3OGFSaGRBYXhrM2N6cTE4ZS9sZlZHdVVINEJyZVJyb3I2ZGV6MHZ0LzFOVEhpYWhySnNlMWhRemM4dm0wV01hQ2h6M2o1TG9haC83cjZFKzhrTkQxYThnT0FtOWhQNFdWaG8vVTNFai9hY2docU5Gc295SDJFRU53Y2d1bTJiWnkxRXZNejRLV3BIQjZZOHdQY0VyZGdock1pei9nZ2VqaU5vdTI3d01YUHhSaUxsRW9QYkJCS1B0aGVJL1A1Ly9Wejd3MmtNbXFXbkpjMk1uTUhicElLUEdWVFlwTzZHY1R4dmJCM3A2aU9YRkdMUk40VzhSS0pUbkw3NkZ6SzMyU0x2eFU2dVpkZ3BaejVTTmh2RDBhQ0htendVYUNXYmlVejciLCJtYWMiOiIwYjgyZGVhM2QyMWFhNjcyMmMyMDk0NWRhYzNjMDNhYzFmODI3YTIxMWRiMTIwY2EzODUwZTJiMWQzZTI3MTJhIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:36:22 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImpibkRXU0NjYXVDMHZ3bzhBN0hJa2c9PSIsInZhbHVlIjoicCtjcllTTlY0ZFd6VEF4TkVhcWNGUGFEV3RncXBJSmdXd085S2lxaHBBeTlJNkI3aERpUjkvbUM2aU5rQWw4S0NlKzF4bmtycjlubmR4cE1TdTBxc1VySjFpV3pPTWJ0R2FwK0RpaFl0NlJ4RXNNOEFxb3k2NWp1TTAyNTJTYm8rWEE1N1BFLy9QeEJaNXpveHhMSmdZWXdHQXdVYXF3aWN4SVdxMFhHcVFjNGNHMnZhcjNXQnNSbU52TGM0VG1nS1U2S0pTYVZIeEhCOVhIK3o1cXZuckFsc3VpZWdKTHd2RUpPMGlMS2J5eXRkNGgxWVBvYWk3T0s5WkFLSE5xdWx6T2lITDZiZ1RaWFVvWEpOeEZqRGEvdlBwZFQ5UDlLWThCeHVsT0MvWStVYUVKNEgzZVYrbE9MdEhIVEJDd2R6VjJHTVpTMmZsVCswcE9pbmFHUTZXdytEQUJuTWROM3dTa29LTzFVYzNuRm5PM3RPdG0yLytXZkxFMDFwUWpEOStIQW9rSTluSkZjUnVwckI2Q2NiWUk0Y3FlanpLbjhJdS80ODhKcVRZTU1EakVYMWdYUERSY2c4QkJMSXU2ZVNOZk1QR0c3aldzTzNKWVk0S1lUTVFXQVVkNW1pS1lsQ3lTaFh1UGFyazlnZG14eXhBYnNJM2NaWjgwcUpLT3IiLCJtYWMiOiJiMTIzZTAxMDIyMjM0ODc5YmIzMjc3YTJjMjc2MGM2OTkwNWMwMzExZDJlMDRiMTc3NGExOWQ1Y2U5ZDM3YjAwIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:36:22 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlRJeFVYNG9JSFNnd3hHSjdKcFRFa2c9PSIsInZhbHVlIjoiZk9scHZVV1pSQ01OK0xEcWJMS0xGUHRhR0FiY2l2N054NDRMV29nd1k2cTNyTmVjd0dVNEpRK3RFeE94SmRnbTdwRzNhMXJuYUtLZTAzamtoYkJUYXBDSjFaeC9NUDFlZWFicllkN3RvaEVxR3dNenIrQzNUSVJCeGNKeHQyOVRXLzdLRUE3MllwL3lES0VPdVNHQW1MQjkxS01lTFZlWTBTa0F6MW16V3FENlBXWnZYNTNRa2x6NytYZ2ViMFNEa2N4Mm1oN0N4RDlLUXJyT3l5eGo0T0NyMVl6V1p3OGFSaGRBYXhrM2N6cTE4ZS9sZlZHdVVINEJyZVJyb3I2ZGV6MHZ0LzFOVEhpYWhySnNlMWhRemM4dm0wV01hQ2h6M2o1TG9haC83cjZFKzhrTkQxYThnT0FtOWhQNFdWaG8vVTNFai9hY2docU5Gc295SDJFRU53Y2d1bTJiWnkxRXZNejRLV3BIQjZZOHdQY0VyZGdock1pei9nZ2VqaU5vdTI3d01YUHhSaUxsRW9QYkJCS1B0aGVJL1A1Ly9Wejd3MmtNbXFXbkpjMk1uTUhicElLUEdWVFlwTzZHY1R4dmJCM3A2aU9YRkdMUk40VzhSS0pUbkw3NkZ6SzMyU0x2eFU2dVpkZ3BaejVTTmh2RDBhQ0htendVYUNXYmlVejciLCJtYWMiOiIwYjgyZGVhM2QyMWFhNjcyMmMyMDk0NWRhYzNjMDNhYzFmODI3YTIxMWRiMTIwY2EzODUwZTJiMWQzZTI3MTJhIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:36:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImpibkRXU0NjYXVDMHZ3bzhBN0hJa2c9PSIsInZhbHVlIjoicCtjcllTTlY0ZFd6VEF4TkVhcWNGUGFEV3RncXBJSmdXd085S2lxaHBBeTlJNkI3aERpUjkvbUM2aU5rQWw4S0NlKzF4bmtycjlubmR4cE1TdTBxc1VySjFpV3pPTWJ0R2FwK0RpaFl0NlJ4RXNNOEFxb3k2NWp1TTAyNTJTYm8rWEE1N1BFLy9QeEJaNXpveHhMSmdZWXdHQXdVYXF3aWN4SVdxMFhHcVFjNGNHMnZhcjNXQnNSbU52TGM0VG1nS1U2S0pTYVZIeEhCOVhIK3o1cXZuckFsc3VpZWdKTHd2RUpPMGlMS2J5eXRkNGgxWVBvYWk3T0s5WkFLSE5xdWx6T2lITDZiZ1RaWFVvWEpOeEZqRGEvdlBwZFQ5UDlLWThCeHVsT0MvWStVYUVKNEgzZVYrbE9MdEhIVEJDd2R6VjJHTVpTMmZsVCswcE9pbmFHUTZXdytEQUJuTWROM3dTa29LTzFVYzNuRm5PM3RPdG0yLytXZkxFMDFwUWpEOStIQW9rSTluSkZjUnVwckI2Q2NiWUk0Y3FlanpLbjhJdS80ODhKcVRZTU1EakVYMWdYUERSY2c4QkJMSXU2ZVNOZk1QR0c3aldzTzNKWVk0S1lUTVFXQVVkNW1pS1lsQ3lTaFh1UGFyazlnZG14eXhBYnNJM2NaWjgwcUpLT3IiLCJtYWMiOiJiMTIzZTAxMDIyMjM0ODc5YmIzMjc3YTJjMjc2MGM2OTkwNWMwMzExZDJlMDRiMTc3NGExOWQ1Y2U5ZDM3YjAwIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:36:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1860602598 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://localhost/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1860602598\", {\"maxDepth\":0})</script>\n"}}