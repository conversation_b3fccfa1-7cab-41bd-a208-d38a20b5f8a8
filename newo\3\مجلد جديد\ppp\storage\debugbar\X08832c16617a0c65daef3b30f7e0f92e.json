{"__meta": {"id": "X08832c16617a0c65daef3b30f7e0f92e", "datetime": "2025-06-17 14:23:12", "utime": **********.082046, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750170191.324369, "end": **********.082078, "duration": 0.7577090263366699, "duration_str": "758ms", "measures": [{"label": "Booting", "start": 1750170191.324369, "relative_start": 0, "end": 1750170191.9451, "relative_end": 1750170191.9451, "duration": 0.6207311153411865, "duration_str": "621ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750170191.945119, "relative_start": 0.6207499504089355, "end": **********.082081, "relative_end": 3.0994415283203125e-06, "duration": 0.1369621753692627, "duration_str": "137ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46118304, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.028189999999999996, "accumulated_duration_str": "28.19ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0086899, "duration": 0.024579999999999998, "duration_str": "24.58ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 87.194}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.0524988, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 87.194, "width_percent": 6.421}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.066854, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 93.615, "width_percent": 6.385}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1026311330 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1026311330\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1182098918 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1182098918\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-64206497 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-64206497\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-985028911 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://localhost/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=3pzjoc%7C1750170159624%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlJydUFvTFJveStEQWFkVHdxMXZ1bmc9PSIsInZhbHVlIjoiSjdXT2ZzZzlER1RCSi9lZHVZWmJZSmc2Uk1EcHI3empzUFFNWTlmeDljTHhSRmtyNVlCQ0FjZlRtMzdmaUVuazdsRXlzdWk1MUx4NlVxamVLOTNQWjRoVzF5SDhpVEVqKzRITFZ5RVV4bXF4Q0xpY3EzS3NjRW1xL3pDbXd6a2JxZTFPZ2xQQnhZZ3pjdG16Mmp0RnJjWHRmbjRSLzZTVERNYktwWm91L3RuOVFjSUlaa3RNMkhwcFRITlQ2QXdHU2tGYU5zRkVackxudUlIald4UnFMVmh2b0IvVUl4V0V0dWljdWhSRzFVVlcxWmtBeEJmZElUZU9WTGFlQlNGcVR3cXRObXFJTDd2YTd5anlXUjJrV3BPUHc5ZlBCckNBcGZFQTEvdHFEK2ZnelRNVVU4NVZkeHM3KzRJOW04eVlvUUNpSFJ0c3ZWWWoxTXlTVTR3ODkxMHBPUUZDRHB0SGlWZlo5UmNCalE5Y1k5ODdQS0NpNjBXQ0t5T3dJWVFSNDlVUVhNMDBTMW9EUGFPWk1BWUNXaTJyYTdsSFZETlljRnlMekphYVNFa1lZbU1HYlBpOWV4RnBWNE44Z0ZaTUJtNndZSW0xZW9UeEFoTjlENEdzYXdQQk1UTVJSOWUxOVV2bURwUi9Yd25LUW5sT0VCS2FSdC83RDVxS3M5ZEEiLCJtYWMiOiJlY2ViNzBlZmI5OGYzNmJiN2I3YjAyOGM0M2VmZmYyYTg4YmFjZGI2NTgzYjUxNDE1OWZjODVhZjk5MTlkNDliIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlQ3TWE2Tkduc0l0cEp3WmFEYjN3MlE9PSIsInZhbHVlIjoiWmRPcmgxVVRGZTc3U25ucDRGVTlqZVp5T2xVbEdZeVVFOXpDMEp2QjNnRTNzY0FmSHhlazZLRTJDczI3WlQvR0k2bkkrN0lIOUR0cEJPSzlnSjhKaDZvWnJhOGFkVnFkVFl5cVQxQXFuQmtzS2RtTk4yZ3JvT2R5Snl3WW5LSzRGTjZIVkV1VkpBQlJ6ZTFUaUdBSHN0Y05FZlpKVDRmOVFIOWpKbXJSZk03UDc3OGhiREFwYkJWZUx6N0VIVjBwTUdnalRsa1BaVEFtaFBzcC9KSG1MMVdCNlpMVStFKzUwRkpmNHhvdzJvYkh6SU1Da0J6Z1BIZUVMa2F6MWZhei9oYmE0STZibjFDUDM1L3VOTmtjazc5VHFWUVpkaVVtQ21NTE9iSHg2bEswcU9wMHVRUzhGZUFteCtBVmNwdGJVMVphZmRvSjgyZWREK29OMHl2bzdJRG94dWFzRWRYWVV4S1dacVpsa3Yxb28vSGgyMzZCVnplMWt6NGQ2RWlSaWNhOThONFNYMTl3b2NaOE1Uc0ozRVFFcmYxSGJkQ3VGZEJzU1hLcThYdGh3WGt1RVNFVEdUbGUvUGJjTkVUOVR1elpXTmZwVXZxcE9DZElOZkdtNVNMUDVIY2lxbndsSWxPd2hpd1ViNjVaL2NtZmtDSTBrczVNWG1UTnlNYSsiLCJtYWMiOiI0YjZmYWNhMWE4YzU2OGM5YmZkNDk3MjEwMjI5MmNlZmIxOTQ0NGViODQwZGMxNzg4OWU4NGRhZTQzZjQyM2JkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-985028911\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HxnOnSuhAAPTfN2VS4q58slgJoytwtHYqaX0oLoW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:23:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ink4Zm9MNklNUzRZN0l2bXUwTXJJUGc9PSIsInZhbHVlIjoib3pEZ2Zhb25HUU1WT2M0REY4RjRIVFhpblJUdmdmM0tGM3pvTFpSMyt4aHJzK29keVA2OXJ0ZHVvNE8wbHNiL3J6emo5NUhBbFA1R2VYZ2h3dFpTWHJHK2trT0dxZmpDUTgxUmhLZGhYQUxGNEhWa2h4QVYxTHFNWnM2MnN4WlladG5tWHhsQ3J2WktrNTgzSFdrcG5Wb3RPc2xkNnc2dFo2TFlOYTlKRHV6anFDTmlSNUVHTFgzZm42UC9zemprWWErSU5CSkNRcnZBUnlUQm5Qc3BQSzA0RHorcXZpTzdhK3cvU1pnbXRxWVVIdXhmS0xFUUx4ZUZsM1dIdXY4YUt6UnB6Q3B2K1NkS2xzRmdEcHkrZlg1WUo3ZlFITENLKzFGUzFKMC9IQ043VE1Ua0FwcFVBMnpVSmZhL2hIUjdzVEJ5SURsblRISXh1WHBZZVhya09oU3BCSEZmSjk5U2E2OTBycHpJczBkV3p5NTZYc2wxUm0wYkQ3Q2xxMHlNQjhjNForVmJVdHBjWktDck11K1RCckpxdEllblFtYWs4NnFnUlUwMEhZS2kzcFRiejMwVFgyRVlBV1BaWnN0N1hobmdBNzlVeXA5aENGMVpHM2Mya1o2VnpwbGZqMkIzUk5lNFR3aEUvc0I0djJsSFcyNVJVSHBWbCtPS0gxTkEiLCJtYWMiOiI2MDIyNzFlNmI0Zjk3NDI4NmIxNjY3ZDViNDhlNjE2ZDNkYjlmNzVkMGJkYWZlYzY0MDVjMzkzYzQyNmE1ODRjIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:23:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImplcXRMb2kxMUt4Q2J4VjRaeEpveEE9PSIsInZhbHVlIjoiTjZuam14dkdCL3NWc0hUWnVHazg4R0YrYndrYUhKYkdEbHJjc2ZOcXA4OUxEUUpDWTVGbEQvelFOSGVOSTgweEg0dnB5TEl3QlRSNVR0U3R4dGRvVFREb3R6ZmllMHFFNDJZdC9JZFhzNUJvZHBuU3ZyT2puNnFZQTZzR1RzVHc0RzRma0tXUzVDYTlsMXNEV3VQQWVENDNxUUpFS0ZldzFud21rZ1BhVWVqWndMd3Y0QTNWcDlicXZlVVR4bjNOOXVmMXd5bTlYN1ZvOXdKV3R5MlpYYVBUcnpwdmovRGxxSDFLS292QVVTazEvTTdVNUFWNU5VMGEvV29qcCs2ZEozVElEMTBHQWNkVEF4RWw0Q09CVXJqYkxuQm9USTVsOGIzSlpjLzR3WUExTnZ4MnNoOG5aVi91T3RDRXViS2tHWlpaOWRLL1JxSGlDaHJ1WG45cEFDazExTXVKVkJ4a2VyREJidFVmL1V3aHlDNE5PeCtadCtvOXFOVWtHd3pvUENpRU15MEFmbUc3N0JDcHRCRWRzejk0d2VtalE1ZWYrWjlXa3RRSVRVRWpIUWJUcHpsM2pYZjN5eTVEb1A2dCtVSlRPYktYTGJZMklsVkZwK0c4TUhzTkFlaWdjalkxNU1OY2J5SmhsSXZhVTZtb0NKYlRGWHJwMjhRbUhadlQiLCJtYWMiOiI1ZDliNzQ0ZTJhMjI1MTZlMTdiMDNhZjc2ZmMwYTlkYTJiNjZlOTM1MTM4YmQ3ZWMwMDg4YjJhNGJmMWUwZWE4IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:23:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ink4Zm9MNklNUzRZN0l2bXUwTXJJUGc9PSIsInZhbHVlIjoib3pEZ2Zhb25HUU1WT2M0REY4RjRIVFhpblJUdmdmM0tGM3pvTFpSMyt4aHJzK29keVA2OXJ0ZHVvNE8wbHNiL3J6emo5NUhBbFA1R2VYZ2h3dFpTWHJHK2trT0dxZmpDUTgxUmhLZGhYQUxGNEhWa2h4QVYxTHFNWnM2MnN4WlladG5tWHhsQ3J2WktrNTgzSFdrcG5Wb3RPc2xkNnc2dFo2TFlOYTlKRHV6anFDTmlSNUVHTFgzZm42UC9zemprWWErSU5CSkNRcnZBUnlUQm5Qc3BQSzA0RHorcXZpTzdhK3cvU1pnbXRxWVVIdXhmS0xFUUx4ZUZsM1dIdXY4YUt6UnB6Q3B2K1NkS2xzRmdEcHkrZlg1WUo3ZlFITENLKzFGUzFKMC9IQ043VE1Ua0FwcFVBMnpVSmZhL2hIUjdzVEJ5SURsblRISXh1WHBZZVhya09oU3BCSEZmSjk5U2E2OTBycHpJczBkV3p5NTZYc2wxUm0wYkQ3Q2xxMHlNQjhjNForVmJVdHBjWktDck11K1RCckpxdEllblFtYWs4NnFnUlUwMEhZS2kzcFRiejMwVFgyRVlBV1BaWnN0N1hobmdBNzlVeXA5aENGMVpHM2Mya1o2VnpwbGZqMkIzUk5lNFR3aEUvc0I0djJsSFcyNVJVSHBWbCtPS0gxTkEiLCJtYWMiOiI2MDIyNzFlNmI0Zjk3NDI4NmIxNjY3ZDViNDhlNjE2ZDNkYjlmNzVkMGJkYWZlYzY0MDVjMzkzYzQyNmE1ODRjIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:23:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImplcXRMb2kxMUt4Q2J4VjRaeEpveEE9PSIsInZhbHVlIjoiTjZuam14dkdCL3NWc0hUWnVHazg4R0YrYndrYUhKYkdEbHJjc2ZOcXA4OUxEUUpDWTVGbEQvelFOSGVOSTgweEg0dnB5TEl3QlRSNVR0U3R4dGRvVFREb3R6ZmllMHFFNDJZdC9JZFhzNUJvZHBuU3ZyT2puNnFZQTZzR1RzVHc0RzRma0tXUzVDYTlsMXNEV3VQQWVENDNxUUpFS0ZldzFud21rZ1BhVWVqWndMd3Y0QTNWcDlicXZlVVR4bjNOOXVmMXd5bTlYN1ZvOXdKV3R5MlpYYVBUcnpwdmovRGxxSDFLS292QVVTazEvTTdVNUFWNU5VMGEvV29qcCs2ZEozVElEMTBHQWNkVEF4RWw0Q09CVXJqYkxuQm9USTVsOGIzSlpjLzR3WUExTnZ4MnNoOG5aVi91T3RDRXViS2tHWlpaOWRLL1JxSGlDaHJ1WG45cEFDazExTXVKVkJ4a2VyREJidFVmL1V3aHlDNE5PeCtadCtvOXFOVWtHd3pvUENpRU15MEFmbUc3N0JDcHRCRWRzejk0d2VtalE1ZWYrWjlXa3RRSVRVRWpIUWJUcHpsM2pYZjN5eTVEb1A2dCtVSlRPYktYTGJZMklsVkZwK0c4TUhzTkFlaWdjalkxNU1OY2J5SmhsSXZhVTZtb0NKYlRGWHJwMjhRbUhadlQiLCJtYWMiOiI1ZDliNzQ0ZTJhMjI1MTZlMTdiMDNhZjc2ZmMwYTlkYTJiNjZlOTM1MTM4YmQ3ZWMwMDg4YjJhNGJmMWUwZWE4IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:23:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1591256106 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1591256106\", {\"maxDepth\":0})</script>\n"}}