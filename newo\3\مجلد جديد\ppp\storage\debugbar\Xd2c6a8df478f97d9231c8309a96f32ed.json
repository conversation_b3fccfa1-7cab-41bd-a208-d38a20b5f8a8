{"__meta": {"id": "Xd2c6a8df478f97d9231c8309a96f32ed", "datetime": "2025-06-17 14:54:38", "utime": **********.657481, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.049511, "end": **********.657505, "duration": 0.6079940795898438, "duration_str": "608ms", "measures": [{"label": "Booting", "start": **********.049511, "relative_start": 0, "end": **********.528977, "relative_end": **********.528977, "duration": 0.47946596145629883, "duration_str": "479ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.528991, "relative_start": 0.4794800281524658, "end": **********.657507, "relative_end": 1.9073486328125e-06, "duration": 0.12851595878601074, "duration_str": "129ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49205496, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.02299, "accumulated_duration_str": "22.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.575414, "duration": 0.013779999999999999, "duration_str": "13.78ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 59.939}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.601248, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 59.939, "width_percent": 4.219}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.623445, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 64.158, "width_percent": 7.395}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.627748, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 71.553, "width_percent": 5.568}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.637145, "duration": 0.0024500000000000004, "duration_str": "2.45ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 77.12, "width_percent": 10.657}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.643737, "duration": 0.00281, "duration_str": "2.81ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 87.777, "width_percent": 12.223}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-270612321 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-270612321\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.635147, "xdebug_link": null}]}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1693702764 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1693702764\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1613100180 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1613100180\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-58951835 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-58951835\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjhmdW9mQldINXppMmVkVzc3YjRRM1E9PSIsInZhbHVlIjoiM0RjajQvTUQycVJreXZzY21TRmlCMjQ1M1YwblNmakZVRXY0bWlFM2N0ekNQckRRT3BTTnpxQnhHZGdTMVNIdDQ1cjNLRW50aXdqRXJYRjQ4WC9GVzJGTVRDc0t4NnZublVucUQ3TTZVNm9jbkV1RkFCcWlYOG14MTAyeFpmUnErSEE3MXlwV09DVXo3VDNadU1wRm85MWh3YXFYOHFkQ1Y2TVljNTkwSXlVYkYzYTBET1FSZXNNMGZSa0o4N0dZZnNKdmFKOVIzUHlYUEs1Wk9KbjdPdzhnR3k1WjFyZDFiWEFQZW43N0taR1gxZnJENG0rOXFjYko0TDQ3UDdNUUlLS0toTVNHWTJPV1dzVzlmdlhDZStBVmowVWh3SDVleGl6Q2NrOTJmZkJ4eWFUNnp3Y2E1b3lnL2JKMENlN2RZaFVIaU9GOW9aWGQvbTVuTHdRdDJGSXZTRmt6SkFPelh2VXp3dUxrMGJyeVU5UFplRVlOMEJsb1pwMDZzOUNNaTM2WnZHTE42WjdyRHFFYkdNSW1hQ043dkdsZHlJaXFUREFKQlJJWjBqUDROakJRUE03QlJQdjZsWWhsK2h3MVNEU0RVbVAzMThhS1I1UFBVS3VNZlp5am9yZXNnYmtTZHl2L0V2bHROTlh1cUc3Mm9ZaEg1Y2JtaWVsV2hqREoiLCJtYWMiOiI4ZWMzMjdhOThiMjM5MDZlOTk5MjM5NDZlMWM4ZGQ4NzQxN2E2ZGViZDE4MjU4MmViYjJhNGJkY2IzNGE5ODYyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkR6QkR3Rm1oWFBnejRsWnhEQm1YL1E9PSIsInZhbHVlIjoiK1RpSXZWQnNnZnFoajZuQzVDVk5qekIzSW5EdUpNMVBTNjRQUmtJWFZGNUJvRmlja0ROUlp6TlY5Nys0clZqcUN4NXlUd3J0MCtHaEMxVWlVcld5V1B5cy9RQTc3Zmw4ZWpnT0JQQ21BamNrUUpCWkY0OHRndUxuWmJHSFpkSHV4L1VrSVdsbkF2K2ZFOWUraGtlNDE2Rkx5bjNJM2NNTDRzYWhuTks0SDZHUExVa3MxZ2NNdk1DRjZDU0lwWjJwcnVLZkJMWDVYYkdRRG9lU2lWSHZFREtWaHdnU0VqMjJ4UU14aHZJSFlNcHR3V253VXlvMXpMTEoyN2tJK1RPMVBlMEJ6dkZndVI5L1BYdGsvVmNMQndMRkpVRkRsaG9adytCRDJyRzV3M3pNNkFOMnlLdEZhZ2I3RUt2UDEwbGVPRFBJbzBOUGtMZ3V1VFdUMStXTGRteFQ4L1AyTVFVMTZxRmVENjlGcXNJbjBhNGpQNW9TVEZmbkgwUm1sUVE4STFyVi8rdXRBb2RnT1BzZWFCaXZqNVR2Q2lCU210M2tVZmtPbnNEaWNVN3pGK3Z2dENsZEtzVDM3N0MrNUU5S1dJcHB0dXc0cXhsSTQ1azUvajhqaDl5YlJyOW9HbSt3WWhHQ2h0UFFzemFEck9DNkNkK0RwWGlzMVFVZXRCakoiLCJtYWMiOiI4ZDBhZjdiNmYwYzYwZmI0ZWZlMDQzY2YyMjk4MmFmNzBlMDk4YjcyY2I3NDNjMWY1OTk1ZjNhM2YwYmEyODViIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1870316716 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1870316716\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1043163450 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:54:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkNjZEliQjBYQ1dCa2lGUG9uVk1CUHc9PSIsInZhbHVlIjoiOEpxZmdqRHYyQTBQYXBsbmN0Y1lSS0FleFpTNGEwdXQzdmtLVjdnNEp3bUh4aDQyd3U1NE5IcDZJam9VSnBuV2NDRGtRUFpyVmJrQk5udTJzSjJwbU9ETDN3SUw1aHkxOThTSWswMjlVWm1GT1dtVjlESkI1UjJ5eUFnVVFUT0phR2o0TVl2bUpRckNZY0p1ME9OYSsxNmdROWtuU3d0K3VuRUdkMXJwNVQ1TzczTHVLSlZiejlKVmpQY3JDeTFlZFVZUGFNbHJ1bkNUT01vNlpaOHpteHh5U2RuVHlyMjNxY1hXREVaWm03S2QvVTRkc0pEZjhkVHhha0JDbFUvaFpKQ1U2aGhKNW9JdVI3NGtHakdDdDVyT3duQms4VVhOclVPcFMvZE1kUHlZT05pVkVvSDVEY1VNcXZ5cXp4cHpFSWdjN3JTK0pLSnB4c2RUeEVrNUl6SUh6cVRqSFhpM1FVNk9DTmczblZReDIxaW5NdmlLUVVrZXpjMElqRUhST255cklYZjRzV09UaElub3ZocUNqUnlwZW9NUDZnSkd0OXVjOFFiOHUzQ3E2M3c2WTRPRVdoZjZsWlFtTzM0WExzOXhpL2hvMCs1ZDJyY3hxaWh2V0J5aVREeG0vcmw2azBoTDRBLzRUNFNIbmFRYXVXSkk5Q1k3ZGlsN2ZGNkYiLCJtYWMiOiJkMDg2YjJjOTU3YzNkYjU1ZDZmNGM5NGEyYzgyNzYxMGI5MmYzYjNkNmY4NTRjMDQ0YmRiZDA0OGMwMTA2MGM1IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:54:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik9wNE1ndnB1NldyaStOdkEwTFEveVE9PSIsInZhbHVlIjoiU3lLYVBrclVOaFpaMXNzTENZZllPdkszeTYzdWdLVGJJSTR4ZzRQWXFVckFSK3A5TkxtM1RpbnBmWjBOK1hhU0cwOVR3SW9TVUFTSURDdThmZ3hVR0ppSjdORGFKSlp6bjgxeXFGczZDQWZwUjkwYWZNZHdFT09MbGdZbVBTR2g0MjljOUwzUEIrQmtiN3dXcVpWa1g0U21ZeE03V2ZnN3E3RUhHWklyNy9UY29CTlpyN2dOeWN2bVhOK3NGYVNtTkJCVzdGdERFVVhKdFN0Y0lEOW43ZFdqL2VQbkdsT2hyMzhhbTY0ZTZxZWZad2N3blM0UEFkK3htdDJ2WjJVbTJBcUJUQmZRR3dIZEg5N0tsWER5ZFZoTC9scTVaQjVUN3ZGTU5IMm53OEppZXBTQnN2MXhSUjRzbDhEbEJTVmhwZkRGbzhmOGR1dTFQWk5YRm9pVlRSRG93cHEzRkxQRVFiR2dwTlloR0dyUFJXR0Z4eU40OVR1M2pqdUJDWDA2VFl0THBRSDlRQis3Q29VaS9QK0VTZXZSM3MvZ3gzdXFCM3MyVVZUZ0JqQ29rWmVkYmRwL01JRG5nRFNmL0lrWElVdmFCOXc2M3kyQXRXQ0lzVmlSTHVhcE91Nit0NGNtOVlHblJ0bDhkV04xb2tHYmdCQTNuTVlsb0gvZGN2RjgiLCJtYWMiOiI3OWIwZDk1ZmVhZmUyMThmMWMxYTljZmU1ZmI4ODk3YzU0NDdjYmRhOWE3Mzg2NWUxMTY4ZWRjMTQwM2MwYTdiIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:54:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkNjZEliQjBYQ1dCa2lGUG9uVk1CUHc9PSIsInZhbHVlIjoiOEpxZmdqRHYyQTBQYXBsbmN0Y1lSS0FleFpTNGEwdXQzdmtLVjdnNEp3bUh4aDQyd3U1NE5IcDZJam9VSnBuV2NDRGtRUFpyVmJrQk5udTJzSjJwbU9ETDN3SUw1aHkxOThTSWswMjlVWm1GT1dtVjlESkI1UjJ5eUFnVVFUT0phR2o0TVl2bUpRckNZY0p1ME9OYSsxNmdROWtuU3d0K3VuRUdkMXJwNVQ1TzczTHVLSlZiejlKVmpQY3JDeTFlZFVZUGFNbHJ1bkNUT01vNlpaOHpteHh5U2RuVHlyMjNxY1hXREVaWm03S2QvVTRkc0pEZjhkVHhha0JDbFUvaFpKQ1U2aGhKNW9JdVI3NGtHakdDdDVyT3duQms4VVhOclVPcFMvZE1kUHlZT05pVkVvSDVEY1VNcXZ5cXp4cHpFSWdjN3JTK0pLSnB4c2RUeEVrNUl6SUh6cVRqSFhpM1FVNk9DTmczblZReDIxaW5NdmlLUVVrZXpjMElqRUhST255cklYZjRzV09UaElub3ZocUNqUnlwZW9NUDZnSkd0OXVjOFFiOHUzQ3E2M3c2WTRPRVdoZjZsWlFtTzM0WExzOXhpL2hvMCs1ZDJyY3hxaWh2V0J5aVREeG0vcmw2azBoTDRBLzRUNFNIbmFRYXVXSkk5Q1k3ZGlsN2ZGNkYiLCJtYWMiOiJkMDg2YjJjOTU3YzNkYjU1ZDZmNGM5NGEyYzgyNzYxMGI5MmYzYjNkNmY4NTRjMDQ0YmRiZDA0OGMwMTA2MGM1IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:54:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik9wNE1ndnB1NldyaStOdkEwTFEveVE9PSIsInZhbHVlIjoiU3lLYVBrclVOaFpaMXNzTENZZllPdkszeTYzdWdLVGJJSTR4ZzRQWXFVckFSK3A5TkxtM1RpbnBmWjBOK1hhU0cwOVR3SW9TVUFTSURDdThmZ3hVR0ppSjdORGFKSlp6bjgxeXFGczZDQWZwUjkwYWZNZHdFT09MbGdZbVBTR2g0MjljOUwzUEIrQmtiN3dXcVpWa1g0U21ZeE03V2ZnN3E3RUhHWklyNy9UY29CTlpyN2dOeWN2bVhOK3NGYVNtTkJCVzdGdERFVVhKdFN0Y0lEOW43ZFdqL2VQbkdsT2hyMzhhbTY0ZTZxZWZad2N3blM0UEFkK3htdDJ2WjJVbTJBcUJUQmZRR3dIZEg5N0tsWER5ZFZoTC9scTVaQjVUN3ZGTU5IMm53OEppZXBTQnN2MXhSUjRzbDhEbEJTVmhwZkRGbzhmOGR1dTFQWk5YRm9pVlRSRG93cHEzRkxQRVFiR2dwTlloR0dyUFJXR0Z4eU40OVR1M2pqdUJDWDA2VFl0THBRSDlRQis3Q29VaS9QK0VTZXZSM3MvZ3gzdXFCM3MyVVZUZ0JqQ29rWmVkYmRwL01JRG5nRFNmL0lrWElVdmFCOXc2M3kyQXRXQ0lzVmlSTHVhcE91Nit0NGNtOVlHblJ0bDhkV04xb2tHYmdCQTNuTVlsb0gvZGN2RjgiLCJtYWMiOiI3OWIwZDk1ZmVhZmUyMThmMWMxYTljZmU1ZmI4ODk3YzU0NDdjYmRhOWE3Mzg2NWUxMTY4ZWRjMTQwM2MwYTdiIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:54:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1043163450\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-846658461 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-846658461\", {\"maxDepth\":0})</script>\n"}}