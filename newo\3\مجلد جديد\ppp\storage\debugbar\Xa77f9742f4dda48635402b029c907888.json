{"__meta": {"id": "Xa77f9742f4dda48635402b029c907888", "datetime": "2025-06-17 14:01:00", "utime": **********.416557, "method": "GET", "uri": "/pos-payment-type?vc_name=6&user_id=&warehouse_name=8&quotation_id=0", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750168859.688798, "end": **********.416581, "duration": 0.7277829647064209, "duration_str": "728ms", "measures": [{"label": "Booting", "start": 1750168859.688798, "relative_start": 0, "end": **********.267315, "relative_end": **********.267315, "duration": 0.578516960144043, "duration_str": "579ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.267328, "relative_start": 0.5785300731658936, "end": **********.416583, "relative_end": 2.1457672119140625e-06, "duration": 0.14925503730773926, "duration_str": "149ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49355376, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET pos-payment-type", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@posBillType", "namespace": null, "prefix": "", "where": [], "as": "pos.billtype", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1496\" onclick=\"\">app/Http/Controllers/PosController.php:1496-1604</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.0318, "accumulated_duration_str": "31.8ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3161762, "duration": 0.02785, "duration_str": "27.85ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 87.579}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.3570218, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 87.579, "width_percent": 4.119}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.392417, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 91.698, "width_percent": 3.899}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.397439, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.597, "width_percent": 4.403}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-714949399 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-714949399\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.407012, "xdebug_link": null}]}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/55/thermal/print\"\n]"}, "request": {"path_info": "/pos-payment-type", "status_code": "<pre class=sf-dump id=sf-dump-1602121057 data-indent-pad=\"  \"><span class=sf-dump-num>404</span>\n</pre><script>Sfdump(\"sf-dump-1602121057\", {\"maxDepth\":0})</script>\n", "status_text": "Not Found", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1157827508 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>vc_name</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>warehouse_name</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>quotation_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1157827508\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1220492127 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1220492127\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1956856926 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168503247%7C8%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImhuSHZ5WGpzRXF1UEFoMGVjRjFWSnc9PSIsInZhbHVlIjoiekJXNlUyNUxLUy83MGZXUHoza3p4Q3h0U2xucjFGTUpza2l1VG5NbE1kUWwxWkRmVU4zdWxOQlE2ZWcvVFBEVHl2K3orWE1pdnhFbjNhZ21tY0dwTlJzSEtBM3UyMFVxZjFlV0xuQmtBMFdvYUpBY25ReWo3RExRb25MZHdVdWp5ZERadTFEcU5UdjVVVkhld0NHNHZnazRycGNYaytKeVFqMDk1aXAySWxUT0UvcVI4SjFGN2NoeS9YRmp2Nnp4Kzcwd1hXeHFSWXpkaWxlcjNZQ3RpcWM5eW5BbWNkdExxeUFnMFY4UTA2NnFKeG9ZNGdFdm1lSVUwYjBIYVhjL3hocXY2TTZkR3YvSzM5MUVvNmdZYjBEenFWdTI0dzI5eHpZQ2dYdmNJL3lQWG1MMUFPLzh2UEFZNExNNTcyUUFac0NHOXl0WnBINmpmc29xZkFLbmNGTWhrZXlxemwxazFPK1Y5cjVWOGJ4M1k3WjQyL3VNbXlVeEN5eUZuZVlDZHV0RkkyUmhQTHZvWHE4RG4reW9uVHl4U0ZRd211eUVtdVRmMG9SMXBxdzNXaUxQeG5XU05yeE43TEhFN0xyaUJuU3A2cVV2Zi9EMmo2anN4bFVoL1pJdzZZYm5lemQ4WVJ0Qy9HSi9UTStqVDB2cDJURWU2Zzg2NjNOT0ZJbmYiLCJtYWMiOiIzNWY5YjgyMTI2MzE4YmYxOTI4MmYzNGJkOTQyYWE1MmZiYTlmMzFhMTYyYjk5NDhiNzI1ZDMwY2M2NGJkN2E4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ik1GL2lJeUxMSnhaaHRxdHVKc2k3L1E9PSIsInZhbHVlIjoiMHVjMXlhcUlrZituaGZ0WWlwOFJNRTJpOWhvOUNhZjBaUkNwKzU5VUkyb0kweEpmT0d2S3AvWk1Ccm8vb1JFMjE0UERvS2Q0YXlUTDJaNWpKZ29zRm9IckZwWW9UUkp5blNDdG1WUUlRUnRjcTU1MHpjQlFoQ2h5OCtOMkJ0Q0paYTNIVzN5RThaUzNPcGY4R21qNTJZL1djK3kydGRQY0MrRnBLNVhXV1RGYUFKYVFWV3pxU0k1MFBoTUdNK0VUTnpHSiswUmg1b1hRM1Y2L0k0b1cvakxRYnVEWkZLd2VEakI2azE1TFVIS0V2MVNDT0hGK3hBRTluU1FtYkIyY2xHTjJ4WVZNN0FUczBXanNvc2lqamZyQTVWRkwvNUVRWU9ITlZlejBvb2kwNGlxVTZzUUp2K09TbFZLcGNYaGcyTkk3Z2xzYlMzVGxkdDljc0hSNFhGTHhvN203YzJuY2Faam5LQllkREd3TXM2dDNZbEZNYVk3UVdSM1M4cGlmbk5VYmVDeVFLRGkwZU4wUmQ3blZsbHVjKzNBaXBadDZlL3NFcE9XK3QyZjFzc2FmT09tNWQ5ZHNNQk1vNkF6Unp5aTY2Q0hLTnh6UEM2ZlF6OU02Z3IzanptYitMQ2dKUTJVMW1wZitBeTRyYzVzMmRtMDE1WHpTYk9QWHcrRzYiLCJtYWMiOiJkODVlY2I0NTAyYzM1NmU0NTYzZDYwY2NiOGVlODg3NDAzYWE3OGQxMDc0NzExMWZlMDVmMjgxYTgzYjU2MTc3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1956856926\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2052120264 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2052120264\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-29297998 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:01:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjRTQTdqL3hjOVFkek9OeFp0Y1RiVnc9PSIsInZhbHVlIjoic3Jrb2JYUndNNm95QlplWkxpOEFpNy9FNlVEcHQ0M0UxdlcxWnNZNlE0eHVHOW52YVVpaG56Z016dEp3c1NKMTF5SmtZaStJWXAxWjEvOEJjWG1Zbm04WFhsZE43WS9uSlpjdVdmdGEzUFVUUFc2QW5RT0R4dnU4OVJjTWZ3U1dVM1QzaDl5ZGs0SlZxcjhjS1NPNlNUcmhadFhtQkJjSW1YcU1zaUtMbEc1NG9IK2IwYTBBSTArOFUwMi9hZlREczJ2M2ZyM09UY3R1VzNIV25qOXJVbmtlRm5LdE5aS1pJU3JjckwyaFJsWGQ0VUZyR1hWcVZ0R1ZycHdCaUF4Zml5MVcwaFUyQVJUMlRGZzA0aTRWT0NHZC9pbEpyU2VzV2FxamcvOXRFUHFQellKNTl5TDhmUVdZSTlscjMrNU5yTlJ4ZDZYL2VaTXFpSVluZ3ViV2RsZitwZTduZ29xMmR5UHBaQk54NG5pRitBdkVqK1VoY3N3d29YWmE4MjAydXNONzFjYkJsWm9PVGhSaU9ndEt3cUs5OXQ5Vmx3cHRiYW4rR1FrbWszd2JGckh0UDdEdzZvcXBXL05iZnhyNExSNEdrS0s4dUMyeEtvUCttQUlMLzUvNjgwTkhrNC9tL3pDbmNMY2hkbmlWeVluWlg0ZTU5R2xPUXc4VFVnczEiLCJtYWMiOiI0ZTJlMDBmZjNhYjU0MTU0YjY3MTE2NDIxZDYxNjk4YzUyMzkzYmFhMzllYjE0OGI0YjdiMWM3ZTQ5MGZiZGU4IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:01:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlRlU3Bzc1JoTkpTdXRIcjlXZ3A1MlE9PSIsInZhbHVlIjoiVTh4RDdoWERuaTNyWFVYSzVYU1JKMEI4SHlUUTdPVmp2cGZzbzNSR2J4ZmVnT2Y0Nk1BQU5ES0tJaTU3MDJaa0lndVBHK3F2eHlwY25DeFVZR1YyNjFjSm5vaXpaTndIZjBxUlkzRzY5dS9IckQzVXA5U3NiSm5mRy9rS2hHamE1My9HRWlodkF4V3BRZEwzTDZJdFNyZ1FUTlcxVUNpZ3paVWpVK3FJb3BhUTY2bXRlREdmZVVYRlUzYUwvMWVTWUR2NDY5WjNxMHIzbk14NEdMWlZxUHpDTEZyTzBvRG1iaFZqL0xXYlo2SHFnQXAvSG5LRWt5L21UM0VKaFNVY0o3QnN5eFliVDkzZWt1ZlRoZnRjOXcrVFd0SVVtL0lxamo1T2lObGluY3hHQ1drRXQ3L0JEVG1VcVFhSUJDMjhqYjE5M2xaT2tOK0xZV0gyRFo2S3FlbEVKajBuWmVxNTBIUTFyM09ub0FqN2p3VW1jZjVjWlQ5MkVBcnp2eXdKMFZ4aGo5ZjJrWjBKTG96SGk2S0pYRWVGVGQxQWJJa3JXbGRyY1hBUnpnSVNPNm9FblNoWDEzRHZZSVFVWW9BRk1QZEl5cjk4TjRLTzJjcXV2SzhqUlY2WlJ3REQxd3FpOFVuOVZjUlE1RUkweUVUQmVmTGduUWw0VFdBSnl6RXIiLCJtYWMiOiJkYTliZjgwNWViZTIyZTZjMDcxZmE5OTkyNzY3MjBkMjlmYmQ1YTM2ODg2ZTY2ZDU2MjE1MmVkNTEyMjcyNmYxIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:01:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjRTQTdqL3hjOVFkek9OeFp0Y1RiVnc9PSIsInZhbHVlIjoic3Jrb2JYUndNNm95QlplWkxpOEFpNy9FNlVEcHQ0M0UxdlcxWnNZNlE0eHVHOW52YVVpaG56Z016dEp3c1NKMTF5SmtZaStJWXAxWjEvOEJjWG1Zbm04WFhsZE43WS9uSlpjdVdmdGEzUFVUUFc2QW5RT0R4dnU4OVJjTWZ3U1dVM1QzaDl5ZGs0SlZxcjhjS1NPNlNUcmhadFhtQkJjSW1YcU1zaUtMbEc1NG9IK2IwYTBBSTArOFUwMi9hZlREczJ2M2ZyM09UY3R1VzNIV25qOXJVbmtlRm5LdE5aS1pJU3JjckwyaFJsWGQ0VUZyR1hWcVZ0R1ZycHdCaUF4Zml5MVcwaFUyQVJUMlRGZzA0aTRWT0NHZC9pbEpyU2VzV2FxamcvOXRFUHFQellKNTl5TDhmUVdZSTlscjMrNU5yTlJ4ZDZYL2VaTXFpSVluZ3ViV2RsZitwZTduZ29xMmR5UHBaQk54NG5pRitBdkVqK1VoY3N3d29YWmE4MjAydXNONzFjYkJsWm9PVGhSaU9ndEt3cUs5OXQ5Vmx3cHRiYW4rR1FrbWszd2JGckh0UDdEdzZvcXBXL05iZnhyNExSNEdrS0s4dUMyeEtvUCttQUlMLzUvNjgwTkhrNC9tL3pDbmNMY2hkbmlWeVluWlg0ZTU5R2xPUXc4VFVnczEiLCJtYWMiOiI0ZTJlMDBmZjNhYjU0MTU0YjY3MTE2NDIxZDYxNjk4YzUyMzkzYmFhMzllYjE0OGI0YjdiMWM3ZTQ5MGZiZGU4IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:01:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlRlU3Bzc1JoTkpTdXRIcjlXZ3A1MlE9PSIsInZhbHVlIjoiVTh4RDdoWERuaTNyWFVYSzVYU1JKMEI4SHlUUTdPVmp2cGZzbzNSR2J4ZmVnT2Y0Nk1BQU5ES0tJaTU3MDJaa0lndVBHK3F2eHlwY25DeFVZR1YyNjFjSm5vaXpaTndIZjBxUlkzRzY5dS9IckQzVXA5U3NiSm5mRy9rS2hHamE1My9HRWlodkF4V3BRZEwzTDZJdFNyZ1FUTlcxVUNpZ3paVWpVK3FJb3BhUTY2bXRlREdmZVVYRlUzYUwvMWVTWUR2NDY5WjNxMHIzbk14NEdMWlZxUHpDTEZyTzBvRG1iaFZqL0xXYlo2SHFnQXAvSG5LRWt5L21UM0VKaFNVY0o3QnN5eFliVDkzZWt1ZlRoZnRjOXcrVFd0SVVtL0lxamo1T2lObGluY3hHQ1drRXQ3L0JEVG1VcVFhSUJDMjhqYjE5M2xaT2tOK0xZV0gyRFo2S3FlbEVKajBuWmVxNTBIUTFyM09ub0FqN2p3VW1jZjVjWlQ5MkVBcnp2eXdKMFZ4aGo5ZjJrWjBKTG96SGk2S0pYRWVGVGQxQWJJa3JXbGRyY1hBUnpnSVNPNm9FblNoWDEzRHZZSVFVWW9BRk1QZEl5cjk4TjRLTzJjcXV2SzhqUlY2WlJ3REQxd3FpOFVuOVZjUlE1RUkweUVUQmVmTGduUWw0VFdBSnl6RXIiLCJtYWMiOiJkYTliZjgwNWViZTIyZTZjMDcxZmE5OTkyNzY3MjBkMjlmYmQ1YTM2ODg2ZTY2ZDU2MjE1MmVkNTEyMjcyNmYxIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:01:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-29297998\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2109113700 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos/55/thermal/print</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2109113700\", {\"maxDepth\":0})</script>\n"}}