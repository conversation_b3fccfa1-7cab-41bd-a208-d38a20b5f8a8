{"__meta": {"id": "X8ac22d0a55a5cd137ac686b13b80e4b7", "datetime": "2025-06-17 14:33:55", "utime": **********.900516, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.080717, "end": **********.900539, "duration": 0.819821834564209, "duration_str": "820ms", "measures": [{"label": "Booting", "start": **********.080717, "relative_start": 0, "end": **********.79045, "relative_end": **********.79045, "duration": 0.7097330093383789, "duration_str": "710ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.790475, "relative_start": 0.7097578048706055, "end": **********.900541, "relative_end": 2.1457672119140625e-06, "duration": 0.11006617546081543, "duration_str": "110ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46341240, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1663\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1663-1673</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.006619999999999999, "accumulated_duration_str": "6.62ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.861592, "duration": 0.0052699999999999995, "duration_str": "5.27ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 79.607}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.8831792, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 79.607, "width_percent": 20.393}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1183632056 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1183632056\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-41044866 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-41044866\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1429862592 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1429862592\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InBJa3VKZmMzWVMyTFBKVWVzbjNPdkE9PSIsInZhbHVlIjoiQmNOaExrQ0ErNG0zUmdHbDBpL2tnV1FiYXNNeWVtNXh5VzFNVktoY0RiQll2bmNuOEswQno5WWZPVWhPSmRCTDJTeHpYeUZMWE4xblFaZGNNNFVKWTlZVHJwMk9ZZWFMaDh0QVdtM0Z2a3haR254MnloTE94VVBGZjRBU1YxVmVaZnlXV3RFZ2lKOUxpdXU1dVAwZDhEWUUwYnV2SFRhYzBoUmlPNXpCSlNiaGdwVFVvUWRtYkczYnFPNGxQaGYrOUcxVEdUNCtnTStiVDFXK1NrVmRaQzJpWExjazhDT3FOSmc4dXF2ZkVDSWlpd0FZU2cxRGVhMHRBMVNyMmZudUc0aFdwTDNjMlNQMWlaVWxmTjNwWlJWcFdvMU1MalpPSG1UY3lYTkR0VlZEWlhobDh1QXR6SzQrNzQyb0crZGpJU0Z5M2JuZ3IwVHdiYTlzRm1pSUZGZkhobmhYSGVLWWR6S0RXTkZJZEZmaFNucEpNNHZiTThKcG01WE8wWmtJMFpiOGhpU3NRcGxsU2l2MWd0c0U5Y200UDQrYXcwTHc1eHFlVVVXeVozVnB0MVZocGhrTDJvSU9INVkydU9xUmJMcmV5dzUvZjZ0L0FHUWZXTEpmQmFTRzZPTlhENHFTQnNOZWluS09NSGt2MVV2b3d0Q0hJbzRNMER1Q0JXMlIiLCJtYWMiOiI1NDk4ZmU5NGFmYjFkODk5ZDg2MWI2YzQzNDc2N2YxMGZhM2YwMGU0MDQzMDY0NmVjNzI3YTU4NTYzNzFlNWY4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InBQUVJxdG9JUTQ1eWdZb1l6cDNGblE9PSIsInZhbHVlIjoidWxscTVqbmpibGxRZ0tFVDJrVDhVK1VadktGVTJaM1JCV2FMVzllcC9uTE5XbnZQdExpQW05VW0wWjRYQTR0cXZ1QXBOd2orTmZWRk1GY2t6K1lKYi8vd3c3ZkdZWW9nMHBtUmpiTFY0V0liK2hEcElGV3Rmc2gydDJ4cWQ3OUtPdTg4QTJsNk1KdCsvb2tpb2l0ajNLQUJRNGYwUnhrOWVObnJZYit5THVwWU5UUkx2YndOZWk2R0ZjeWVwbHhXbXlkZ3Z4RzlUL29ENmxWUWh1MldaL1V3Zk81dTI0ckV1OTkwTWZTU0ZwTW81Y3ViUXcrTWJFZDQ2Ty9DbE95aHdzMjhtK1BHMVJ1TWxRRjEzeXM2c3I3N0VnWkVrT0hZVUc3VUMvZEtkdGRESnVZU1MwY3ZTVU5EUjUrRFBtWHNXMVMxK2g2bTErUTdHeVFqQlYvN3dEMEpSOWNhSWJ0RXBiMmtKWFM5YWd2clc4UDlwNk9zRlM4QTkyN25Tc1Jhc3kybmxOSCtDY0dxQkVVVE9ubThuQmNRQURoalZabXpDK0xVN3hnZjF5WVdQWERLZDU0eEZkRUs5cFVKd3M0VVJxSy9NRUdwOHdQcWJ2eGErVlJKWk5tL3pvUFdiU1lkWkhRV3VWaEQyYVBuRFQvV2RxZlRDNG9vZUIrWHNldzQiLCJtYWMiOiJhYWY0MmQwMzZiYWZkODJiMDk2MWVjYzFhNjBhNTZmOTYwNTc2NGRjODRkZjIzOTkzOTNjMTNjYTUxNDg0ZDBkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1842928271 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:33:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkFWVkpNbjFJZk1BN0xESlNoTDJiTWc9PSIsInZhbHVlIjoibjZlV3JDOEZ4LzRnbjJDZy9NUmU4SGQyYzFuRXI1eUd6ZXNQVXJUTjR1UXBiR3lzL2toVXQ0OXlieC9WZEZkYmNwWUY1VWlnWEZBcnR5VHJjVks4NERJczRtampKRHdnbmhxWW9TUUg0UzZ0TU9TMVpSVE80UnFZeVNWMEJkM0VGbFByTE5SYUxFaDhZbXZ1aHVlNGVESU1oZWhMZGpnenVmUStzZGVWU00wUTBDdWVTRFpLbGxSQi9Ba1pZenpieXF0NTUyak9kdUg2YmY5QXIzdlJ1aXIyZU8wNXRUbk9obVFlTmpCOHR2SzhPQ2FVWlBodzNyZkg5YTN0TE9vU3o0TXpjSEhDQ1lBR01sZVZHN0hncXFmcGxVVzY0eUVXWXRnNnZleFFzQng0RmdSMFVUaERYVDJQRWZLM211UktLL25pckRld1o5dGRhRlh3R1pHdnNad0JjVW1lOVcrTFYxU3FYQ3hqdWtvbGd2K2c4aE5FZ0xyalZEd0RpdG1BR0hBc244dCs2L1ZtaGpnay8rcGRDL3Z1Q1o2RjlGUE91R1RWVVF0Vkg0Z0xzVmJiTnB1bU1Kbnp3NllrZ3FrNm8wcDJWTzJyODhuYlBXUXovRGtBZHJSOFRxaHQ3RGVaT1IyYVJNK1l4RCs3SmdTSGVlKzRZb2hyeWpMSyt4UzMiLCJtYWMiOiJmNDZhOTJhZjEyMjZjMDgxZTAzZTFkMDAyZTA3ZjlkMGUzMzgxYjU5ODcxYmE5ZDc4NThiNTE4ZWI1OGE2ZTA0IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:33:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InU2ZXJVcW1UeWJvMEtKSE01VE1jT1E9PSIsInZhbHVlIjoiK0pCaG1yOUs3dmNuYVBjWTZaTDRuTnhPK05TOWlwd2JBLzJ0bmNERzRBK1VnWkdtNGsxYUR1NmhNODdEVmM0azdtRkpJMlNucm9xc1ZCRHYxby82dW1DQlcvN2R6RnVXN0tick91S3pTNzNqYXEwQytGd09CK1QwN1FGZ0lVMXlxSFdpNUFKOEVZMkEwUFB3WlJHajFiTm9zaVVvYzJZWDVIOWkzeUsxWDBwNnpySENZNGdXYWk2OGZwU1plSTNYSEpLRTBwNDNpcDZCNjY4ZmVNYlJiczhwdVd1VHVRLzlJWksybW5GUHQ4L3VybWcvSUp4SlMzaURjWnZRUS9tajVQYU5CcklwSXB6YkFqSCtDQ3Z3UTZTMDZ6ckdIcEVYdnR6akFma1dmYUluckx0Uzg4WTE5emRQMGJzeGszVDZCQ0Fra3JDMzZmZC9GdWRPdW5WSWsrdURMbnlHbFFCQ2g4bXl6YzhpZDRDMHRhSndjY1NXN1R5aklwSzVnSWdxK0NuUU14ZUMvMmdjSHhYSFR2L2VGd28zYWdUL3VFa1gzV1M5WjdISmttbFRFdHJveUIxL3VPelhjc2xqOG9LTWdXUXM3bFZDTzFZTHhDTXE1MFB2OFpqNnd3Q2VWMWphUE5GK05nSldKcmJ5RVM0L3kzcGQvVDJQZFVTelRTOGUiLCJtYWMiOiIzZmQ2ZGIzMDM3OGVmNTcyMDYzOTY2YjkyYzQyYzU0NjRjY2M0NDk3MzI4YTI5MTAyMzQ2MDEyZGUwMzU3NTU0IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:33:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkFWVkpNbjFJZk1BN0xESlNoTDJiTWc9PSIsInZhbHVlIjoibjZlV3JDOEZ4LzRnbjJDZy9NUmU4SGQyYzFuRXI1eUd6ZXNQVXJUTjR1UXBiR3lzL2toVXQ0OXlieC9WZEZkYmNwWUY1VWlnWEZBcnR5VHJjVks4NERJczRtampKRHdnbmhxWW9TUUg0UzZ0TU9TMVpSVE80UnFZeVNWMEJkM0VGbFByTE5SYUxFaDhZbXZ1aHVlNGVESU1oZWhMZGpnenVmUStzZGVWU00wUTBDdWVTRFpLbGxSQi9Ba1pZenpieXF0NTUyak9kdUg2YmY5QXIzdlJ1aXIyZU8wNXRUbk9obVFlTmpCOHR2SzhPQ2FVWlBodzNyZkg5YTN0TE9vU3o0TXpjSEhDQ1lBR01sZVZHN0hncXFmcGxVVzY0eUVXWXRnNnZleFFzQng0RmdSMFVUaERYVDJQRWZLM211UktLL25pckRld1o5dGRhRlh3R1pHdnNad0JjVW1lOVcrTFYxU3FYQ3hqdWtvbGd2K2c4aE5FZ0xyalZEd0RpdG1BR0hBc244dCs2L1ZtaGpnay8rcGRDL3Z1Q1o2RjlGUE91R1RWVVF0Vkg0Z0xzVmJiTnB1bU1Kbnp3NllrZ3FrNm8wcDJWTzJyODhuYlBXUXovRGtBZHJSOFRxaHQ3RGVaT1IyYVJNK1l4RCs3SmdTSGVlKzRZb2hyeWpMSyt4UzMiLCJtYWMiOiJmNDZhOTJhZjEyMjZjMDgxZTAzZTFkMDAyZTA3ZjlkMGUzMzgxYjU5ODcxYmE5ZDc4NThiNTE4ZWI1OGE2ZTA0IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:33:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InU2ZXJVcW1UeWJvMEtKSE01VE1jT1E9PSIsInZhbHVlIjoiK0pCaG1yOUs3dmNuYVBjWTZaTDRuTnhPK05TOWlwd2JBLzJ0bmNERzRBK1VnWkdtNGsxYUR1NmhNODdEVmM0azdtRkpJMlNucm9xc1ZCRHYxby82dW1DQlcvN2R6RnVXN0tick91S3pTNzNqYXEwQytGd09CK1QwN1FGZ0lVMXlxSFdpNUFKOEVZMkEwUFB3WlJHajFiTm9zaVVvYzJZWDVIOWkzeUsxWDBwNnpySENZNGdXYWk2OGZwU1plSTNYSEpLRTBwNDNpcDZCNjY4ZmVNYlJiczhwdVd1VHVRLzlJWksybW5GUHQ4L3VybWcvSUp4SlMzaURjWnZRUS9tajVQYU5CcklwSXB6YkFqSCtDQ3Z3UTZTMDZ6ckdIcEVYdnR6akFma1dmYUluckx0Uzg4WTE5emRQMGJzeGszVDZCQ0Fra3JDMzZmZC9GdWRPdW5WSWsrdURMbnlHbFFCQ2g4bXl6YzhpZDRDMHRhSndjY1NXN1R5aklwSzVnSWdxK0NuUU14ZUMvMmdjSHhYSFR2L2VGd28zYWdUL3VFa1gzV1M5WjdISmttbFRFdHJveUIxL3VPelhjc2xqOG9LTWdXUXM3bFZDTzFZTHhDTXE1MFB2OFpqNnd3Q2VWMWphUE5GK05nSldKcmJ5RVM0L3kzcGQvVDJQZFVTelRTOGUiLCJtYWMiOiIzZmQ2ZGIzMDM3OGVmNTcyMDYzOTY2YjkyYzQyYzU0NjRjY2M0NDk3MzI4YTI5MTAyMzQ2MDEyZGUwMzU3NTU0IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:33:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1842928271\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-656341258 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-656341258\", {\"maxDepth\":0})</script>\n"}}