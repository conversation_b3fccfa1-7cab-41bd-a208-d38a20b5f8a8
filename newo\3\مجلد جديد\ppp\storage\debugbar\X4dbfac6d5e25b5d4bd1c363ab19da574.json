{"__meta": {"id": "X4dbfac6d5e25b5d4bd1c363ab19da574", "datetime": "2025-06-17 14:56:38", "utime": **********.550294, "method": "GET", "uri": "/customer/check/delivery?customer_id=6", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750172197.980213, "end": **********.550319, "duration": 0.570106029510498, "duration_str": "570ms", "measures": [{"label": "Booting", "start": 1750172197.980213, "relative_start": 0, "end": **********.474604, "relative_end": **********.474604, "duration": 0.49439096450805664, "duration_str": "494ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.474619, "relative_start": 0.49440598487854004, "end": **********.550322, "relative_end": 3.0994415283203125e-06, "duration": 0.07570314407348633, "duration_str": "75.7ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44962072, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.019889999999999998, "accumulated_duration_str": "19.89ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.515211, "duration": 0.01874, "duration_str": "18.74ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 94.218}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5391471, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 94.218, "width_percent": 5.782}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1164601116 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1164601116\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-573881590 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-573881590\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-68642585 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-68642585\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-371647832 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InFzRmlVNTBWOGZ4NEQxdCtLK2FOMEE9PSIsInZhbHVlIjoiOGd6bGxyMGhtU1d1eWs0dktUZEEyREhMR1NiV1ZVUWJFMXBZemNsazJJb25SZnMwcUNnZ2p2SlN0ZThnTVd3SVd0RWxMekNPb2Y0TWJFWlRXdG5OVUJrbkJxQTdOTGxza01zaHdBN2JVajB5Vmg3UHJUMXViSDVWdkdhREdRcXlKVnZ2MkQrWG5ya1JLZWtlcVRXbzlhSXVzeEJGUDhqTnhNcER3WFZ4R0l4NjVZKy9PMkhqRjMvN3ovVlVxWm9LM3VwSmdkUzB5b0hQR0FkTS84MlRJK1N4OEpwMG1RNmJNSzZ0SmpoU01EYjNRWGhGS2U2dGpBYWVYbXJBUC84dGk1ZWpFc0YvcTQ3R1VGcXM5am1TQ3JwN3UzZEFpdWlzdVFrUWpnVFAyTzhrTjVCUkk3V016L2dLcFNadDM2VkphcGJrclpjRnVTUGtaajJLTXlCdWhMblVQaER6WURDb09kRnZMZW9tMlZlcW52U1RuM1VMVU9PM2pkVXl2S08wSlBTTm9qOEFYZ0hTN2lrSHFjb1dHa0ZiWHU0Q1hKR1RXRWJBMmRTakhWeHpxd1lqZjZCQ1BmVjVYZ2hIOTA0dnRvd2hZTy9oM2p6a0NJZytNbDE4NkxLMlJzU1d3RnhYdWFuTFFzVjBLNU9rVHdBWVI3L09LbUFzV3Q4MW1EOW0iLCJtYWMiOiIwZTZjY2Y4OWYzOTNhNDJiYzIwMDRmYmY0NTNiNWMyODhjZDUxZmQ0ZDhmYTNiNjc5NjQ4YmJiZDEwOGYyOGJiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImhrUGxKMGJwVTF2RW1TcnRiSUpPR2c9PSIsInZhbHVlIjoieUgvQndTMklRRjhMRU1EQm45cDV4N1VmRTFLclI5R3M1d2VsRXNFa3J6aW10RG9jS0lDZnJBMCttVzFteUFKS3IwU2FmTy9tN2xtT280SEVxTUFZbmpOTTZvMldKMUIvbER3MnhSVWN0ZUFla1FUQzN0RmQvUTkrUDdESU1icVFRT2l5Sk5rQ1VTTEVJWi9HdWhBd1pKMTVVdFczQWNLYkI1cDVWK241RXYwcDVEbEFuZ3BYTm1INHdCN1RTRWgzbCs0MEZuR1hsUm9nZUlHV2JlYkwxZU4vUHRmdmxNWUxFYUw5YW80cFZwZnlFenQ3dExNL1pFd3dPNlJ6c0trSy9EWG00N0V1UFJEaHVaNm9mOUY4Y0VXUXY4SGM5VE4vdndzelRsZ1NWL0I0SkU5dEJJa1Z5VUVGOTdqWlNRTFJzRFhMLzl3N0FIRjF0TFoxZC9MZ0l5a0lDTFV4bzRUVFN5RjhtL1MyME9YOHVNcVE5dGZ3UnlubldENHVPL21YVkZJK3hxVTNFUWE0WHlLRk1EQnNwZ1F4VW9wcU1tc2E4R29mY0s3K3hZc3ZkcUw3N0hTYnJYNGdud0ZrN3ZvSkIzb3pMaXJHL2Q1YW8wb3lSMC8rVE9VeW9nNVBjV2xVOVRYQ3p0VkxFSXdUcjdxY1F4YnYrTFdlYSs2aDdMMVkiLCJtYWMiOiJkNjkzNjE4ODc4Nzk5ZjVkMDUxMWZkMGFjZDQ2YjVjMTIzNzk0ZDcxN2FmMWU5YmNhYWI4ZWJmNzY3YjU4NTYyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-371647832\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-637506490 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:56:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkVTSjJNajU3QzRjK1dMRjlhWmZpcnc9PSIsInZhbHVlIjoiWGkyNktycXhxS3lrNHJsbHFwc3pqTno4QzNZZmNIRlZ0aWY5RTlLM0cvVjVLQWRMbkRoYk5VaG5yaE1iRUxBaXpWUGRYazA0d1Y3SjhFYW9ZTlZzZzR5aGVNWlVFSnk5eDM0dTlLRFdWdHlXb3l1UzIrL0ZCZHVSRXlnRXZaZ2FRdmhCOXJjTnFEUEZaTmtBT05sL1JLSjJIVFY2WDh3OVpDVlExak1aN2lwTkNHd2NBVVR6bm9hdFFPWHgxekp3UHZXTmNtVzlFMHBhWlhENnFPM2pYREtRM21Jemd4dDdKempNclhFajhNU3lRcHl0L1R0dU80dWRNRmk3K3pOV0hjMHB2akdXb2tQQXN6czBrVjg5ekVQeDNITHN6N0dMUEhKZmsweFV1M2RxeW04alhZajFQeDhMS0IyV3o5eVdFVmhNYVpSbDhKVTE4cExWQkY3cWhnVGJPajl6MTVZT1BOdlJPNmlEZTJWSEl6TDEvRWE5RnB3K041R1JHNFVaRGJBOXJhREFsMHg4Q0JqbEFVZXZoN3cwcURreG9JYUc5NGNjM1hzeWp3L3VmamtEaU5WRkNhclo4dmJ2cU9BYWxWK3liQ0JJZGZYT3BQempieHRiVTdaZ0tSak13eXBwOTVVZ0pqSzl6UmtRMWRaeEJEalgycnNsaFd3SUhVWFAiLCJtYWMiOiIwMjlhMjg4MDNkN2Q1ZWYxZjYxM2JmNjE5NzcwZDM3NzlmMDcwMGQ3ZWUwZGE5MGZjNWY1NzVlNzE2ZTFhOGExIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:56:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImZManpDOXZOa0FxQUhqUWlkdG5ZMGc9PSIsInZhbHVlIjoiOElGcDZtWDNGdURsSUhKU0g2bHYxM2h5alhaaTZVajUzdlc3S0g2QkFmZ1MzTnlNU3p1U0ovSERKOFBnYUFzL0tYYWMvYjBZS1lpT0hwZVU3Z3ZTNlNWalNZeHRpQUxFVXl2eHBIRU1BQmR3TDYxVXluRXRVSzk3bVkyMkhuUXJuYVhwNzFyamhYbUwvRnkrWitEQWMvVDBnT0Fxa2dlazhvdGR6SmJWc2dZRS84dVVTalduWE9HSnhuaEhBSHB4SWhOYmVzRWozTWhKVXhrWjJObllpZWpXMm81Z2YwdWxyK2UxSUp4SWVSbEorZTE1YUp0dFp0R0QzUDZFU1NHLzhtTlBGblI4TnR0dW9QSFVMTC9UQS9ZNkp0ZHFRNEJDVVoxWkN2ZE8vNVZmUnV1Y3QwTUR0TWlsUmFFaUUrZzNLQ0VsNDZ4NVpIWXIvcDNoTW4rZWk4aGQxZDdpOGRYWHRydEFBVy8yWjY0b29GWkNuQzZ3R1dEUnkvYWUyK21MZkVyaUhZUGNOc0F6THFBMUV6ZnZ1QVBLWEJKQzhVYm1oUk5GZmREQkxxbTAvYjlybE5DSXZJdFREV3A3N281SlhqNVBaUTRJdTZrOXF0N1pkeEY0UDBqelJpOUFaYjlYRzNvT3JmNFZtWHdHVDkramFGVkt1MXRTdWZkTkVXTEIiLCJtYWMiOiJmMjVjZjViNDgzMGNiYzhhOGFmZTAwNDM2ZWJmODFmZGExYzEzYWM5MTNhOWU4NjU4YzE5NmQ3ZTA3NWM0Y2M1IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:56:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkVTSjJNajU3QzRjK1dMRjlhWmZpcnc9PSIsInZhbHVlIjoiWGkyNktycXhxS3lrNHJsbHFwc3pqTno4QzNZZmNIRlZ0aWY5RTlLM0cvVjVLQWRMbkRoYk5VaG5yaE1iRUxBaXpWUGRYazA0d1Y3SjhFYW9ZTlZzZzR5aGVNWlVFSnk5eDM0dTlLRFdWdHlXb3l1UzIrL0ZCZHVSRXlnRXZaZ2FRdmhCOXJjTnFEUEZaTmtBT05sL1JLSjJIVFY2WDh3OVpDVlExak1aN2lwTkNHd2NBVVR6bm9hdFFPWHgxekp3UHZXTmNtVzlFMHBhWlhENnFPM2pYREtRM21Jemd4dDdKempNclhFajhNU3lRcHl0L1R0dU80dWRNRmk3K3pOV0hjMHB2akdXb2tQQXN6czBrVjg5ekVQeDNITHN6N0dMUEhKZmsweFV1M2RxeW04alhZajFQeDhMS0IyV3o5eVdFVmhNYVpSbDhKVTE4cExWQkY3cWhnVGJPajl6MTVZT1BOdlJPNmlEZTJWSEl6TDEvRWE5RnB3K041R1JHNFVaRGJBOXJhREFsMHg4Q0JqbEFVZXZoN3cwcURreG9JYUc5NGNjM1hzeWp3L3VmamtEaU5WRkNhclo4dmJ2cU9BYWxWK3liQ0JJZGZYT3BQempieHRiVTdaZ0tSak13eXBwOTVVZ0pqSzl6UmtRMWRaeEJEalgycnNsaFd3SUhVWFAiLCJtYWMiOiIwMjlhMjg4MDNkN2Q1ZWYxZjYxM2JmNjE5NzcwZDM3NzlmMDcwMGQ3ZWUwZGE5MGZjNWY1NzVlNzE2ZTFhOGExIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:56:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImZManpDOXZOa0FxQUhqUWlkdG5ZMGc9PSIsInZhbHVlIjoiOElGcDZtWDNGdURsSUhKU0g2bHYxM2h5alhaaTZVajUzdlc3S0g2QkFmZ1MzTnlNU3p1U0ovSERKOFBnYUFzL0tYYWMvYjBZS1lpT0hwZVU3Z3ZTNlNWalNZeHRpQUxFVXl2eHBIRU1BQmR3TDYxVXluRXRVSzk3bVkyMkhuUXJuYVhwNzFyamhYbUwvRnkrWitEQWMvVDBnT0Fxa2dlazhvdGR6SmJWc2dZRS84dVVTalduWE9HSnhuaEhBSHB4SWhOYmVzRWozTWhKVXhrWjJObllpZWpXMm81Z2YwdWxyK2UxSUp4SWVSbEorZTE1YUp0dFp0R0QzUDZFU1NHLzhtTlBGblI4TnR0dW9QSFVMTC9UQS9ZNkp0ZHFRNEJDVVoxWkN2ZE8vNVZmUnV1Y3QwTUR0TWlsUmFFaUUrZzNLQ0VsNDZ4NVpIWXIvcDNoTW4rZWk4aGQxZDdpOGRYWHRydEFBVy8yWjY0b29GWkNuQzZ3R1dEUnkvYWUyK21MZkVyaUhZUGNOc0F6THFBMUV6ZnZ1QVBLWEJKQzhVYm1oUk5GZmREQkxxbTAvYjlybE5DSXZJdFREV3A3N281SlhqNVBaUTRJdTZrOXF0N1pkeEY0UDBqelJpOUFaYjlYRzNvT3JmNFZtWHdHVDkramFGVkt1MXRTdWZkTkVXTEIiLCJtYWMiOiJmMjVjZjViNDgzMGNiYzhhOGFmZTAwNDM2ZWJmODFmZGExYzEzYWM5MTNhOWU4NjU4YzE5NmQ3ZTA3NWM0Y2M1IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:56:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-637506490\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-231044007 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-231044007\", {\"maxDepth\":0})</script>\n"}}