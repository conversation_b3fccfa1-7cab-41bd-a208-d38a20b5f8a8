{"__meta": {"id": "X48d21b664a34b57d0b597f59a7a9e7de", "datetime": "2025-06-17 14:57:09", "utime": **********.566701, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750172228.965442, "end": **********.566737, "duration": 0.601294994354248, "duration_str": "601ms", "measures": [{"label": "Booting", "start": 1750172228.965442, "relative_start": 0, "end": **********.484954, "relative_end": **********.484954, "duration": 0.5195121765136719, "duration_str": "520ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.48497, "relative_start": 0.5195281505584717, "end": **********.56674, "relative_end": 3.0994415283203125e-06, "duration": 0.08176994323730469, "duration_str": "81.77ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46326352, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1663\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1663-1673</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01086, "accumulated_duration_str": "10.86ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5309942, "duration": 0.01028, "duration_str": "10.28ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 94.659}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.5534759, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 94.659, "width_percent": 5.341}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-606584908 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-606584908\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1058476795 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1058476795\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1174111287 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1174111287\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1000862823 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjlETklGSE1OazRuVkhMbUFuSXFzQlE9PSIsInZhbHVlIjoicGZqR1VSaEVUUEdZNElqOTh3UGlHdlhTZ01Wb0NUazVxVFgwZ3VDSDdFc0txeFZ4c0N1dFRnOUJzSFpSLzEraUZwNDFQcDVhcFBjWVpkajFoQTFINDh6RERnZ3M3S2EzaVUvYjFrQ01xWFBENVZHMFh1NHdxYTdzWHVJUFUvNW5zT25UTFpwLzlESTVwRWZIdEd2ZStyZkhQOTRHNkYrN3FuUVNwSE9PUDZCTURNY1JZSzdxZGNrb3lJU1JNWGlRQ1JKa21jdW1jNHVEVk02Q2VCeS90SFI5NnEzaHJxTTdZdDBEenNHcXlwYkNsQXc1MDFPMVZ3djJTbDRveTllaVJHUkRVM2xJUGQ2SkxuV1oxZFU2MGpGNlg5N0g2aGxja2pZM1J3bTlGblppV0hUbTR5NHlYWkF5M2Z5ZWVBemkrN2FYMHR5OFZWZldsMWRJSzdLRUdZU3k0ZDczMzlUQkNvZXZvVVNBdjllNFk5NXVXNWVmam5iN0ZuU1Z1NTE3ZU1FQWZvd0l2V0FoLzNkeFZnSXRlUHRVeVZ6TWZaRTh3dkdPZ0RlZzFlSUI0YUpFK3dkZ010aysyUHdRMGZ3OFY0MEdFSGVXUlBmMkxSVXVMbSs5K3dCZGtLNTJ6VnRSRS9SV3hoU1NnWHBsYTFDTkx2L0thY1dIQ2htWjVZbWgiLCJtYWMiOiJlN2VhMGQ5ZTg4YTcxNDNhYjZlYWQwMTEwNTY3OGExNzBmYTVlYmEzOTIwYzQzYzk0YWIxNzNjOTU3YTAyZWZjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ijg5UW10RTUwdUM5M1dOamYxWUV6TlE9PSIsInZhbHVlIjoiaTY2K0YzM0RIR3BWeFRVVWdpUmY3VGJSaVdUY054eW1MajR0WUJsQkUybldPQThtNVowVVAyRjE1RzUzZ3EvYUNvVXo0YmRCSGtIc0FtQTJka1llS3dZL2RicFEyQ0ttbXJ4RzJPaytkVTl0MWw2VFh5RlhBc1NBT2c0VEtMTUFyQTRWRDk5Qk1oV3FEbUhhaDVCOXVZYTdGNll0NDVBMGtMdzR6RFFmRTJjVGx2ZFBaN1ZzRmhrQjIwSHhIV1h4LzA0T0I5UjdVMUhRUWVtWVdnK0dLUWZsRVhhalJOaE84ZlNMUytFRFJ6MVVRK2JIVzZUNFBFeWIrbU15Z2g0OWI4Q0hxNGFkZHo1QUxSVm1mcnM1ODJMeEs4Q0NBOEQyMTlWbjNwTXArcDI3elNwQUFDczI4L3lTdUZhRy9XckVsSHVZdUdyNWM4ZWF6dmo0VHBvU1BBd1l0aUp5Sk5hZ3JIa0YxcDVEb28yVWZzeHhwaHF2WXJ1dnlyZzh2eFEzN3VjTmNSRFI0MFpjbnVCczB6dXREMjhqMkMreHN1QXZaeGJEOHM1c2xuY2tHRnBVWUxuNmlNV09ncngyNVRYSUprOFRmSjRydy92ZTZIQ3pLNDhPVElZZXZ6dTlzN2VNVnEyNDQ0ZWRtQUUwdlJBSFkrdGhmMWE4VXR4eXNtdVUiLCJtYWMiOiJmZGIxNGMzMzA3NDc4NzNiZWIxMTllNjBjY2Q1YWVjYTY3ZDUxZTNhZWM1ODUxNmM3ZTIxNmNkMWEyZDdmMjc4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1000862823\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1833125621 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1833125621\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:57:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjFpNE5UenBTdEdjc1ZWZ1JoSUlWanc9PSIsInZhbHVlIjoiZnpiTHJybWd3SzdodGlXNFVTTm1WUUtqdjFwMEY3VHpQMmpwcGs2eHhCZjJSdDNWdDdFTUdhbEc2SFpuZ0JSQm5mcm5MMTFCdnVxSDBrZ2dQb0xOSkR0a05TaG9seUw5SGJhNU9ER3hlcmE4Z1FiTnNuT3h2cXRGTFRjb0hiMVRyWis1MDRYaEtoVW5SMUNLTGZLelNFODR2Wmhhanh2RjQ4NENnYUw4ejRjZVArMTQxL3FzR0J1QVE2Ky9lUjdkdExNMHlQcTJSdGhUNSswR1k1OWcwV0htMTdqWkgwdFA1RStOYUtXa0VieTRqbDJZblpWUFV4RVJlTXN2TDBxWWNER3JQekFBbEJwSFVlM3lDY0g2RTNyb1RyMGF5RE52Z2w2OTZLMlIxV0V6SVpGd0xoMDRnblMwdmplbjhZZUdKOGRyaVdFRitEODRCbXl1N1ljeU9OcTViUmdUQm43SXpZejZyTHhNWWhENWRhUGtudWZ6MzVTQTVIUlg5UnhpeHJ6dFMzcDhNeWFiM1VRVzNDazQ3WHZyQndieUxHNDhneHFFQ2s3cW9vR2FJY2puUkZGekN6Tng2ZHZqWS8zK21kS3hhRDNuZXppWGxuNkVTSnpyRERDdHVxanBqbnNrZ0haWDlIK1FNSEhuSjJFSkZyMHdWRENENHpuWlc0S2kiLCJtYWMiOiJkYjUwNzE5ZTdmYWIzYjc3ZTIwMGY1MGNhOTEwMmNlODhkZmU0MTJiM2Y0NzU3NzU0Y2M2Y2ZhZjM2M2ZmZDY5IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:57:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImJHcTRuWUEraWd0Qmh2dXlMV1VNOWc9PSIsInZhbHVlIjoiOU9MbnJ5MkRNdmlPTWYzbkFNMjVBd2VGVjhEc2Q5UWRZc2MyazdFN0Q0dmt4YUpHV0ZsRHhpZERzS2d4cGZWVmh2MHlwVDE4UTg1RzIxMXJHK0R3K0VYcEc2QVJaeUZ4dmFlL3pKQXphb2h3OTRTdkd4eXZGZVpVZHpBNVQ1bkRQdHNLRTk0VWF6b1d1bXF0OWh3ek56UmNzWjZId1JMNzhEUXVzZU1ESEE3eWJUV1hwT2pXdXV6ZmVTZFpKYTJVK000dkx6MzRvRlNXN2wvOWFGK3ZEMXVMeWRYa0QzNzczdzRhTldoL2RIcldUOWN0ZnVTTEI1eFh3QkJYNkRCVllOMlpaajAxZGU0ZlZGdVk3MTNWS1FuSFphSVRrQ0dMbi8wTi9hT1ZOVUsxMHpGWXdtV0psUnBTQmpRYk5VN3VRUERUbWdzR1g0MjBjN0N1bEVPSk44TEtxUUhGdjhwS1U2dlNmeEI4Q3FiQktnRi9wNHZzbW9DbVhzaHFkN1BpZVBDZ3lwVnEvRkNTV1NKVHNJVlZFTkQ3aTU4cWlrdk5DdSt6U0lGTnFJYVd4cjVCd0xOYjV6bFpBOFd0ZW90Rk42UlpKTlNtUTV3RlFxNmgrZ1ZpSUJUYzZETXZQOVRtbkYxMXl6NFRmNFNRUzBhNWR1OEU5NTdYWmJWTGVLaksiLCJtYWMiOiI4ZGViNDQ3YjkwN2VkOTcwZTljZDNhYWI5MmJlYjVhNDE1MGQ2MmE5NDYwNTMzMjBkMjM1NTJmNGI0NjJjNDcwIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:57:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjFpNE5UenBTdEdjc1ZWZ1JoSUlWanc9PSIsInZhbHVlIjoiZnpiTHJybWd3SzdodGlXNFVTTm1WUUtqdjFwMEY3VHpQMmpwcGs2eHhCZjJSdDNWdDdFTUdhbEc2SFpuZ0JSQm5mcm5MMTFCdnVxSDBrZ2dQb0xOSkR0a05TaG9seUw5SGJhNU9ER3hlcmE4Z1FiTnNuT3h2cXRGTFRjb0hiMVRyWis1MDRYaEtoVW5SMUNLTGZLelNFODR2Wmhhanh2RjQ4NENnYUw4ejRjZVArMTQxL3FzR0J1QVE2Ky9lUjdkdExNMHlQcTJSdGhUNSswR1k1OWcwV0htMTdqWkgwdFA1RStOYUtXa0VieTRqbDJZblpWUFV4RVJlTXN2TDBxWWNER3JQekFBbEJwSFVlM3lDY0g2RTNyb1RyMGF5RE52Z2w2OTZLMlIxV0V6SVpGd0xoMDRnblMwdmplbjhZZUdKOGRyaVdFRitEODRCbXl1N1ljeU9OcTViUmdUQm43SXpZejZyTHhNWWhENWRhUGtudWZ6MzVTQTVIUlg5UnhpeHJ6dFMzcDhNeWFiM1VRVzNDazQ3WHZyQndieUxHNDhneHFFQ2s3cW9vR2FJY2puUkZGekN6Tng2ZHZqWS8zK21kS3hhRDNuZXppWGxuNkVTSnpyRERDdHVxanBqbnNrZ0haWDlIK1FNSEhuSjJFSkZyMHdWRENENHpuWlc0S2kiLCJtYWMiOiJkYjUwNzE5ZTdmYWIzYjc3ZTIwMGY1MGNhOTEwMmNlODhkZmU0MTJiM2Y0NzU3NzU0Y2M2Y2ZhZjM2M2ZmZDY5IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:57:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImJHcTRuWUEraWd0Qmh2dXlMV1VNOWc9PSIsInZhbHVlIjoiOU9MbnJ5MkRNdmlPTWYzbkFNMjVBd2VGVjhEc2Q5UWRZc2MyazdFN0Q0dmt4YUpHV0ZsRHhpZERzS2d4cGZWVmh2MHlwVDE4UTg1RzIxMXJHK0R3K0VYcEc2QVJaeUZ4dmFlL3pKQXphb2h3OTRTdkd4eXZGZVpVZHpBNVQ1bkRQdHNLRTk0VWF6b1d1bXF0OWh3ek56UmNzWjZId1JMNzhEUXVzZU1ESEE3eWJUV1hwT2pXdXV6ZmVTZFpKYTJVK000dkx6MzRvRlNXN2wvOWFGK3ZEMXVMeWRYa0QzNzczdzRhTldoL2RIcldUOWN0ZnVTTEI1eFh3QkJYNkRCVllOMlpaajAxZGU0ZlZGdVk3MTNWS1FuSFphSVRrQ0dMbi8wTi9hT1ZOVUsxMHpGWXdtV0psUnBTQmpRYk5VN3VRUERUbWdzR1g0MjBjN0N1bEVPSk44TEtxUUhGdjhwS1U2dlNmeEI4Q3FiQktnRi9wNHZzbW9DbVhzaHFkN1BpZVBDZ3lwVnEvRkNTV1NKVHNJVlZFTkQ3aTU4cWlrdk5DdSt6U0lGTnFJYVd4cjVCd0xOYjV6bFpBOFd0ZW90Rk42UlpKTlNtUTV3RlFxNmgrZ1ZpSUJUYzZETXZQOVRtbkYxMXl6NFRmNFNRUzBhNWR1OEU5NTdYWmJWTGVLaksiLCJtYWMiOiI4ZGViNDQ3YjkwN2VkOTcwZTljZDNhYWI5MmJlYjVhNDE1MGQ2MmE5NDYwNTMzMjBkMjM1NTJmNGI0NjJjNDcwIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:57:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1198231378 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1198231378\", {\"maxDepth\":0})</script>\n"}}