{"__meta": {"id": "Xeb89b8e36a8b26cfdc55fcb9805a5447", "datetime": "2025-06-17 14:50:30", "utime": **********.359117, "method": "GET", "uri": "/customer/check/delivery?customer_id=6", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750171829.797841, "end": **********.359142, "duration": 0.5613009929656982, "duration_str": "561ms", "measures": [{"label": "Booting", "start": 1750171829.797841, "relative_start": 0, "end": **********.285212, "relative_end": **********.285212, "duration": 0.48737096786499023, "duration_str": "487ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.285225, "relative_start": 0.4873838424682617, "end": **********.359145, "relative_end": 2.86102294921875e-06, "duration": 0.07392001152038574, "duration_str": "73.92ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44976936, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01984, "accumulated_duration_str": "19.84ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.324999, "duration": 0.01889, "duration_str": "18.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 95.212}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.348863, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 95.212, "width_percent": 4.788}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-389207478 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-389207478\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-327446916 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-327446916\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-410682012 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-410682012\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1860453057 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InR2VCtGczU4NlY0RThCT0RGMzBlNlE9PSIsInZhbHVlIjoiR2NFaE8vNmdUTGFhd3V2T1dTZUlxb2h2MWl6K0EvQnBlM1BZZnZDVHhOYzVaM3Y3UDM2MmQ0L3R3Smw3Q29NUnl1clZlenVrK0tlUWZpMyswY1orTnB5bEI4aG9TZnRmSHRsS1V5NlN5MG9CcUV2YkpOTHI2VkRIa0hSSFhoNHNPekY5VXlwYnJoRG9mNEpXL1VaUit1eUNPeENDUnBwaXE0ZmNTdTFJN1hxMDczM2wwdCsxTmhRcW0xOGxQNTM1Y0FaQlNtaW5EeHMyK0p1OU9WandPalU2K1pWM0pSVG9CeGUxVzBTdEViMTdYSHNGQ3V1QnMvNEdZM0ZvaFRHUDVHVVU3eHM1QkJhMTl1a3RPaXNuZG9oUjhEeEt3SmVGNXJZa1F3SmNDY1JVRE5iRVE1ZnJ6dU1wVitZOGcwUFNPZFNxQkxmYTE3S0dCcXFFZVdPNC8yK0Z1ZWZvS0VOclphQjdhQ2RIK0s4Y2lRS2NBVUNkVVRFNmpUUjVpRzN3Z0RwejFyYzRHZ1ZKdnFqaTc2ckI3Q2pkM0NtYk03WTV0SHJQUFFaK3JaMkt0aHdxVkYwelBHUmpkdVhITjBvam1OMkYwWnIxRGdtRGxnR0pUalA4R09LTGdQTzFmaFVSdXpUSkIyR2xpVHZnaE5vaE5kTFE3c0t2d01LMS8yekMiLCJtYWMiOiJkMzQyOTkxNzgwY2IzYWQwNjVhYjA1YTQ3NmFmYzY2ZjE5MjEwMGZjOWQ5NzU2MzJmMWJlYzNmODcwNmE3ZGM3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjFCU0hNWlo4RFRyeWtCaGQ1SUhYVHc9PSIsInZhbHVlIjoia3FuZEpFemh2RlBKWE8vMEZZZC9WcW4rN3YyVjJoRUt0cERBMzU1WnpSbUx3bE9rWkFlekt5dTB1ZjhSd1hOVzB1KzQvVVdOTEhOMzZrZmREQm4vaWZLYXdrWHVONVNzR2FnMWY5WFJickFUa2hqeTlKNVhMN2F5OURLSjd3ZG5pRzZMYkVuWjUwdnVtRU95cHZPaWFDcWhFeGU2MkdmeWl2T1Z3Y05iUHdmS0ZPeStITFpSamI1U0Z2RHFzZ1N6SUVmOVJPQW80L241aWQxY3ZEdGNaNXhpcm9KZVRNL0xKVndNYVpYV0RIK2JEaHVEU29TUmdmKzg3amJRQ3JwVHMrcDZKb294d1VQbFp1aHVxVUd5bDg4bTNmaEowQkY5OWFjUkZuTUhJWGZiQlNhK0NibUtrbHJ0YXBJK0FzL2krNXp0VkpHVEdzSU5WQ1M2VytSSWRaVFZPY2M5Q0ZVQjFtMUhRZFVyT2dFWS9WMGc3R1N2cWNEbHpiSmxwcHc0U211MktkQy9xUEszbitLZU01T2VnQ2hyN1RIcFhvMnVZWVlMcTg3RzZnZEVPcUk3dUxkOUFSRUVXVml5b3ZodkY4NFlVdWNYUHRldmlHZGZtbHFtWVRmY2REYUtET1NONlduWDdaejBuZDUwRldmS0xLaG56aGlubWUvcTYrWHAiLCJtYWMiOiI5ZDFhZWMzYmUxNWE1MjI0ZjIxNGUwNmVhY2QyNGFiMjczYzYzNDhkMzNjNDIxOTVkYmM2NDU1ZGRhNzVmYjNhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1860453057\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-273179392 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-273179392\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:50:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkthbUQ0TFlYT3o1NDVKOHI0NWFET2c9PSIsInZhbHVlIjoiUG5Qb2JGVWZaS1R6Qlh0Qld5Ym51cFNxNW4zSmRialFoVnBmS3c0UE1zbldEVjhrc3hWalNEMzRCZWxlOVVpR1UrY3BqaWVoOVo5V09VZkk3aFpzbkdaejQwQjhkTExxOHZaVFM0ZDBXSit5T0l6TDQ0RzcwelEvVGdtb0pMcTJuVDRXTm9DNDR1dTlFYVVFa1gyaURBQXFaUVV0Rm1tVjFlS3FoVVBkcnA1Q2ZrQStOL1FvZmRweFZHQ1VGcmVWTFNxYUpyRWE4eVdLd0NRWXd3YXk5cjhoWFo5NXpvK09jcC8zcTFqb2g2UW5MQmRJY1FFM2dpTE1QeVlxcUFMU2N3cksxa2ZpMDBYVThJSWJtcVpZMHgvWDRmZ2YyVzRTWjdLb3hXeXl2UGFaczZlOTB5by9YSUU0bE5YR3JlYnF2eTVMS3VaajJvZlR2K1pDQnRNVEsvcndCbks0OVhPMnA2cnNQMWFnTEUyNG1YTkNtU3REM2xjWndQTW9SS0FBTFd0czlCRGhUOGdlUlRnbzVjYmpGUlZGME14WXdTR0lHVWhlVDhUcTZTdm9hb3pvS3JYVTlhdDVDMTJKTXBic1BKN3pibk5qUUxmSS9lNWRUbWMyV1c0UGRLdndsOEFiTlFIVmx5Z2ZuODQzaWpYSGFSdGE3LytmaGhHMC9QbnkiLCJtYWMiOiI5ZTljNGYwZTUwZjU4ZGI2OTQ4N2Y1NDA1YzBmNDI4NWFjZmVkMDIxMDUwMjIyOTM2ZjdlMzQ5YmFhNDQxNGM4IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:50:30 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlNGSC9FL0hvVmJWUHkwVnY1NGpQdkE9PSIsInZhbHVlIjoiMzdGMGg5dUNXR3NHSldDQkJRTkxoVTFvQll2dmF4NUhldjB1MkNIcjN4Q042Q05iWmJBYlYwVVpCckZjM0hIRzhlTitpU1R4SDU3TmtIK3dPbVFYeHJOcTNCeVUyT3ExY0VpMWJDZXN2THBYTmtnOGh2S25zbFVpSmdxMlBkejd5UEl1K2tpRGYvK205d0ZKa3M5bTdhaEw1YlhUY2piSHBQQW5jNzRKL0NHVDVsMExZajlCejFIMXdZZ0N1T0FTb0lydy9icG12OGRleFFEbG1vTW9ZdWcrQmNVMENUWnZKMlA5MHFNSlFseUpycm5MUysyMUVXbnRkTmN0K3d4RE50YkNiRkdlZWlMTUh5UldJNGcwWmNaWXo0SVcxdW4vR0tVT3JtQk95M2kvMTl4enM0RmpTZ2YwMlNtZ2hOd3l4R3dzcTVYeUZCcVBOZWFZYnRRTGw0UVhhYUp6Qi9nRy93MnE1aWZoamhTL09rSW9QbjZ4SmhSQ2pTVjR4aFE0TWdYVUZEeGNNQXhDYTNOWXFUNm5sZWZBYXlMclo0cVptNUMvbDhOVmJ2WFp4V1BxQjNmejVlRlpLWG0wS3U0akNWM29SWXoxSTFXb1NEU3RyMXJSS2J2UkxQRGdKajhjVkVqNzMxNy9EeXdCZzVrYzJCOVVsTjlHRDlEdHh3U0QiLCJtYWMiOiIxY2FjYWYyMmU5NzUzOTM5NDc1MjMwZDg0MTQ5OTU2ZGI3ODMyNjI4ZGYxNTFmNDY2ZmZmMTA0Y2U2MjNlMTVkIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:50:30 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkthbUQ0TFlYT3o1NDVKOHI0NWFET2c9PSIsInZhbHVlIjoiUG5Qb2JGVWZaS1R6Qlh0Qld5Ym51cFNxNW4zSmRialFoVnBmS3c0UE1zbldEVjhrc3hWalNEMzRCZWxlOVVpR1UrY3BqaWVoOVo5V09VZkk3aFpzbkdaejQwQjhkTExxOHZaVFM0ZDBXSit5T0l6TDQ0RzcwelEvVGdtb0pMcTJuVDRXTm9DNDR1dTlFYVVFa1gyaURBQXFaUVV0Rm1tVjFlS3FoVVBkcnA1Q2ZrQStOL1FvZmRweFZHQ1VGcmVWTFNxYUpyRWE4eVdLd0NRWXd3YXk5cjhoWFo5NXpvK09jcC8zcTFqb2g2UW5MQmRJY1FFM2dpTE1QeVlxcUFMU2N3cksxa2ZpMDBYVThJSWJtcVpZMHgvWDRmZ2YyVzRTWjdLb3hXeXl2UGFaczZlOTB5by9YSUU0bE5YR3JlYnF2eTVMS3VaajJvZlR2K1pDQnRNVEsvcndCbks0OVhPMnA2cnNQMWFnTEUyNG1YTkNtU3REM2xjWndQTW9SS0FBTFd0czlCRGhUOGdlUlRnbzVjYmpGUlZGME14WXdTR0lHVWhlVDhUcTZTdm9hb3pvS3JYVTlhdDVDMTJKTXBic1BKN3pibk5qUUxmSS9lNWRUbWMyV1c0UGRLdndsOEFiTlFIVmx5Z2ZuODQzaWpYSGFSdGE3LytmaGhHMC9QbnkiLCJtYWMiOiI5ZTljNGYwZTUwZjU4ZGI2OTQ4N2Y1NDA1YzBmNDI4NWFjZmVkMDIxMDUwMjIyOTM2ZjdlMzQ5YmFhNDQxNGM4IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:50:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlNGSC9FL0hvVmJWUHkwVnY1NGpQdkE9PSIsInZhbHVlIjoiMzdGMGg5dUNXR3NHSldDQkJRTkxoVTFvQll2dmF4NUhldjB1MkNIcjN4Q042Q05iWmJBYlYwVVpCckZjM0hIRzhlTitpU1R4SDU3TmtIK3dPbVFYeHJOcTNCeVUyT3ExY0VpMWJDZXN2THBYTmtnOGh2S25zbFVpSmdxMlBkejd5UEl1K2tpRGYvK205d0ZKa3M5bTdhaEw1YlhUY2piSHBQQW5jNzRKL0NHVDVsMExZajlCejFIMXdZZ0N1T0FTb0lydy9icG12OGRleFFEbG1vTW9ZdWcrQmNVMENUWnZKMlA5MHFNSlFseUpycm5MUysyMUVXbnRkTmN0K3d4RE50YkNiRkdlZWlMTUh5UldJNGcwWmNaWXo0SVcxdW4vR0tVT3JtQk95M2kvMTl4enM0RmpTZ2YwMlNtZ2hOd3l4R3dzcTVYeUZCcVBOZWFZYnRRTGw0UVhhYUp6Qi9nRy93MnE1aWZoamhTL09rSW9QbjZ4SmhSQ2pTVjR4aFE0TWdYVUZEeGNNQXhDYTNOWXFUNm5sZWZBYXlMclo0cVptNUMvbDhOVmJ2WFp4V1BxQjNmejVlRlpLWG0wS3U0akNWM29SWXoxSTFXb1NEU3RyMXJSS2J2UkxQRGdKajhjVkVqNzMxNy9EeXdCZzVrYzJCOVVsTjlHRDlEdHh3U0QiLCJtYWMiOiIxY2FjYWYyMmU5NzUzOTM5NDc1MjMwZDg0MTQ5OTU2ZGI3ODMyNjI4ZGYxNTFmNDY2ZmZmMTA0Y2U2MjNlMTVkIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:50:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2050277839 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2050277839\", {\"maxDepth\":0})</script>\n"}}