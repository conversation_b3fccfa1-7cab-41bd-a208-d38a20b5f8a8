{"__meta": {"id": "Xa23ee54dab6e90c489ddc99d4dec1308", "datetime": "2025-06-17 14:48:15", "utime": **********.926887, "method": "GET", "uri": "/import/csv/modal?&table=product_services&status=true?id=", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.308598, "end": **********.92691, "duration": 0.618311882019043, "duration_str": "618ms", "measures": [{"label": "Booting", "start": **********.308598, "relative_start": 0, "end": **********.799003, "relative_end": **********.799003, "duration": 0.4904048442840576, "duration_str": "490ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.799017, "relative_start": 0.4904189109802246, "end": **********.926912, "relative_end": 2.1457672119140625e-06, "duration": 0.12789511680603027, "duration_str": "128ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47528368, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x import.import_modal", "param_count": null, "params": [], "start": **********.908425, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\resources\\views/import/import_modal.blade.phpimport.import_modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fresources%2Fviews%2Fimport%2Fimport_modal.blade.php&line=1", "ajax": false, "filename": "import_modal.blade.php", "line": "?"}, "render_count": 1, "name_original": "import.import_modal"}]}, "route": {"uri": "GET import/csv/modal", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\ImportController@fileImportModal", "namespace": null, "prefix": "", "where": [], "as": "csv.import.modal", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FImportController.php&line=125\" onclick=\"\">app/Http/Controllers/ImportController.php:125-137</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.036359999999999996, "accumulated_duration_str": "36.36ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.847654, "duration": 0.02822, "duration_str": "28.22ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 77.613}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 6274}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/ImportController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ImportController.php", "line": 44}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ImportController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ImportController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.886244, "duration": 0.00563, "duration_str": "5.63ms", "memory": 0, "memory_str": null, "filename": "Utility.php:6274", "source": "app/Models/Utility.php:6274", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=6274", "ajax": false, "filename": "Utility.php", "line": "6274"}, "connection": "ty", "start_percent": 77.613, "width_percent": 15.484}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'ty' and table_name = 'product_services' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 6275}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/ImportController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ImportController.php", "line": 44}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ImportController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ImportController.php", "line": 129}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.894938, "duration": 0.0025099999999999996, "duration_str": "2.51ms", "memory": 0, "memory_str": null, "filename": "Utility.php:6275", "source": "app/Models/Utility.php:6275", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=6275", "ajax": false, "filename": "Utility.php", "line": "6275"}, "connection": "ty", "start_percent": 93.097, "width_percent": 6.903}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6", "_previous": "array:1 [\n  \"url\" => \"http://localhost/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/import/csv/modal", "status_code": "<pre class=sf-dump id=sf-dump-1834864883 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1834864883\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1528272727 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>table</span>\" => \"<span class=sf-dump-str title=\"16 characters\">product_services</span>\"\n  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"8 characters\">true?id=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1528272727\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-784118832 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-784118832\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1463025851 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://localhost/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2032 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=17t60l2%7C1750171683312%7C3%7C1%7Cl.clarity.ms%2Fcollect; PHPSESSID=o1mek9etnn9ejsik46u9hjcosg; XSRF-TOKEN=eyJpdiI6ImpycjdqN2tVY2J2bHVERG5IYUpPN2c9PSIsInZhbHVlIjoiRlNLMjJ1d1d1dmRER1EwL3IrWkcrTFpReHUvL3dXSFFDOG0yZ0dwK2VjZmtaakNEMkNIUk1TMHh1VG1abUt4cmd3OEZyNDVXNVRRZWw5b25jNmd5eDBDcUp5ZVUwakVmUXRyWGNXMWpNL1QrcUhhYVpCZ2xEMlhKdEtmaVU0Z2JpdkQ1elppNnJYMkpoL2J6SFlub1NKUVlxNGJIUmkySmh6VkZ1cXVoQVNjZnczOWV1OEFPMW1KcWVORjU4VEFrMGxidS9Sc01PWVB1R25XNExJblFhZ0ladUxqcWxHL0UxQXQ2WVhDUlhzbjJnaW1jRzUrMHIvd3dEcGNKQnZZVC9LK2d5c2ZveEQxNzRLRnAwOEMwVGtWUkQrVGxiUW5pZy9yZ2xaSnhRRUt1THFBUUV1cGFpV1JwQksrU014cHpTK1RHeXhkOUVnd2g0UzcrNnNna1NoN012K0NZMmtEZDdzRlB6d2kvMWdmczhUZDNIYmtubE9Kd1E3NlpoY0c2K0c0Mk03OGJYZjd1eHZVSFZqYVhwNjZLWFZ2eUF5MFh6OExtWWhqSzR6ZVR4UGdNTWxPemFnZ3lFT3VCUStlOGpUNlBKVzNRVGthSW0wSU9MSUFGMFZrQklxWnZJUFVVUUhpWDJCdzJqbDlOZ2dSa1krd0tkbjFwa3ZGVXFtcEEiLCJtYWMiOiJlZTVmYjMxZjFjMWQ0NWRmZGJhMGNiMjllNjhhZjJmZDZiYzZhNzExNDkyMTljMWE5OWEzNjQ2ZDNhMjEzMzc1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImRZVlZGZ05jaG1GNWxzNm55eXJXZ0E9PSIsInZhbHVlIjoiT2w2VTRUYmhsOXJXL29ySFYxWGx5bWhSTzFtZ1FVenMrVGZoNUVUQlZDR09rTTFKaGV5bnMycDVZWkdoQm9kRThYdk85dENWOGYwYm0vbXRYU1NsU1JaTDRmRlRKOVNPTHlmeUR4ZG5KdUpndEJjand5bFRXNFVQemtDUXdWbTY2d3l5aXBIeGJmeUF3bjFjSUhJb3ZDSFQ4OTRTTnB0NlpmZFcyOFgxbWRuN1JMR3kzOGdhVXh2NEdKbjNKaXJLdnNlUERMY0N5OGlCOUxQV0tiajZaOXRZbnBMZnpSc0xVTzJFUmVORnBKSkRadHk5U1JFTUVFMGZ0MThUVHhQaS96UnJNKzRmTmpTUWk4TE5NT3dweTN1eGVYUllicHJuL0d3Q01ITXlFVHlrbVFNYnhqcW1iQVZnMHM0Rk1qa29oT3Z6dXkzWWxCMForeUp6Nk9EaVBvL1kvR1gzWTdmMldKTmh0RmFIVUN5SGJPTWtBME9RVkc5SXZheU1Eb0c5ZktRK1M3cDBXWlQ3QjN1QzZnSklndjk5dGpyQm9BU0dVSTcwU2h6S21rNm9jR2VXRTA4c3B3NlZKcngycHNaMXpQRDNwQWJkVGJZdVNQRjZVRFNmK3h2WHNvMCsvVWd4cW1yRnNVZk43T1RsZUpOK2c3VE40WkE5V1dBZWV6R2kiLCJtYWMiOiJlZjg0YzdiODMwOTY5NzQ4Y2Q5MTBiYzg2OGNiYjgwYzFiYWE5ZTlkZGU2YzBiYzgwYWQwOGI3NGIwMmMxNDQ0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1463025851\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-66938441 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>PHPSESSID</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HxnOnSuhAAPTfN2VS4q58slgJoytwtHYqaX0oLoW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-66938441\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1923893936 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:48:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Iis4WGJ2cjN3WTVHcUZoVGE3dXpxb1E9PSIsInZhbHVlIjoienI1eE9PNTZLbzNRUzFBK20zb2pTbEloQ3ZsdmVqVys2UWxVb1o4MndWdGlneVhlTDhCN2g4NkFUNVNCVXJiUjNkcCtqcTFyWkdHVVV6dEdROXVEV2tyVEVaeEhMTDljYjFRcTNETzBBOG1heC9NdFh2bEpjMjc3UjVUbUpHVzBRb3dQR2l5YlJpSEY2T2hNaWZGK0YrWVZ2MjB5cyt4WHlWbC9pY0NNL1MxeENOdWZqb1pYcUN5MG56aGloUUc1cmJJc3pmTVMwRGtKY2ZJbEc0U2RTY2VEYnpUdHlIcjNyVHZoeGhQWmZ6K1RFdHZ2bXBJRlpWSGgyUWxSaGwvcUh5UlRCRXZtT1E5K2RZREJVU3BKNDJweHRFci9sanoyaHRENWhmT1JkTUVaKzNuTjB6eGE5WDZhMGFYZXhlcTlpZHJDa05OQ0tOT3l1cVRHY2N0Q0crRlFySmxSbXh3b0VnRWhScWxFQ25wRUg0NGhiMDB6a3ZaQm9pQ3hFOXg4WktLNWw5VkRiUS9GRTJGUTJ1N29sUEJ1VFZzSWpLdGZFNFI2TTk4UlRnbkdHUllEenZVRDVoZVlwMkxqY0lYczFzZ1R0R2YxVm90b1F3MUk2RjJOZFpaakxjYVlKZllRYlJKQzI2UFRKU1gvZ0lVNGErVHZhaithdVVIZDRQZFUiLCJtYWMiOiJlZjg3OWViOTA5ODZiOWIzYzc5NmRiMTRiZTAwNDMxYWYyMTI3NGJlYWVlZmU0ZmU2YTRmZGQ2ZTIwNzU1NGIxIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:48:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ikd2Z1BMUjdoclBKaCtjdkRyUlBFZmc9PSIsInZhbHVlIjoiMVkwRkk4U2JRUjlwck5zUDdxdGRvTjg0Y0RIZGNPMnl6TkdtVGNIMm5vN1VJblZzOEVLQ2NKRmlUTzAvOS9pdlFudUt1V0M1RFB0ZXhVbTAyOENxdEZ6MzNmbFNweTFURnVJQW1wUkszczJiZ0FVeHVWYzlsbEhpVlRtNEFEWW5hVy9RYjhyVWk5MFVQdUVrL2RaZzlUUHdES2t5MlExUkduVHhucUpaOHhCQ21EUWVrUGFkU0JGbkQzMVVDcXhDVEVzaVB6TWhhUGVJQkFNek5RN256ODQ1VyttNEZFaUxIQnA2S2NLN3B0Rk52U3Mwa21CRURScG1qek9JYjRta3BQeDBvRGEzRWxhS2hCSTVUYlVLTEt3NzJoU2llQk1LMHNJdzN4REFYN21BdWN5bTZKb1RjWUYwZkE2MFhTVDlsTGZZQ21YaU8zOHF5dUJkQkxDMmMxaWVMRTdiWUlycU5QakJwakJZRUZhR2lYcTNBckcvUjZ4a3owcWFGNkFlVFZNS2kvT1FYcllLck96UnZSMkIwa282MVZUL1R1UlU5RmVxNnpzSUdkNlcxZWVtRTFtUVJ4ZFBCbWtrR0xNdnFNclQ5NVg2VVlzUms4RzNCLzFDM1ovalYvejZvek1BN2phSkhDOXpMclMwTGJVSnVra0VqQnZCSHdOZ2hUVVoiLCJtYWMiOiIxOTA3NWExMzEyYzk0NTQwMjgxZmRhNzM5M2EzYmEyY2FlZmM5MzdlNzJmZWViYzA3N2FmYTUwM2Q1YTY4YTYyIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:48:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Iis4WGJ2cjN3WTVHcUZoVGE3dXpxb1E9PSIsInZhbHVlIjoienI1eE9PNTZLbzNRUzFBK20zb2pTbEloQ3ZsdmVqVys2UWxVb1o4MndWdGlneVhlTDhCN2g4NkFUNVNCVXJiUjNkcCtqcTFyWkdHVVV6dEdROXVEV2tyVEVaeEhMTDljYjFRcTNETzBBOG1heC9NdFh2bEpjMjc3UjVUbUpHVzBRb3dQR2l5YlJpSEY2T2hNaWZGK0YrWVZ2MjB5cyt4WHlWbC9pY0NNL1MxeENOdWZqb1pYcUN5MG56aGloUUc1cmJJc3pmTVMwRGtKY2ZJbEc0U2RTY2VEYnpUdHlIcjNyVHZoeGhQWmZ6K1RFdHZ2bXBJRlpWSGgyUWxSaGwvcUh5UlRCRXZtT1E5K2RZREJVU3BKNDJweHRFci9sanoyaHRENWhmT1JkTUVaKzNuTjB6eGE5WDZhMGFYZXhlcTlpZHJDa05OQ0tOT3l1cVRHY2N0Q0crRlFySmxSbXh3b0VnRWhScWxFQ25wRUg0NGhiMDB6a3ZaQm9pQ3hFOXg4WktLNWw5VkRiUS9GRTJGUTJ1N29sUEJ1VFZzSWpLdGZFNFI2TTk4UlRnbkdHUllEenZVRDVoZVlwMkxqY0lYczFzZ1R0R2YxVm90b1F3MUk2RjJOZFpaakxjYVlKZllRYlJKQzI2UFRKU1gvZ0lVNGErVHZhaithdVVIZDRQZFUiLCJtYWMiOiJlZjg3OWViOTA5ODZiOWIzYzc5NmRiMTRiZTAwNDMxYWYyMTI3NGJlYWVlZmU0ZmU2YTRmZGQ2ZTIwNzU1NGIxIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:48:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ikd2Z1BMUjdoclBKaCtjdkRyUlBFZmc9PSIsInZhbHVlIjoiMVkwRkk4U2JRUjlwck5zUDdxdGRvTjg0Y0RIZGNPMnl6TkdtVGNIMm5vN1VJblZzOEVLQ2NKRmlUTzAvOS9pdlFudUt1V0M1RFB0ZXhVbTAyOENxdEZ6MzNmbFNweTFURnVJQW1wUkszczJiZ0FVeHVWYzlsbEhpVlRtNEFEWW5hVy9RYjhyVWk5MFVQdUVrL2RaZzlUUHdES2t5MlExUkduVHhucUpaOHhCQ21EUWVrUGFkU0JGbkQzMVVDcXhDVEVzaVB6TWhhUGVJQkFNek5RN256ODQ1VyttNEZFaUxIQnA2S2NLN3B0Rk52U3Mwa21CRURScG1qek9JYjRta3BQeDBvRGEzRWxhS2hCSTVUYlVLTEt3NzJoU2llQk1LMHNJdzN4REFYN21BdWN5bTZKb1RjWUYwZkE2MFhTVDlsTGZZQ21YaU8zOHF5dUJkQkxDMmMxaWVMRTdiWUlycU5QakJwakJZRUZhR2lYcTNBckcvUjZ4a3owcWFGNkFlVFZNS2kvT1FYcllLck96UnZSMkIwa282MVZUL1R1UlU5RmVxNnpzSUdkNlcxZWVtRTFtUVJ4ZFBCbWtrR0xNdnFNclQ5NVg2VVlzUms4RzNCLzFDM1ovalYvejZvek1BN2phSkhDOXpMclMwTGJVSnVra0VqQnZCSHdOZ2hUVVoiLCJtYWMiOiIxOTA3NWExMzEyYzk0NTQwMjgxZmRhNzM5M2EzYmEyY2FlZmM5MzdlNzJmZWViYzA3N2FmYTUwM2Q1YTY4YTYyIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:48:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1923893936\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://localhost/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}