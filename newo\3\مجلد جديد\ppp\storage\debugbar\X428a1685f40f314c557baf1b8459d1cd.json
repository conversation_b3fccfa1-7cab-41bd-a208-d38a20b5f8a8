{"__meta": {"id": "X428a1685f40f314c557baf1b8459d1cd", "datetime": "2025-06-17 14:51:24", "utime": **********.746067, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.122199, "end": **********.746092, "duration": 0.6238930225372314, "duration_str": "624ms", "measures": [{"label": "Booting", "start": **********.122199, "relative_start": 0, "end": **********.663733, "relative_end": **********.663733, "duration": 0.5415339469909668, "duration_str": "542ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.663749, "relative_start": 0.5415499210357666, "end": **********.746094, "relative_end": 1.9073486328125e-06, "duration": 0.08234500885009766, "duration_str": "82.35ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46107288, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00634, "accumulated_duration_str": "6.34ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.701715, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 60.41}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.7197802, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 60.41, "width_percent": 15.3}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.730824, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 75.71, "width_percent": 24.29}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6", "_previous": "array:1 [\n  \"url\" => \"http://localhost/inventory-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-955518788 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-955518788\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-739502354 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-739502354\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1450351776 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1450351776\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-87220588 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2032 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; PHPSESSID=o1mek9etnn9ejsik46u9hjcosg; _clsk=17t60l2%7C1750171861220%7C7%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImtjaWdDclkxTmIya1NLcEh6c2doQlE9PSIsInZhbHVlIjoiWCtpQm4rZXIxSEp5Y1NXTEsvNnNvNy9LOFRFLzZjYzdWR3Z0RkRsTzhWNGVLbUU1S0hueXFRTWRJWUVZRHlHSmxkS1lkaU1IUnVCNmJLNVNCMng4bGl1dFRSUkR0YXFqMzhGREY3eStoR0tsbEFaVmMvcVlPRlV3V1JraXFndkFVUThnUFd4L2daV3RYWFhTdzJNUEROVE5FdFRCcFdZSGd1ZzFpeDJTM04xbmwrUTAxWjFYZC94ei9Qb2V2bTc3UnE4dHQ5aGVrY2lDMGJwRXZrUGlmbHhWMlJMR0oyUmR6RGxsOElhV3crMUFOMzZEQkc2bi9WcFNiOUFGS25vb1FHSlM4aGRnMVdoZU9IZVFyWnYvVER3Wm9LOG5XaE5ueEgxY2IvR0lvZFFwakdJbzlCSmhYKzRUdURBajVwQjQ0TzEwd0xSb1hTWDROWlpsVkxodjFCU2g0QjlETlBWengweTY5RjBScUtiUkVjK2tnTkpkY01nWkFJRERUdklOK0lpdFZJRTdBL0tldFhOa1BNYzJRYzJ6dCtGK2t5SXZzbys4RkZnamtIc2szWDJxUFBMU3g2U284TGU4eVVjcHZjRkRGNEtkTThJMmZHUTJ1TnoxUW1XZWJGS1lEZUoraHZOWC80L1JQWXVFa3ZDMHBkM1hnNWZLL3JWb3dDaFYiLCJtYWMiOiJjMWRhZDg3ZjhlM2I5OGRiN2RhYmY4NTFkMzhkMWUzNDFmNzVhYzZhMjYwMmRmMTU3NjFiYWRlMGI3NWQwYjcxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjBCRnV6WnE0R0hTd3hJYkk4UEEzNlE9PSIsInZhbHVlIjoicFpBMk9adE5aS201cnQ2ejduZGM5bE12emJSQlA5SEcvcEY5YUZ6V1Uwajd0MTByWEl4QXAwRngwczA2VnVPTzFsRmxiLytHSjd3MFhSZ1QxVitEaE5QcnpveWpCL3VaNXBCRTBUbU5CaCt4N1J6SGlRVWZNUnQ4L1QzcFovUkc0bjg2N05TSUJlMHhnRU13UHBqb1U4amRhenRicVJjTTYxcFNSbzliTTJhV2RFTTI5Qkp1QVdlNENRQWZ0R2NtZWlxSVZVQXJHa1UrSENzUEZDQ1pFWFA0OEozN01tL1RGbHVRSmtoczRGbUVJMEFVUHJBVXF3Z0xDM0dVWmFyWU9hU2VUTUgxV3JQVFA3bDhVMkpXRXpJNHdtMXhOc0FqalVkZHlZYldjemp2RUF2Z0lZb3A2UmJFUE1veEZXSWg3RGQ1ZVBRTFIyR0NGN0d6WTlJcXUrU0dlN0xvQjc3dXdHYUFIbDlqdVdqYXNZcUFxeTJqRHVuUENFMSt5Z1UzTFJvYTU2cGFZbVU0MDd1WHJVcFZpZHJ2MWhZWWlKM0ZkTS81bGZ6cXlsd29HdXA0aVNVc1BzUFpHSlpvQmRxeXdhVlVxNWZ1S2JYbFFXN09sZEd4SDAwa2N2eUZNL0dJWFVTVFljd2UzY1o0YW1FODk5eFJJMnkwUHBhSGMxUUkiLCJtYWMiOiI2YmMyNjNlMDBjMDcyMDY3MTI4MDRkZGY5ZjRiZmRjZjk0M2NkMzg3MDJmYjAxNjJmZWIxMGNhMTA3OTE1NDg3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-87220588\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2071129501 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>PHPSESSID</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HxnOnSuhAAPTfN2VS4q58slgJoytwtHYqaX0oLoW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2071129501\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:51:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IitrTkExMlkyR0p2Z0RuR0oyeXFkMHc9PSIsInZhbHVlIjoiOEdIcmJLNXFuclVlUHNvNHBSSE1FVjdQQm5hZHBnTVo5WVQ4em9DNXI3ak1tMnlocjFlU3E5c0tPOXl5bTRxNllVK0pLQitpWUFCb1dkaHNmY3RlS2JYUDl6SXo3cDh2eVJmdFhjaHZXSDA2ZmJrZHVQeXZiNUpEYTQ1b1hXOWNaUEJVbFg1NDRsVlNZL3JNSU5oOW1BMkE2M0l0QzVIdXVndzMyaVJMTllRTmhIclNMR2pGVlFCY3ZuRGIwQTRaWnRwTG9jbVVld1A2MHN5ME10bzZCS2JKdHphODFSUHBTNXZrQ3NvbHFQNmlrNXJtNzU3bi8rdjlvaEwxTlFuM3ltTlJ0MG4rK3JGaW9YdHlEVFh0OW8rZmJLL1ZqZlBFL2Z2cUFMUUxyVGphZ1lCMHlPNzU5a2k4eDdLSUFEY3JUSTQzaFFMZ1JjdHNqTzV4blRJNnVJbXVHTzFZeEhhbzhFTXh2dXRlQzdSamN4NGVrM01FZzRpK25mb3o4ZWtVVVgwR3RFLzljSmtXLzdNWG5UNDJQRW5hdVpUb3NHOG52SGY1MDN2NEF1UkFBT1Y1OHZJd3Z4MU1TNmJIMmFLZlVWNCs3NGd1OUxlMUxOOHdNRTM2U2x0dDRJeHpjSFhIZXkzeWpiREJUam9wZkFsNkw1Z0V2eXlCRHYyWUhqVTMiLCJtYWMiOiI5ZTRjMWFhYjMyMTZjMWZmZTVjYmUzZDU3NWE5ZTc2NjIwY2VhNjhiMDg1MDFhMjk2OTYyZWYyOGViOWU3YjM0IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:51:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjZsZ05aYS8yMUhyc1VKazUyN0RjSWc9PSIsInZhbHVlIjoibEZEZHFDanVnY0Z2U05ianNkampEejdheENtYyt3NmJpaHByMFNNWGZ0K090T2RyWlVNZys3eHh0WjdZOWpYbEcyV0NMblRZQnoyZnpEQVd6aVhJNy9TQnRFaWRjbDQ5eUtUcXpKbDRoeFM5OE9PaTVjRkVnR1VHMy82VkhzNDE5QVhBc3NZRThEaUpkbFFMWHBzcXE5NXhmbWZIbkVXdEY2YlcwaER0OFNNd1N2QVB4Tnl2OVozdDI1bCtoWFVxN0FLNjE1aDlINXFvazNKRUhEV2pVZFVyZEx0czlsLzUwb2oyOE1tSlIwWFpkWkhwdTZwRi9lVUpvODErNXV3Y2UwWXEvc2NMdEtlRzFyb3ZTUjUxNnZzQjV2UGU4aFI1TjloN1ZmeEFOQ0kxbjdvcC83WFRLSW9yR3ZqU3liUTgwSGpHSDIrajNXR0ROMUFHSjViaVU3UVRsUkIwMGJjaEs5R0NmUDhBYjZ2cXM0WGQveHh0RWJTZkQra0hvdWhCL1JIYk1TT1hhTFRXV3ZOQytxRG1RM3hXM0o1V3R0T2lYYzJpMkIwVmxnU05RS1p1a0FQU0V1Si9IOFlKVVduVXcyRm5DSU90MThuNnlqMmNFWDhBaDRRRzQ5c0RuakFLTWxieE00V0dWc2NtUGpsQzR5TW40eXduZEs5bTUyUGgiLCJtYWMiOiI4YjVmZGNhNGM1YWNhMWFiODBhOWUzZTQxM2FhNmE1ZTQ0ZWY0ZGM3Y2M4MmI3ZWU2ZDhmZDQ5OGEzYjVkMDU5IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:51:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IitrTkExMlkyR0p2Z0RuR0oyeXFkMHc9PSIsInZhbHVlIjoiOEdIcmJLNXFuclVlUHNvNHBSSE1FVjdQQm5hZHBnTVo5WVQ4em9DNXI3ak1tMnlocjFlU3E5c0tPOXl5bTRxNllVK0pLQitpWUFCb1dkaHNmY3RlS2JYUDl6SXo3cDh2eVJmdFhjaHZXSDA2ZmJrZHVQeXZiNUpEYTQ1b1hXOWNaUEJVbFg1NDRsVlNZL3JNSU5oOW1BMkE2M0l0QzVIdXVndzMyaVJMTllRTmhIclNMR2pGVlFCY3ZuRGIwQTRaWnRwTG9jbVVld1A2MHN5ME10bzZCS2JKdHphODFSUHBTNXZrQ3NvbHFQNmlrNXJtNzU3bi8rdjlvaEwxTlFuM3ltTlJ0MG4rK3JGaW9YdHlEVFh0OW8rZmJLL1ZqZlBFL2Z2cUFMUUxyVGphZ1lCMHlPNzU5a2k4eDdLSUFEY3JUSTQzaFFMZ1JjdHNqTzV4blRJNnVJbXVHTzFZeEhhbzhFTXh2dXRlQzdSamN4NGVrM01FZzRpK25mb3o4ZWtVVVgwR3RFLzljSmtXLzdNWG5UNDJQRW5hdVpUb3NHOG52SGY1MDN2NEF1UkFBT1Y1OHZJd3Z4MU1TNmJIMmFLZlVWNCs3NGd1OUxlMUxOOHdNRTM2U2x0dDRJeHpjSFhIZXkzeWpiREJUam9wZkFsNkw1Z0V2eXlCRHYyWUhqVTMiLCJtYWMiOiI5ZTRjMWFhYjMyMTZjMWZmZTVjYmUzZDU3NWE5ZTc2NjIwY2VhNjhiMDg1MDFhMjk2OTYyZWYyOGViOWU3YjM0IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:51:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjZsZ05aYS8yMUhyc1VKazUyN0RjSWc9PSIsInZhbHVlIjoibEZEZHFDanVnY0Z2U05ianNkampEejdheENtYyt3NmJpaHByMFNNWGZ0K090T2RyWlVNZys3eHh0WjdZOWpYbEcyV0NMblRZQnoyZnpEQVd6aVhJNy9TQnRFaWRjbDQ5eUtUcXpKbDRoeFM5OE9PaTVjRkVnR1VHMy82VkhzNDE5QVhBc3NZRThEaUpkbFFMWHBzcXE5NXhmbWZIbkVXdEY2YlcwaER0OFNNd1N2QVB4Tnl2OVozdDI1bCtoWFVxN0FLNjE1aDlINXFvazNKRUhEV2pVZFVyZEx0czlsLzUwb2oyOE1tSlIwWFpkWkhwdTZwRi9lVUpvODErNXV3Y2UwWXEvc2NMdEtlRzFyb3ZTUjUxNnZzQjV2UGU4aFI1TjloN1ZmeEFOQ0kxbjdvcC83WFRLSW9yR3ZqU3liUTgwSGpHSDIrajNXR0ROMUFHSjViaVU3UVRsUkIwMGJjaEs5R0NmUDhBYjZ2cXM0WGQveHh0RWJTZkQra0hvdWhCL1JIYk1TT1hhTFRXV3ZOQytxRG1RM3hXM0o1V3R0T2lYYzJpMkIwVmxnU05RS1p1a0FQU0V1Si9IOFlKVVduVXcyRm5DSU90MThuNnlqMmNFWDhBaDRRRzQ5c0RuakFLTWxieE00V0dWc2NtUGpsQzR5TW40eXduZEs5bTUyUGgiLCJtYWMiOiI4YjVmZGNhNGM1YWNhMWFiODBhOWUzZTQxM2FhNmE1ZTQ0ZWY0ZGM3Y2M4MmI3ZWU2ZDhmZDQ5OGEzYjVkMDU5IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:51:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-623790522 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-623790522\", {\"maxDepth\":0})</script>\n"}}