{"__meta": {"id": "X91556cd447b0d7a8271ab9ddfdbb1d72", "datetime": "2025-06-17 15:02:36", "utime": **********.296358, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750172555.7042, "end": **********.296381, "duration": 0.5921809673309326, "duration_str": "592ms", "measures": [{"label": "Booting", "start": 1750172555.7042, "relative_start": 0, "end": **********.213298, "relative_end": **********.213298, "duration": 0.5090980529785156, "duration_str": "509ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.213314, "relative_start": 0.5091140270233154, "end": **********.296383, "relative_end": 1.9073486328125e-06, "duration": 0.08306884765625, "duration_str": "83.07ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46340168, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1663\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1663-1673</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.013560000000000001, "accumulated_duration_str": "13.56ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2572012, "duration": 0.012230000000000001, "duration_str": "12.23ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 90.192}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.2830062, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 90.192, "width_percent": 9.808}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-145351330 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-145351330\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-890882444 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-890882444\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1821463054 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1821463054\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-224525796 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImR5bzdsb1Ixc2ZKZk5ZV3BrUUtwVXc9PSIsInZhbHVlIjoiMmx3ak9qdHNRQzhWNVJaWXRCZjVKcEJHcUU1OXdIOXBkVnNTdVlwNjdHek9SMFdmYTZQRGpybjdoNG9uR2FaM1dLUjBteFdaQ2VBcWEyc0ZqUWRBUFAxYUk4TWs2OUtHNjQwSC83MkZybXA3TjBVM1JraThDckdOTHZDMU1uVjlUa3RkNUNDVmhkWndYdHJPN3NlSG9PZHNjM0hlR3FPRmlPNkFxeTRiYUJ4MjFxRmk1NG8ybUhDT284Yk1sOC9FeHpXanZlY04zV0F6TzVrTlVSbXovU1phckllU25zczNwMmpEUUJCRGZCSFVpbWJZRzlSOExyU0l0RDREN3E3d3d4VVQwMUtxMVpjWmhQanpsQlFBQm9Xa0hLWlVhUXcrQzIwYXA5czhqck5Iem52R2l2bFVCZmFOdXNFRitxTk1EUHF6L1ZCZVowQlhhK1l0SVlXWHJrWU5EaVcvWVE0b3BRQndqN29QSlR4d3Z2ZWZVNWZ0R3V1ZUZpN0l4YnRBNkQvMXhOYjVnN3Y3RXY5SWdJY2UvbVJQOXNBZWVuZGJTL1FST0tjVHhiUExkcHdBdlcvK0owQ2NydW5hcXR1WmVRUWZ4YW9ZTVV0Qy9ZaHcxZ3dMN3Z0M1l0MmZwY0VUL1pBN1p6YVJScTRCajlnaE93ZnVrSjFBOEJUaEI2YWgiLCJtYWMiOiJmMTI2YmMwYjYwZmY0OTJjZmUyZGNiMWIxZWRkNDhmOTY3ZWM0ZTcwYzlhZDVmZWJiZGU0YmMyYTQ2MDUxMDEyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImsyVmF0bThxTXhIYkQ3bHdZRlAyc0E9PSIsInZhbHVlIjoiSkhUc0dmMTFDbFc5NU9VQW5FKytQdUE1bGJIRU9NZXI2Z2JUOG1GdVk4bnFZQ0xnSkVmQjdsT3hCK1hMUHBwNTJvTElxbUFHQks3ZEZqa3NTMkNlUlVidnBBUzlVVHFHYmJ3eXQvcGxQYVpPcFYvZG5nYTN0UlVnVHZRWnlCVk81czFLNStBQUpaV1prRnlTcUpKVDJ2R0p0R3poZkZrcVNyL2Q1SkhzdjI0VE1tRWNYM2RZSVh1aElVaHNzZEtzYzhUajg2ajIvdGdkcG1wSHVzMkhwcE5pMUZtdkhOV0x4MFloSGFyZlZOTy9icmtwVjlTZmdZbWs2Rzh1SS9tZld6Yzl5TXJ1V04rMUN2R2dlY3FuWHZYQ3RSdGo3ZmdZODBYLzlmUVkrd0NrMkkwMGVnK0FBbTIrSU5MS1FQZ3ArSEVQRUF1N3l3dHlHcy9TSkVtSDVwdDZZdHNFTUg5enNFMkJRWDI3c3gyOTMwTzIwam1WY3pvU25Sak9sSk9IQjRTTWNZeVFPZGdFTENOMFJkVG4rdGluRkxQazRLMTRtOVlxTHptRVBSN2srRDlvRklnNGpoV0IwVCtJOGFXcUdKaUNDR013TXBrMmJaZ2lpRG9mcWRMajQ1VnVSYnEwV2ZuVGhHZ0VBZmpRSmxtT012TG0rQzE3RC80Zy9pMXUiLCJtYWMiOiJjMDE4ZTY0YTY3OThjNjA5MmQwMjUzMTc2OGM5MGJmMTA4NjI3Mzg5NGFiMjljYmEwMDExZmNmZTBiOTMyOTdkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-224525796\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1985209022 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1985209022\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-136740097 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:02:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjMyb08zaXBEVXg5b2pWQlpqRkpQTkE9PSIsInZhbHVlIjoicmNpYXVuMThkSG1OajFhLzYrSkM1VkkrVUhvMUtxdjZSakRCZVhIQkdZeWV1Zks5WVB0aEErbk04RGlRaVE1UFZXbm5OWDdRVDRjaTBkNitiSEFIT2t5MnlsTVdjUHJWbTN4WG5vQUZESnBYMGU4NTBHWFN3cWdpRks0dU4vMmZMaVJpSWhKQWdzcmVRQllIcmJkTFVFNTNaemVYbzMvU042VEwyamJ3cUNDN1A3YkRKRmY4cEdadHJaaE1oUXdKaEl3bmRuaG9sdStRTWR3Y1JFRFcwZzJyTlBrUVh2YjdOeWVMekptamdjcWdLR2NuZE9LM1I4QzhoaW0rV2FjMTEwZ3NZYytrL2pZbStaT29HR3h6ZVpnN0h2ZHM0d09sdC9ZTFBQckhEbSt5bXFQOW0zU0FKVzErc3VPOE92YkR0WFRJcTQ1LzR0Ky83bUhreWZjakp3aEpudm5MNFJEVFYvRXlQZzFwVVR5R0lKMFZnYm4vVkxIYmVBQ3krZUdtbVV1WDA0Y3ljTGNxUmRpT1BaelhWeThuczZ0VEhROENlVldlTXF5ZkJmQmVaWEZPZG5JcFlucE9TSVZ4UkZERUtaTlZpUUZSb0RnQmpPNnYzTWpFcDFKZEhFS09sOFJZM042VkZoSDlTN2NOS1FTUndYSGhwZGlONm9UNWp6Z20iLCJtYWMiOiI5NjJkOGIzZjdjYTk4ZWVmOWZjNjEyM2I2MmQ2NDhiOTA0YTJmZDc0YmYyZmFmZTNjMDEwZDk2NWQyMzk5NjUwIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:02:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlBQaGdyb0hpL0phNlh4bnFOb0VCYVE9PSIsInZhbHVlIjoiTzZ5QWo5Z0lMaFBhaXQ2cm1yc2JCTWJMdW1ZTkFVNm93eGxSK2lEV3p2R1k1OUcxcGNkd2xQbzBLOU96Mmp1cnVGVUs0clN5a1BBNGRlbEdiMWFBMFc3eVd5OFNMbmk3eENTQ0VxVGJ0RFV3UHJBcGJpT1NIVmVuVmlwbCt5OE1IaG1pTzdXL3kzdnN2SFNQSWVOOUtRMnZ2YTlXNVp0WVBRUjhZMWkxMGdoY2I3Q3pLZ0VweG9UeHRiZVJoUFhRa3BYMWEyTjdKeGphcStaV2t2VDFZdVdISFc4Z2QwdmVGNE5oblJFektVaU8ydWQ1M09ZcE9mKzhGUFdqWmpoYjJoZzJMajkrQ0pqRFJsVmQ3dFh3YXVYeFVMWXlSZnhNZ2c2eWdKOFhocFJtMFVBT2dlQkhCU2lTejZGVVBlQnlGNFREcGdiUElhTlBpUjdhd2EyMnIxV0dXRnFDdkY1OUtZVDFUMkFrS05PalpCUDhrUTM0MjRKWFg2RjZEVExzTTVVZk81dG9TdGYreFhhclNVZ2djNkpTUEFqUm1ucnNwU3FTM0dJdnBFTFlWYmVGZWZVZHVpQ1BHV1cxRGhYNzlmczFZSUtnWmExaWg0UVFZak9OR05CdXhqR2lVOElwckZEMVM5NS9BNUlpdm00N0ZsQ1hQWlY2c0txTTA1emciLCJtYWMiOiJhNzdmMzUxODk1OTU3ZTYwZTIwMGUwMGU3ODM3ZTY4NWNlYWJhODE0NjY3NWQ5YzhkNzAwNjI4NTQzNGRlOTViIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:02:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjMyb08zaXBEVXg5b2pWQlpqRkpQTkE9PSIsInZhbHVlIjoicmNpYXVuMThkSG1OajFhLzYrSkM1VkkrVUhvMUtxdjZSakRCZVhIQkdZeWV1Zks5WVB0aEErbk04RGlRaVE1UFZXbm5OWDdRVDRjaTBkNitiSEFIT2t5MnlsTVdjUHJWbTN4WG5vQUZESnBYMGU4NTBHWFN3cWdpRks0dU4vMmZMaVJpSWhKQWdzcmVRQllIcmJkTFVFNTNaemVYbzMvU042VEwyamJ3cUNDN1A3YkRKRmY4cEdadHJaaE1oUXdKaEl3bmRuaG9sdStRTWR3Y1JFRFcwZzJyTlBrUVh2YjdOeWVMekptamdjcWdLR2NuZE9LM1I4QzhoaW0rV2FjMTEwZ3NZYytrL2pZbStaT29HR3h6ZVpnN0h2ZHM0d09sdC9ZTFBQckhEbSt5bXFQOW0zU0FKVzErc3VPOE92YkR0WFRJcTQ1LzR0Ky83bUhreWZjakp3aEpudm5MNFJEVFYvRXlQZzFwVVR5R0lKMFZnYm4vVkxIYmVBQ3krZUdtbVV1WDA0Y3ljTGNxUmRpT1BaelhWeThuczZ0VEhROENlVldlTXF5ZkJmQmVaWEZPZG5JcFlucE9TSVZ4UkZERUtaTlZpUUZSb0RnQmpPNnYzTWpFcDFKZEhFS09sOFJZM042VkZoSDlTN2NOS1FTUndYSGhwZGlONm9UNWp6Z20iLCJtYWMiOiI5NjJkOGIzZjdjYTk4ZWVmOWZjNjEyM2I2MmQ2NDhiOTA0YTJmZDc0YmYyZmFmZTNjMDEwZDk2NWQyMzk5NjUwIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:02:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlBQaGdyb0hpL0phNlh4bnFOb0VCYVE9PSIsInZhbHVlIjoiTzZ5QWo5Z0lMaFBhaXQ2cm1yc2JCTWJMdW1ZTkFVNm93eGxSK2lEV3p2R1k1OUcxcGNkd2xQbzBLOU96Mmp1cnVGVUs0clN5a1BBNGRlbEdiMWFBMFc3eVd5OFNMbmk3eENTQ0VxVGJ0RFV3UHJBcGJpT1NIVmVuVmlwbCt5OE1IaG1pTzdXL3kzdnN2SFNQSWVOOUtRMnZ2YTlXNVp0WVBRUjhZMWkxMGdoY2I3Q3pLZ0VweG9UeHRiZVJoUFhRa3BYMWEyTjdKeGphcStaV2t2VDFZdVdISFc4Z2QwdmVGNE5oblJFektVaU8ydWQ1M09ZcE9mKzhGUFdqWmpoYjJoZzJMajkrQ0pqRFJsVmQ3dFh3YXVYeFVMWXlSZnhNZ2c2eWdKOFhocFJtMFVBT2dlQkhCU2lTejZGVVBlQnlGNFREcGdiUElhTlBpUjdhd2EyMnIxV0dXRnFDdkY1OUtZVDFUMkFrS05PalpCUDhrUTM0MjRKWFg2RjZEVExzTTVVZk81dG9TdGYreFhhclNVZ2djNkpTUEFqUm1ucnNwU3FTM0dJdnBFTFlWYmVGZWZVZHVpQ1BHV1cxRGhYNzlmczFZSUtnWmExaWg0UVFZak9OR05CdXhqR2lVOElwckZEMVM5NS9BNUlpdm00N0ZsQ1hQWlY2c0txTTA1emciLCJtYWMiOiJhNzdmMzUxODk1OTU3ZTYwZTIwMGUwMGU3ODM3ZTY4NWNlYWJhODE0NjY3NWQ5YzhkNzAwNjI4NTQzNGRlOTViIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:02:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-136740097\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}