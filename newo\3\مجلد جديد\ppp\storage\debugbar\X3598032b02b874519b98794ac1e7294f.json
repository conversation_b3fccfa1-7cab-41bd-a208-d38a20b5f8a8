{"__meta": {"id": "X3598032b02b874519b98794ac1e7294f", "datetime": "2025-06-17 14:52:51", "utime": **********.210109, "method": "POST", "uri": "/inventory-management/check-zero-quantities", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750171970.613923, "end": **********.210132, "duration": 0.5962088108062744, "duration_str": "596ms", "measures": [{"label": "Booting", "start": 1750171970.613923, "relative_start": 0, "end": **********.097351, "relative_end": **********.097351, "duration": 0.4834280014038086, "duration_str": "483ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.097368, "relative_start": 0.4834449291229248, "end": **********.210135, "relative_end": 3.0994415283203125e-06, "duration": 0.11276698112487793, "duration_str": "113ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46558640, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST inventory-management/check-zero-quantities", "middleware": "web, auth, XSS", "controller": "App\\Http\\Controllers\\InventoryManagementController@checkZeroQuantities", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "inventory.check.zero.quantities", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=856\" onclick=\"\">app/Http/Controllers/InventoryManagementController.php:856-917</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.02893, "accumulated_duration_str": "28.93ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1392071, "duration": 0.01052, "duration_str": "10.52ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 36.364}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.161389, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 36.364, "width_percent": 3.629}, {"sql": "select * from `product_services` where `created_by` = 15 and `type` = 'product'", "type": "query", "params": [], "bindings": ["15", "product"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 871}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.167565, "duration": 0.00455, "duration_str": "4.55ms", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:871", "source": "app/Http/Controllers/InventoryManagementController.php:871", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=871", "ajax": false, "filename": "InventoryManagementController.php", "line": "871"}, "connection": "ty", "start_percent": 39.993, "width_percent": 15.728}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = 3 order by `updated_at` desc limit 1", "type": "query", "params": [], "bindings": ["8", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 881}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.175466, "duration": 0.00894, "duration_str": "8.94ms", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:881", "source": "app/Http/Controllers/InventoryManagementController.php:881", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=881", "ajax": false, "filename": "InventoryManagementController.php", "line": "881"}, "connection": "ty", "start_percent": 55.721, "width_percent": 30.902}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = 5 order by `updated_at` desc limit 1", "type": "query", "params": [], "bindings": ["8", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 881}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.187268, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:881", "source": "app/Http/Controllers/InventoryManagementController.php:881", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=881", "ajax": false, "filename": "InventoryManagementController.php", "line": "881"}, "connection": "ty", "start_percent": 86.623, "width_percent": 4.494}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = 6 order by `updated_at` desc limit 1", "type": "query", "params": [], "bindings": ["8", "6"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 881}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1916902, "duration": 0.********99999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:881", "source": "app/Http/Controllers/InventoryManagementController.php:881", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=881", "ajax": false, "filename": "InventoryManagementController.php", "line": "881"}, "connection": "ty", "start_percent": 91.116, "width_percent": 3.353}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = 7 order by `updated_at` desc limit 1", "type": "query", "params": [], "bindings": ["8", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 881}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1954172, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:881", "source": "app/Http/Controllers/InventoryManagementController.php:881", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=881", "ajax": false, "filename": "InventoryManagementController.php", "line": "881"}, "connection": "ty", "start_percent": 94.469, "width_percent": 2.8}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = 8 order by `updated_at` desc limit 1", "type": "query", "params": [], "bindings": ["8", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 881}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2004118, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "InventoryManagementController.php:881", "source": "app/Http/Controllers/InventoryManagementController.php:881", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=881", "ajax": false, "filename": "InventoryManagementController.php", "line": "881"}, "connection": "ty", "start_percent": 97.269, "width_percent": 2.731}]}, "models": {"data": {"App\\Models\\ProductService": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\WarehouseProduct": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FWarehouseProduct.php&line=1", "ajax": false, "filename": "WarehouseProduct.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 10, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/inventory-management/check-zero-quantities", "status_code": "<pre class=sf-dump id=sf-dump-1412872994 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1412872994\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-511066733 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-511066733\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1198465387 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1198465387\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1271807270 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">62</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2032 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; PHPSESSID=o1mek9etnn9ejsik46u9hjcosg; _clsk=17t60l2%7C1750171898702%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjBSd0FTMERXYXRwc3BwdHhSLy85S3c9PSIsInZhbHVlIjoiUWhYL3BRWjU4eWRKYTRVanFPTW9CaHlwWVVJZlZ4ckI0eSthdkZsT1lNVTZzWFNqeEZPcGdab3dreE9NSXlmak56amRiT1JnTTdNY1A3b05DRzlxdVRWVmRrVDg0SjB1R2JJRkxUS2ZNTjZwdnNUbDRFcVczVWw3SFpkUzE3VWIyZnBjVXJON1Q5bitBVXdCcGNrdklMN0ErYStXb21tYk9IR1pUY2pDR1VEelBncnA4MU1EejhKZS9nMXJGNDBZWTRBVFNpbTBHeFpXalQrKy8wWklUNEpVckxCenp4TGJFU0VCSytCaENJL1J6ZEc1RmxaL2c1SEFncmsxSTVjc3BsQW90RlJGUEI3S0FyRFdUR3pNUTFIcUl1N1ZoUitrUFRXT1VpRVlVUTU3dGYwYzNGZmpQWEhXZ3pydEx5UC83bGo2Z2lMM3NKUWI4My9UY3JWYm5FRXRFUVhjaTdlZSsxRGUrMVN6WU9QS1R1KzJrSVRHSEd5cVBnQ3Z0VGg4ZUpuU3NBZHhaT2lOV3Y3QkhmditUd24rOFBNSG1ETy9UR0c0WUZpYVZ6a3pmMytEK1pFVDRuQjB2b1BqRmR3UW9nVTUwbm5nWjJoNEtTLzAxTm9uSTlzMVR0b3lVZEptL0NpTWs1UGNsTDRzcis0VEJUUnV4NllDMXprS092MUkiLCJtYWMiOiJmNmMwNGM4NWI0YzlkZmRiNmJiZDhiOWUzYmY3ZjlmMmU5NWVmNmEyZWE1ZTUzYWQ2ZmY0ZmYzMzc1NTI4MGI4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjZHY3kzUmphcE56dnB2enBmSDBKZlE9PSIsInZhbHVlIjoiNlJ2RGpjU2lxWUlISzlyUHNwakx2NVNrcUMrZEtBUE9BcGkvcU9jTGVLM0tUY2JPYzg4NjR6ZktBV1oxcFI1YXE1dDgxazlyUkNCcjI3U1JPR2dQR090ZEYxZVlOZlhSRDBQTjBwTXZlZlV3ZjY2MGlmMDNsNFVwd3pJa092d1RGdHE2UzZsR2N5RWJqR0xmdDByK0NBQ0diOHFsd0RxeHMrRXpyOSs4cFZFRDBhZkhTV1hmVHVCdW9HL2d4MTg1VWtaVHNaYVMrZDBSQW1pUWgvTVc3aDRCRFc1YjNNYk05NXp0N0FWMGdXSlV0Y3FyY0hndTliYVhFaytjbGpScnU5Z1RTVXdlYzVvK2R1a2d2ak1Fek1NNzVDYjhZemhGcWtYc2g4YUFSQStxRU1MeHVrVlhON21YR3o2SlBGUjNUSDlJb2p3K01sSkJ0bkFON05uMDYwY3JMY1NHbGFLdGFjcTdoZ1Y1Vnd1ZVZVY3dDWWJmVUxqMmcwNHVFeWd6QnNuSDdRMUhlUWdmcENBck12OWpRcEg3N3RYNHVNNVJKakhIdWxWMVp1djhweFh2djlKemNxR2ZOSUYrenlJV2FYU3RGV1VlNWxrVFJRcnVxSWJ5czBlRXR6TnZ2R3JIYnlKcnh4TmhjeEVyOFJWZy9seS9ZOGlxMnpvRFFYOW4iLCJtYWMiOiJlYzA0MWU3NzA3NjEzNzY3OGU5M2I0MmJmMjE0NTQ1YzdjYjI4MjMwZDAxNDhkNWQzYTM5NTA2YWY4YmQ3OGY1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1271807270\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1878781585 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>PHPSESSID</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HxnOnSuhAAPTfN2VS4q58slgJoytwtHYqaX0oLoW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1878781585\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1569750381 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:52:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InVTMFBLd0FsZlJqVm1valBWaThZdUE9PSIsInZhbHVlIjoianFlQ2ExYzNxNGxYT3RJbUw4RDBSRTB5MGhHWXNlRFVmam4wc3N4U1Rab2l5OHB0RE5zZmsxQkxER3Z5YkxUY0NhbG5uam9qTDF4QVBCQlJqZXNsODFyZVJIU29LL0MwWit6T2FBVDdRTkE5ODRSbmRIWWdFSjRsSFJ1SU10dU8yL25UTE9ZcDlac1FPbGZhS3hPcFgyaFNYQ0Z6TmtlbDJZMGFlNDhhQVpJMjlHa0doQnE1alEwbHdXU0YwWW8yVG53N1BrUlRvMFhDcmZZUldWcVc5U0VKZThyK1ZRd0NZc29GVVZNbnlvSG5SM1N4cHhvN1JYUEdEK20wYksvSzNCdVlFQmREZkE1WGdEUGtvRGVOR0crMzJsaWZ4MWtWcGtpbzNWaUxER0pIYVJMYm92VUdRYlJXNEFSU3dwZE5JUU1KTlpDMGhoQXRIdDM2YmpQYklIbHVsT1d0NHFTWlo5Q2Y3Wk0zQVdNZnozdEh1Uzl4R1JwU1NZTmNacThsY0d2bkxZR2RqRHpFVE9CR2d5eThuaGw5eVdvamdqSWJ2TmlRWEN1dXJSSWxYYUkzYXdTZ2wzdkpWdzZIZmRhdjNvZjlkTjNRSmZHWHV3eC9yYnAzVERWMWovK2Y4aXAzbXBGWENKQXYrd0hBanZxQUEzanV2QzA0dWdvUUF4NGkiLCJtYWMiOiIzM2YxYTVlYWU2Y2I2NTZkODZiNjI4YTQ1MmZlOWEyMDc5M2E4M2EyNzg5OTIwMGJhNmUzNWMwZjA5Y2I3MGU4IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:52:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Iis1VW5iQWJTTnlJN0cxdkJwRzJDMGc9PSIsInZhbHVlIjoiY3MzRk52VUE4K1U3eTBtc2lMTHJEYjJ3bGxVc2l2NDR3WkJhSm41cDJCaldvUDNwVnMyaGlyMFlpQ1BDNmYvaE5wQis5WFUvVjIyZ0k1V05hNXRBQWVpb0lrWW5ERHZPaWFtcGRHQ0dwd0JBKzZLd1JaZ0hpc0w0Rk9RV255d0lqK1ZDeEZtbnVnNjFReWpBM1lCUHIraWZIOW9BMHIzZHpqWk9aMzhURHBoaUNPTUc1SlJnTGJzTFJYR3hXMitiTjRKcDZvUElzTmF5NWx2UGZ1ZVhkQUtyMEREWU1YUDNDUUlLMlNMYUtmUzRBSjNSVFQ4STRIVWJWWlFMVUpMZ01hZmMvdmtkRFlMT0pSTm55VlJaMURvcC9uWEZ1TVM2UmtSQU5tbnZxaWZCaEVyVkRVRkN0Q3lOSnlXMFcvbGJzOVpIaWc1Rzk5TFFsUjBwa1kyVTYyZ2hvVy9NR0FKL1V4eFQxdU4rREl4c0d2VVZ3MklWZFlBa1hBTkJoRlpHNWZaTlpGMHdTN05qUWxoWFJ6ZzNRS0l3SUJXV2ZHdGNCNE5QS0tRR0h5bGpqRzlpa1dHZWRJL0lSYU1rVVl2TVhKdnlONjl3UTVsUGFTZlFZeE1tZFdKRkUxakxKeDRROEZyZ0NDeWo3bllUTWJPaDNTU1JTTktWWW5yQml5eDIiLCJtYWMiOiI5ZGUzYjIxNjUxNWVkZTU2MTQ3YjJmZjQ5MjIwZGI1MTljNTBmZDg0YWY2ZTMxZjI0ZjlhZjk0OTkyZWZlNWMyIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:52:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InVTMFBLd0FsZlJqVm1valBWaThZdUE9PSIsInZhbHVlIjoianFlQ2ExYzNxNGxYT3RJbUw4RDBSRTB5MGhHWXNlRFVmam4wc3N4U1Rab2l5OHB0RE5zZmsxQkxER3Z5YkxUY0NhbG5uam9qTDF4QVBCQlJqZXNsODFyZVJIU29LL0MwWit6T2FBVDdRTkE5ODRSbmRIWWdFSjRsSFJ1SU10dU8yL25UTE9ZcDlac1FPbGZhS3hPcFgyaFNYQ0Z6TmtlbDJZMGFlNDhhQVpJMjlHa0doQnE1alEwbHdXU0YwWW8yVG53N1BrUlRvMFhDcmZZUldWcVc5U0VKZThyK1ZRd0NZc29GVVZNbnlvSG5SM1N4cHhvN1JYUEdEK20wYksvSzNCdVlFQmREZkE1WGdEUGtvRGVOR0crMzJsaWZ4MWtWcGtpbzNWaUxER0pIYVJMYm92VUdRYlJXNEFSU3dwZE5JUU1KTlpDMGhoQXRIdDM2YmpQYklIbHVsT1d0NHFTWlo5Q2Y3Wk0zQVdNZnozdEh1Uzl4R1JwU1NZTmNacThsY0d2bkxZR2RqRHpFVE9CR2d5eThuaGw5eVdvamdqSWJ2TmlRWEN1dXJSSWxYYUkzYXdTZ2wzdkpWdzZIZmRhdjNvZjlkTjNRSmZHWHV3eC9yYnAzVERWMWovK2Y4aXAzbXBGWENKQXYrd0hBanZxQUEzanV2QzA0dWdvUUF4NGkiLCJtYWMiOiIzM2YxYTVlYWU2Y2I2NTZkODZiNjI4YTQ1MmZlOWEyMDc5M2E4M2EyNzg5OTIwMGJhNmUzNWMwZjA5Y2I3MGU4IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:52:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Iis1VW5iQWJTTnlJN0cxdkJwRzJDMGc9PSIsInZhbHVlIjoiY3MzRk52VUE4K1U3eTBtc2lMTHJEYjJ3bGxVc2l2NDR3WkJhSm41cDJCaldvUDNwVnMyaGlyMFlpQ1BDNmYvaE5wQis5WFUvVjIyZ0k1V05hNXRBQWVpb0lrWW5ERHZPaWFtcGRHQ0dwd0JBKzZLd1JaZ0hpc0w0Rk9RV255d0lqK1ZDeEZtbnVnNjFReWpBM1lCUHIraWZIOW9BMHIzZHpqWk9aMzhURHBoaUNPTUc1SlJnTGJzTFJYR3hXMitiTjRKcDZvUElzTmF5NWx2UGZ1ZVhkQUtyMEREWU1YUDNDUUlLMlNMYUtmUzRBSjNSVFQ4STRIVWJWWlFMVUpMZ01hZmMvdmtkRFlMT0pSTm55VlJaMURvcC9uWEZ1TVM2UmtSQU5tbnZxaWZCaEVyVkRVRkN0Q3lOSnlXMFcvbGJzOVpIaWc1Rzk5TFFsUjBwa1kyVTYyZ2hvVy9NR0FKL1V4eFQxdU4rREl4c0d2VVZ3MklWZFlBa1hBTkJoRlpHNWZaTlpGMHdTN05qUWxoWFJ6ZzNRS0l3SUJXV2ZHdGNCNE5QS0tRR0h5bGpqRzlpa1dHZWRJL0lSYU1rVVl2TVhKdnlONjl3UTVsUGFTZlFZeE1tZFdKRkUxakxKeDRROEZyZ0NDeWo3bllUTWJPaDNTU1JTTktWWW5yQml5eDIiLCJtYWMiOiI5ZGUzYjIxNjUxNWVkZTU2MTQ3YjJmZjQ5MjIwZGI1MTljNTBmZDg0YWY2ZTMxZjI0ZjlhZjk0OTkyZWZlNWMyIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:52:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1569750381\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1041245684 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1041245684\", {\"maxDepth\":0})</script>\n"}}