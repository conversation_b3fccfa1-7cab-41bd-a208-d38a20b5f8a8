{"__meta": {"id": "X5db1ab1ec0d7cb78727d9d5d309641aa", "datetime": "2025-06-17 14:54:16", "utime": **********.311613, "method": "GET", "uri": "/customer/check/warehouse?customer_id=6&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750172055.750557, "end": **********.311637, "duration": 0.5610799789428711, "duration_str": "561ms", "measures": [{"label": "Booting", "start": 1750172055.750557, "relative_start": 0, "end": **********.22732, "relative_end": **********.22732, "duration": 0.4767630100250244, "duration_str": "477ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.227333, "relative_start": 0.476776123046875, "end": **********.311639, "relative_end": 2.1457672119140625e-06, "duration": 0.08430600166320801, "duration_str": "84.31ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46235848, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01967, "accumulated_duration_str": "19.67ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.266402, "duration": 0.01748, "duration_str": "17.48ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 88.866}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.296685, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 88.866, "width_percent": 5.186}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.301773, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 94.052, "width_percent": 5.948}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  8 => array:9 [\n    \"name\" => \"tea-شاهي\"\n    \"quantity\" => 1\n    \"price\" => \"2.00\"\n    \"id\" => \"8\"\n    \"tax\" => 0\n    \"subtotal\" => 2.0\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-2049767488 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2049767488\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2002886462 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2002886462\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1363559044 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImR4Vyt4RE1vbEVVSkJJb0IwOTlrSHc9PSIsInZhbHVlIjoidUU1aCtkdW81a2V1bDVhVHF5ZEtCNFkwUytrQ1M3OGRORlBIWHQ5Tm5Xc0J4enRmVHpyZXkwTytXaWYrdS9VZUZYM2pVUjN5b3BZdW9xL0tyenRaQmx4azYvK0F6N0VRSDZ5NXF2djJEdTAxZVk2NkZoblRJYTVRS0dld2tuMVkwZ3grbGE4c2g1M0Y5TlVreC80ckkzUWFwSkVjWmswS2hRTEU3NHY1WTFkaTM4K2Vkbndwbmp6VHR1WlpnN2hiaXYyVko0NGk5a3BjUTB0Nk95Z2xYbTcxWEFtandycnE4U0NFTitCcitMRnRSVWdxTk5xWkY2dmthcFpXbW9mVjRZelh2R24rYlcrNjFHY0wzVTgySldYV1lHQW9UajdjYmIxVER4V00wcWQ4N2dXZEpubzZlU1JVVThQWC9teWxnUXFhVlZwelZ2UnY4NWJvZmVQcithS2p3TWJrZUFRaFFvQnBSVTlaZXdlNEZPY2Q3d2tUTkhHSXBScGJBNGdGbUZ4UGtWY0pHUHpReitXTndRWkJxT2c2T2duYUw0MGlLMkY0NjByT3Yxc0Nndmk2cEFQOVBraHgrNlNnM2JLM05qR3NyS1pHWVpvUzg0K2xsOEVIcHpsMHZlYmhTUGFqcGpvSU5iYlNKeHpBelpoc2phR0k3QlBmNVNzeVpwUEciLCJtYWMiOiJiMzdhYmQ3MzIyNjcxZWVmOWUyZTY1ZTRiYWFhZTJiNDczMGRhNmFjMzVjMTc5YmFlNWI3ZjA2NGY0ZTdlNmJlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Im1kNVQrSW93OGtKVmYyK2dYZ3l4V1E9PSIsInZhbHVlIjoiMENYVC9HVmZBV1UyUHh4S1psaXJGNXJCNVYyeWNKdlllVCtyYmIvaGFXeVJSc2NtdWY4a2txSXZHQ2J6M0JLZkhINGZpcW5hT1JqU29FUzkxK0FmK2dsTDh4Nkd5WkNVbVVZOEZTWWErNkZ6VmdFTjdubm1ROVdDNWovWms1UlhWMWlaYjMrKzVod2xqK280MHkwR3EyTDE4RlgvQktxdHFYWWFCMTRzcmVDOVJ1QlpGZXRpdFpXQnpES1pKK0c3UEZtSEZScmxSdVNpYURaK1ZGbDlHK2NwVmh3aHFkOVphMFl6TUIyZnhkQlNaN1JTeWdSNHFxZFl6L2l1ODFoWmt4L2ZDdzBCRGRFVFRKcHVCUUR3NGdrdXVwQWJ2dG53ZFFrSXhCekpiUHAyMEx1TkhhQVNhL21GOXl4aUJ2NWdyZzMyYlBGakI3SHpZQmtYNTIxMFlicXBJV2NYNUttTUNGeHpIY1ZVNitIcGgxa2pJTWhIKzdsOWlzZkJLRGI0a3NDc0ZRUzlNZ2xrZVBGV0NyblhmU3YzZVN4cWd6QVFBYk82VlVXNjdhbnFubFdmbXI4aFN2VUdCbHNmYjVRa2ZEWC91UmtLZmkzZnl3VmliRmxyQ3ovNlVWb0RhbXNRVjBDUEZORGNLY3RmVE0rTWJqREJZdkU1dlljMC9QMmoiLCJtYWMiOiI4NzY2ZjdhNmY5MGY2ZjIxYWNiNTEzYmE1MWQ3N2IyMmU5MTk3OWNkNjg2NTAzMGY5MDdkZWJlNjlmNDYwMzJiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1363559044\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-879831934 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-879831934\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1686179830 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:54:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkEyMmpncTJlVDlkQWpWbVFxU3J3dEE9PSIsInZhbHVlIjoiTERBaDJVNGp4c3BkdGVsbXVMRzRwY3lzZ1l5SklBTTBIY0Q3Mkp1NmpWU3Y4N2hyT1Fqb1NEQnJHRzdWdVRLUlQwYlN6VXUrcGo2dTFudEQreTdjalB3MUZJR0paNUpGbVc2UGRXT1RVcGdZQld1ZzQ5ZEdqdnQ0NDBNb0tSY0VEZnc1d0I5SkJwSGdZOFFISUovTk9TalV0R3FGb3RpVEpoMm9waUpudXYvcHl1elQxTTgvTVQ4bC9PREl2OHFPU3JLYXBuNWo5Ky91b1VDK3BvT3pEalhEb2YrWWdTTzhSSWt4aDBpRjlGYnVBTk1kR3NVOGU1L2pJUmVhTm5MN05IdXhCTkMrRjhrU1lteVBpWXR0STVvRFlCU1g3bjF0WmZLZ3VFWlNxVEdYeDhhTmd5MHFSbnZKaTJJcXdrMmZYNkNYUTZDeXpETWorZGxua3hQcEszeVNVeG01TFJrZ1VxU1NiRFcycHVZdWlPYWNsb0hLNDkyZjBMbWpoMndtSnkyeTRNTlptL0JjdTFJdHdPb0RlSUc4WVR6Qk1Fbkh6blpiK216N09EdGtXeCtGUlE5bndheHprK25MUk56Q2RTTEMxMFVNTnpjOG0wWUdUTTlMcGJZK3ZXSmFWZGpLYS81QUJtUW9RODVXQklYQmdGemUzK2hoQ09wbVJjRDIiLCJtYWMiOiI4NDdmMmZkYmY5Mjk4MDFiY2Q3NjdjOTljNzhhYTlmMDE4ZTY5MzVjMzk3MmEzNDMxMGExODdiMThmOTAwMmM5IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:54:16 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InpVUXRLWDYyc0VvcjEycjl4RlgzMVE9PSIsInZhbHVlIjoiS2VWQlEyVUNDN3hvb2o2a1AzSEszdTI0bWRqVWNTMzRzdFUvanMwOXI1Vm9XRXdpeUZLbndnVGZVRXZWUG5uR01LMGJkTnFJQUE0UnB2dm1ySmRIZE1pazdkZEJXa0RvYTl1elR6SnFkNHFOc2lJTnR1cWYzRVF6U3FYQ0x4WkFnVVYzc2dBK2E2b2phaUFqaFAvRmJSRHZPOUIxVHF3Rkp5cUJEZGd5TDBuY2NyZ2dVTnBROGt3eElUbHAveUp3T1hSYTdiR2FnRVZXMktoRXk1VHUvdEtKb1QzVE5zOGx6VDQyS1RvTWI5QmtkLy9TcmxlVDRyWkY1Zy9tV3BIejg5WFpyUy9sL0k5UEtsRHIyeUdEVmF1QVBqenlIcnVwZUpNYXp0Tk5jUHk5aERzR2I3UG5iYmtrQStaNkdxbXNUR3NxYkpZdjNHNzh5eVM5S2dPb05KTWtmZ1IzMlk0TzllMFozaWVPdE9XZEFxQjZZcUFQM2hLWDRpMlNiSURhV1BOWk5VZHJhaTRPVXhvejltT0M0aWFxckhxZjdKSlYzTE1XY1llNHErRVIycWR6S3Q3ZlFGTmErZ2tjbENGWkxLQmkzN0dBVVVCY2dhcnNwK2tXc2d6a1BUVlZlNUE1R05KdlNKN2xEaTZMZWJsTFg3OVBKdlpBRW9XRlpMZXoiLCJtYWMiOiJhMTM5YzY0NmRkZGM2MzU0ZTRkMmNiZDhkNzhmZjVhNDE0MTY2ZWJiMmU0Y2Q1M2VkYzE3Y2UyNTMyNWExZTQzIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:54:16 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkEyMmpncTJlVDlkQWpWbVFxU3J3dEE9PSIsInZhbHVlIjoiTERBaDJVNGp4c3BkdGVsbXVMRzRwY3lzZ1l5SklBTTBIY0Q3Mkp1NmpWU3Y4N2hyT1Fqb1NEQnJHRzdWdVRLUlQwYlN6VXUrcGo2dTFudEQreTdjalB3MUZJR0paNUpGbVc2UGRXT1RVcGdZQld1ZzQ5ZEdqdnQ0NDBNb0tSY0VEZnc1d0I5SkJwSGdZOFFISUovTk9TalV0R3FGb3RpVEpoMm9waUpudXYvcHl1elQxTTgvTVQ4bC9PREl2OHFPU3JLYXBuNWo5Ky91b1VDK3BvT3pEalhEb2YrWWdTTzhSSWt4aDBpRjlGYnVBTk1kR3NVOGU1L2pJUmVhTm5MN05IdXhCTkMrRjhrU1lteVBpWXR0STVvRFlCU1g3bjF0WmZLZ3VFWlNxVEdYeDhhTmd5MHFSbnZKaTJJcXdrMmZYNkNYUTZDeXpETWorZGxua3hQcEszeVNVeG01TFJrZ1VxU1NiRFcycHVZdWlPYWNsb0hLNDkyZjBMbWpoMndtSnkyeTRNTlptL0JjdTFJdHdPb0RlSUc4WVR6Qk1Fbkh6blpiK216N09EdGtXeCtGUlE5bndheHprK25MUk56Q2RTTEMxMFVNTnpjOG0wWUdUTTlMcGJZK3ZXSmFWZGpLYS81QUJtUW9RODVXQklYQmdGemUzK2hoQ09wbVJjRDIiLCJtYWMiOiI4NDdmMmZkYmY5Mjk4MDFiY2Q3NjdjOTljNzhhYTlmMDE4ZTY5MzVjMzk3MmEzNDMxMGExODdiMThmOTAwMmM5IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:54:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InpVUXRLWDYyc0VvcjEycjl4RlgzMVE9PSIsInZhbHVlIjoiS2VWQlEyVUNDN3hvb2o2a1AzSEszdTI0bWRqVWNTMzRzdFUvanMwOXI1Vm9XRXdpeUZLbndnVGZVRXZWUG5uR01LMGJkTnFJQUE0UnB2dm1ySmRIZE1pazdkZEJXa0RvYTl1elR6SnFkNHFOc2lJTnR1cWYzRVF6U3FYQ0x4WkFnVVYzc2dBK2E2b2phaUFqaFAvRmJSRHZPOUIxVHF3Rkp5cUJEZGd5TDBuY2NyZ2dVTnBROGt3eElUbHAveUp3T1hSYTdiR2FnRVZXMktoRXk1VHUvdEtKb1QzVE5zOGx6VDQyS1RvTWI5QmtkLy9TcmxlVDRyWkY1Zy9tV3BIejg5WFpyUy9sL0k5UEtsRHIyeUdEVmF1QVBqenlIcnVwZUpNYXp0Tk5jUHk5aERzR2I3UG5iYmtrQStaNkdxbXNUR3NxYkpZdjNHNzh5eVM5S2dPb05KTWtmZ1IzMlk0TzllMFozaWVPdE9XZEFxQjZZcUFQM2hLWDRpMlNiSURhV1BOWk5VZHJhaTRPVXhvejltT0M0aWFxckhxZjdKSlYzTE1XY1llNHErRVIycWR6S3Q3ZlFGTmErZ2tjbENGWkxLQmkzN0dBVVVCY2dhcnNwK2tXc2d6a1BUVlZlNUE1R05KdlNKN2xEaTZMZWJsTFg3OVBKdlpBRW9XRlpMZXoiLCJtYWMiOiJhMTM5YzY0NmRkZGM2MzU0ZTRkMmNiZDhkNzhmZjVhNDE0MTY2ZWJiMmU0Y2Q1M2VkYzE3Y2UyNTMyNWExZTQzIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:54:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1686179830\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>8</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">tea-&#1588;&#1575;&#1607;&#1610;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>8</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}