{"__meta": {"id": "X5eded26154eb001155bad82cd6d9bf81", "datetime": "2025-06-17 14:54:33", "utime": **********.692042, "method": "DELETE", "uri": "/remove-from-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 3, "messages": [{"message": "[14:54:33] LOG.info: removeFromCart called {\n    \"id\": \"8\",\n    \"session_key\": \"pos\",\n    \"request_data\": {\n        \"id\": \"8\",\n        \"session_key\": \"pos\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.655634, "xdebug_link": null, "collector": "log"}, {"message": "[14:54:33] LOG.info: Cart before removal {\n    \"cart\": {\n        \"7\": {\n            \"name\": \"ww\",\n            \"quantity\": 1,\n            \"price\": \"9.00\",\n            \"id\": \"7\",\n            \"tax\": 0,\n            \"subtotal\": 9,\n            \"originalquantity\": -5,\n            \"product_tax\": \"-\",\n            \"product_tax_id\": 0\n        },\n        \"6\": {\n            \"name\": \"\\u062e\\u0636\\u0627\\u0631  \\u0641\\u0648\\u0627\\u0643\\u0629\",\n            \"quantity\": 1,\n            \"price\": \"9.00\",\n            \"tax\": 0,\n            \"subtotal\": 9,\n            \"id\": \"6\",\n            \"originalquantity\": 5,\n            \"product_tax\": \"-\"\n        }\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.683811, "xdebug_link": null, "collector": "log"}, {"message": "[14:54:33] LOG.warning: Product not found in cart {\n    \"id\": \"8\",\n    \"cart_keys\": [\n        7,\n        6\n    ]\n}", "message_html": null, "is_string": false, "label": "warning", "time": **********.684007, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.103221, "end": **********.69207, "duration": 0.5888490676879883, "duration_str": "589ms", "measures": [{"label": "Booting", "start": **********.103221, "relative_start": 0, "end": **********.590427, "relative_end": **********.590427, "duration": 0.48720598220825195, "duration_str": "487ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.590439, "relative_start": 0.48721814155578613, "end": **********.692073, "relative_end": 3.0994415283203125e-06, "duration": 0.10163402557373047, "duration_str": "102ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48635272, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "DELETE remove-from-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@removeFromCart", "namespace": null, "prefix": "", "where": [], "as": "remove-from-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1675\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1675-1748</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.01169, "accumulated_duration_str": "11.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6300051, "duration": 0.00924, "duration_str": "9.24ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 79.042}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.65088, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 79.042, "width_percent": 8.982}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.674386, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 88.024, "width_percent": 6.416}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.6777341, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 94.44, "width_percent": 5.56}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 16,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1653170280 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1653170280\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.683181, "xdebug_link": null}]}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:2 [\n  7 => array:9 [\n    \"name\" => \"ww\"\n    \"quantity\" => 1\n    \"price\" => \"9.00\"\n    \"id\" => \"7\"\n    \"tax\" => 0\n    \"subtotal\" => 9.0\n    \"originalquantity\" => -5\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  6 => array:8 [\n    \"name\" => \"خضار  فواكة\"\n    \"quantity\" => 1\n    \"price\" => \"9.00\"\n    \"tax\" => 0\n    \"subtotal\" => 9.0\n    \"id\" => \"6\"\n    \"originalquantity\" => 5\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/remove-from-cart", "status_code": "<pre class=sf-dump id=sf-dump-2037300290 data-indent-pad=\"  \"><span class=sf-dump-num>404</span>\n</pre><script>Sfdump(\"sf-dump-2037300290\", {\"maxDepth\":0})</script>\n", "status_text": "Not Found", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1305346121 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1305346121\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1793329831 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1793329831\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">20</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlJEWU05LzBVTktLTHRTalBQWWduSVE9PSIsInZhbHVlIjoiMjFnSUFXdlY0NW02UWFoT1NXRllTWGZ5WXF6OUtDYjVLallabW1kTjhjRERPMVVGekhpdjNOWGpXaTFNYXlRT1RjNWpwcTVWK0tkNmxPQks0RDY2ZytkdW1vMjlreFE0TnJ1OWtaVnFCV3Y2WE81Wk42dkRudU9YeEFqNGlmbVNWWXdxeTNhOGV5ajc5VVA4OS9haVRCTnplSGFrcVl1dDhLdjRUSDZWTGZSOGF6aUJVM2FRcXhicUJXK1N6VmYydERHaEU2alJZSUMxMmp3eXRzcTdNMzFiMWFSL1VkL3I3eTYrcXdwSFVkbUc0Nk5WbHdmSEcxK29ZVnJIM1VYb0hxOHVEM0FRakwxMG9VYjduaFIvTlMvdDlnZysvZVByR25UZlZCVnduNFNtai9aTFhJbzNaYml4OXY5Vm5uSUg4MXd3N2xqM2VNWG9xSU43bGhJNGpIY1dEc0w5cGhKTUxyc3QvcGRPcUZyQlovQitkNWJYVGdlRDBtb1dhL0p2Z0ZtMWFETEFIWlg4NE1kZzVNc2Q2ZTFsUGIvMWhmUzlXZytUbEhxUyt3d2tjV2NPQ2x1ZVI3SGFPbUl3M2tnQlpEOFhMWU5DbEZHRkFXZGtueUk5TW1YbjhLZ25VYXMvMUlWck0wenBYSkJnZmFxV041RVhUM0tndlQ1SXNXRFciLCJtYWMiOiJkNTRiZDcwYzYxNWQ3NWE1Y2JlN2VjYzNlMzVmMmM5NGFiZmZkM2QwNzQyNTg0YmVlNjc5OTU5MmZiODBmNmM5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImxqNFBvT0hkK2ZrbGp0VTVreDV3S0E9PSIsInZhbHVlIjoiYW5iM0VBaGxLa2NFVW12ODFhN01jOEpycE9TKzJkY1BqMnFXMzZ2N0hIZlgreDJES2FvQmVodkRtUTIwajRpakZqSDdPZDNqbnUwYlAydEg0and4UWVBQ05RR2VKNjlzWm5wbVduYldzU1RpcWNGaSt6ZWhvRHIrK1RSMkpkYW5PK1liNFllQk9MUkVVaTh0QWdzeVI0Y2I3OEVIa1JPOHhFUWhJYS9VNFJnQ1lzN0syaFduM08yclhrU1FqNlNZa1NHY3FQTVhzYSt3aWxuTnJMSGw2RmpvNzV3YUhxVVZ5dit4RDU3ZjJFRnNFY1NWU0g3dW45R21BSmdqYnMvL3R0UTEwSnMzVlducGk5amNsMXpMR25VQm5Hb09mSW5qclVuKzdSeTN3YXVKYXVIbmVDaVVKRVNxN2xVUzVPdCsrYUN5bkU4ZkUyQ3duN0taeWJYRVZaVkliSUJWaW9hb3Q1MXFZR2RRNWpqeVBxTkUrZU9ONStvelEraHhCSm43cFErU0g3V042YUZRbTVqNHcvMUpEeGt2ckk5UkxtVHRCZjNpR2hXb1pja09yUGE3Q1VONERpcUgvTGV6WWVzc0pSMVRFMFFvTFJhc2daSVQ0TUI4RmVMcW1JV0tiTHRMaDRSeE9Ka21nQTgzY3JxNHJLTzA3YjJRK2xRZmNqdHkiLCJtYWMiOiI3ZGQ1OTNkNjM1MWViYTVkYTM4YmY4MGZhZjYyMWNhYjFmNDg2ZjIwZTk1MjU4ODZjZmIzZGU2YjA5MDY3ZWYwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:54:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImV0VXowU2lUOUxqSEpiL2t1SjhZZXc9PSIsInZhbHVlIjoiM1hFNUdTUm5LYnZGQ1NPU2taR2RudEw1QzhNSllMV3BrME15YTd2TFdrc0x6Y0NYdkhxdWpZaGhVQm91L0ExVEw2RW5CR2Ivc2hJa1hHc0swN24zUXhacHdTOUFMTkhScE1pdVRvMEVWcmFTWjJQKzMzcHBhNmNLSi90Y0tCeFBxSmwzOEkyVEMwZWo4TVZoR01jalY2cHh1SUs4TnpsaytIZ0JnVlJkSkdBQnVCUFRTdUhVckd2RGJ3Yjk1YXNWek5QKzFkcTVyd05zTzkrWFVNcWs5SkdwRUFleUpFRDVpdGtBTmU3aXhyK1FqckdRamdnQ3dSVTBiZG1RMm40UU1HY2NiSUxtK05ON0M2SUJrWVZOcDZ2Rjg0OEZMSVoyMVJVUlAzWGNJdGpKaEExdHlQcnYzYTVQbmJGYW9JS3pSSWwxSk93TmFENnh0b2hJbGJsU1drdE54cmFVMG1mK2VRbmdlMnp5NUp4aUd3Y0p6RzZvVHBrQTRJVUZqa2pMdStXYWZHT1RhU3Z6Ym0vVEdMSkd2M05hRngxZGNaMGZ3QXlINzdmdm9oMFhvVm9JR0N3YzcwRmI0U3BOODFRejRvVkZmMXRLOExmWEprSHF4V2MyM29Zamo2QzQrU1VHUHVPOCtpZndOMExiY2dqNW1yUzFuNXFnWDlWMldVaEQiLCJtYWMiOiI5ZWQwMzFiZTQ0NjhjOTQwMGQ4NDgwZjNmNjI0ODczODNkMWIxZjNmZDU0NTMzYjIzMzUyN2I4NDNlYzMyY2EzIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:54:33 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkdkRFFGMmdkS3VLbXEzRTE4RWtmQUE9PSIsInZhbHVlIjoiUkhnd014YmhpR3pqeVFjazd5NjY0YXJjdUhSYU5kMXkvTEUzUXNuamNhSkUwY2FHTjBMN1h0Q0J0MUxQQ3ZtbkFFM2NOc01PRWtIWmM2NXZEK3ZkYTl3Y0JJd0F3bWFNK3lDelhvRENYRGlCYXhqMkx5S0p6MUdZamgxazgwUXEvL0RpOGFpbmY5azRqd2p0Mlc4bVVjRitSaFpTTzhNRXJuQUJiNFBCMVNRUWlvMy9IT1d1NnY0N21VWnFRbXFGTHkyWjFSNWZSRXh4N1MvM1BXTVZGWW5JcUVPSUxwQkg1L1NieDI1aStaa0VYVmdlSmQzNG1OR25IY3JmMllvYTZibWF6OUhjeWk0ZmkyL2dDd1JERVBQNFFBY3ZBL3pwcWdmYVJWN3ZLVGN6MzhyM3daWldBZGxGcXEzUktlSXlPYnhlQjIvYjB1QzcwTW5SUVh5cHh2WXozbTc3QXhNMWY0SzdOb0F5clYxMVk1NWZQNnNrWi81RzhCUG1rZWc5eUlNR3gwaFI3UUJ2RTczL0NneWRRbmdKZHFNNVZ0c1NtZXRPVDQ2MW5pK3VYK21xRnBxa1BEZVY1V0h1MGRKQmdPMm9WSlNQbURuUXdoTW9KMERvZFZGMWVxUjEvakpLWUtieVZ3YWNub25DQjhEQ09BWXhGSVpQbVVZbW9BWjgiLCJtYWMiOiIwMTYwMGJjMDJiMjlmOGZhMzc0ZjJkOTQzMGNiYTAzNzNiOWI0NWQwMWYwMzJhZWUwMGZjOTA1ZTkzOGE4ODA4IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:54:33 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImV0VXowU2lUOUxqSEpiL2t1SjhZZXc9PSIsInZhbHVlIjoiM1hFNUdTUm5LYnZGQ1NPU2taR2RudEw1QzhNSllMV3BrME15YTd2TFdrc0x6Y0NYdkhxdWpZaGhVQm91L0ExVEw2RW5CR2Ivc2hJa1hHc0swN24zUXhacHdTOUFMTkhScE1pdVRvMEVWcmFTWjJQKzMzcHBhNmNLSi90Y0tCeFBxSmwzOEkyVEMwZWo4TVZoR01jalY2cHh1SUs4TnpsaytIZ0JnVlJkSkdBQnVCUFRTdUhVckd2RGJ3Yjk1YXNWek5QKzFkcTVyd05zTzkrWFVNcWs5SkdwRUFleUpFRDVpdGtBTmU3aXhyK1FqckdRamdnQ3dSVTBiZG1RMm40UU1HY2NiSUxtK05ON0M2SUJrWVZOcDZ2Rjg0OEZMSVoyMVJVUlAzWGNJdGpKaEExdHlQcnYzYTVQbmJGYW9JS3pSSWwxSk93TmFENnh0b2hJbGJsU1drdE54cmFVMG1mK2VRbmdlMnp5NUp4aUd3Y0p6RzZvVHBrQTRJVUZqa2pMdStXYWZHT1RhU3Z6Ym0vVEdMSkd2M05hRngxZGNaMGZ3QXlINzdmdm9oMFhvVm9JR0N3YzcwRmI0U3BOODFRejRvVkZmMXRLOExmWEprSHF4V2MyM29Zamo2QzQrU1VHUHVPOCtpZndOMExiY2dqNW1yUzFuNXFnWDlWMldVaEQiLCJtYWMiOiI5ZWQwMzFiZTQ0NjhjOTQwMGQ4NDgwZjNmNjI0ODczODNkMWIxZjNmZDU0NTMzYjIzMzUyN2I4NDNlYzMyY2EzIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:54:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkdkRFFGMmdkS3VLbXEzRTE4RWtmQUE9PSIsInZhbHVlIjoiUkhnd014YmhpR3pqeVFjazd5NjY0YXJjdUhSYU5kMXkvTEUzUXNuamNhSkUwY2FHTjBMN1h0Q0J0MUxQQ3ZtbkFFM2NOc01PRWtIWmM2NXZEK3ZkYTl3Y0JJd0F3bWFNK3lDelhvRENYRGlCYXhqMkx5S0p6MUdZamgxazgwUXEvL0RpOGFpbmY5azRqd2p0Mlc4bVVjRitSaFpTTzhNRXJuQUJiNFBCMVNRUWlvMy9IT1d1NnY0N21VWnFRbXFGTHkyWjFSNWZSRXh4N1MvM1BXTVZGWW5JcUVPSUxwQkg1L1NieDI1aStaa0VYVmdlSmQzNG1OR25IY3JmMllvYTZibWF6OUhjeWk0ZmkyL2dDd1JERVBQNFFBY3ZBL3pwcWdmYVJWN3ZLVGN6MzhyM3daWldBZGxGcXEzUktlSXlPYnhlQjIvYjB1QzcwTW5SUVh5cHh2WXozbTc3QXhNMWY0SzdOb0F5clYxMVk1NWZQNnNrWi81RzhCUG1rZWc5eUlNR3gwaFI3UUJ2RTczL0NneWRRbmdKZHFNNVZ0c1NtZXRPVDQ2MW5pK3VYK21xRnBxa1BEZVY1V0h1MGRKQmdPMm9WSlNQbURuUXdoTW9KMERvZFZGMWVxUjEvakpLWUtieVZ3YWNub25DQjhEQ09BWXhGSVpQbVVZbW9BWjgiLCJtYWMiOiIwMTYwMGJjMDJiMjlmOGZhMzc0ZjJkOTQzMGNiYTAzNzNiOWI0NWQwMWYwMzJhZWUwMGZjOTA1ZTkzOGE4ODA4IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:54:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>7</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ww</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">9.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>7</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>9.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>-5</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>6</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">&#1582;&#1590;&#1575;&#1585;  &#1601;&#1608;&#1575;&#1603;&#1577;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">9.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>9.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>6</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>5</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}