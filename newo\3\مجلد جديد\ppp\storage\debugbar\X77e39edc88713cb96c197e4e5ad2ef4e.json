{"__meta": {"id": "X77e39edc88713cb96c197e4e5ad2ef4e", "datetime": "2025-06-17 15:07:43", "utime": **********.878374, "method": "GET", "uri": "/debug/products", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.204233, "end": **********.878395, "duration": 0.6741621494293213, "duration_str": "674ms", "measures": [{"label": "Booting", "start": **********.204233, "relative_start": 0, "end": **********.752857, "relative_end": **********.752857, "duration": 0.5486240386962891, "duration_str": "549ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.752884, "relative_start": 0.****************, "end": **********.878397, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "126ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET debug/products", "middleware": "web, verified, auth", "uses": "Closure() {#1645\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#456 …}\n  file: \"C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\routes\\web.php\"\n  line: \"480 to 541\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Froutes%2Fweb.php&line=480\" onclick=\"\">routes/web.php:480-541</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.01803, "accumulated_duration_str": "18.03ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.814924, "duration": 0.00644, "duration_str": "6.44ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 35.718}, {"sql": "select `id`, `name`, `sku`, `created_by`, `type`, `sale_price` from `product_services` order by `id` desc limit 10", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "routes/web.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\routes\\web.php", "line": 493}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/CallableDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 240}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.8314931, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "web.php:493", "source": "routes/web.php:493", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Froutes%2Fweb.php&line=493", "ajax": false, "filename": "web.php", "line": "493"}, "connection": "ty", "start_percent": 35.718, "width_percent": 3.55}, {"sql": "select `id`, `name`, `sku`, `created_by`, `type` from `product_services` where `created_by` = 15 order by `id` desc limit 10", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "routes/web.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\routes\\web.php", "line": 507}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/CallableDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 240}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.8364549, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "web.php:507", "source": "routes/web.php:507", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Froutes%2Fweb.php&line=507", "ajax": false, "filename": "web.php", "line": "507"}, "connection": "ty", "start_percent": 39.268, "width_percent": 4.326}, {"sql": "select `created_by` from `product_services` group by `created_by`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "routes/web.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\routes\\web.php", "line": 519}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/CallableDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 240}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.840623, "duration": 0.00321, "duration_str": "3.21ms", "memory": 0, "memory_str": null, "filename": "web.php:519", "source": "routes/web.php:519", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Froutes%2Fweb.php&line=519", "ajax": false, "filename": "web.php", "line": "519"}, "connection": "ty", "start_percent": 43.594, "width_percent": 17.804}, {"sql": "select count(*) as aggregate from `product_services` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "routes/web.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\routes\\web.php", "line": 523}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/CallableDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php", "line": 40}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 240}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.847174, "duration": 0.00233, "duration_str": "2.33ms", "memory": 0, "memory_str": null, "filename": "web.php:523", "source": "routes/web.php:523", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Froutes%2Fweb.php&line=523", "ajax": false, "filename": "web.php", "line": "523"}, "connection": "ty", "start_percent": 61.398, "width_percent": 12.923}, {"sql": "select `product_services`.*, `c`.`name` as `categoryname` from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15 order by `product_services`.`id` desc limit 5", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "routes/web.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\routes\\web.php", "line": 530}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/CallableDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 240}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.854025, "duration": 0.00463, "duration_str": "4.63ms", "memory": 0, "memory_str": null, "filename": "web.php:530", "source": "routes/web.php:530", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Froutes%2Fweb.php&line=530", "ajax": false, "filename": "web.php", "line": "530"}, "connection": "ty", "start_percent": 74.321, "width_percent": 25.679}]}, "models": {"data": {"App\\Models\\ProductService": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 27, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/debug/products\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/debug/products", "status_code": "<pre class=sf-dump id=sf-dump-660003101 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-660003101\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-390039907 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-390039907\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1461018798 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1461018798\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1358446266 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2033 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; PHPSESSID=o1mek9etnn9ejsik46u9hjcosg; _clsk=17t60l2%7C1750172039209%7C11%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IncrbTlSQzNBb0E0dHVLdDhkcTFGN2c9PSIsInZhbHVlIjoicGw4T2dBaG9rUko1SlpRUnVFSnVKWFdRZk9WUlkybUREMDg1b1Jrc0hRK0Nlb3FXK3RwdklLTWYySHYrQjY3Sjd6ZzlZWTlSYVZkN21ZQ1hLWXYwMm83VmVwTkxQOUJZN25nZU85YzBqK1d4V1dXMVpxWW94Z1ZxQVRVWTFEZmlyY1J6MjZQa2FLTm5zam9RWFhkVjVoRzlqMThoSGQzMnZodUlPZmRGTmJ5QUo3WHA5dXkzemtEb25Od2tlQVdTbDhma08yQmVCcEVzdlRDNng5VVZnL1ZDRWI5c0VWeGFDOGtKQll5MzQ2NTM2S1AvbUJWZjRoT3piTmZKUUZETWo0endxSVEwYTJEdU5ZNVVTekFPVUtVelFOd3M2Nmk4NHcybmdNa1JkcDVFY1cxblo5cXBaaEFsS2ZDZE0rcnVVNzlHRFkxNTVyNTdzRks1UTVUeWJXMFFEM28xNy9pbnU1SzBZbWxqVG14bU1YWXFnSitVTzkwQ2NWYW5uMnJLOXhiQmc1cUNlZTRzbkI0a2pYUDlVdzJlelB4em53Yk9HK01WWS9zaUN6SnY1RGFHYkRBT3VlSkRYQTcvVmxaYnBTdWwwWlNqNmdSdkdXN3pBSnhOZjlmU0lVMTVGMTJ6eWREaXdqNkgreExYR2krMHhmclZsejIyTXVyaW0vMVkiLCJtYWMiOiJlZWZlZjgwNDNlYjRlNDM4OTZjMWNiNTQwZWMwNjY5YWUyYTg1Nzk0MDM4ZDllN2RkOWE1YjFmNWFlM2Q2ZjFjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Impqc2ZqSFNxb3BITW9oSUJLMnJCeEE9PSIsInZhbHVlIjoiemlsQmlIcFlVczE0djlWZHNXNnpKWFpMdlV4a0ZLTVhzWjB5TlB1ZUQrZWQ0S0FaSkUveGpMcTFGOWNmZXI5UXA0azNaVXU2emcyWFNFdmRvR0c0Z3duYzA2UXg0Z0c2cloxeWVRRlZBdWQ5aFBDTkZCa2toT3ZTSzI3ck9wUG5zVVV4TzNvTUlBaHJOVDJYaXd6Tkk1aVFId2JxQkpCNU9uT01yQzJidU9RWnhOZXl2SUhObld3bWtEdTBvakRicGZJb3p1VmpHWEFUQTJsSEozVVJNMjJjTU8zRE5mU2Z2Z09JcUlIc0VZNGd4amNUazlxQ3czS3Foeit0aXVjVXJFTUxteVN3ajNMeStkV283VnZHRFlpeG9rY0dmUVp0MlMzeFlNaFo3WU94V3dIVlFsa0RmMzNSbUtDcXV0TnpySE9rSVZMZVRNbzFLYmt1ZStFbjRIemNDNm9NdlN3YjRSQzBGcHZ3NmFjS0ZPOHdqazdhejVTM1pOWWZiSzkvbjN6SjArMytreHRpZTUwZUVCNTlScElVQ2FCTk8vT0dlVDY5dzVrL2JMTmwvcXdSUlFIN0tYT1NQM1gvckcvaExMbDh0RzAwMDJNbEJ4d2tHcFZyOFNZUXBIM2c5eWtBNkVOZXFmampVWTJrOUt2K29BazlQcTJhWXlIVXUwaDYiLCJtYWMiOiJhODA3MzQxNmNjOTE1ZjlkNjljYTA4ODBmMGU4ZDdmZDFkNTEyM2U2YjU3MzdlZTFkNWE2NzY4YzRiNjI3NzIxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1358446266\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1773661822 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>PHPSESSID</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HxnOnSuhAAPTfN2VS4q58slgJoytwtHYqaX0oLoW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1773661822\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:07:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjRnalZGOHd3Qkx2WmNyMk9McEpVV3c9PSIsInZhbHVlIjoiNWt5amdCejJMNU8xMlpicHVsRi9xV2E5TjBvbzViMUtzT09CTWY2RjlXNC9yWkpubUZxS1QxWEVEQTByZVJ6cUhLWVYrd09QYVY0TTFxOEpqYXVCc0o3Z0xXblZ2YkdISDJ5OUVtdzl5am8vZ2N2bDZPVVh5S0QzcWkyZ2NnQllnOGZuRTM1NXlNTXg4NmNTZkZJazZveFBBaWpZZGt0UCt1NVA2TTQyVVZHOUphcmpVV3haZGdnSFhRei90b0NlZElBNTA5THRDQmkvZlRvRHBEVkdoWklFUXVNenRURnUxWXF1NkdnOGVidTdmd3I1MHVHMjBWZnBlcHB0MCtzMzlXd0JTVlh0bXo3dWQ3eGFNa2hZaUJMSzVmOGphNC9GMGl5TnJNTWo4QVY1ejRkQUlCZnVETGc4c3JGYTBWdkRNZGw5WGd5ekZnc3FPNFBjQ2JBaUtOTmJlYzIrczZYNzZ4L0xLUy85TUN0aFM5ZGtMdWtvSndIeVpoejlqU1l0amZOSTJ5eC9nWmpOVzhaemxpNEpTc2s2UU5EZmlhZlk0T0k3a1ZlSWJPNitESmlUQ3JSeE9RL2tPTUxoTnFFekh3Z2xnd3N2M3p4djlpL29CZUdqS2dCV2NLdC9SazkyTHhKcFJJWSsvRSswT0FtdHl1UUJrSXJmRnpPdFc0RkUiLCJtYWMiOiJiNzBmMTk0ZWM1OWFlNWNhMDc2NDNjY2MwNDNhZmJjODhjY2QyYjUzNjlmYTMyMGViZmFlOWRiZWYyYmJkOThhIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:07:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjNJUHZYVHZKNFhqeG9zclkyeDRnaFE9PSIsInZhbHVlIjoiN1RDVi9ObzYrTDZKSlBXVGZRd0Fra0pBcnBjbVFnYzhkT2JOcTRvRHBsQXVCSjBvVi9QN3NJaXl4dVN6QjVLR0Y5ZUZ3MUpRdW9KdU9Tc21SOFZWblhLSE5SNjlNY3BmYVU4Y1l6NkJLZ3hXQjJPeUNXUm9tT0R2b0Z2ZzFoMWhZOXNtTjhoajB5SDRqdEVLRjdxTmtRRXFMM1BZM1ZJVlpNeWx3NDlaTEJVdThIc1pJZktwd2dqa2dnOVREYS83cjF3RHhxQWtKQkxTNGdIMUQ3OFB3QVByY1ltclBId3hqVHY5ditCNTF4SWZFa1pqSEFmYTlBOW5ZczFDeUt5VmtzUHVUa00vNnh1Qkhka1J0aE9uRVhySTUxNHg5ZGxJMXlndE1lbzQwQ2orWUIvcTNFYWlCOTNLYUZnZE1rbktUVjhTOXJ3SkJqcUFOd3M4WnRBKzhCMU5XMHBUT0N6bVh3RmdPR1VaVGZhU0E5SlFYODN2YmRWTHVlZU4wbnRVbE14VzlzNmVHTlZ5aVFSWmg5NkhFTjFIQ1JkOEVzUVduamlZNGprZVFwcGZsNFlReGRhVW9wYnVyM1pocEVTdHVOYkprWHZEM2grajNBOXpNanVZeVFXK0VrcVZ0c2ZWZWYwdVFoSFJSS0NsV0phektNWG9mQlpYSjd6Y081RHUiLCJtYWMiOiI5MTZhNTE3ZWFmNzlhMGM5NDU0YzAxOTIwYzVhMzJmZjg4Y2UwY2I1ZTQwYjIxNzUzMjZlMWZiYTAzYTZjZjYxIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:07:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjRnalZGOHd3Qkx2WmNyMk9McEpVV3c9PSIsInZhbHVlIjoiNWt5amdCejJMNU8xMlpicHVsRi9xV2E5TjBvbzViMUtzT09CTWY2RjlXNC9yWkpubUZxS1QxWEVEQTByZVJ6cUhLWVYrd09QYVY0TTFxOEpqYXVCc0o3Z0xXblZ2YkdISDJ5OUVtdzl5am8vZ2N2bDZPVVh5S0QzcWkyZ2NnQllnOGZuRTM1NXlNTXg4NmNTZkZJazZveFBBaWpZZGt0UCt1NVA2TTQyVVZHOUphcmpVV3haZGdnSFhRei90b0NlZElBNTA5THRDQmkvZlRvRHBEVkdoWklFUXVNenRURnUxWXF1NkdnOGVidTdmd3I1MHVHMjBWZnBlcHB0MCtzMzlXd0JTVlh0bXo3dWQ3eGFNa2hZaUJMSzVmOGphNC9GMGl5TnJNTWo4QVY1ejRkQUlCZnVETGc4c3JGYTBWdkRNZGw5WGd5ekZnc3FPNFBjQ2JBaUtOTmJlYzIrczZYNzZ4L0xLUy85TUN0aFM5ZGtMdWtvSndIeVpoejlqU1l0amZOSTJ5eC9nWmpOVzhaemxpNEpTc2s2UU5EZmlhZlk0T0k3a1ZlSWJPNitESmlUQ3JSeE9RL2tPTUxoTnFFekh3Z2xnd3N2M3p4djlpL29CZUdqS2dCV2NLdC9SazkyTHhKcFJJWSsvRSswT0FtdHl1UUJrSXJmRnpPdFc0RkUiLCJtYWMiOiJiNzBmMTk0ZWM1OWFlNWNhMDc2NDNjY2MwNDNhZmJjODhjY2QyYjUzNjlmYTMyMGViZmFlOWRiZWYyYmJkOThhIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:07:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjNJUHZYVHZKNFhqeG9zclkyeDRnaFE9PSIsInZhbHVlIjoiN1RDVi9ObzYrTDZKSlBXVGZRd0Fra0pBcnBjbVFnYzhkT2JOcTRvRHBsQXVCSjBvVi9QN3NJaXl4dVN6QjVLR0Y5ZUZ3MUpRdW9KdU9Tc21SOFZWblhLSE5SNjlNY3BmYVU4Y1l6NkJLZ3hXQjJPeUNXUm9tT0R2b0Z2ZzFoMWhZOXNtTjhoajB5SDRqdEVLRjdxTmtRRXFMM1BZM1ZJVlpNeWx3NDlaTEJVdThIc1pJZktwd2dqa2dnOVREYS83cjF3RHhxQWtKQkxTNGdIMUQ3OFB3QVByY1ltclBId3hqVHY5ditCNTF4SWZFa1pqSEFmYTlBOW5ZczFDeUt5VmtzUHVUa00vNnh1Qkhka1J0aE9uRVhySTUxNHg5ZGxJMXlndE1lbzQwQ2orWUIvcTNFYWlCOTNLYUZnZE1rbktUVjhTOXJ3SkJqcUFOd3M4WnRBKzhCMU5XMHBUT0N6bVh3RmdPR1VaVGZhU0E5SlFYODN2YmRWTHVlZU4wbnRVbE14VzlzNmVHTlZ5aVFSWmg5NkhFTjFIQ1JkOEVzUVduamlZNGprZVFwcGZsNFlReGRhVW9wYnVyM1pocEVTdHVOYkprWHZEM2grajNBOXpNanVZeVFXK0VrcVZ0c2ZWZWYwdVFoSFJSS0NsV0phektNWG9mQlpYSjd6Y081RHUiLCJtYWMiOiI5MTZhNTE3ZWFmNzlhMGM5NDU0YzAxOTIwYzVhMzJmZjg4Y2UwY2I1ZTQwYjIxNzUzMjZlMWZiYTAzYTZjZjYxIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:07:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1245600424 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://localhost:8000/debug/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1245600424\", {\"maxDepth\":0})</script>\n"}}