{"__meta": {"id": "X3f010072aee70dcae2b3b068c05efad5", "datetime": "2025-06-17 14:39:08", "utime": **********.626095, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750171147.670416, "end": **********.626129, "duration": 0.9557127952575684, "duration_str": "956ms", "measures": [{"label": "Booting", "start": 1750171147.670416, "relative_start": 0, "end": **********.473961, "relative_end": **********.473961, "duration": 0.8035449981689453, "duration_str": "804ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.473978, "relative_start": 0.8035619258880615, "end": **********.626133, "relative_end": 4.0531158447265625e-06, "duration": 0.15215492248535156, "duration_str": "152ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46118304, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.025939999999999998, "accumulated_duration_str": "25.94ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.546812, "duration": 0.023719999999999998, "duration_str": "23.72ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 91.442}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.590518, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 91.442, "width_percent": 4.549}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.607764, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 95.991, "width_percent": 4.009}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6", "_previous": "array:1 [\n  \"url\" => \"http://localhost/inventory-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-922654511 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-922654511\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1957626273 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1957626273\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1340876555 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1340876555\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-804558903 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=3pzjoc%7C1750171139944%7C6%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlRJbVU5RGFQWk1hUlNLV2xoNlUxVmc9PSIsInZhbHVlIjoiU3REOEN1RTF1UllWUmlGR0R1Sk5Cd214MGNyL3BmM1VCcWwxUlVGMU5aRjgwN3hUR2paaXphTkJGSUJSOEJWc21HTzRjTWs4L0FWYmRvMnZ0dkVDSm4yNm8zK2pKKyt1a0Q3NUEveUZkbnRNV1QyQlpkLzFzbmVDWmNwRWxNZjV3SzlDQ3NYNHQ0Vkk4VzFuTDYvOEQ5bmJvL3o0YzdJR0Q1ZHRqU0N1OUpzdkZTSWRzUHQ0ODdzdkp5UU5xWEhzVytnY3p6czFmY0lEUGVIYWlQY3NCK0ZHOUt3VlErSVdNbWZldGN2bkgveC9RcnBoUDJPVTNoR1lXdTFISElDamx3eTdLeElrTG9RYlE5cW1wNlVVL3NVY0ptUmJ3SloyQWE5WG9scXNyK2w4YXZrWVdJdTZXUDl2Z3BPUGNXdkozWFdKWUk0dlVDVWNKOWl4b3puc3h5QWswa2s2ZVVqYWw5ZG1Vb3ZHdk5ubGJ3cGhkOUVxSlFtSHB6OW1MQnJVQnRUNFhJRUhNd2xnaEVaU1M0VEF3TVdQK0laWFhMSnZnTW91bGFoemcxcEJ6LzBFbzdvekFQb2VzdUh4SGsrMEJtWDVyRENFTVpnZTI4aGJrZnZwWi85alNBZVBQQUFHNnJyY2t3Sk9EYVNKNVovMnhhS1lWUmNtWmEvb2wrWWIiLCJtYWMiOiI1NjUwNzQwMzNlNzNiZTNiMzRiNWIyNTdkOTkzNTQ4MGIzZTk2MTNmM2E4YzBjZTlhNzBlZDk2NzQyMWM4MjlmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkNad01ha3E5YzVPZ2U3NFRrc2dvMVE9PSIsInZhbHVlIjoiR2tlNHpHakVrdit6eklsQ1NMMkczdkVKYk9vZVlGV1hOUG12Smd3Sm1VSExnZVNOUi9iTDdyRTBZdzg5eWpDNlFvNU8ySE1SWURUa3JsVlJrM09mVXd0UUNoMms3QXdXelY3RC9rWUFGeHFhYnlSeGZNeGdsN1VKRU5FTWI0MFo4L3lqcjErd0kvZEpqUm9UZWJKUzQ2cDEwUHN3alJ6di9OVjlZVkJiZUE5VnRBQ3RZbHJURjg4TExieHdrTFhTOEZWZnMyS3ZHckpseXlRR1llN3F6NjZWSWY5TEtLQ3NzTXowVUZFYzFjb3RoaE9jRU0yWnZ0UEtTMmpyMWpacUlPNkVSa29sR1o4RUNZa09aN1l3NkkrQ3psaW1vTkNhQUIxWjZqR05VY1BiYzF1ak9qaWhNczZyUWNOT0FValhHdW9SRy8yZUFBY09sYW5UT1B2T0Rqdld1WUdGSEgrK244a0ZxS0hoZmN2NGo3a2JNeS9HbWpxL0FoQnByeGNQMG14Ym0zYVh0Nm9xTmZRNXpjYmxLcFpZYVFyUDg0bVRRVUtGODFPelZJMklnRGsyTTdYck9GVVRrUWpES3dTZ2l6STFPdmZQRWp6cktmbmhBRnVEc3loQmNMdk5lajJCM1RHMVovS2ltVldERVdxVTNzbGdUcTNpKzErQjJJL3QiLCJtYWMiOiIyNTMwNzYzOTRmMWRiY2YxZDA5MTA4ZTA4OGFmMTljODhjZjQ4YjAwZjRhMjRiY2Y1OTBjZTM1YTdmZTY0MDc3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-804558903\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1103293927 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HxnOnSuhAAPTfN2VS4q58slgJoytwtHYqaX0oLoW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1103293927\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2133759537 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:39:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjdQdWVzb3ZVWXRMM2wrY1NEZ0VOblE9PSIsInZhbHVlIjoidldscVNHQ1N0ekhtNkxsb0FrUW0vTDdYQjFoWCtsdDJPNStDMWlFLzVubndFNjdSaFhONExiY0FSMCtMbys0ZkZnMi9lek9PVkJoTC85NXhJYUJ0V1liMHI3ODU2by9pSGJEVW9aU0J3ZUZwcW4waU5jcW5hb0J4cFFyMGsyWEdNc0pwL1k1dHdqRCtDcm5hK1VaOFJ1N1Z5a1pBUmEyc2l4eTNCODRKT3l5Y2sza2pjeDFWKzBKeW93aWdBbkZrOXFPcjllbDlTQjVVTDM4N245S0VXNmxQNXRINzlPTUJqSm53Si81dkh5aVRrZ3cvZDlZNkFPNFZZVVk5S3BmN1VpL1gxcE9qcFRiQ25wNzhtTUFYdkloaEhYMHkwYXBjbzQrRzdKQzNIUTB0RWNiN3JIdnUrdVdub3dzOVdmR3JuWmVubFVYYlNhSmJZR3gzN3JJZ3hSSTV1UEpsZXdnaHAxU1ZCMVNWUzdOZlk1ZS9Rb09DNVllOVlxek04Znc1b1BYV2I2QklPZXZVRmdjcjhjMWJXVHZraHVueURCYWE0TGVBYWx4d3VOb2ptUXRFSmVnRDhaTmkxa2NMYnhmYnYxSXBVblBMUzVzcFhNd29FbFNhbVdwZ2FsZTBINHlScmdlYllPVktQK1FLek5WajA5T1o4QU54YTVqaHBrOE0iLCJtYWMiOiIzY2EzMTRhNzBlMGI1MDY2YTQ4NWVmZTY1NzJkNjlhZTBjZjI2ZGMxNWFiYTBkY2ZjMjQyN2ZhMjI3MWQxZGZmIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:39:08 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InlRVTQ2VlMyTFNrOU1yQStBSkJoSXc9PSIsInZhbHVlIjoiQ2FhSGs2MW5aL1F6Nm56NWs5b1hoUlo1TDZTL1o3eGhSaDJaeVN2MnEwY0NsWU5XWGhHZm03YlVYQ2JGS09UZ04wUWVYQkhGcmlVak9tcjBucmhxUkdFcml5b2ZCS0VXUHoxblZLVzhGK2xyc1FnUktOWEdjMEQ3RWcrendoN1IwMXhLT05MN3pjcEJRN2NxNWpYUTRpbnFySE05YzczdGhFaGpLNUF5Q0ROZGlNYzFpRTRSbkhHQ1Frc3J5OHNOdzBlU2pKR2Z4YktqbmtLMWQ2NllSdkZDZ0RPQVdhTkJ0VmNUMmhydllDay9hWGhQMXJqWUxqWWNxOS9DNG1VWnFxVWpVdXBBNzd1a3U0ajI1RnRpY3JqZGRZaEZsOWpyQnJMNXppdm1IaWNrWFJYcTZZR2ZYNHNtQ3VSSVhZVkl2Zm9haU9Lbm9YU0hRL2JoWHlrRTVrVlVuMWhQblNCQkpEZ3lIVlNlWnpNdzRMZnY3RDQ5SU5vTm0wY05VNW5od1FQL043aUxGVjY1WVBrMTR6eVU3R0lIRWpOTlA0UnZBMVUxeDgrZEF0dUdIcGppSFdUOXVMaHR5N2t0QkV3M3h5TGlOKyt4WkdkdmtiSUJSTytWeDlEaWwrZWNXRjlhTWQ2YWZiTnZMYkF3NzNKQnBEU3NxMzY2MUhKbWpzM3YiLCJtYWMiOiJlYjM0NTFjNjRmY2E0N2JhNzM2YjAzN2Y5MzkzNmYyNTg2NzgwNmEzOTAxOGQ5ZGVhYzY4MWU1MWI5YWVkNDRmIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:39:08 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjdQdWVzb3ZVWXRMM2wrY1NEZ0VOblE9PSIsInZhbHVlIjoidldscVNHQ1N0ekhtNkxsb0FrUW0vTDdYQjFoWCtsdDJPNStDMWlFLzVubndFNjdSaFhONExiY0FSMCtMbys0ZkZnMi9lek9PVkJoTC85NXhJYUJ0V1liMHI3ODU2by9pSGJEVW9aU0J3ZUZwcW4waU5jcW5hb0J4cFFyMGsyWEdNc0pwL1k1dHdqRCtDcm5hK1VaOFJ1N1Z5a1pBUmEyc2l4eTNCODRKT3l5Y2sza2pjeDFWKzBKeW93aWdBbkZrOXFPcjllbDlTQjVVTDM4N245S0VXNmxQNXRINzlPTUJqSm53Si81dkh5aVRrZ3cvZDlZNkFPNFZZVVk5S3BmN1VpL1gxcE9qcFRiQ25wNzhtTUFYdkloaEhYMHkwYXBjbzQrRzdKQzNIUTB0RWNiN3JIdnUrdVdub3dzOVdmR3JuWmVubFVYYlNhSmJZR3gzN3JJZ3hSSTV1UEpsZXdnaHAxU1ZCMVNWUzdOZlk1ZS9Rb09DNVllOVlxek04Znc1b1BYV2I2QklPZXZVRmdjcjhjMWJXVHZraHVueURCYWE0TGVBYWx4d3VOb2ptUXRFSmVnRDhaTmkxa2NMYnhmYnYxSXBVblBMUzVzcFhNd29FbFNhbVdwZ2FsZTBINHlScmdlYllPVktQK1FLek5WajA5T1o4QU54YTVqaHBrOE0iLCJtYWMiOiIzY2EzMTRhNzBlMGI1MDY2YTQ4NWVmZTY1NzJkNjlhZTBjZjI2ZGMxNWFiYTBkY2ZjMjQyN2ZhMjI3MWQxZGZmIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:39:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InlRVTQ2VlMyTFNrOU1yQStBSkJoSXc9PSIsInZhbHVlIjoiQ2FhSGs2MW5aL1F6Nm56NWs5b1hoUlo1TDZTL1o3eGhSaDJaeVN2MnEwY0NsWU5XWGhHZm03YlVYQ2JGS09UZ04wUWVYQkhGcmlVak9tcjBucmhxUkdFcml5b2ZCS0VXUHoxblZLVzhGK2xyc1FnUktOWEdjMEQ3RWcrendoN1IwMXhLT05MN3pjcEJRN2NxNWpYUTRpbnFySE05YzczdGhFaGpLNUF5Q0ROZGlNYzFpRTRSbkhHQ1Frc3J5OHNOdzBlU2pKR2Z4YktqbmtLMWQ2NllSdkZDZ0RPQVdhTkJ0VmNUMmhydllDay9hWGhQMXJqWUxqWWNxOS9DNG1VWnFxVWpVdXBBNzd1a3U0ajI1RnRpY3JqZGRZaEZsOWpyQnJMNXppdm1IaWNrWFJYcTZZR2ZYNHNtQ3VSSVhZVkl2Zm9haU9Lbm9YU0hRL2JoWHlrRTVrVlVuMWhQblNCQkpEZ3lIVlNlWnpNdzRMZnY3RDQ5SU5vTm0wY05VNW5od1FQL043aUxGVjY1WVBrMTR6eVU3R0lIRWpOTlA0UnZBMVUxeDgrZEF0dUdIcGppSFdUOXVMaHR5N2t0QkV3M3h5TGlOKyt4WkdkdmtiSUJSTytWeDlEaWwrZWNXRjlhTWQ2YWZiTnZMYkF3NzNKQnBEU3NxMzY2MUhKbWpzM3YiLCJtYWMiOiJlYjM0NTFjNjRmY2E0N2JhNzM2YjAzN2Y5MzkzNmYyNTg2NzgwNmEzOTAxOGQ5ZGVhYzY4MWU1MWI5YWVkNDRmIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:39:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2133759537\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-92675054 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-92675054\", {\"maxDepth\":0})</script>\n"}}