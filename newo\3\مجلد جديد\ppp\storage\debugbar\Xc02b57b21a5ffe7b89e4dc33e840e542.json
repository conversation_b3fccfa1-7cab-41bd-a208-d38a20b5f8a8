{"__meta": {"id": "Xc02b57b21a5ffe7b89e4dc33e840e542", "datetime": "2025-06-17 14:47:59", "utime": 1750171679.02857, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.423998, "end": 1750171679.028604, "duration": 0.6046059131622314, "duration_str": "605ms", "measures": [{"label": "Booting", "start": **********.423998, "relative_start": 0, "end": **********.932858, "relative_end": **********.932858, "duration": 0.5088598728179932, "duration_str": "509ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.932869, "relative_start": 0.5088708400726318, "end": 1750171679.028608, "relative_end": 4.0531158447265625e-06, "duration": 0.09573912620544434, "duration_str": "95.74ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46091264, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.021969999999999996, "accumulated_duration_str": "21.97ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.973645, "duration": 0.01911, "duration_str": "19.11ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 86.982}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": 1750171679.006068, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 86.982, "width_percent": 5.098}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1750171679.015939, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 92.08, "width_percent": 7.92}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6", "_previous": "array:1 [\n  \"url\" => \"http://localhost/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-210962444 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-210962444\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-484206106 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-484206106\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-141801804 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-141801804\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1056788510 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://localhost/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=17t60l2%7C1750171674402%7C1%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjhLR3J2NFI2SkhUT0ozSzdMTDc0Wmc9PSIsInZhbHVlIjoiSUs4eTVKa2RtZ3l5N2NhRzlXSnVZLzdBWEtNQWh5VkFMaDA0MGhRL2FtNkc2ZVQ0WXdGRlR5dEhHNjFCeG8wMjNSQU1JMnRUVWxhYzBmMVJ0ZmxEUHhrT3dzWUdFb1NiQUVyRS9xWXpqNmQvZ3ZLSnhWRmlNenNsT2sxVmZBTkRRN3gyR3BEZkdQVlNNamRxNFdoNTFpb0k0U3VoZ3E2OS9qczdIcUZQMEhpYmFKakFNZmNlVXNWK0dtS2xDMzRXeHVnWlFoTzhTV2M5dmwvNG5kN2tBZ2cyQVR0TFJ0RGhuelcyNERRd3hQSjhqTjYrLzV0ZjJnWmZTQnhtbERneUwycU0zWEdwNHp2bEZOSHJlelRWbzdLMTZJdGRiQkZBL3BQNmZnNkFWMFNhS3B1SCtmcEl6RjF1VnlyWVhyM1ZPY2ZlUmkwTEVLdzRuQklwYnpsSHJFUFY2UmdId08vUlB2REhTRk9KSHpQOEdUTU1NZU12T0pQVXpNQThWWTZLenFuYXNrU2JUMi95ekhnR2hZS1Y3WlBjakJVWURkWkUrUWk3Tmp0S25JSGRRcmxRUHZ3TnRSUUpWUDAyK3NIZGpQclV1N0NlU2Z6UTFlUlJ0R2VTLzByeWxYM2YzdjE1U3ZvblJXK0FyMFpaMVgyemNGSTJMaG5ZL0JTZFlLZUciLCJtYWMiOiI2Y2NmNmRiMzNhMTc3OGM4MGNhZGM5YmQxMzM5NDIzZWE2YTBjZWRiMDY2OTg3MWQ3NDA4YTM1MWMwNjZkMzU4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjFOakdQUWdGRVhXdkp5WXBCU0tQK1E9PSIsInZhbHVlIjoiUWNiOEFBOVhzc1ZQUnRRcGc1WDM4cmljaDMxaEZlTW1JSGtMVWhxRmJMOUova3lUVzI3c1NxblBSS2g4d1FqQklRU0x4OUdWNENFaUhGaVhHTVE2eFRWUWhoNUk1ditWbXBQblNPcFhoQ1QzdVQ3YUpRck9oME5RQ0I1dHRHQTlvZXpTTWw0R2pkNm1PcGtEQ0hYcTZWd05uckJmc3hjWmtEOXN5NENVOWo1Q1pIVXYyUXpFdmxPYkhVazNCU2p0ZURpc2dPRnYrRkxCWllGdmNEQ3VmZWcvSGJncEtjcjJnQWt6T2Zrc09PT0tLNTQvZ3RzY0VIb3l6d3V6OFNOeFp0WG4xZGJNRm9DSndGUUlNY3JzNXZaMnV2c3hXWll1Qm04QUJxSXBhRWxJR0duamIrdUhDMDV1bnVqZGdzeWdxSVZTR25JcXl6V1V6T3ZIWld3N3kyTjRDOWxRT0pTZmVZTEovTjFkS1crMi9TcEgxc0plaHFhaDlDVEdLQ3p4QXd6V0pXU21QS1RuVkRHQXUzTWtHRTQwdW9yYzhyWTJnYjd3SlpZemh6em9RblI5V2JvelQrWnkxTjYrR0hOeXZFR2RZOFBxRFlMNDFQWEtydGo3VTNsWFlBNm9GVlI4c2M0SklFM0FFRWN4alRsY252bWdxaGFzeFlMdG96cmYiLCJtYWMiOiIzMDc3MmJhMmI4OGIwOTkxNmU1YjU4OTdjNTEyNmNkNmRmYzU2NjI2MGY1ZDc2MjdiNDk3ZDE0MmE1ZGM5MTk1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1056788510\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HxnOnSuhAAPTfN2VS4q58slgJoytwtHYqaX0oLoW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2056105487 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:47:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjlpWEJpSzVyTzVWSjVsVFB0bFBDN0E9PSIsInZhbHVlIjoiZTZyWDF1K1BUcFlnSGpIbHVZSnNtQ3ZCVitBSVFLUTJ0WFE3eUdWZ0xZSjhZdlZDKzBMTjVmc2NVUm84VHBkZUdaNk0rMlRZSzYrNktQNmUvc01OZlM3UFhvN2tKNjBmUGNMdTBuS1NTQzVnRHFNUTVZSHRkaDlBNm1HSnFWdUhiMklaME03c3JqSnFhdldlMzk2UmJRaWZQRFdISTRRa0hTbEs4dlVkL2dOWVlZNmV4a3pNNDJuaXZIMzRsRW91TlQyUi9lcm5PUnNKQzdtM1AybU5RUVFUK0llWkVKNFN0MU5kMFFjME5RZzd5WDg4UkYwME9xQlJab21iam1SdVJ1VVQxUm9NblZqb0NRWk5YeThMRVZ2d0ozRHJjTktUYkdkbWtMQUZqaEprNzh5b1paQUkvd0lPalF6R0dSOFVYMno2b2U1VTJWQ0dGbWNqKzk5aERvWTlwcWNHWXllaHR3YUtRM1c4OGZRODM0c1VpY0ZMbnJlTVQySjlsSHpvK0R6bDNoYlBtYTI1VENveTJ5MDhlakxQY0ROa2JwRGQydHMwQ3ZNMlptS1ZkcWh4UWt2K08weCtLY2tMOC8yUGpoaGxtSG9ra1NJMUdHNGwyMHlUeDZCdUFMM0FHcVRHM1RMS1VoOGhWSVk3cExoMEtGWXBwZy9Ud0s2WGJNMUgiLCJtYWMiOiI5OWJhZDk2NmYzN2I0ZGJmMTYyMzBmMTkzMDM2NDA3MzMxYmE0NDllMWQwODA0ZTg5YjQ3NThjYThlNjJlMzA4IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:47:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImRNdXdCRXoxaXJ6SWR6bFIxRXJpdXc9PSIsInZhbHVlIjoiL1I3QUttSkFRL2VBTGU2dHdIQXBjc09YTVNuMjRUUnFsMmRtVG1TTkEzVHF0SUdBaDhNcjdIQnZxN1BOVUxvRkYrZlZlRHpvbGoxL3g3WnN3OGZveVVnLzNGSUJaRDE1MlQrYzlYc1p5b1hPUWYzVGduWnRYVlJMZktZK1dQOTUrTCtKL0pYUFdKR3cwWDk2aDZJRG1FUHJQRHNLa0JKV2djUGFueFEzMVdsakFmVEZmM1o2bG16RUlPYmdjWWZKUlZTNkMzUUVteHBqU3I4eTBncHZHeW0vOXdVZHJ1c3dYNG92YUdETlBRZXc5bUh1c3ZTS3pWcU5xNkNSZG1xMTJCTW4wNVBhYjgwQkt0WE9seVVENlNadnRucUpRS0E3c1l0YXpRdHlzNG10bjFEYlV3ekpzUURETHB1TXErMXZGdzhPYytiRTY0U3lrQWZ4c1RuaC8yVkh6ZjRLUS9zR1VwbWxWRUVUUXF6RW9DL0tTVGwxaDRqaFdpMkhzSUJ0NFZHN3NFckdEanl1cVVZN0Z0M1VTajZUYlk1WCsvTTdXenJQQjdmbkYveDM2R25lQjdRclRxWEZPK0hSUWQ1dEVYOG0xVFFTM0pFendFWUZTalpjanB5WXNjWTh4blpGc2hIR2FnK0duYmxraEtURlAvVmptdjlLSGNieW80THIiLCJtYWMiOiIyYTg0M2U0ZDM0MDBiZDY1Y2Y1ZTE2OWUwODkxMDBmMWFmODg3YzUyNzExZWZiNzZjZjc5MzU1Nzg4OTY5NTA2IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:47:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjlpWEJpSzVyTzVWSjVsVFB0bFBDN0E9PSIsInZhbHVlIjoiZTZyWDF1K1BUcFlnSGpIbHVZSnNtQ3ZCVitBSVFLUTJ0WFE3eUdWZ0xZSjhZdlZDKzBMTjVmc2NVUm84VHBkZUdaNk0rMlRZSzYrNktQNmUvc01OZlM3UFhvN2tKNjBmUGNMdTBuS1NTQzVnRHFNUTVZSHRkaDlBNm1HSnFWdUhiMklaME03c3JqSnFhdldlMzk2UmJRaWZQRFdISTRRa0hTbEs4dlVkL2dOWVlZNmV4a3pNNDJuaXZIMzRsRW91TlQyUi9lcm5PUnNKQzdtM1AybU5RUVFUK0llWkVKNFN0MU5kMFFjME5RZzd5WDg4UkYwME9xQlJab21iam1SdVJ1VVQxUm9NblZqb0NRWk5YeThMRVZ2d0ozRHJjTktUYkdkbWtMQUZqaEprNzh5b1paQUkvd0lPalF6R0dSOFVYMno2b2U1VTJWQ0dGbWNqKzk5aERvWTlwcWNHWXllaHR3YUtRM1c4OGZRODM0c1VpY0ZMbnJlTVQySjlsSHpvK0R6bDNoYlBtYTI1VENveTJ5MDhlakxQY0ROa2JwRGQydHMwQ3ZNMlptS1ZkcWh4UWt2K08weCtLY2tMOC8yUGpoaGxtSG9ra1NJMUdHNGwyMHlUeDZCdUFMM0FHcVRHM1RMS1VoOGhWSVk3cExoMEtGWXBwZy9Ud0s2WGJNMUgiLCJtYWMiOiI5OWJhZDk2NmYzN2I0ZGJmMTYyMzBmMTkzMDM2NDA3MzMxYmE0NDllMWQwODA0ZTg5YjQ3NThjYThlNjJlMzA4IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:47:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImRNdXdCRXoxaXJ6SWR6bFIxRXJpdXc9PSIsInZhbHVlIjoiL1I3QUttSkFRL2VBTGU2dHdIQXBjc09YTVNuMjRUUnFsMmRtVG1TTkEzVHF0SUdBaDhNcjdIQnZxN1BOVUxvRkYrZlZlRHpvbGoxL3g3WnN3OGZveVVnLzNGSUJaRDE1MlQrYzlYc1p5b1hPUWYzVGduWnRYVlJMZktZK1dQOTUrTCtKL0pYUFdKR3cwWDk2aDZJRG1FUHJQRHNLa0JKV2djUGFueFEzMVdsakFmVEZmM1o2bG16RUlPYmdjWWZKUlZTNkMzUUVteHBqU3I4eTBncHZHeW0vOXdVZHJ1c3dYNG92YUdETlBRZXc5bUh1c3ZTS3pWcU5xNkNSZG1xMTJCTW4wNVBhYjgwQkt0WE9seVVENlNadnRucUpRS0E3c1l0YXpRdHlzNG10bjFEYlV3ekpzUURETHB1TXErMXZGdzhPYytiRTY0U3lrQWZ4c1RuaC8yVkh6ZjRLUS9zR1VwbWxWRUVUUXF6RW9DL0tTVGwxaDRqaFdpMkhzSUJ0NFZHN3NFckdEanl1cVVZN0Z0M1VTajZUYlk1WCsvTTdXenJQQjdmbkYveDM2R25lQjdRclRxWEZPK0hSUWQ1dEVYOG0xVFFTM0pFendFWUZTalpjanB5WXNjWTh4blpGc2hIR2FnK0duYmxraEtURlAvVmptdjlLSGNieW80THIiLCJtYWMiOiIyYTg0M2U0ZDM0MDBiZDY1Y2Y1ZTE2OWUwODkxMDBmMWFmODg3YzUyNzExZWZiNzZjZjc5MzU1Nzg4OTY5NTA2IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:47:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2056105487\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-991992934 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://localhost/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-991992934\", {\"maxDepth\":0})</script>\n"}}