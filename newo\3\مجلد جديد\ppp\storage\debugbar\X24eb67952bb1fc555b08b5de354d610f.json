{"__meta": {"id": "X24eb67952bb1fc555b08b5de354d610f", "datetime": "2025-06-17 15:10:26", "utime": **********.023741, "method": "GET", "uri": "/customer/check/delivery?customer_id=6", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.434036, "end": **********.023762, "duration": 0.5897259712219238, "duration_str": "590ms", "measures": [{"label": "Booting", "start": **********.434036, "relative_start": 0, "end": **********.948224, "relative_end": **********.948224, "duration": 0.5141880512237549, "duration_str": "514ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.948242, "relative_start": 0.5142059326171875, "end": **********.023764, "relative_end": 1.9073486328125e-06, "duration": 0.07552194595336914, "duration_str": "75.52ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45020000, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01639, "accumulated_duration_str": "16.39ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.992428, "duration": 0.015619999999999998, "duration_str": "15.62ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 95.302}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0133, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 95.302, "width_percent": 4.698}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-461570495 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-461570495\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1350325298 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1350325298\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-136624671 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-136624671\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2072032531 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjE4SmpQRmZhSW5tcnZvMWtUYlVkeXc9PSIsInZhbHVlIjoiL3RiVEJlbEtjS3dBdCtqdGp6TXV5aS9wYThyZnZhTElUVFhOQVdJQnk5eWFUV0MvRmRuaHhQVUZESi83MjJVYTd2T2N5Q0VJQXlVald6NDQyMjI4clZHNjNvTm1nL3hDNUJpS1ZKNGpKVzJ3Skw4bElCL1AvQnM2VzVac1NON1VJNXc0a0FXdHRIUklyMTlmby9LRjNuOXRHamdTU3pRbHdnNi9kNGk4Yk1lUGM5b3hEWUZKQVdkcWduY3c3QmI5UHFzVXA4OUluZ0pnbWJmSGJuVkk0alVLbTk0U1JSc1JYWldxTVQ5T2JzdU5hNjNsWkh0eW9Ra2ZXMGJPaVQzNHhkV3JyNjhHTkpRMm4wdVk3WURDQXIySlYrZGlsMzVudUVjVzFpakQ5eG5veFFNTno0WkNvaU5NcVBBTSthWURITHRSZVRDaU9GR0tWdW9QYWV2R3YxVkVOaUFMeVZxbEh3S3RWWlZtcEx2RGFadHg3QnNwamNjemtMKzJQL05ObTRqY2NqY0NjZkcrRzJPQ2pURkN6aHlLRXVkNmtWaFBMR1pjUTdVNlBwdzF1aVZCQTNadVNuMXVrOGZXUFBaR2dQdTVzRzJFcndsdWtEb2NHbjRpVk5hbHM3aGZXQm82UjNXMi9GK2FweVhMVUROYnJWY0phSkFCczhBTlFPM2kiLCJtYWMiOiJhYTA5NDAxZTgyMDg4ODA2YzcyNTU4Y2FkMzgyYzU5N2FmNGFiYzFmNzE2ZTE5YjAxMzI4MTE5ZWMzMjJmZTA2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjRjeHlqWTh2RXhqbmJydFVqUURkNVE9PSIsInZhbHVlIjoid25QZitWSkZxaEdlUSs1VFJIQXdiSlJPa0lWeFNFSDdYQmRwZ29obU9ISzhiUVNmSVBYZkZLQVRhK1Q0RFlWUk1kVlJkTEorb254c2VqM1NVNDl6V2FRVDJOVi90TXpDRE5ia0dmT3RpdzRPdmh5VXVFa3l2N2VSSTBCdWg2Zng3RCtQZ0ozZThWMWpQQU90QXdxU0xCa3pSZTlkQ1Q4R3ExNkNmREdFWlJlNGs2V3YwcHVJaG02L1ZSNzh5OGhKMzUvTmlNYUdIcTY1VWl5cVFTZWViM3c5Z2NCRzdQS1RFcVF6OFNsVmcxMXVUMjBZVnJ0QkpCYWpiL0s5dDJiTGhJZUdJdG04M0ZjVmU3MmlEWDZPM2kzVFVyTndNMm9oRE82RFEvZmhuTU84dVVkZnNLblhsSitTMkVwUjJpa2F1ejFYZE9udUJhUkVqWWVCRWFsbWQ1am9vMWp3SGN1RHNXdFd0STBOeHd3TitlU0dvOEtqdzJCNC9wSVAwSWNITVdXakhWQ3hxVkZlQUszNHdZTExDMmlzNmRWNEtUMUtmYzdtTStBUnoyM0xrOE16VHdMZDVRRFFQOXcxdHN3WExuWFRMZnVuWlRSSFdEZUdtN2Q0R0NrMUpBM0VSOUZDM2I1R21XcHV4cUtrTEp0aUpkQmFoZUx4VmRnQ1cwdWYiLCJtYWMiOiI5NzBkNjFjNDlkYWZhYWRiMGQxMDY3MGZhYWVlYzViMzRlNTk4NzM1MmYzZWUxMGMzNjMwM2M0MWM0OGUyMjA2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2072032531\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1239407005 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1239407005\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-100837426 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:10:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkdwTVQ1U1ZUV01laDQxY1B2Y3kwdFE9PSIsInZhbHVlIjoiZ1YvOEVCek9BU2VwT3B6amd3MjRVQ0RNd2orMG5OUExXc3Bpb2ZFeFNsblB6ZUZTRW9mVzdJUCs0R3g5VG1tRjVFanh5Q09adVl5Vm1wT3V1WmJXMDgwSk1MbFluR1JUNittcnZmS1Q0MlEvRlY3a01GUDNHV1N2R2U0cloyNHBBZ0lFRXRoRTBXQ1Qzb1lCSWZhbW1jVUpQaUdKWGt5TDRNSUN3RHhNVlIyN1ZuQzNDOHIyOTA3ZWtmdUQwd1Qxbk5HT1d1UjBBMy9BMmdmY1lPWU04SEg1L2RqZHgwTUFmcEE5Vkd0WlNzaGg3b09YVlc2QzRJNktnTUxqUU44S2ZWQmhkbEE1a2hJbXVSNUxzUnZ0SWd2SXl4d1ZBQk9jVTcxOWtPcnRaWG51d0k3R3g1UzEzaHNHbWptUFlKL1FVWmdCTDg1OTZNT2d6UmtCMDM2UE1rNFNaQm1pWit5cWI4b0VmcUVNbXhoTWpzZTdvL3kwRVJQem40NWpLNVlBRlhSbmUrVC9GbkxnaXl5YnRiTjcvaTZXVjAzdURqQWlDYkpmSXo2dTdVS3kxNmlCNEpIOHh5SlA1VDFScWN3eGoyZitmdEw2YlBvODlvRXZKT3FmNWkwMVNUWWtWQU43NisrWGQwekErWXVrS09leUVPOFk0UEZ6ZzZKUFdWZjMiLCJtYWMiOiI0Nzk3YWQ2OGI2YjdkMjI4ZDcyNGYxMmExMzRiYTQwMjc0ODcyMjkzN2EyMWIwNWQ0NzVlNTM0NDdlMzg1YzQ1IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:10:26 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImFLMnFzb3IzQmE3WkJwelhiWWI2QVE9PSIsInZhbHVlIjoiWjhMZUR0SXMwUUMrWldVUE1xRVdkTmNZYkxKTktKSU14TCtPRUNOM09FTWpwc2VITDEvSklTd3JhemRkTDR2WERUMGtUSW1PK0dFVlVGZ09jeW5xMlc1M2daakNCaGljdG9mZFk2QytkQWMxdEczUWlIRWZxTkRuTVdFQ0ZObDJwR01YTHQwaEozckxxVW40UFp3blViLzRwU2hVOWVYM2g4TlNJSjNHY0pNYWl5TGtMUnkwRnppMDBBKzZ4MUFDNmMzajl6aFlhaVdXUnI0dElVRGNiY1ZLOG92UWNuU2JuV1FYK0Z5elEvQkZrZmJISTZtc3RrQ1N6M2ZZK2ZUOXBBektWc0FQeGNzejF0MmRVK3FPS1B4c0NzNVIwODROeG1QcjdPd2JiSU1GV3p1L1BNbExHSmh5OERDMEhTMUFZbXNQakFnWTNmUmdwOGhFbWVIdHlmQm5wTG9sSW5lcUVEQ0tScE5HcDUwK21PNFBrL2lubGpXazkyTkt6QXFZRm92RVFhZVRuYmVzZTZNcWZheGZ2SmMydmYxNnQzK0Nac3huMUw4bW9iMyt4QzZGTVA4amo4M2o5ZFo4elNvTmRIMFhPQUREdE5sRHlWUk5RVUR2eE5lUGxsV0VMY09SY3BZWjlpSHZJNUNWd3pZa3cyZ256dXJmcHNMdTc2V1giLCJtYWMiOiI0ZDk1NjdlMTVjODJhNDM1MzZmMzQ5NzM0MTFjZWU0YjVkMDg3NzNkNmI0YTQ1NTBiNTlhZDVkOWFhN2NhMWQ3IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:10:26 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkdwTVQ1U1ZUV01laDQxY1B2Y3kwdFE9PSIsInZhbHVlIjoiZ1YvOEVCek9BU2VwT3B6amd3MjRVQ0RNd2orMG5OUExXc3Bpb2ZFeFNsblB6ZUZTRW9mVzdJUCs0R3g5VG1tRjVFanh5Q09adVl5Vm1wT3V1WmJXMDgwSk1MbFluR1JUNittcnZmS1Q0MlEvRlY3a01GUDNHV1N2R2U0cloyNHBBZ0lFRXRoRTBXQ1Qzb1lCSWZhbW1jVUpQaUdKWGt5TDRNSUN3RHhNVlIyN1ZuQzNDOHIyOTA3ZWtmdUQwd1Qxbk5HT1d1UjBBMy9BMmdmY1lPWU04SEg1L2RqZHgwTUFmcEE5Vkd0WlNzaGg3b09YVlc2QzRJNktnTUxqUU44S2ZWQmhkbEE1a2hJbXVSNUxzUnZ0SWd2SXl4d1ZBQk9jVTcxOWtPcnRaWG51d0k3R3g1UzEzaHNHbWptUFlKL1FVWmdCTDg1OTZNT2d6UmtCMDM2UE1rNFNaQm1pWit5cWI4b0VmcUVNbXhoTWpzZTdvL3kwRVJQem40NWpLNVlBRlhSbmUrVC9GbkxnaXl5YnRiTjcvaTZXVjAzdURqQWlDYkpmSXo2dTdVS3kxNmlCNEpIOHh5SlA1VDFScWN3eGoyZitmdEw2YlBvODlvRXZKT3FmNWkwMVNUWWtWQU43NisrWGQwekErWXVrS09leUVPOFk0UEZ6ZzZKUFdWZjMiLCJtYWMiOiI0Nzk3YWQ2OGI2YjdkMjI4ZDcyNGYxMmExMzRiYTQwMjc0ODcyMjkzN2EyMWIwNWQ0NzVlNTM0NDdlMzg1YzQ1IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:10:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImFLMnFzb3IzQmE3WkJwelhiWWI2QVE9PSIsInZhbHVlIjoiWjhMZUR0SXMwUUMrWldVUE1xRVdkTmNZYkxKTktKSU14TCtPRUNOM09FTWpwc2VITDEvSklTd3JhemRkTDR2WERUMGtUSW1PK0dFVlVGZ09jeW5xMlc1M2daakNCaGljdG9mZFk2QytkQWMxdEczUWlIRWZxTkRuTVdFQ0ZObDJwR01YTHQwaEozckxxVW40UFp3blViLzRwU2hVOWVYM2g4TlNJSjNHY0pNYWl5TGtMUnkwRnppMDBBKzZ4MUFDNmMzajl6aFlhaVdXUnI0dElVRGNiY1ZLOG92UWNuU2JuV1FYK0Z5elEvQkZrZmJISTZtc3RrQ1N6M2ZZK2ZUOXBBektWc0FQeGNzejF0MmRVK3FPS1B4c0NzNVIwODROeG1QcjdPd2JiSU1GV3p1L1BNbExHSmh5OERDMEhTMUFZbXNQakFnWTNmUmdwOGhFbWVIdHlmQm5wTG9sSW5lcUVEQ0tScE5HcDUwK21PNFBrL2lubGpXazkyTkt6QXFZRm92RVFhZVRuYmVzZTZNcWZheGZ2SmMydmYxNnQzK0Nac3huMUw4bW9iMyt4QzZGTVA4amo4M2o5ZFo4elNvTmRIMFhPQUREdE5sRHlWUk5RVUR2eE5lUGxsV0VMY09SY3BZWjlpSHZJNUNWd3pZa3cyZ256dXJmcHNMdTc2V1giLCJtYWMiOiI0ZDk1NjdlMTVjODJhNDM1MzZmMzQ5NzM0MTFjZWU0YjVkMDg3NzNkNmI0YTQ1NTBiNTlhZDVkOWFhN2NhMWQ3IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:10:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-100837426\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1813670163 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1813670163\", {\"maxDepth\":0})</script>\n"}}