{"__meta": {"id": "X43bb6aa04f8768ac46248a6c43a5b5e7", "datetime": "2025-06-17 14:52:14", "utime": **********.674109, "method": "PUT", "uri": "/financial/products/8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.014535, "end": **********.674133, "duration": 0.6595981121063232, "duration_str": "660ms", "measures": [{"label": "Booting", "start": **********.014535, "relative_start": 0, "end": **********.53614, "relative_end": **********.53614, "duration": 0.5216050148010254, "duration_str": "522ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.536151, "relative_start": 0.5216159820556641, "end": **********.674136, "relative_end": 2.86102294921875e-06, "duration": 0.1379849910736084, "duration_str": "138ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47982480, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PUT financial/products/{id}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@financialUpdate", "namespace": null, "prefix": "", "where": [], "as": "financial.products.update", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=699\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:699-732</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.037720000000000004, "accumulated_duration_str": "37.72ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.574712, "duration": 0.02714, "duration_str": "27.14ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 71.951}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.616908, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 71.951, "width_percent": 2.28}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.6416302, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 74.231, "width_percent": 4.905}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.6473172, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 79.136, "width_percent": 4.083}, {"sql": "select * from `product_services` where `product_services`.`id` = '8' limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceController.php", "line": 702}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6554658, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:702", "source": "app/Http/Controllers/ProductServiceController.php:702", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=702", "ajax": false, "filename": "ProductServiceController.php", "line": "702"}, "connection": "ty", "start_percent": 83.218, "width_percent": 2.466}, {"sql": "update `product_services` set `type` = 'product', `product_services`.`updated_at` = '2025-06-17 14:52:14' where `id` = 8", "type": "query", "params": [], "bindings": ["product", "2025-06-17 14:52:14", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceController.php", "line": 725}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6591341, "duration": 0.0054, "duration_str": "5.4ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:725", "source": "app/Http/Controllers/ProductServiceController.php:725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=725", "ajax": false, "filename": "ProductServiceController.php", "line": "725"}, "connection": "ty", "start_percent": 85.684, "width_percent": 14.316}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => edit product & service,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-244330693 data-indent-pad=\"  \"><span class=sf-dump-note>edit product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">edit product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-244330693\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.654111, "xdebug_link": null}]}, "session": {"_token": "zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial/products/8", "status_code": "<pre class=sf-dump id=sf-dump-1752910447 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1752910447\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1981384460 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1981384460\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-140509480 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>field</span>\" => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n  \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"7 characters\">product</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-140509480\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-290665214 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">72</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2032 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; PHPSESSID=o1mek9etnn9ejsik46u9hjcosg; _clsk=17t60l2%7C1750171898702%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjdydXNKcDNhOE5DcVY3VUJzTHpkOFE9PSIsInZhbHVlIjoiWUhNVmtlOWlBeWVxcTBDQzg3dUN0MFhPTlNlTVBJdVN5cHNTdDVlaHU3WGcxV0JiUkE4dG9HbGZPdUwzNXBOYndqK0w3NnZFYWE4cFBhMEdnVTN0TGQ0Z1Uxd2NPVGF1K1FtVy9aaXQ2NkVqSWRyL2JsWnUzemVyaVoyMVVzUWtidGJVdzBGSTBkaVZYbkFaaUJTblpVeTRuWVZoSERVMExBOStNSWlGNkxiQ0ZnR1JqQ2kyMmNoMy9qc1NGV3czR0FDaWRXZ1pkTlZuTG9Ea3UydEdrMGVPVjFYNDVNdU1HZnJhWmx5SjNGVk8raG0rVytUVysxRWRkS2VuU3d5ZVJlNk5jZnpPL09uYmtqeFlsV2FSb3ZUV0pzcFg1b3JnbFRMRWdLMUl1SzhvVFczRW9FNEUxc3dXczhtTXBTREZVeEF6ajg0YXdpSFlhU3JTY3A2K2NXSkdUcW5xNWZTZ3ZuL2hMbWQycFdVZ1dOQW01V0liV3VTa3NFUmxSZ0VVbCtvdzRpQlM3SFJBTWFvaGRma1QvZ2g0RjgxZ21NZGc5TmVHODg3ZFNVbHhBT3IzcVUySUozbWVERkNxZWRMZzREVkIrUGF0aFh6MVN2d1pYZmxBUWhpV25LTVhqcUx2VGRaYmZBV0NRc3MrMzBsZU9kVWtWNEhJRGhNOXVqZDciLCJtYWMiOiJiOTcwMjliMDgxMTBmZTM1M2FlZjVmMjUxOTRmMTY2ZjZiMTU1ZTFlZDExNmRkMzg3ODM2NWU1ZDg5OTljYmNlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImZEMzNOOVFsdU82WGJkZW5tYk9Md2c9PSIsInZhbHVlIjoiMlBaS2JJODFhQWhOUFBKYW5DL1o4Yk1kUEZkQW1xdTJHVDhFbUw3dUJSZWFVWHFVWjJVV1p5Zm9tS0hIM0RJMmU1d0d6UFlYK2FPaWFTMG9YL1BaSk4vdjMzaU5hK25QQi92dHFHZHZBTUZ0VUtRbFpvdnM3dE5JL3FFVVpvM3JWc1N1dk9Va0ZjZ21OR2VHRnM1bDZjVGRvUHhYV05UTG5CcitQZGNoak4xeTZaT0lTZVJGajBpVmMyeHFKSUpDTmRmb3NseUxPMWxSUEtkMDFIaWNSUWMzeEs1VzdXbWxpMDhGUTNBSzF4Zi8wNXVkTDlLMVNGQ2xwOVk2aWNKREN2ckJqVkdZcktXdXVNQkd3TWlMNlo4bzUzSklSMmIzSDlqTFE4Rkp5d3hnS2NlN3B0ajNXMFgzWnRaT0hYZzE2L1BZRE1IWXlnK1NHNittNkhydEh5c3FHdlMrdVo5QytZNFV3ZmlIZVBRZEQ4VXNYaWtiZCt6WGFkS0hYZkMyc0lYUTV6OFFrS1o0cks4YXNZSDZJdkF0RTZFanIxcVZnTnhmL0swNjRkVm0rTUx0T1RMb0N5QktKZ1hKQ3U5SUhxYzl2TVo5ZmNwdEFldENXcnRZTk9kNnVCbms0OUkxWUpNUnBkMSt5aXJQWTN2cnZLZk5lcHo2d2hiUm5ndWwiLCJtYWMiOiIxYzNmZTMxNzljMzU5YzFkODY0MGExNGUyODc5ZmJkN2UyNmI3ZWVlODI3NmY0OWZiOThhNzA4YTQ1ZjQ0NWQ0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-290665214\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1954617057 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>PHPSESSID</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HxnOnSuhAAPTfN2VS4q58slgJoytwtHYqaX0oLoW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1954617057\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1825938320 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:52:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImZyYjBkSVd2RWU5c0VQYUZOTFowS3c9PSIsInZhbHVlIjoibm8wMHpONUI5UVcycUJiZzdiSFNtSHhBMHBodjdCd2lXamRneHRvZzRyR3ExdFI1elp5SDBlS2tZOGZhRDNrTmt1MURXNGdENGhxWHpQMkVZbnl0UXBLTnBHSGhqTWR0TjZscTFkVVdvZWRSbkd4bGswM0VZcXcrcnY2ZjZrZFBCKzFYMnZZZUxpUmFJdEJnMVY3WDliREU3Ymd2Y2lSZitDb0RaSHVmN01RcUFjTXZaZ3gvSEpUcmp6TUpjTXRXNUdlNGk3a2crelBkUlE3djRKTkhiRG96OS9tSGJpWDlmc3dKZmtZMmtUQk12RjAvYWZEaVF3Zm9PelZyQ25ia29GQnByNXZBU0JzeVoxaUU2RWZDQjIzUU1XOVdjLzlmUXVhcndua0QvcUlEamYvbTVQOVA1MEtZK2Z3MVc0dDN1S2V0ZytlaXJOZTdOZk1LNUxHQ1ZoaFgxaFJLT2RuWmV0NFltU1ZmRFZiMlZSQXJuaGk0d2ZCeEUvZG1zMTdSa1ZkR1FscG5iOHdDdXZ1cVlzbXJ0cHk4SGZrbWNkdTVjQWh0eFB1UE1vRjJWTGJteUdvZXBsdDRveCswZnowNGRMVllnOXJ2dzVYdFY3YzhWTjRuaXdmZ01lcVVweWJ5bk9rRTgrWXQ2V2pySHczYjVtM3dRSVZLZ3I5alRuVWYiLCJtYWMiOiJmYzZjZjhiMWZlNTM4MzViMTExYTVjNDAzNTQxZmExMTc3NjY1YjFjZDRjNjFjNDAwMzAyMWNhZjBiNjkxYzUyIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:52:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlF1TzRiZHl0RVpnQ3Z6dlFhUjM2TkE9PSIsInZhbHVlIjoieDVmcFJxbG90Rkh5djUyZm1yWklDRDU2VWoxb2xHYkR1emV6VzNIVnY2T3ltYzFScDA2Z2FabTVuRlQ3MXFadFZkUU9wRFdBQzBmZktEdGlCenVPc054UTB5Nlludk9qZ2YzQ3l6MHgrenRFOEFjczI4dW85NmtTRFFVYUc3eVFYdXpIUEp0Qll4czM2OUI4b0FTRi9vOHpCSm9zU3ozemdEYUJOYWhrRHFJS1dyTlBUQzgrTnJTbXllV2J1Tnk2ak10TVJuRmtBTGFGQXRkNy8xc1JMQjFzZVhPamlmanFkL2FlOEJWQXlycXpLbjhDdzRqTDc3NFNXSnFlMmRhck5yN0p5a1BMcS9aTXNqWk5QMGRsMWxrN0VFTlN4U3dXK0FYYmd1c3NJN09aeWpKc3JEOEdKK1V0ZzRvd08yYmpnUHFIUjZqKzZXRTF0VXBPMC9GS3dETzJkSXFSTnNBSzhEOU5OcUhIZUw3QWJQT2dscTdIcWl2WWdOSHowY2dvdVYrbSsyTjdRRWhodENVYWZUUmE4bXNwaGREVGt0enplNy9BSVVtQXpUZmdRek5kQk1HSCswWGtZeUNkTC8rVjBYWXNxTXVHSkVyYytRbkovandTaUZRb0ZBZGoySEFzM1ZYU2ltOUY5THdtQUZ0L1E4S2xPZEFTayt1bnlvYVMiLCJtYWMiOiIwNzI3ZGRlYzFmZGRhMjNlOTY0Njg1ZWVlZWNjYjRmOTQ3NGYwNTFkMzNiOTFkYTYzMzczNmVlNDIxZDg1Mjg3IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:52:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImZyYjBkSVd2RWU5c0VQYUZOTFowS3c9PSIsInZhbHVlIjoibm8wMHpONUI5UVcycUJiZzdiSFNtSHhBMHBodjdCd2lXamRneHRvZzRyR3ExdFI1elp5SDBlS2tZOGZhRDNrTmt1MURXNGdENGhxWHpQMkVZbnl0UXBLTnBHSGhqTWR0TjZscTFkVVdvZWRSbkd4bGswM0VZcXcrcnY2ZjZrZFBCKzFYMnZZZUxpUmFJdEJnMVY3WDliREU3Ymd2Y2lSZitDb0RaSHVmN01RcUFjTXZaZ3gvSEpUcmp6TUpjTXRXNUdlNGk3a2crelBkUlE3djRKTkhiRG96OS9tSGJpWDlmc3dKZmtZMmtUQk12RjAvYWZEaVF3Zm9PelZyQ25ia29GQnByNXZBU0JzeVoxaUU2RWZDQjIzUU1XOVdjLzlmUXVhcndua0QvcUlEamYvbTVQOVA1MEtZK2Z3MVc0dDN1S2V0ZytlaXJOZTdOZk1LNUxHQ1ZoaFgxaFJLT2RuWmV0NFltU1ZmRFZiMlZSQXJuaGk0d2ZCeEUvZG1zMTdSa1ZkR1FscG5iOHdDdXZ1cVlzbXJ0cHk4SGZrbWNkdTVjQWh0eFB1UE1vRjJWTGJteUdvZXBsdDRveCswZnowNGRMVllnOXJ2dzVYdFY3YzhWTjRuaXdmZ01lcVVweWJ5bk9rRTgrWXQ2V2pySHczYjVtM3dRSVZLZ3I5alRuVWYiLCJtYWMiOiJmYzZjZjhiMWZlNTM4MzViMTExYTVjNDAzNTQxZmExMTc3NjY1YjFjZDRjNjFjNDAwMzAyMWNhZjBiNjkxYzUyIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:52:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlF1TzRiZHl0RVpnQ3Z6dlFhUjM2TkE9PSIsInZhbHVlIjoieDVmcFJxbG90Rkh5djUyZm1yWklDRDU2VWoxb2xHYkR1emV6VzNIVnY2T3ltYzFScDA2Z2FabTVuRlQ3MXFadFZkUU9wRFdBQzBmZktEdGlCenVPc054UTB5Nlludk9qZ2YzQ3l6MHgrenRFOEFjczI4dW85NmtTRFFVYUc3eVFYdXpIUEp0Qll4czM2OUI4b0FTRi9vOHpCSm9zU3ozemdEYUJOYWhrRHFJS1dyTlBUQzgrTnJTbXllV2J1Tnk2ak10TVJuRmtBTGFGQXRkNy8xc1JMQjFzZVhPamlmanFkL2FlOEJWQXlycXpLbjhDdzRqTDc3NFNXSnFlMmRhck5yN0p5a1BMcS9aTXNqWk5QMGRsMWxrN0VFTlN4U3dXK0FYYmd1c3NJN09aeWpKc3JEOEdKK1V0ZzRvd08yYmpnUHFIUjZqKzZXRTF0VXBPMC9GS3dETzJkSXFSTnNBSzhEOU5OcUhIZUw3QWJQT2dscTdIcWl2WWdOSHowY2dvdVYrbSsyTjdRRWhodENVYWZUUmE4bXNwaGREVGt0enplNy9BSVVtQXpUZmdRek5kQk1HSCswWGtZeUNkTC8rVjBYWXNxTXVHSkVyYytRbkovandTaUZRb0ZBZGoySEFzM1ZYU2ltOUY5THdtQUZ0L1E4S2xPZEFTayt1bnlvYVMiLCJtYWMiOiIwNzI3ZGRlYzFmZGRhMjNlOTY0Njg1ZWVlZWNjYjRmOTQ3NGYwNTFkMzNiOTFkYTYzMzczNmVlNDIxZDg1Mjg3IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:52:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1825938320\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-578134034 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-578134034\", {\"maxDepth\":0})</script>\n"}}