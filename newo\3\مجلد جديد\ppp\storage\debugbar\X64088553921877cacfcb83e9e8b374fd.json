{"__meta": {"id": "X64088553921877cacfcb83e9e8b374fd", "datetime": "2025-06-17 14:54:41", "utime": **********.687006, "method": "GET", "uri": "/customer/check/warehouse?customer_id=6&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.136969, "end": **********.687026, "duration": 0.5500569343566895, "duration_str": "550ms", "measures": [{"label": "Booting", "start": **********.136969, "relative_start": 0, "end": **********.605451, "relative_end": **********.605451, "duration": 0.46848201751708984, "duration_str": "468ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.605462, "relative_start": 0.4684929847717285, "end": **********.687029, "relative_end": 2.86102294921875e-06, "duration": 0.08156681060791016, "duration_str": "81.57ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46234432, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.017419999999999998, "accumulated_duration_str": "17.42ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6437821, "duration": 0.01523, "duration_str": "15.23ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 87.428}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.670974, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 87.428, "width_percent": 7.463}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.676364, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 94.891, "width_percent": 5.109}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-1392474469 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1392474469\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-397721076 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-397721076\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-211391566 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-211391566\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1948829047 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImtKNy9UWllVWEFoc3FhOVVEdHZTQUE9PSIsInZhbHVlIjoia3lTanpqUGZSYXpNdDZIK2pYV205QXlGaFFoeDhxWWFLaXk0UDBDR1JlZktUUitnMkRmb3JIcmxRYmlEU1Q3OUpRRWUvN2YzK2hCYW1YUWdGQ05BdG5RVnphMHFlUk4yOFdCR3JPT0xYYlN0SENmb2NWTEx5cWhZQ0ZWSFJvbWhrWU4zTkxOcHF6RWdSelhySVNINkNCTWkxWHZDZUhpSXozV0NGWGxRZTJGOTJYTC9RWjFIcUNxL2drdFdjeXpFaUUyWERkaUVzSW0yYkdFVy9GQVhNb1ZKSjN6djF2c29VOXBpbEpPKzF5QzlTL1BxSUJ3NTFvYXFiRjJyeFI2M0FMNjczYTBDcDdGeGRjSXBoZFp6STFEOTR2eHN6Q2pkVEMyYlBubWUxNWViUGpDWmYrK0N2eW1mUW9oU2VHUTVJWmhURWs2cFU3OUcxRkhZYzNCVmZkUWVNWTY2bS9vWUtzNGdTNlZOZC9lMXVLR21UakxwUTNFL0NtQUVVZEZpS3dGT21IMU1LL2pTZUhieWIvWlU2cC9uYTh1eUFCRzNHd3lkV211eHIraUQveWVtU2hpUW1NdE8xTTVvdEkvTG0vU3l1ckhnbWRHbHlQQm15b01KeXRSaXdrYVNzSEcwWWZ5VDczblJWMDRsUHNFY3pkZjJNQkNmb0NqK2tEb1EiLCJtYWMiOiJkMzYyNmYyYmJhMzJkYjA5NjVlYTk4ZWE2NzhiZjgzMmJkNzg0NDljYjExYjFjZWI1NWVhMjE4NmYzMzFiNTgwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InVBYVRBR1ZsNXNoVEhSRGxLa0xQTFE9PSIsInZhbHVlIjoiWnJwMGhEOWVRbHk0QVppSXhTQjZmamw1ZTJ1djJGVFplR0xVRGwrd1dKdTZPajA2cjNWU09aUnNNNWpHN0JDbzB4UXFyQzRiV2FQSHhoaFBISTk3TzZBUUxxenJlU0VNemdpN2RiQlAvU2FWMGJLenUzZVRROG9nemFXb0FDSzlWb3VmczA3UjlkbVVLdzMzbVd3am5POHFvb2duVjZZMTZ6WFhNRW00WGN5aGtmSS81T3lPVU81WjRMYUlqYk5XMWtQaXhCR2V5a3QwKzlnME1KN0JGTjVBTU9VTDlwbStrcnVqY01VTG1DdGg5NElYVHN1TEUxb09CQWkyY1RwWjdTd0ZGUDZ5YkUrUUxTZWVHOUl4OVVYS1crdlY1OTJFVXp1Qi9VQUxmM2dVa04vS3FiOWEwRENneGtnMjQ2TUZhbkxRY29rbUUyOVJmRndFVGZTVW5mdkpHZytmbnY0NXA4eGpzQnFoQmIrSldrdVNJc2E2bzBKZ0NDalVlUmRQYVk5Z3lIOE80bCtHUnExVTlDd1RVRjROQ2RMWDlNWFRhV0E3TU81azVJYkVqUVh1RDBhZzZ1bEFtWTUrSjVka0MrOUJzblk5TTNrTGJFdUpFZnJmRUlkK1dUZjFhWWhubzhwVzBZWWEwNS9WMTlDMy9NYmwrK2JQUy9QaEtBTWciLCJtYWMiOiJmMzk0Y2MwZGNlMzMyNzI4NDZkM2U0OGJhNDA1MjA1Njk5MjQ4N2Y5NTJjY2I4MGQwZGI1ZjlmYzdmZDEzNDM4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1948829047\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-761206258 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-761206258\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:54:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkY1c2dPU00vVzF0TVU3b0x5QnQ5Ymc9PSIsInZhbHVlIjoiS1UrRU9GK1hKc1BEOExLbzBjc0lxbEoybkRyZE53QkdBTmxzb1UvVjFVQUR0c0x0Vjg1ZDc1NHdhd2dMeWp0alJidFhqWE50cEVGVi9mNVNuV2pCUnF2dzhXajRWMEJnTERWSVltQzMvUmExdUZIZDlWM3hsUm1ibWsrd0lGQ2drQXVZU2tDZnpscnN3dng1OWxrK2dNTjFLYjd3UHdXZ3EwV2p4eC8yT2lER3ZEUU8zUFJVRzRodUN1aVJNRlhkZExQSUg1aytoVHpDbmRBMzAxc2FadGxBU2ZKS0JwMnM3MW5VSHBmbGpXbTEyN1RIY0J3OU9DNFA5L01md3RCQ0xiTUE2dldWYzA3MTAzdWZoenNXeHNoMUpTZnJyczJITU1WamVRTkxrMVJaS25hNWVQNjlLemkwU3YvallPL2s1WVlGNlVxTEN0SWpRcFhmQ1dXV1pVdzRUdG1pdWVid21xS29zajlsdTRtci9wVTF1ZTRYajVqREtOaUZXMkp1K0ZnMUwvN2xoV1c3aTB6TkphZDdMc2M2dVJGdm5oSURMTmk4b3RkRnZaUE5CaHZGVlNzc1BxcWRTMzFheXh5eFdQeitjMlZKWEJoY0ZmMDQvaEMrY2dkemdTTzh6VkhjT3BhY1l1UHdSVXhpS3Fjb0NVSTF0cHJwa0lha1c0T3UiLCJtYWMiOiI2MmQzZmQ4MmNjMWQ2M2YyY2JiMzUxZjYzMGY3NzM2OTk3ZDM1OGIyNzgzNzQ4YTU4NzRlNjMzYTgyN2JmODJiIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:54:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkdyaUhZK2hFdnJHbFJQUE83bTZJaWc9PSIsInZhbHVlIjoicExyTnQ3d1ZCeVFRQXlSUEdEaytMeUtiU3FoTWlsTjhPTUEyZGRVSWNUWEUyZDFyaHNGaXJ1Uy9pU0QvQ3NYajBLVDRlVllYdk03TENmbTNIcFV3ZE5RbnlvTjRBVHRnN3NNUUhmMm9aUzVCdEFaaU5OSWNFaG5wM3lheWN0cVFZS0hiUWlYTUh2VHZIQ2piSTNsM3U3R3I4aWhacjJZVVJ4eHJpQW8xSDEyejRvR2xGYU9HTk5FS2wrVVZPSGhMZ1FqaGhORWs1QzJOZVc0b2k1VHNEQUdsMnE1Wno1aWc0RU1UNlVtaXRDS1c5aXlNRUdNUUVUNzExenRpR21aaGZFVDM1aElJNlE3ekh2Y2JYNm5MZUR3U3ZTZ1F2aEVtaU92RmpaTmtvRkJ0RUkxbW1zTDh5RnVLeFNIRnJFSGpqaFZvUG1ITDhEK1NmTVNybXFNOW4rUVRvTlpxRm9ZTmtEUWVqSWFSTEdiak80UXd4VTdSdDI1OVgrVlpGVklLYlU5TUVpSHRyMkxoWWZxSmlBdUh3UHh6QTRQMmlvSE01WkdPZW1ZT2xkRlZFajFFTGZ2WmpnVXZqek8xUnF0dEdneWR4dzkvVWpDS05rVGZtNU1UOSszNDBTeTB5aEFVRkZ2UTljSVdYb01PU1QrckwxMGQ5TEdWS2xucVpxT2MiLCJtYWMiOiIzZmU5YzkzODE2OGU2OWJlMzFhOGM0NzM5OTMzZWFjMTI5N2FmN2I2YjM1ZWZiY2JlMTFmNjM4OTQ3ZmQzMjk3IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:54:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkY1c2dPU00vVzF0TVU3b0x5QnQ5Ymc9PSIsInZhbHVlIjoiS1UrRU9GK1hKc1BEOExLbzBjc0lxbEoybkRyZE53QkdBTmxzb1UvVjFVQUR0c0x0Vjg1ZDc1NHdhd2dMeWp0alJidFhqWE50cEVGVi9mNVNuV2pCUnF2dzhXajRWMEJnTERWSVltQzMvUmExdUZIZDlWM3hsUm1ibWsrd0lGQ2drQXVZU2tDZnpscnN3dng1OWxrK2dNTjFLYjd3UHdXZ3EwV2p4eC8yT2lER3ZEUU8zUFJVRzRodUN1aVJNRlhkZExQSUg1aytoVHpDbmRBMzAxc2FadGxBU2ZKS0JwMnM3MW5VSHBmbGpXbTEyN1RIY0J3OU9DNFA5L01md3RCQ0xiTUE2dldWYzA3MTAzdWZoenNXeHNoMUpTZnJyczJITU1WamVRTkxrMVJaS25hNWVQNjlLemkwU3YvallPL2s1WVlGNlVxTEN0SWpRcFhmQ1dXV1pVdzRUdG1pdWVid21xS29zajlsdTRtci9wVTF1ZTRYajVqREtOaUZXMkp1K0ZnMUwvN2xoV1c3aTB6TkphZDdMc2M2dVJGdm5oSURMTmk4b3RkRnZaUE5CaHZGVlNzc1BxcWRTMzFheXh5eFdQeitjMlZKWEJoY0ZmMDQvaEMrY2dkemdTTzh6VkhjT3BhY1l1UHdSVXhpS3Fjb0NVSTF0cHJwa0lha1c0T3UiLCJtYWMiOiI2MmQzZmQ4MmNjMWQ2M2YyY2JiMzUxZjYzMGY3NzM2OTk3ZDM1OGIyNzgzNzQ4YTU4NzRlNjMzYTgyN2JmODJiIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:54:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkdyaUhZK2hFdnJHbFJQUE83bTZJaWc9PSIsInZhbHVlIjoicExyTnQ3d1ZCeVFRQXlSUEdEaytMeUtiU3FoTWlsTjhPTUEyZGRVSWNUWEUyZDFyaHNGaXJ1Uy9pU0QvQ3NYajBLVDRlVllYdk03TENmbTNIcFV3ZE5RbnlvTjRBVHRnN3NNUUhmMm9aUzVCdEFaaU5OSWNFaG5wM3lheWN0cVFZS0hiUWlYTUh2VHZIQ2piSTNsM3U3R3I4aWhacjJZVVJ4eHJpQW8xSDEyejRvR2xGYU9HTk5FS2wrVVZPSGhMZ1FqaGhORWs1QzJOZVc0b2k1VHNEQUdsMnE1Wno1aWc0RU1UNlVtaXRDS1c5aXlNRUdNUUVUNzExenRpR21aaGZFVDM1aElJNlE3ekh2Y2JYNm5MZUR3U3ZTZ1F2aEVtaU92RmpaTmtvRkJ0RUkxbW1zTDh5RnVLeFNIRnJFSGpqaFZvUG1ITDhEK1NmTVNybXFNOW4rUVRvTlpxRm9ZTmtEUWVqSWFSTEdiak80UXd4VTdSdDI1OVgrVlpGVklLYlU5TUVpSHRyMkxoWWZxSmlBdUh3UHh6QTRQMmlvSE01WkdPZW1ZT2xkRlZFajFFTGZ2WmpnVXZqek8xUnF0dEdneWR4dzkvVWpDS05rVGZtNU1UOSszNDBTeTB5aEFVRkZ2UTljSVdYb01PU1QrckwxMGQ5TEdWS2xucVpxT2MiLCJtYWMiOiIzZmU5YzkzODE2OGU2OWJlMzFhOGM0NzM5OTMzZWFjMTI5N2FmN2I2YjM1ZWZiY2JlMTFmNjM4OTQ3ZmQzMjk3IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:54:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1740343038 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1740343038\", {\"maxDepth\":0})</script>\n"}}