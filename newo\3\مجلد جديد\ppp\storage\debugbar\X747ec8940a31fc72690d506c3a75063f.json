{"__meta": {"id": "X747ec8940a31fc72690d506c3a75063f", "datetime": "2025-06-17 14:53:59", "utime": **********.479336, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750172038.861559, "end": **********.479359, "duration": 0.6177999973297119, "duration_str": "618ms", "measures": [{"label": "Booting", "start": 1750172038.861559, "relative_start": 0, "end": **********.379971, "relative_end": **********.379971, "duration": 0.5184121131896973, "duration_str": "518ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.379982, "relative_start": 0.5184230804443359, "end": **********.479361, "relative_end": 2.1457672119140625e-06, "duration": 0.09937906265258789, "duration_str": "99.38ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46107288, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.021120000000000003, "accumulated_duration_str": "21.12ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.42205, "duration": 0.01946, "duration_str": "19.46ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 92.14}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.4569159, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 92.14, "width_percent": 3.456}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.4672089, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.597, "width_percent": 4.403}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6", "_previous": "array:1 [\n  \"url\" => \"http://localhost/inventory-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-515655074 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-515655074\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1680157422 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1680157422\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1005636902 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1005636902\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-157230572 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2033 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; PHPSESSID=o1mek9etnn9ejsik46u9hjcosg; _clsk=17t60l2%7C1750172014163%7C10%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjdUSUEvSDFNc1FQRGg2a1lvUHNBZkE9PSIsInZhbHVlIjoiNXpUaUtxdEE4dG91SFB6M1FUckNqUUF5UUNDYUtCVmo3MS96c3lTYWlNdE91UmwvVThWVGRSV2QvTnVCNVJFRGF6SThKYjNuaG9BWmVOOWxLdWRDdmROL0N2SDV3T1dQNXB0elI3Zi92c0NNZmZ3cUxHR3JtK3g1SVdIRTJYUkVNTkpSZ3JSLzJ0MTI1RHVVUnNyMUNFNU9KU1Y4SWlQOXRxUEV3dWNtekx6UmVVTzFnTW5INzBMV2FDNmluZHc0M3E5NitOV29VZ0RDNGhad0txSnBOT2YxMmk3WXhZZ1pxRDFPQjVhSk1SMXY5alN4alFzZWxIL1I4UVZCVEs4YkVEalN4Nkt2TVhZdjFRdHlPRlY5a3lKRGNRTjBzUHIrb3VYcEdUTml1dkErN25xWXcvU090ekxTOSs4NFkxQ3dDaEdHcFZ6UW90TUNpZDlNUS80bE1VRFVLVWczcURZTS9hcHhLSXNsWjBidzI1TW1CK0haOGlkNEI1ektGZFN1Nk1xWENmR01WTGhwYTQ2ckJIUUVjUDZHNUNGTFBLV3dpL2RJRWZEbzNqRkg3bkNZTEIzZzJLOGdHQ0FnK1dLMTNEUDBpUmFVMy9BcVRZZjA4UzY3OGd5M0MwMzdkNWlNYU5NeDE2REtrNWE5eXdOZElyOVZQK1NGem5EZkZYMVoiLCJtYWMiOiI1MWViYjliMmQ1NDg4NDYyNzE1MTE1ZjA0ZTMwNGIyZTJlN2M2N2I1MDcwNjRhOTA5NTk3OTJiZmYyNWY3MmE3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InlUUmZmN2pDZXM0K21Pb2xGMHlFWXc9PSIsInZhbHVlIjoiSGRkUi82QWhuRGJmSDNVT092Y0c1cUlPcHBubWFidUtUb3lZRDVkaDhkVlBtZnl4U2Q5V3p4cFc4RmhMYUtDNy83TlRubk9yamt2K01FVmtoZ0c4ZUFtaWtYWVNRY3kzZTBGcU5pQjZMeW9zaHQ1a0pwQ2toVWM4TjVCc1hGc0tkb1UrR3JxWlRmam5HZWdVa2hUbDNPc0dESGlad09IbTZRRFVmNTBpS0RaNXF1NWFjMWZrOG5aWUdFUjJSanBlMnpGOUhzeldjSytvd0E4YkNiT2RHOEh6eTNBOUxPUUFLaEZOUUJXeFE0S1U5cnRWb2pKa2RuTHFaZmxwdGJ1VzB1UC9WTWdjK2hFaGJlaUZOQzdyc0F6b1QzTElORUM5TklGRXNuSmRLN0JPY2JzQ0hGT1lBNzBwOEsyRVZhWFJzUEJkalZnMzZIejlFdGZwYjFMSGhiRnNmaWlsQXhZTU5rVlU5aG0yVXFNeXVoc2lvV00rLzhZZ0swczZRTm5YWk5kdzcyTUo4d3FwS1RLa1V6cXJ2Rm9VTEJCeGNQS3FDVWErR2x1RXBXeDRmQUI1dGNualRUcngrWTdxMnhWcVRYZU10bHFnL0svSDV4akdRNnZMVjRjR21IUU44SFlOMDF0OWNKQTRZWFYzVlk0OXkrbTRKai9FMnlHUnlHRXQiLCJtYWMiOiJhMDUyNWQ4Y2Y0OGUzNTM2YjMxMjJlNWYyZDUxNDIyZGRiNGRjN2EzYzUwZDNhNmJhNWNlYmQyNWE1YTEwY2I5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-157230572\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1014164063 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>PHPSESSID</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HxnOnSuhAAPTfN2VS4q58slgJoytwtHYqaX0oLoW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1014164063\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2105825010 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:53:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imp3SGxpV2puUHpKQXR1YURTLzhKdGc9PSIsInZhbHVlIjoidkwyNDJiRzY5ZEdMazZtQnBYQjE3R2hlbnNTeXdZdmU1NzlKQ1RxYzZDdCs1ODNESGZoWnJLS1l0NGJQWFJDYjVyOEZVOHhUaXJiU0FQOGJESU9Xd24vUFBkcjF5cFBTeTRQdTVVNlNRTDNpYWlGWElmR2V6Q3J0eUdqV1ZJM3FMbmN5RVFEY2s2Z1hUZmU5d09sK2ZsaEc2d1pHWVBYY3ZOUlhYQ2FDa05xS2VxQlVHM01aS2FJSkNaSXJydmswTHBhVGE4azZia0lhblhBUmZKUU5QK1d1dGpHdkhHcmhLOFdvRXUxczZZdTJmdVhQZU0xVGxOUEZRck1odmlTQWN2RjJOd3ZjNENiWEtHZGlDSTlNSFlIVDNaK0FIWHpYZU1Cek9ZUXZadm9pRjFLVWFwWVVsQVBSYUtTK3hpQzZubkVFUkpyQzFpbVlkQzIyU2tWaVRNVWZxWHlQdWJqTzFxdGpHb3ZzUy8zc1kyVnIxZEdLUHBBQ2kwcUNIRzZLQWVjb0RhTU5LMVZuaFYvWTM2dHA0SEpuMFBoTVZwLzE5aUE3YmdIb1dYTzE0TG9OUWlaVEdVUlN0M05nbXdqYXlxdTF4UlJXWUlhbFU5VEh3WGZCb3c3NTFoWTQ4RUZhbFpiYUdYSms3TFh3TE9vV25FSERsUmdodHdvVElubVUiLCJtYWMiOiIyZDVmZTAxZGUwNDUyNDZhOTc4NGU0NDA2ZDI3ZTQzM2E2MGZkZGU4ZTZhZDI0YjUzZGViMWM3OGYzNWVhODdhIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:53:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkxOcStEZ1pvTnFVVkhRc3pMeTVjOHc9PSIsInZhbHVlIjoiaDN2eG01djNGZzlwaWh1TFE2NS9XM3lmMEtNMzgvRXNCWjZZSUcyMU9LazFlVkttazZYZHhaTTFUTUltOUJwR1h3TjhPdWplYUFSN1BDT3VHc202a3lzY3J2VDhyQksydmNkclVVY04vY05lbDliR3VXbnpqbFBaZENDUHZNOUN6bHU0cnd2YnlMYjVrdng4MXBKQ2N1ejNBU0lqVVl6U3RVR3E1ZDJXWnRQa2JUc2F0UitDTUk2OWhWYm1YUUhIQkluSzZXVVF4akVQRmNyR05LQ2FHc3hvM1JId0FQTXBMaXM3UWxLTEtGOTVpbUhNK1JKWWN5c2NCVDlYTUdZMDg2TlljSVdRaElCVVpwRGpnSVBlL1pMaERsRVNMN1NTaHhlOGNhdFVqdTBKZ1ZVY2pTRElCWlVVeGZXNnZzQnhkVkRacHFseHFXNUs3eGRHWTJKSHpEU2Z3THFzK1N1a1h0ak9yMVhKdUI3V2pjTXhaRWZxYXpmRHBudzVZZjdqTmRCRnQyL3ZYZU9vSFh4RFVJeGhEcmpvUWIrS1kxSlk0Njh3ZWNXbW1Pc1pVVHF3S3pxWEVuRlhVTEpZRkpUaEF4WDZucmVhLzVQVk9iRXN3S0VJY3lwd2N3MXZ3MzhQRDFNcXJOYklrR0RnTk9SM0NJUUtHN1pJN3ZNT0xLOWUiLCJtYWMiOiJkMzFmZDI2ZjllOTU5NWVjZjRlMGU1MjFkNzgyYWMzYjQ4NWE2NmVjN2UwMjYzMDU2MjBjMDY4YTE5YzI1YjBkIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:53:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imp3SGxpV2puUHpKQXR1YURTLzhKdGc9PSIsInZhbHVlIjoidkwyNDJiRzY5ZEdMazZtQnBYQjE3R2hlbnNTeXdZdmU1NzlKQ1RxYzZDdCs1ODNESGZoWnJLS1l0NGJQWFJDYjVyOEZVOHhUaXJiU0FQOGJESU9Xd24vUFBkcjF5cFBTeTRQdTVVNlNRTDNpYWlGWElmR2V6Q3J0eUdqV1ZJM3FMbmN5RVFEY2s2Z1hUZmU5d09sK2ZsaEc2d1pHWVBYY3ZOUlhYQ2FDa05xS2VxQlVHM01aS2FJSkNaSXJydmswTHBhVGE4azZia0lhblhBUmZKUU5QK1d1dGpHdkhHcmhLOFdvRXUxczZZdTJmdVhQZU0xVGxOUEZRck1odmlTQWN2RjJOd3ZjNENiWEtHZGlDSTlNSFlIVDNaK0FIWHpYZU1Cek9ZUXZadm9pRjFLVWFwWVVsQVBSYUtTK3hpQzZubkVFUkpyQzFpbVlkQzIyU2tWaVRNVWZxWHlQdWJqTzFxdGpHb3ZzUy8zc1kyVnIxZEdLUHBBQ2kwcUNIRzZLQWVjb0RhTU5LMVZuaFYvWTM2dHA0SEpuMFBoTVZwLzE5aUE3YmdIb1dYTzE0TG9OUWlaVEdVUlN0M05nbXdqYXlxdTF4UlJXWUlhbFU5VEh3WGZCb3c3NTFoWTQ4RUZhbFpiYUdYSms3TFh3TE9vV25FSERsUmdodHdvVElubVUiLCJtYWMiOiIyZDVmZTAxZGUwNDUyNDZhOTc4NGU0NDA2ZDI3ZTQzM2E2MGZkZGU4ZTZhZDI0YjUzZGViMWM3OGYzNWVhODdhIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:53:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkxOcStEZ1pvTnFVVkhRc3pMeTVjOHc9PSIsInZhbHVlIjoiaDN2eG01djNGZzlwaWh1TFE2NS9XM3lmMEtNMzgvRXNCWjZZSUcyMU9LazFlVkttazZYZHhaTTFUTUltOUJwR1h3TjhPdWplYUFSN1BDT3VHc202a3lzY3J2VDhyQksydmNkclVVY04vY05lbDliR3VXbnpqbFBaZENDUHZNOUN6bHU0cnd2YnlMYjVrdng4MXBKQ2N1ejNBU0lqVVl6U3RVR3E1ZDJXWnRQa2JUc2F0UitDTUk2OWhWYm1YUUhIQkluSzZXVVF4akVQRmNyR05LQ2FHc3hvM1JId0FQTXBMaXM3UWxLTEtGOTVpbUhNK1JKWWN5c2NCVDlYTUdZMDg2TlljSVdRaElCVVpwRGpnSVBlL1pMaERsRVNMN1NTaHhlOGNhdFVqdTBKZ1ZVY2pTRElCWlVVeGZXNnZzQnhkVkRacHFseHFXNUs3eGRHWTJKSHpEU2Z3THFzK1N1a1h0ak9yMVhKdUI3V2pjTXhaRWZxYXpmRHBudzVZZjdqTmRCRnQyL3ZYZU9vSFh4RFVJeGhEcmpvUWIrS1kxSlk0Njh3ZWNXbW1Pc1pVVHF3S3pxWEVuRlhVTEpZRkpUaEF4WDZucmVhLzVQVk9iRXN3S0VJY3lwd2N3MXZ3MzhQRDFNcXJOYklrR0RnTk9SM0NJUUtHN1pJN3ZNT0xLOWUiLCJtYWMiOiJkMzFmZDI2ZjllOTU5NWVjZjRlMGU1MjFkNzgyYWMzYjQ4NWE2NmVjN2UwMjYzMDU2MjBjMDY4YTE5YzI1YjBkIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:53:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2105825010\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-833880798 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-833880798\", {\"maxDepth\":0})</script>\n"}}