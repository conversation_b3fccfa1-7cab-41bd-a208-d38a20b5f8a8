{"__meta": {"id": "Xcf7053291c0bee8e972b93e8385b27e8", "datetime": "2025-06-17 14:50:06", "utime": **********.337108, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750171805.644737, "end": **********.337134, "duration": 0.692396879196167, "duration_str": "692ms", "measures": [{"label": "Booting", "start": 1750171805.644737, "relative_start": 0, "end": **********.219943, "relative_end": **********.219943, "duration": 0.5752060413360596, "duration_str": "575ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.219954, "relative_start": 0.5752170085906982, "end": **********.337138, "relative_end": 4.0531158447265625e-06, "duration": 0.11718392372131348, "duration_str": "117ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46094560, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.030959999999999998, "accumulated_duration_str": "30.96ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2693002, "duration": 0.02853, "duration_str": "28.53ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 92.151}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.312104, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 92.151, "width_percent": 3.941}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.322759, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 96.092, "width_percent": 3.908}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6", "_previous": "array:1 [\n  \"url\" => \"http://localhost/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1950212595 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1950212595\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-42730569 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-42730569\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1536282116 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1536282116\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1648139687 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://localhost/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2032 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; _clsk=17t60l2%7C1750171683312%7C3%7C1%7Cl.clarity.ms%2Fcollect; PHPSESSID=o1mek9etnn9ejsik46u9hjcosg; XSRF-TOKEN=eyJpdiI6IkNMQjdWQkhUdmR3WFBXc3ZsYnlMSGc9PSIsInZhbHVlIjoibDA1SU1pUEc5eE1DRTBVN1B0REUrMDQ4ZXJ1bXRGcmxmVEJoNy9NNW9CQlo4YndEU0QvM3hpZkhFeXcvOENjNnZUbmVnSXl0NFNINStpTFdpQ0p4YjZMLzltUzRTVFNsM210eG9ucnZrUWd4a2Q4YnFxdXFRaDA1VUw0anlxN2xkejFUQ0RLWC9mcWlySjN3d0FYYkhjRVVQcWt6dXk5TFJlZVpHeU1NTERuQzd1NnV1SU1RTUFPOUUzaFNEVFFWSXd5YjkySDdBaWdJWW9sUC9EVTlaOVVYRWx4Y05hNkltTzhvY0hhRWcxaEE4K0JEeHVCV2hoQkNwNDdjdjlUbkpJUTZaWGdFVnJVdXEwdk9HNG1aYVVUZ2NWdkZKZUtsQ2wxbnh5dTBzNGkvNmhhTTlDbnJHYnZmRmxYNHJwZjVNWjJlY3A5OExKdDhqTXp2aTV4TDduY1FzQml1NmRjUmsySXp0dThMNjNpK0IxUW5xdGg3ODc2b2x4NjgwUUtuVVBGMmQ4MHdGTStoK3MvWkhFbytHSFc2Qis4bFY3bHZNZjFleDhQc2xuQVMrVjA1SjNhZ3FVSU5vVVVBeHo5NjF5UStHVVU2dDg4NnlXNjRkb2twQ2U1MnA2dW10TUZtZm43YkxsSkliUjB2SzVOQitSQ0ZERXlXWUFBVWxxM2EiLCJtYWMiOiI1NzRiZGFlNDM2OTVjODQ1YmIxY2MwM2ZiNjcyZmFmMzc5MTQwMDI0NzFhNjI2NGRhZjMwOWQyOTdhZjQ1ZDU1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Imh2eG00bGRMYUc2dTdtMHU3clhES0E9PSIsInZhbHVlIjoiNmF3ME1ienpycm9LNENrdHR0NHg4Mmg4TnJiOGF4SUticUxvZjJlNHo5dUk3ekVDQlNzZGJ0UmdRM1E4RWZnM0JSK1lXQkI0Ri85aGJmenlmVXdXTU14Tk5QR0I0TnJEMlBGNGoydHN6d0FuQVRsTjBhVHBpWTQvUUNDdVZzNlNrRjM2WUFJSC8vUyt4UnZPM0VwV1lTWDJleUE3MDVRZXhVOFdCVTA4OUx0cXdZOWp4VzZrdlhqRmF2Z2I0VlgzeUR2UFhHdFNidHhsR1M3QU11UVVmOHZoZ3hPRmtjUHh3RVkxdjh5c21pSms1Z2FrSTU1MjUzeGhvSXZaRzY2QkVHK2RFSkFhaDJzdHpLaHh3V0Y4VVZLblZsRmVzOHF6UDZldEZ3eGNvdE9La1JCY0ttYVZuNG82S0h0Nm1obTdZb0QwK2VsYW1KUGdleVpSSFIxN0VHVmFLTS9pZXRLa3FiV0p3T2ZKZEswaU9DL0YzWE01QTE2TTJaQXh3aURpKy94YWpXVmZCZWd6ajE5aGljU2hqWVNYR0VSR1VGalRZZllub3JqeG5tQk00WEQ1WkdNR2tqRnRvdWhLcHZEQ21KUGwrMDlDckhwT245N0s2ZWNScU9wa3Z3dzBmNHliakdPZGpGRmtpUEJ2VFY5Y1RsTkRCWU5jSER1YmwvMCsiLCJtYWMiOiIyYTk3YzhhOTFlNWY1YTdlNTYzNGRmNGZiNjRiOTA2MzE2MGU3MmRiMDI4MGZjZjMxNmZiOTlkMWMwNjAzNGFjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1648139687\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1669514813 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>PHPSESSID</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HxnOnSuhAAPTfN2VS4q58slgJoytwtHYqaX0oLoW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1669514813\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1507067418 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:50:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkJTQm51eDZuZFAvcHhHWm1zdCt5TkE9PSIsInZhbHVlIjoiU29rNzlBTGNMek9oUTdPNmRuN3cxcG9PSE1TL213VlkzOFh2clJXSmxmTnBTbUNxSnpURm1RL1dpRU5hM1RDR0YvSGVadS9tWHJNVTRGaURzS0xPV0FLeDVMYng0ZnBUZW9zcWs5TEpnRkg0dGQyeTY3SDJXS0JCVUF4b0lCTkZrZ1VtZjNGVGJDckJYSnNjQm1JTEFUMFNYakRoOXEranB3NCtjbHRyLzhuK2JTVVcvS2JTL1NGSTZBVEVuSGpYS3hIZXk3L2dCUWFWRmpoNnhNaUgzNmwzcEhaS0VWSkk4UHVSZERwYmVLbk9HZURQY2RQTjJUUEM1T1RTaTkrWTJPMmIwekRPR3BvdDRtVWVFTFRRVllLUWY0YUg4NHV3Y2RMOWtuZnJCcDF0RXNCdTIxU3ZOQmRsK2ZNQWowb1pxMm9LOVgzb2xFNVBQeUlwbzd2VDYydDZ4ZThZRTRiM3ZkVlBLK01ma1l3M3g0ajkzN1lQZE9JR09vblREMm9HU2hKem03L3Jzb2JWN3NxYi9ab09Vaks1UENWN0VtRG0xeTdjeGhDc1dyT2JmaFRIZU90YWFSZitucE1hb2F2LzlXYncybVNWWUNOQTdGU1dySDZacEMxZ3l1K0dCZCtpalZxN2lyV3U3dEd6czd6VFZIWWRtdFdHT0tZMzdlVEUiLCJtYWMiOiI1YWE2OGZiMjhkYmEzZTI4NzU1ODRlNGNiMzQ1ZDZjYzc1NDg2ZDJlYjM4OWY0MGRhMzkzNGE4MWI5ZjlhMzBhIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:50:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im0zSFRZOURLUWdLSUdWUEhaQzZ0Zmc9PSIsInZhbHVlIjoiQ2VTRHBGTnE3NUZ4Y2pUT3VML2sxWVJGbVJFQzlTZmdKUjZnbGZoRkl1bkw3VlZza1gycWFsZXlsRDJGRm5odExqZ0c5dmlSc3l4Z0hodm95dkQ4dG9JcEMxM1lxekdSclQyQ0NoTG5LOW9mR0pDQStqSWJFcjFRY0VnOFVXWVVSeDZyb1JNV01sSmZyTmZTeDNmY21RT21CaEQvWnFmWmg2QTlJM3c3S2hCMXNBZlY2SHJhSjBzNGRON2ZRTUdjNSt4TWF6ZFdNVXp6QVhIbktQUEtCVzQxb2R1WnV3QXNtNlhVQ2xQT25zQXorQndsNjRFak40WGlxbmxyZWRpcXNXeFlMcGZqNG8yUmMra0I5ODZRT0JPQ3dWbzhGa0hnVUJNZzJpcnhEZmxEM1poYW1SdVdsY0ROTlllaSs1cU9raVN3MFZXa3gwcWtzeFdOMnExdHVzR2puaWxJOUFYaDdPSVdjR3g4SVFSTFVET1ZDbXNkcitFRXIwWU1TTVY5NmZuK0o5U1pIWkpBQ1Y3d3Z3ZERSZWNvOHdVcVpUOGhsZUtMdjVqb3dYWDV3VXk5ZnhnM0drRE52NmtXNXpvL0R6R25FK2FybWlpMjByNGN2emcxMkRSSGZYcEpQd0IweEE3SHF1OVNZeVhNSkNMZGVJMEtkdkRUeGxyV0dLTnUiLCJtYWMiOiJkMjFiNDg3MjBlNzFhNzE0NDM3OGI0MzA1ZDk4MTRlMzdkZGEzM2Y0NjQyNDdmZTJjYjllYmIyNjY3ZTgzYjNiIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:50:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkJTQm51eDZuZFAvcHhHWm1zdCt5TkE9PSIsInZhbHVlIjoiU29rNzlBTGNMek9oUTdPNmRuN3cxcG9PSE1TL213VlkzOFh2clJXSmxmTnBTbUNxSnpURm1RL1dpRU5hM1RDR0YvSGVadS9tWHJNVTRGaURzS0xPV0FLeDVMYng0ZnBUZW9zcWs5TEpnRkg0dGQyeTY3SDJXS0JCVUF4b0lCTkZrZ1VtZjNGVGJDckJYSnNjQm1JTEFUMFNYakRoOXEranB3NCtjbHRyLzhuK2JTVVcvS2JTL1NGSTZBVEVuSGpYS3hIZXk3L2dCUWFWRmpoNnhNaUgzNmwzcEhaS0VWSkk4UHVSZERwYmVLbk9HZURQY2RQTjJUUEM1T1RTaTkrWTJPMmIwekRPR3BvdDRtVWVFTFRRVllLUWY0YUg4NHV3Y2RMOWtuZnJCcDF0RXNCdTIxU3ZOQmRsK2ZNQWowb1pxMm9LOVgzb2xFNVBQeUlwbzd2VDYydDZ4ZThZRTRiM3ZkVlBLK01ma1l3M3g0ajkzN1lQZE9JR09vblREMm9HU2hKem03L3Jzb2JWN3NxYi9ab09Vaks1UENWN0VtRG0xeTdjeGhDc1dyT2JmaFRIZU90YWFSZitucE1hb2F2LzlXYncybVNWWUNOQTdGU1dySDZacEMxZ3l1K0dCZCtpalZxN2lyV3U3dEd6czd6VFZIWWRtdFdHT0tZMzdlVEUiLCJtYWMiOiI1YWE2OGZiMjhkYmEzZTI4NzU1ODRlNGNiMzQ1ZDZjYzc1NDg2ZDJlYjM4OWY0MGRhMzkzNGE4MWI5ZjlhMzBhIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:50:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im0zSFRZOURLUWdLSUdWUEhaQzZ0Zmc9PSIsInZhbHVlIjoiQ2VTRHBGTnE3NUZ4Y2pUT3VML2sxWVJGbVJFQzlTZmdKUjZnbGZoRkl1bkw3VlZza1gycWFsZXlsRDJGRm5odExqZ0c5dmlSc3l4Z0hodm95dkQ4dG9JcEMxM1lxekdSclQyQ0NoTG5LOW9mR0pDQStqSWJFcjFRY0VnOFVXWVVSeDZyb1JNV01sSmZyTmZTeDNmY21RT21CaEQvWnFmWmg2QTlJM3c3S2hCMXNBZlY2SHJhSjBzNGRON2ZRTUdjNSt4TWF6ZFdNVXp6QVhIbktQUEtCVzQxb2R1WnV3QXNtNlhVQ2xQT25zQXorQndsNjRFak40WGlxbmxyZWRpcXNXeFlMcGZqNG8yUmMra0I5ODZRT0JPQ3dWbzhGa0hnVUJNZzJpcnhEZmxEM1poYW1SdVdsY0ROTlllaSs1cU9raVN3MFZXa3gwcWtzeFdOMnExdHVzR2puaWxJOUFYaDdPSVdjR3g4SVFSTFVET1ZDbXNkcitFRXIwWU1TTVY5NmZuK0o5U1pIWkpBQ1Y3d3Z3ZERSZWNvOHdVcVpUOGhsZUtMdjVqb3dYWDV3VXk5ZnhnM0drRE52NmtXNXpvL0R6R25FK2FybWlpMjByNGN2emcxMkRSSGZYcEpQd0IweEE3SHF1OVNZeVhNSkNMZGVJMEtkdkRUeGxyV0dLTnUiLCJtYWMiOiJkMjFiNDg3MjBlNzFhNzE0NDM3OGI0MzA1ZDk4MTRlMzdkZGEzM2Y0NjQyNDdmZTJjYjllYmIyNjY3ZTgzYjNiIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:50:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1507067418\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-715865164 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://localhost/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-715865164\", {\"maxDepth\":0})</script>\n"}}