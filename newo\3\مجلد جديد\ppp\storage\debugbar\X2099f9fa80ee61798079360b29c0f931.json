{"__meta": {"id": "X2099f9fa80ee61798079360b29c0f931", "datetime": "2025-06-17 14:51:45", "utime": **********.791487, "method": "PUT", "uri": "/financial/products/8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.040488, "end": **********.791509, "duration": 0.7510209083557129, "duration_str": "751ms", "measures": [{"label": "Booting", "start": **********.040488, "relative_start": 0, "end": **********.654579, "relative_end": **********.654579, "duration": 0.6140909194946289, "duration_str": "614ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.654591, "relative_start": 0.6141030788421631, "end": **********.791512, "relative_end": 3.0994415283203125e-06, "duration": 0.13692092895507812, "duration_str": "137ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47983264, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PUT financial/products/{id}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@financialUpdate", "namespace": null, "prefix": "", "where": [], "as": "financial.products.update", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=699\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:699-732</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.03718, "accumulated_duration_str": "37.18ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.69644, "duration": 0.023190000000000002, "duration_str": "23.19ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 62.372}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.731914, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 62.372, "width_percent": 2.609}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.75492, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 64.981, "width_percent": 4.384}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.7592182, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 69.365, "width_percent": 3.093}, {"sql": "select * from `product_services` where `product_services`.`id` = '8' limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceController.php", "line": 702}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.766907, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:702", "source": "app/Http/Controllers/ProductServiceController.php:702", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=702", "ajax": false, "filename": "ProductServiceController.php", "line": "702"}, "connection": "ty", "start_percent": 72.458, "width_percent": 3.819}, {"sql": "update `product_services` set `expense_chartaccount_id` = '282', `product_services`.`updated_at` = '2025-06-17 14:51:45' where `id` = 8", "type": "query", "params": [], "bindings": ["282", "2025-06-17 14:51:45", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceController.php", "line": 725}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.771307, "duration": 0.00882, "duration_str": "8.82ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:725", "source": "app/Http/Controllers/ProductServiceController.php:725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=725", "ajax": false, "filename": "ProductServiceController.php", "line": "725"}, "connection": "ty", "start_percent": 76.278, "width_percent": 23.722}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => edit product & service,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-13168622 data-indent-pad=\"  \"><span class=sf-dump-note>edit product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">edit product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-13168622\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.765637, "xdebug_link": null}]}, "session": {"_token": "zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial/products/8", "status_code": "<pre class=sf-dump id=sf-dump-539469317 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-539469317\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>field</span>\" => \"<span class=sf-dump-str title=\"23 characters\">expense_chartaccount_id</span>\"\n  \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"3 characters\">282</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">87</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2032 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwu%7C0%7C1960; PHPSESSID=o1mek9etnn9ejsik46u9hjcosg; _clsk=17t60l2%7C1750171898702%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImpqWFV6Nk8xZTZnMHN5R25xNnQyUXc9PSIsInZhbHVlIjoiL0FRZXVjd1UvRnFpdEo4ZzdxOXVBYTFmYTQ2Vy9ROGRFU2xKMlhwRGVacUFoMnJrQnRzMlN0U0FGazF6cWZoL0VJYlhEWk53Q2RCQ2tPUS9iSzluRG9Db2RnZ2pqRkVMelc3QWFCR21BVjh0a1lGcVkxZW94VVp3WHdzZ2NzT211L3loZG56L0pGZ1A5cXQrYWlGNUVxT2hoOVJZaEZpcHcvZHJ0dnZQQ3R0K1Y4aTZtMDZKS3VPa3QzU3dyZUpQdEVkTUZrYWRhTkpNbk5KU1hDYm82TG05b2kyTXdNWFk3VDJkK2ZTRnNYaCtCWXNmYmJ5UENDTEl3a3NMaGR5RTAyVnBBZnJLS0NvYitzY1g1V1VOMmw4Q0RLUWY1MWN5RzY4b0NLcmlua1NNenRRVmpybWxXdHRyb1VzanFRcWIvaWRUWUpUVUxFUURsWXhmbERZMUFzZGlUTnB5NWtaeFVRWksybXZnTDcxZTZBbENNZndPbWsxNVIxd002eGlFVDcxQk5CN0IxMFdLcFUybXBkQnRINGVGeDcwaHA1R1FHVGY5bm1GS25LL21FQ0RVQ09DcTN1czFzZXVkam43MVQxU3dSa0JoSm10djB0VUFvZXpuWjc0c3Y2bWpnTjVGUHU1Zm1nNEpSZ01XcExlTzYzVGl4TzYrdk8raEZIN2UiLCJtYWMiOiI1Nzk4YjM4ZDU2YzJjNzVjY2UxN2QwM2Y5M2MxZjRlMjkwOTcyN2M5NjAzZGVjOTZiNzllMWJhMWRmNTYwZDI0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjU5elZIeVlzdHBDNGVSUW5GMDVBTVE9PSIsInZhbHVlIjoicS9ycVVZdjFlektNRHhkT2RXTGNWeFdlZmRrRUdETGEzSTJmcHpwNGhjb0dlWllaMFpGY0hJMldsdUtacUdZU0JGd05LSGZzc1NEOSttbEQ3emVpVDUxVU94VTkyS2tNQWQyRTAvQWpxQURtWnhDYVpURnhlek9SSlBNVEMzd3Z5Z2FOYXJQdk5EeDFyUHMvelNyZ3VGUUdubGczTkZydFBaWXcvcnRVT0NrblpEVDVGNTZTL0ZxaCtyVDFNSXR1N0Vua3N5dlpHSnpYNk45RHVuSDNLbGhlNkdxQ0ltNW9udWlNSWhWU2JRa1VJT1l3NE5ycTRCaFo5MFR2UmU5M01hOVlUSlIxSElZY0ZyeVBFOTZIUzR3Q2l6UllrUXJuZzJpeWMrMnA2V3FQVlU1V01JSmJvYWg2eFJmV1gxOWUycitUNXBYUHVuOFM1ZjRKbmhzQkpNck12MHhZRi85R0ZCeU5TSEE5b0s3Q2pQSTc2YjdaQ2MyaDAxejVzdU1YQmRGaGxnSE9OcGwrUGtFN2RzTUF3QWZEY1RUZVhLV2tTQjc0NDNjQ0tSUW5jYWNLREQyOHYxTGRsMUtXOGJCSTNCMVYyem9qUkpybkFzeGdrdEI0cFU0SVdaRHYzK2s0M0RwVktiRkFLNVpjYzZiVG5veVlHejFvVGQ2SXdZdHUiLCJtYWMiOiJhNGY1NjdhYTE0MGVjMmQ3MDNiN2UzOWMyODk3YmEyNjYxMTc4NDQ4ZjYzNzdiNzAyNGY3MTc4NmE5MmU5ZjExIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>PHPSESSID</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HxnOnSuhAAPTfN2VS4q58slgJoytwtHYqaX0oLoW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1471061706 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:51:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IklVL29XWnByOWxja0h5UnA3ZE1UUFE9PSIsInZhbHVlIjoid1huYkVpOE1ibVFqMzgyZkZnM3FTd1lXWHNEblFqQWV2RVRCeGpyYnhacElaeTdrSit3R0x6WVF4TERoT3FsRnFKU01CbkVQMG00QURqcXlMVUExelF5MkV4cmE0M05LSFlHR0hiS216TElFK25Gb1BGdFZIL3diZ09xK2R6Y0NiNHlYS1JHR2taUGc4VC9lcXN2WU4yN0w0eFJXamEzclhVcjh5VFhiSURCdDlKTGxKUzNSTzg4RURXdEEyRVNPUldLckpicWIyKy80RkZGMEdFSFRZZEdTTms3eTUveHUzUkdUOWgwYk91OHRTQ2Mrd0pJVWRlUHdmK0RIRDdzaGhFdVlNdGw5bFFFdFZmYXJtbEZrSnlWOHAwaG5zVXR2RUNLOFV5OFc4UkMzMXV6QTFWdEV2K3RFZGNLaDE5emE1RWxweUlLS21Kc3dFM2E3bHlWcWNiQkt5VW1JUWhYZndnbm04M29XTDFwd3FuV0ZLbm9sUXNjQi9aVkNVbjJCeUsyQjF1Ly9QTXlRN25OYk5JQ0ozZW5QV1hnQmI1ck9iMFpVTis2ekhNWjNyQVFPM3BydW5SWklwbk8zTFdkWXRIeGdjMU9zcmsyVjBlbmFIOVlVK3ZhSjJpVVZuUzFzWVVpaUNlRERHd1V3elRiLzdGZ2ErV2Q3TDRNYTV5YTAiLCJtYWMiOiI3Zjc2OGQzYzZhNzc1MTZiYjVjOGQ0Zjk0MThmODIyNjQwMzQ5M2RmMTQ5ODdkNWZmNDhhNTAzN2Q4NmE4OGVjIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:51:45 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImZOMnRCUUdweE8zODlpRm9mV2cySXc9PSIsInZhbHVlIjoiR0t6ckZQcyt6dkEzTHg5dkdYc3BUNXhhVjZGSDE1cnVBdUFvc1EwL3V4RnpqYUc3c2NyTjk2MkRyL09YUTZWdDE0alpUd2sreHN0Y1ZnL0xaeG4zN2h3U2lLbkloczlteEExZWNyRFltSkY3S3RzRzVPZm5zNWR0RmZPdlhrMU52SHc4K0syYmNFZUpHSFpBMkJxdGhLYTVUS2I2dk52cytlVkRsV00vR3k1MkJkU3Nici9sWm9Hay9LRFpxWCtQTmM0bllEbUs4cjhmL1ZLOEJzMGNjWUUxWkhvbnRmL2o5bUlkUG8zMFhOUW0ybUVSbGZFNUw0TXVXeld2M1JHQ1NwMWVPRi80SEtkYkJoVlA1SE50cXVTZnF4U0NtdHRRNm1ldkNvU2tBMmUvdS9jUmFZM2o2VUc4MkdRWGtoQXpEZUNURm9rSHpKaFM0SXdxWmFkTmpRMjVmd3ZUWDNURDVGdnRVS0Fra0RhYzhGYVBPd0t1SE5INU1tTmVzSGRPWURYbkVtZDJJWEZnOUtiWGRUUjl4c0lGZEJWend1enhDYm1OTWt2V3RMeTVncHErNFpFdmh4UWhJMVE1SFIwZ3RiVlB1Tjh6dUZ3cng1dEpZc3Y4bXVqZHRnbEZYY1VJaStGMzYwYkRBRFlMYVhiOGJjaDJSaHRaQm9HdnJJSW8iLCJtYWMiOiJmOTY5MzFiY2E4NzljYWRmMmQzYjBkOTIyYWNlYjlkODk1MmY0ZjM2ZmJkODk1ZTJjYjFhMjFkNjU0YzVhNmM4IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:51:45 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IklVL29XWnByOWxja0h5UnA3ZE1UUFE9PSIsInZhbHVlIjoid1huYkVpOE1ibVFqMzgyZkZnM3FTd1lXWHNEblFqQWV2RVRCeGpyYnhacElaeTdrSit3R0x6WVF4TERoT3FsRnFKU01CbkVQMG00QURqcXlMVUExelF5MkV4cmE0M05LSFlHR0hiS216TElFK25Gb1BGdFZIL3diZ09xK2R6Y0NiNHlYS1JHR2taUGc4VC9lcXN2WU4yN0w0eFJXamEzclhVcjh5VFhiSURCdDlKTGxKUzNSTzg4RURXdEEyRVNPUldLckpicWIyKy80RkZGMEdFSFRZZEdTTms3eTUveHUzUkdUOWgwYk91OHRTQ2Mrd0pJVWRlUHdmK0RIRDdzaGhFdVlNdGw5bFFFdFZmYXJtbEZrSnlWOHAwaG5zVXR2RUNLOFV5OFc4UkMzMXV6QTFWdEV2K3RFZGNLaDE5emE1RWxweUlLS21Kc3dFM2E3bHlWcWNiQkt5VW1JUWhYZndnbm04M29XTDFwd3FuV0ZLbm9sUXNjQi9aVkNVbjJCeUsyQjF1Ly9QTXlRN25OYk5JQ0ozZW5QV1hnQmI1ck9iMFpVTis2ekhNWjNyQVFPM3BydW5SWklwbk8zTFdkWXRIeGdjMU9zcmsyVjBlbmFIOVlVK3ZhSjJpVVZuUzFzWVVpaUNlRERHd1V3elRiLzdGZ2ErV2Q3TDRNYTV5YTAiLCJtYWMiOiI3Zjc2OGQzYzZhNzc1MTZiYjVjOGQ0Zjk0MThmODIyNjQwMzQ5M2RmMTQ5ODdkNWZmNDhhNTAzN2Q4NmE4OGVjIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:51:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImZOMnRCUUdweE8zODlpRm9mV2cySXc9PSIsInZhbHVlIjoiR0t6ckZQcyt6dkEzTHg5dkdYc3BUNXhhVjZGSDE1cnVBdUFvc1EwL3V4RnpqYUc3c2NyTjk2MkRyL09YUTZWdDE0alpUd2sreHN0Y1ZnL0xaeG4zN2h3U2lLbkloczlteEExZWNyRFltSkY3S3RzRzVPZm5zNWR0RmZPdlhrMU52SHc4K0syYmNFZUpHSFpBMkJxdGhLYTVUS2I2dk52cytlVkRsV00vR3k1MkJkU3Nici9sWm9Hay9LRFpxWCtQTmM0bllEbUs4cjhmL1ZLOEJzMGNjWUUxWkhvbnRmL2o5bUlkUG8zMFhOUW0ybUVSbGZFNUw0TXVXeld2M1JHQ1NwMWVPRi80SEtkYkJoVlA1SE50cXVTZnF4U0NtdHRRNm1ldkNvU2tBMmUvdS9jUmFZM2o2VUc4MkdRWGtoQXpEZUNURm9rSHpKaFM0SXdxWmFkTmpRMjVmd3ZUWDNURDVGdnRVS0Fra0RhYzhGYVBPd0t1SE5INU1tTmVzSGRPWURYbkVtZDJJWEZnOUtiWGRUUjl4c0lGZEJWend1enhDYm1OTWt2V3RMeTVncHErNFpFdmh4UWhJMVE1SFIwZ3RiVlB1Tjh6dUZ3cng1dEpZc3Y4bXVqZHRnbEZYY1VJaStGMzYwYkRBRFlMYVhiOGJjaDJSaHRaQm9HdnJJSW8iLCJtYWMiOiJmOTY5MzFiY2E4NzljYWRmMmQzYjBkOTIyYWNlYjlkODk1MmY0ZjM2ZmJkODk1ZTJjYjFhMjFkNjU0YzVhNmM4IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:51:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1471061706\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1358581400 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwnmKcRLmNWGDZ856YJrjbbNuEV0Tli3iJOiUNS6</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1358581400\", {\"maxDepth\":0})</script>\n"}}