{"__meta": {"id": "Xb784e27ad4ba80cad7643f4ede665544", "datetime": "2025-06-17 14:56:41", "utime": **********.261351, "method": "DELETE", "uri": "/remove-from-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 3, "messages": [{"message": "[14:56:41] LOG.info: removeFromCart called {\n    \"id\": \"6\",\n    \"session_key\": \"pos\",\n    \"request_data\": {\n        \"id\": \"6\",\n        \"session_key\": \"pos\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.222784, "xdebug_link": null, "collector": "log"}, {"message": "[14:56:41] LOG.info: Cart before removal {\n    \"cart\": {\n        \"6\": {\n            \"name\": \"\\u062e\\u0636\\u0627\\u0631  \\u0641\\u0648\\u0627\\u0643\\u0629\",\n            \"quantity\": 1,\n            \"price\": \"9.00\",\n            \"id\": \"6\",\n            \"tax\": 0,\n            \"subtotal\": 9,\n            \"originalquantity\": 4,\n            \"product_tax\": \"-\",\n            \"product_tax_id\": 0\n        }\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.251731, "xdebug_link": null, "collector": "log"}, {"message": "[14:56:41] LOG.info: Product removed successfully {\n    \"removed_id\": \"6\",\n    \"cart_after\": []\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.251932, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1750172200.67515, "end": **********.261387, "duration": 0.5862371921539307, "duration_str": "586ms", "measures": [{"label": "Booting", "start": 1750172200.67515, "relative_start": 0, "end": **********.158995, "relative_end": **********.158995, "duration": 0.4838449954986572, "duration_str": "484ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.15901, "relative_start": 0.4838600158691406, "end": **********.26139, "relative_end": 2.86102294921875e-06, "duration": 0.10238003730773926, "duration_str": "102ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48634848, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "DELETE remove-from-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@removeFromCart", "namespace": null, "prefix": "", "where": [], "as": "remove-from-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1675\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1675-1748</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00865, "accumulated_duration_str": "8.65ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.200755, "duration": 0.00463, "duration_str": "4.63ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 53.526}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.217174, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 53.526, "width_percent": 17.457}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.241073, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 70.983, "width_percent": 12.832}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.244822, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 83.815, "width_percent": 16.185}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 16,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1475989639 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1475989639\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.251144, "xdebug_link": null}]}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "[]"}, "request": {"path_info": "/remove-from-cart", "status_code": "<pre class=sf-dump id=sf-dump-1081502480 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1081502480\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-141205037 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-141205037\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2110913432 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2110913432\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1188362379 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">20</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IktlS05JTTRYZjQ0aW9EanIwRm8rUEE9PSIsInZhbHVlIjoiemNTUEJFTXhtSW9RQnNPMU9Ua3o0dFRzQnJTa1htNlBYV0NpbEJVZk9oUGZlSkw4TXkwSkdzeElKUnhpV3RZdjE1eWdGekxFWExXZjNVSGZjN3NybmYydHkrTlZqZitUV2IvZlhBWGZBSGs0TTJkUHI2Y2ZTbHNjL1ZpaTQyV0dYTWE3UWRjZzhHeUVRdGYzeUpqblc0L0FPbm1WbUh3R0x2b3lxN1dsKzFYZXpPNGZ5NDg4OWVFaVpRWncvYU9OeGhaMURQU0pwSGVtNTdBYXU4Um8rbElpajhOMkpvbHBsSzZob0kyanRjRFdjTmkrbjl5dXg2NExUdWlyK2R2bjdSblFnTmxUOEIzTllaWUZOcU9DeDcwWWZDVlNhS3lFYVFWSjUrYmVtdFhReGJwMGIra1NzTmZVNjh1UzF3QS9DWHZYY0EraVgzSXYvM0RxWTQ2UmcyWjhESXZ0VFRqc0dSN1JiYnUzL1FhMFVSM0M5V1NTM1ZZS2tBVzJucUpzQ292U1gzOFBYTSt4WlFZSXhHV0pEMTN3QnMrVDNOMThFeExxQXg2RGtrOHpHdUtRdXZJaW1PN2ZjSk1PeENueUNxVGNtTnY2aTBvaWlPd3hvWXBXa3l0T0lqMEhYaEg1bTV3dHVqUm00T2thK1lCR3FtR05QMXgxY0FKU0haNFAiLCJtYWMiOiIwMWRiYzgwMzJmMzZiNmE3N2MyMzgwMjdhOTM3NmNhODk5MjZiMDAxNGY4NjA1OTUyNTNkZTdhZDc5NzUyZGU2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ik9zV2w2amVNZnJpSDkrYzRHZ0NhdEE9PSIsInZhbHVlIjoidFRJd1RtYjdoWWpZZVEyTDNvSndTcml5SUpUZjQ3a09RV1pUWXdwdFJFSWl3Uk1vcGh2bTU5dHFiaVFmMU1YVmg1K1ozR1J6QlBNWlprOC9FMFJST1VCTWJqSUdXVzBKTVNHc1plV1h3TWFFMG9mYkVETTdyR0VaWW04OGhmdjFjbEFSWER5RFVjaEpJczBsellrN29WRlIwSTY1OVYrMFNYVGt3NW5iNjNCNXp5S3FFQlgvME4yT256REJpYVhkR0tjcVBUN1VVemlSS2J2eHBIUEI4MnRraEpyZmdyelY0ekJLMnp5OVo5emRGcjYyYVRGblFiWGMzOGlQVlEyVU51SkppeDczemViczU0ZzdHdlhtd1doUXZ3WEV2Z2o0QmRweC9xVE8xRTdTZVE5SXROelZHb29rdDAwVTFsbFNuWEdZMWpUN1pqc3JkVFdsbnhVZENkQnNBYXgwQ083dlhVVHdpeFRPTHgzcmJ0REU3QXhlVUVkUXJGbG0rZFhoVjdPKytaZGNpOE84cmgrM0ZhNVQ4VE1xYlJ5dUd6N1JBU0ZDeTArb1Ayb1hibG5sd1RKU2lsTkErTmpoWGpRSjVIblJFTzFXT3FrM2JyeVBrdlAyMG5KUVQwbDhzVXNDWFhzUm1zTDB0YXZsL3RaWlNEZ3VCaXdzUXA1bGJpdTAiLCJtYWMiOiI2NDY5MGFjZGRjY2JhOGE1Yzc2NzFjZTAzMzk2OWZkNWY3Mjk2NzhkZTNhYTc5MDBiOGRkZWI0ZTIzYmQ4ZTVkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1188362379\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1385502271 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:56:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ink0eldNZVB6dWRhenVxUmM5T0VhYkE9PSIsInZhbHVlIjoiTUM5ZFJGTUJZZVM5aTl1ZXl0NVlrWlJlQzQ0alpCdHZLV1V0YzVhcXVkd2NtZkdmWklZSytQZHhDVkdXalIzUmM5MEdDOG1CVkdUenZTNkd2NDY5Vy91Q1pkeG1VTzhLNnBtanVjMkJIdkt1ZHFTN3BVWDh1cFg2ZHE4YmpJNHA1TUk5MjFReTVaOHF4a0pqYWhaU0hEdDZ5LzVxbUZzVmFidXpsdUEvM2trMDdtVWxDU0Y0OXgyd3lNanhIbnB6Q0M0Wm5QNktXRU1hMVRMUGFKaXdvV2RvVGtTTXNXem9JMk9oVDJYMG1wN2N0S1hvdkV5Q0ErdkNJNkR2bm9nUHFzZ0pIVXVSMjhLbzl0ZXZCR3Y4SEFKem0ySFB3Ris4L3R2cVRZUktPS1U3YW43SUZjTXE1N2p3cHdHZEh4ODIxTlJ1aVhWR3d0c1hLYUFqWTVYcWNpNExOWUx2clBLTERZRjA5MEs5Q01ZUUdzT3V2RkZ3UUxFRkxDQkozTVphOFlzb2cwdGQ5S20zcEM4L3RPVlBBYjNES0RHQVJ2QWlPQzdjNjVQbGRiV3pWczR4cXpZdnlRUTQ3Sm9kUnAxRmZtV0pXdFVSSnhSY0xMMkl2N2xWakdoOUVnK0ZqWFpSSlNTelJnNjdhaTN6a2V5TUlmZHBYZHdQR094S2diQlQiLCJtYWMiOiI4NzA1MzExZDE2OWFmYzczYWZmZDcxZmY0ODU4NjE5ZjNjMGFkNjVkN2YyZmM1NmIwZmFlNTA0OGM2NzIzNWY1IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:56:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkNlVldDSHZnN0hWdkFTNjR2NDVNb3c9PSIsInZhbHVlIjoiUGxOWWZvQ1p1Y1lXMTFadGNhalV2d3BkL2VrcjQydkFNNXB4WmpXZUFQb01HTUc1UVlCNHBjTmNLUzB5b1lacXh3cC9YallNbkM3UzJySXkwendWQW9TQjYwdVhSTmFtVUd2UTdwbzRtQmxqRmFLdFhXM3JiMEVIMCtFZ1VsM2ZaSDlENlgvR0IrTWtxVld2SW5MYnN5aEJNcHlnMThSQlVRQ2dUdytPL3hYTlVzZXQ1YkJEUHZJZ2szSllhY1FOdENGM1pldHZ1UnVTRFVxY1JsL2h4TE5ic2Y2eFAvaTlTT2ljZGJucDh6TE1RMkV1ZnYrMW1SRVBUSjBVWHNYMklmNE90SHdWa1pUOThNdktJUmUyQlczUzZVc0ZTRWFQSllZaEVGN283c3psWERDc1F0b2h4YUU1K0QydWE4L2s4MGxpOEhMcGxDdTNHMFdrSVYzcXczOXhkU3o3MW9EdjNCNHN0T3JWMDF2VUxueWRURXdDUXhXYlhRbmRYcXhBVG9wZlNzMXhMNlpTRnUvV3U0YkYyOEVQSWRlVXg2NDg0VDA0N2VQUkhiR3ZwYWtDVWQyVFR2QjU0dUxHT0UxNjIrRWtQZXNxN1lGN3Q4YjUxbGF2UVhZVFpHVFRQMVZkU0I2bUpkcGFzUXRLU04zNlJvSDhhQ0pOSlcySGdsVUoiLCJtYWMiOiIxODMxYjMxZTgxZDc2NTRkMjYzOTdlZDAzMjA3OTk3ZjllN2RmYTI5MTdkMzVkYTBiODFjOGY0MTA1YzQ5NTE1IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:56:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ink0eldNZVB6dWRhenVxUmM5T0VhYkE9PSIsInZhbHVlIjoiTUM5ZFJGTUJZZVM5aTl1ZXl0NVlrWlJlQzQ0alpCdHZLV1V0YzVhcXVkd2NtZkdmWklZSytQZHhDVkdXalIzUmM5MEdDOG1CVkdUenZTNkd2NDY5Vy91Q1pkeG1VTzhLNnBtanVjMkJIdkt1ZHFTN3BVWDh1cFg2ZHE4YmpJNHA1TUk5MjFReTVaOHF4a0pqYWhaU0hEdDZ5LzVxbUZzVmFidXpsdUEvM2trMDdtVWxDU0Y0OXgyd3lNanhIbnB6Q0M0Wm5QNktXRU1hMVRMUGFKaXdvV2RvVGtTTXNXem9JMk9oVDJYMG1wN2N0S1hvdkV5Q0ErdkNJNkR2bm9nUHFzZ0pIVXVSMjhLbzl0ZXZCR3Y4SEFKem0ySFB3Ris4L3R2cVRZUktPS1U3YW43SUZjTXE1N2p3cHdHZEh4ODIxTlJ1aVhWR3d0c1hLYUFqWTVYcWNpNExOWUx2clBLTERZRjA5MEs5Q01ZUUdzT3V2RkZ3UUxFRkxDQkozTVphOFlzb2cwdGQ5S20zcEM4L3RPVlBBYjNES0RHQVJ2QWlPQzdjNjVQbGRiV3pWczR4cXpZdnlRUTQ3Sm9kUnAxRmZtV0pXdFVSSnhSY0xMMkl2N2xWakdoOUVnK0ZqWFpSSlNTelJnNjdhaTN6a2V5TUlmZHBYZHdQR094S2diQlQiLCJtYWMiOiI4NzA1MzExZDE2OWFmYzczYWZmZDcxZmY0ODU4NjE5ZjNjMGFkNjVkN2YyZmM1NmIwZmFlNTA0OGM2NzIzNWY1IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:56:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkNlVldDSHZnN0hWdkFTNjR2NDVNb3c9PSIsInZhbHVlIjoiUGxOWWZvQ1p1Y1lXMTFadGNhalV2d3BkL2VrcjQydkFNNXB4WmpXZUFQb01HTUc1UVlCNHBjTmNLUzB5b1lacXh3cC9YallNbkM3UzJySXkwendWQW9TQjYwdVhSTmFtVUd2UTdwbzRtQmxqRmFLdFhXM3JiMEVIMCtFZ1VsM2ZaSDlENlgvR0IrTWtxVld2SW5MYnN5aEJNcHlnMThSQlVRQ2dUdytPL3hYTlVzZXQ1YkJEUHZJZ2szSllhY1FOdENGM1pldHZ1UnVTRFVxY1JsL2h4TE5ic2Y2eFAvaTlTT2ljZGJucDh6TE1RMkV1ZnYrMW1SRVBUSjBVWHNYMklmNE90SHdWa1pUOThNdktJUmUyQlczUzZVc0ZTRWFQSllZaEVGN283c3psWERDc1F0b2h4YUU1K0QydWE4L2s4MGxpOEhMcGxDdTNHMFdrSVYzcXczOXhkU3o3MW9EdjNCNHN0T3JWMDF2VUxueWRURXdDUXhXYlhRbmRYcXhBVG9wZlNzMXhMNlpTRnUvV3U0YkYyOEVQSWRlVXg2NDg0VDA0N2VQUkhiR3ZwYWtDVWQyVFR2QjU0dUxHT0UxNjIrRWtQZXNxN1lGN3Q4YjUxbGF2UVhZVFpHVFRQMVZkU0I2bUpkcGFzUXRLU04zNlJvSDhhQ0pOSlcySGdsVUoiLCJtYWMiOiIxODMxYjMxZTgxZDc2NTRkMjYzOTdlZDAzMjA3OTk3ZjllN2RmYTI5MTdkMzVkYTBiODFjOGY0MTA1YzQ5NTE1IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:56:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1385502271\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-923554693 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-923554693\", {\"maxDepth\":0})</script>\n"}}