{"__meta": {"id": "X2bc21427b1748b74123de4b667a53f6c", "datetime": "2025-06-17 14:35:23", "utime": **********.18185, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750170922.197116, "end": **********.181872, "duration": 0.9847559928894043, "duration_str": "985ms", "measures": [{"label": "Booting", "start": 1750170922.197116, "relative_start": 0, "end": **********.03555, "relative_end": **********.03555, "duration": 0.8384342193603516, "duration_str": "838ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.035563, "relative_start": 0.838447093963623, "end": **********.181874, "relative_end": 2.1457672119140625e-06, "duration": 0.14631104469299316, "duration_str": "146ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49205496, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.014329999999999999, "accumulated_duration_str": "14.33ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.100091, "duration": 0.0092, "duration_str": "9.2ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 64.201}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.124052, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 64.201, "width_percent": 6.141}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.1500669, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 70.342, "width_percent": 6.56}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.1541972, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 76.902, "width_percent": 8.025}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.1638198, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 84.927, "width_percent": 9.072}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.169111, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 93.999, "width_percent": 6.001}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2121565063 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2121565063\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.161831, "xdebug_link": null}]}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-758207477 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-758207477\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1296798751 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1296798751\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-807453980 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-807453980\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1273292149 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjRKNWl0eVg2NGxZR3ZpekloNEFWQ2c9PSIsInZhbHVlIjoiYS9kTVBBSE5La0tZdzJZQXV1OTBOSlp6ek5sZlZNNTJ5RVhRbzQ5a08zTlQyMGdlcEc1aDlDazl1bUFDQlFpS2RvcmRzK0dZWCtEZElSN1hZMUNJUmFrMkRNRm9iM3JhNWh5THo0aUplN3NETUM0VzBJNDNWTHlkc2lLRXN4aEptVU9EOW1iZHBwY2J0RGtjQk1MT2lQc0xGOW9mc1VPakdIU2wzTmM3ZDZkWkJrZmJsNmlyN0dqaXkyTjVwd1pSeHdTTEUwZjlSUytDNlNmUm9kTnM3VVN0SzZsUHA2dXh3azZ3VWltaU9EVlJVMzErclVGSGk4aEJMemJYekwzVWVhaXUyL3Mra3Zkd2Y0THNIb3Z1bURDVjBucTJPeXZheDRmMUlIUTNxaFYxSER0Q2lFN0dnT2twM3ludVc4YzhxUC9EZGNoK1c2ckh0SWQ4bnZFa3lYSTRUajJPVmt2NzJYblk3WHdlSm02ekZjVmdyelZnVTFDTzZoWWNFenNTQ3BDVVA4WUdFTldrRmF4WW9hdHFEeWttMkJtcllQY1RjSFdoTjQwRWIvMnpsMHpKdEZDeUNMbXhnRzloYWRUNWtUSmxUS0duUFJ0RUg4d3FuZUF4Mkl5dWJOVXVIV1dNTGV4TklSUStQQVAzMksraFg3SFpSZjR3UWIzWVNJeUYiLCJtYWMiOiJjOWYzMjlkOWQxNTAxZjUxMmI3OTRlMzg0OGEwNTY5YTk3MzgxZjJiMDJjM2IzZjFlNGFiZjYyZDhkZDU3MTJlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Imo1MTYvWUppc0phQWZLMGhCQXF5dVE9PSIsInZhbHVlIjoidWFiVkVhN3F6V2NGYzAvZ2dMWXY1NmRGdXc1UWpIdWdXMTFLeHcxUm1teGJGOGRBTlo5eFJlUGpnRjhFZUxPRE5UQXY4clpRcmhITGg2RS9BT3FUMS9rSndMLzVLMDU1ZDBNaU5NNkNGZWtneXhzOXFIbnByOEtEQzMwR2VyK0hFYW0zZVdBTWlGTkNWY1dRS1NFblRKSDRITTlnbHIzNlVKaVFHK3lra2xQaDJSVnltMkFnZlAxOVJGL3p5ODFqTHU5YllSSG82SEVSZ0FBNGZGa2JDSElPTmg4dkpIdlhHVHNRamxJVTdTaHhEb1BweFI2UlJFZ2dEV00xcVBYa3ZTRnFmWWg0YVFHTXdsUnM1bEh0V1FOQXhkWU41bzJTYXJBU0NWaEhGWEVGRXhBdTRBYi9mbEtZM1RSODBycmpHM0lRekxkQ2VNOUpHN2o0MEg2NU4vYnl1WnRIZEp0R1c5SFlVc09TNStsVVBDK014bVJUZ0MzS1hxOS8vQzR0WDlCOWJQcGNrdXYxOXZvNzgxVkZ1bURDTnZqVTF6a3dEaFlwZ3hDRCsrVEdCWTdMWllHT0JRVWc4Q1RuZ2dVcjlQNjFmL1A0aGpvSTU4MjFwaksyMFBzT3JUM0F4eFJXRSt5Z3ZDSmJubW81bGl3VlRUUDZwQmhaQVNmNm9jR3EiLCJtYWMiOiI0ZmZmZjlhZTM4Njg4NDI3YmY2NDY1OTdiOTNiNWI2MmZhOWJjZmM5NGRkYjE5ZGEyMDJiNzU1NDRhZGFlZTEyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1273292149\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-869449220 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-869449220\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-310743965 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:35:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkZuS3dsdmFkZCsxdnZlSm5udTBDMGc9PSIsInZhbHVlIjoiQ1VpTGRFSG4xVTZXS1lNaWpOV2wrUkhobmVHNXVYZ00vQjRMQmZySFdXMFV3Vll4SWtyRkF2TkQvaVlkdU5BQ3AyVFA4VHJWcmRoQUxQMWtYRnMwVWwxY0FZdWFVcjVnUjVsMGdnQm5YNFJaUHpMby9iYkJSbFRLSitlTk5WSS9SM2VCcmpyQ21DRGlzU0ViOG5FcTBqSGlja0tucXl5aHppMHQvMmtVS3ZaYlpRT05TL3ZvVDlDRU05NWlxbWpvMHlJc0JEU0lUK09XSEtzTkJzTEowWEV4WmRmL010SU1iZ3czcC84dFR3Q1VWZ21JTmJwZXQyVUNrdStpbnYxTTJ3dkJTTG9Rb1EydVBtNldDeGpaYXI0amNqd3o4UG9kMG9kQzRab1R1ejhNSzFZczdRcy9YS09UVUdEZVhaY2t0eHlGUm81T2NHU3RjQWJDM3A1WExsVlAxY0VSSitORzJQalNBZFZaQXpJYldiSzVRelNKeGlNSXA5VlVzL2srSkNRa1k4QlF2UVRJM1kzeDlYbnpac0R3L1BMTDlQd3FMaTlkT2pSRGZKYUkxdEEvdFR4S2M3bzNWMGRuVUI0ck1tZUhaSVdVcXlhVHlpWnh3bmJnblMrdmNGSTdSV0pmbm5JYUtySmFUL3pHa0pvTXdiWFlIc3NobExyU2pEb2ciLCJtYWMiOiIyMjcyM2FmMDU0ODkyMjUzZGM5YjFhNmI3ZThjYTQzZDhiNjNhMWJmYzdkNmU5Mzc2NTg4ZjA0MGU4N2RlYjhiIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:35:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InRrNVA5S1UrRklXaktuTE5ENThFTVE9PSIsInZhbHVlIjoiaFdhbk03R0xXUXNsbThkNVc3d3hNZUF6ejgxc0ZJd0d5bkhtZHc0Y245VFlHWFJrekdzVVk1YWpMTTdkR1R5K1ZQUUNXVFhPcFpZeHBIYlRPaW9RdEhBMDB0L0x2WTkvYUFSYzZMQytDd1dQMHc0TGNvdVVES085OE4rNnpXSVVJNGFYWjNOMXRvYUtyU1VSK3VDaUZxdHdkUTJTVjV6ekwrYllNLzVTTmlNdGt2aWxEMWd0TjZiKzVIZXo5eGtRbTQyV3FoU0U1MFNqMkVxaTRva1I2cVVEZ0FXSWZad3M3UUh4ME9aVSs4UG5yVlVxNHZsOTdSREZnZXRIY0xFd2I4U2M2WHVNRzIydjJwbC9GcnRDRUhia3hDUXN3aXIwbG02RXArMUQ5TFhyUzFabFo0MXVhditkbkxWb2lnR0x3VllXOURjY1hrRHdOWk51TVpncFVPWUpJV3B3QkpiV3o4aEZSTlY3WllMM0lkUWZWVDdNZ3FkS2JBd2FNMGV5dlVMUlpFUHRUQ1R3Q0VJc1dQaU1jZGRCYWNKekZNazBoNnh4VFNXOCtDclZqYythSTRtczBwQzJlZnFUZG5ESTErazVvZVFDQlM5ZUZlZDN4UGU0QSt3WkJPdGpUVUxFTStPS3A3a2lDc1ZNMkFsSFRYSzR6SEtEeTMvYTQ1U3EiLCJtYWMiOiI0MTIyMDgzN2RkNWVhNTkyNjJkNTJmMDgwYzQ2YTgxYjBmOWIyZGM4NmM1OWJkYTFlYzUwZWJkOWYyMGI5YmEyIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:35:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkZuS3dsdmFkZCsxdnZlSm5udTBDMGc9PSIsInZhbHVlIjoiQ1VpTGRFSG4xVTZXS1lNaWpOV2wrUkhobmVHNXVYZ00vQjRMQmZySFdXMFV3Vll4SWtyRkF2TkQvaVlkdU5BQ3AyVFA4VHJWcmRoQUxQMWtYRnMwVWwxY0FZdWFVcjVnUjVsMGdnQm5YNFJaUHpMby9iYkJSbFRLSitlTk5WSS9SM2VCcmpyQ21DRGlzU0ViOG5FcTBqSGlja0tucXl5aHppMHQvMmtVS3ZaYlpRT05TL3ZvVDlDRU05NWlxbWpvMHlJc0JEU0lUK09XSEtzTkJzTEowWEV4WmRmL010SU1iZ3czcC84dFR3Q1VWZ21JTmJwZXQyVUNrdStpbnYxTTJ3dkJTTG9Rb1EydVBtNldDeGpaYXI0amNqd3o4UG9kMG9kQzRab1R1ejhNSzFZczdRcy9YS09UVUdEZVhaY2t0eHlGUm81T2NHU3RjQWJDM3A1WExsVlAxY0VSSitORzJQalNBZFZaQXpJYldiSzVRelNKeGlNSXA5VlVzL2srSkNRa1k4QlF2UVRJM1kzeDlYbnpac0R3L1BMTDlQd3FMaTlkT2pSRGZKYUkxdEEvdFR4S2M3bzNWMGRuVUI0ck1tZUhaSVdVcXlhVHlpWnh3bmJnblMrdmNGSTdSV0pmbm5JYUtySmFUL3pHa0pvTXdiWFlIc3NobExyU2pEb2ciLCJtYWMiOiIyMjcyM2FmMDU0ODkyMjUzZGM5YjFhNmI3ZThjYTQzZDhiNjNhMWJmYzdkNmU5Mzc2NTg4ZjA0MGU4N2RlYjhiIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:35:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InRrNVA5S1UrRklXaktuTE5ENThFTVE9PSIsInZhbHVlIjoiaFdhbk03R0xXUXNsbThkNVc3d3hNZUF6ejgxc0ZJd0d5bkhtZHc0Y245VFlHWFJrekdzVVk1YWpMTTdkR1R5K1ZQUUNXVFhPcFpZeHBIYlRPaW9RdEhBMDB0L0x2WTkvYUFSYzZMQytDd1dQMHc0TGNvdVVES085OE4rNnpXSVVJNGFYWjNOMXRvYUtyU1VSK3VDaUZxdHdkUTJTVjV6ekwrYllNLzVTTmlNdGt2aWxEMWd0TjZiKzVIZXo5eGtRbTQyV3FoU0U1MFNqMkVxaTRva1I2cVVEZ0FXSWZad3M3UUh4ME9aVSs4UG5yVlVxNHZsOTdSREZnZXRIY0xFd2I4U2M2WHVNRzIydjJwbC9GcnRDRUhia3hDUXN3aXIwbG02RXArMUQ5TFhyUzFabFo0MXVhditkbkxWb2lnR0x3VllXOURjY1hrRHdOWk51TVpncFVPWUpJV3B3QkpiV3o4aEZSTlY3WllMM0lkUWZWVDdNZ3FkS2JBd2FNMGV5dlVMUlpFUHRUQ1R3Q0VJc1dQaU1jZGRCYWNKekZNazBoNnh4VFNXOCtDclZqYythSTRtczBwQzJlZnFUZG5ESTErazVvZVFDQlM5ZUZlZDN4UGU0QSt3WkJPdGpUVUxFTStPS3A3a2lDc1ZNMkFsSFRYSzR6SEtEeTMvYTQ1U3EiLCJtYWMiOiI0MTIyMDgzN2RkNWVhNTkyNjJkNTJmMDgwYzQ2YTgxYjBmOWIyZGM4NmM1OWJkYTFlYzUwZWJkOWYyMGI5YmEyIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:35:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-310743965\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1039262947 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1039262947\", {\"maxDepth\":0})</script>\n"}}