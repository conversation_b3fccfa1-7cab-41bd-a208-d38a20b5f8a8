{"__meta": {"id": "X6e890b6f9978e1e504f379658551c2ab", "datetime": "2025-06-17 14:50:26", "utime": **********.770921, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.193494, "end": **********.770942, "duration": 0.5774478912353516, "duration_str": "577ms", "measures": [{"label": "Booting", "start": **********.193494, "relative_start": 0, "end": **********.697853, "relative_end": **********.697853, "duration": 0.5043590068817139, "duration_str": "504ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.697865, "relative_start": 0.504370927810669, "end": **********.770945, "relative_end": 3.0994415283203125e-06, "duration": 0.07308006286621094, "duration_str": "73.08ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46326352, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1663\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1663-1673</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00973, "accumulated_duration_str": "9.73ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7396271, "duration": 0.00888, "duration_str": "8.88ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 91.264}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.759881, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 91.264, "width_percent": 8.736}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-2023365007 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2023365007\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2047794371 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2047794371\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1925908082 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1925908082\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-601825644 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik5CajBpZlVnb3A2Ukxzd1ZxVzVnSUE9PSIsInZhbHVlIjoiWmJaaE52UC8vWG94Z0x1czBaVTVWWkxKY0hVaFFEQjRzZDhJaEFYM0gyTGJjc0V0anJFLy85dUI0RkhIbmgxeTJ3dGZ6SHRHc3I3dmh0L2M5ZVEwTFBXMkdSSTYwU1phVDZja0NrMzhuczNHQzFBZVUyWGd1d2czNm40dHhOUDl2WkxmeGQxVlp2Wmh2UkVzNDVIcHdXa28zL0Q2ZlFrMzRMMk1Zc0N3a1pRY2pNUUxEYi9JN0ZualpLa3Azd2V4dWxHQXlBNHFVUTRHWmxXYlpMWG1xVUVsWmF3WklpWC9xV0tITERIMW1FV1REZXFlNkFKZXFCNEtHL0N5a2tZTE9iaWF0MmU4anB1VDEwOXVaVlVzZTR4NFQ3ZU9PTnptRUlkRWdWYWI4NXFMTFMvZVoxazlPaDJkQmlNQm1pUDdTNFQrdTQyV3dLUnFkMlBQTmVHQmwvTHZZWHhXbjVsZVN3aEt6cEd1RUhSdTdvMFY5U2tuVHpRbkZ4UFNlQUt1OERqUStaK2dqN04zTS9NYjlFdDlNdjBOaXQzdkZQUkFOaWc0UTFyRlhEb296UDM2NFIzUHhPVHJTUUt4RjNid0NrNUptUUkyQXFXUkpyS08rellJSXgvR0Y5MG8zL213aC91NlNEcW4zL25VeElKSFRURytqa1BkbjZmOTlaaUgiLCJtYWMiOiJlYjEwYWMxMjE4NWIxMTdhZWIyYjMzMTUyOTE0YzNmNTg2ODdlYWU3NWIwNWU5MzEzMDEyMWZlMzM1ZDIxY2M5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkxNL25EekNRNUNodnZUSGI2OXJyTUE9PSIsInZhbHVlIjoiTVIvZkFCRkhnQm1wRWlZRGFOT3ZjQXk1Wlh1RERXM2IyVXdiVXFIcGF5YkFRZ2xIZXd2N3RwU2dFSHNmY2M4a2t6YWIvZ3NjRGRkYXVRQkk0QlhXdnhNRWFqN2Q2QUxmdTF4SVQvdEFOWnlCRVUxOVQvQ2plSGZrTkRIaDUvMXY1QWRlMlI5KzQ2b3dXQ01pcURjUTU3ZFhHcTNkUVdjdEhVWUpVR3RNVXBQbGRQN21GbytuTFpsMXZXSUFZZGdkQ2xwNGlsbmNGTmNaT2R1T1pUZDZBRS9CTUJyUVNpNTBwRlYyaHVZYWgzcVFVT1NWRzluTk1KbGxaZStMYTFiSXFOeGZKQzVUanROemxtUVRUNFBDcDRBMVRQRHpTZnNhWm5BME4zNEp6a1ZWRnpRN05qMzV4allnVllYVzFIcE85MGhwUlA3U2JpV3NZR0pkZHNOUjhYY3pDZTZmU2JlU05MNXBlY3NZYnF2cGkvM0FvdnhzT1hkWG1tK011NGpWTjVpUXFYeGVJTEpPaFRKUWM0MkVJRGVRQUxmUjc4amxuam95Y1hDYUtXdHhMbHRSZjBBM2ZYbGJaMWpIY2tUUVhIWlQxWnpES2NsWDNwNG5ZWUdYRGw0cGdqM1QraGFhZFR2azBuWHlUYzI3enJ3cXZ6MFBlc1dzNjczQ1FwZHUiLCJtYWMiOiJjNjA0MWZiOWQyNDliNGJkYzBlMjNiMjJkMGNmNzU5NmMzZjJlMGRjNjc0ZTkzMTg0YTg4NGRlMjgwMjkwY2I1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-601825644\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-47777974 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-47777974\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-484555071 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:50:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ijd0NFlCKzEzd21La2x1YXFWcldveWc9PSIsInZhbHVlIjoiVzVoNkxBT3F2eHFHOFcxdGF5ZmJmRFRxLzVGb1UrUjYyOHJRNkVYOWFhbUNvTnpnNkJkbVlJT0FrRm50QkJhek9oOGVBejRaQjlQN0VvcC9RNUlJSWdNY21FdGc5L1hReERzWmRTN05ONkZXTGUzalNzT3pqU0gySVdtN1dzNGRyb3ZTTS93RGdGSUU1ZUc3VFlRQTRMRnZTVE1tSWJtY3QvSmIwV1VGcG1sb3hlM0ZRT2NjNVZzTkNpOUhFQnVqMHh2S1Nab1dHbDRTTHNxRmNkRDVIdjhsRjA2TjlKcTlST0pPOXc1Qk1WYWpVOFpMemxtaWE1WnBWMTJ3NE1URG1RZ2VyTGlTSmVWUi9FRVRWTGc5Vkx0ZFhlVnkxV1pQYmxQMDlwamlXZzRCS0VGZE9idnRvb2d4N1ZLMDgxN3FUQUVpSnBJeUhsbHFRR3Q1cWZqWkx2N1FWZFRGZzljTzNvRkhud3Q3WGNWOGxMWDl4VEZBOGJjNTF3VUphNDJuRy9MY0tqV21BbVh6emthVmI0bjZjQWdqVEg0ZkZnRnRvSURUQndJM1E1Rld0ZnZUblMvdkw3QWlzWTB6dHdPWjAxbFRJaTVZZ1hGeDNqR05IVlU3Q1VKeXBUK3l4Z0NoSTlhZDJ2KzhNYnMwR3JrUzBSNXVHVnAvZ1dYYm1tb0giLCJtYWMiOiI3NDdjOThjOWU2NjE2YzBmOTE2Mzg3YWM4YzNlMWVlZDE2MWFiODQ5MzYxODhiMzg0YWNiYTI2YjIxOGE5MGM5IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:50:26 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImR5cFhpQlFhQWM3MFhJWnVzcExST2c9PSIsInZhbHVlIjoiS3RKL1pOb0IxV1hsd0gxc2JPR1BHd3Q2VzYyNVBaUmczdkZKMkY0VStVQkFYZ0lZUFdhcmdEYXZnSW80V1FjeFZzZ0FRYTJFZU9BdVk2WndHamZIN0lYUG5CMk1jZFVjaWVManNLSjV6bEZhYnBUMWFzREtBR0syR3BQR1FScjgwNE10YkJyZjhoL0xtZjI2di8wSUZwSkRmTjRXdjFSRnBZcHlNQ2ZCTkF4UzBuOWVDVEdTZ2xveTEvcVNLby9YcXpTV1hHU1NaM3pZZFpid0xiN3B6T3l0QUxFMkxQNVU2TGhqQUpGODRzRGV4WGhSOFhiS1F3VmQ0UVdJUzVPUGN4U0lxWVk2ZVozZTgyUnZ0d2pmNW5EajFnNFdHMSszcFl0N3pUczRPckF5Q0lkZytUMGpIR3doWFdjb2JSS29RU1FOM09MckJsSFAxb1JLZE9GVUFYbHI5djFDOTlUaXlOWHV4dFdtV3c5czQ4VmcrNFlSUFBoTEdZTkRaYnhGa05hemFtZmk1Q1RJSWJKbmxPSHJQZWVCMW9oM1RUTWg5YTZiamFzbGhUTjhpZGJvU0ZXNUpCbGhVQy9MUVkvbFpCeXBaaU1TQ01uTDNoTUtYU2xRN21Mc2hweGQ3V2FCOWhYanlSYjBuNk1jNFF2Wkg2OVQxSjNnTzZocWFLVU0iLCJtYWMiOiJhYzZhOTM4YmYyZGE0NGY3MjVlYzI4MWRhOTY0YTVlZTRjOWVlMTEzYTI4MDhlNjUwZmM5MzBiZDg2MzRiMzNiIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:50:26 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ijd0NFlCKzEzd21La2x1YXFWcldveWc9PSIsInZhbHVlIjoiVzVoNkxBT3F2eHFHOFcxdGF5ZmJmRFRxLzVGb1UrUjYyOHJRNkVYOWFhbUNvTnpnNkJkbVlJT0FrRm50QkJhek9oOGVBejRaQjlQN0VvcC9RNUlJSWdNY21FdGc5L1hReERzWmRTN05ONkZXTGUzalNzT3pqU0gySVdtN1dzNGRyb3ZTTS93RGdGSUU1ZUc3VFlRQTRMRnZTVE1tSWJtY3QvSmIwV1VGcG1sb3hlM0ZRT2NjNVZzTkNpOUhFQnVqMHh2S1Nab1dHbDRTTHNxRmNkRDVIdjhsRjA2TjlKcTlST0pPOXc1Qk1WYWpVOFpMemxtaWE1WnBWMTJ3NE1URG1RZ2VyTGlTSmVWUi9FRVRWTGc5Vkx0ZFhlVnkxV1pQYmxQMDlwamlXZzRCS0VGZE9idnRvb2d4N1ZLMDgxN3FUQUVpSnBJeUhsbHFRR3Q1cWZqWkx2N1FWZFRGZzljTzNvRkhud3Q3WGNWOGxMWDl4VEZBOGJjNTF3VUphNDJuRy9MY0tqV21BbVh6emthVmI0bjZjQWdqVEg0ZkZnRnRvSURUQndJM1E1Rld0ZnZUblMvdkw3QWlzWTB6dHdPWjAxbFRJaTVZZ1hGeDNqR05IVlU3Q1VKeXBUK3l4Z0NoSTlhZDJ2KzhNYnMwR3JrUzBSNXVHVnAvZ1dYYm1tb0giLCJtYWMiOiI3NDdjOThjOWU2NjE2YzBmOTE2Mzg3YWM4YzNlMWVlZDE2MWFiODQ5MzYxODhiMzg0YWNiYTI2YjIxOGE5MGM5IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:50:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImR5cFhpQlFhQWM3MFhJWnVzcExST2c9PSIsInZhbHVlIjoiS3RKL1pOb0IxV1hsd0gxc2JPR1BHd3Q2VzYyNVBaUmczdkZKMkY0VStVQkFYZ0lZUFdhcmdEYXZnSW80V1FjeFZzZ0FRYTJFZU9BdVk2WndHamZIN0lYUG5CMk1jZFVjaWVManNLSjV6bEZhYnBUMWFzREtBR0syR3BQR1FScjgwNE10YkJyZjhoL0xtZjI2di8wSUZwSkRmTjRXdjFSRnBZcHlNQ2ZCTkF4UzBuOWVDVEdTZ2xveTEvcVNLby9YcXpTV1hHU1NaM3pZZFpid0xiN3B6T3l0QUxFMkxQNVU2TGhqQUpGODRzRGV4WGhSOFhiS1F3VmQ0UVdJUzVPUGN4U0lxWVk2ZVozZTgyUnZ0d2pmNW5EajFnNFdHMSszcFl0N3pUczRPckF5Q0lkZytUMGpIR3doWFdjb2JSS29RU1FOM09MckJsSFAxb1JLZE9GVUFYbHI5djFDOTlUaXlOWHV4dFdtV3c5czQ4VmcrNFlSUFBoTEdZTkRaYnhGa05hemFtZmk1Q1RJSWJKbmxPSHJQZWVCMW9oM1RUTWg5YTZiamFzbGhUTjhpZGJvU0ZXNUpCbGhVQy9MUVkvbFpCeXBaaU1TQ01uTDNoTUtYU2xRN21Mc2hweGQ3V2FCOWhYanlSYjBuNk1jNFF2Wkg2OVQxSjNnTzZocWFLVU0iLCJtYWMiOiJhYzZhOTM4YmYyZGE0NGY3MjVlYzI4MWRhOTY0YTVlZTRjOWVlMTEzYTI4MDhlNjUwZmM5MzBiZDg2MzRiMzNiIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:50:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-484555071\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-193104098 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-193104098\", {\"maxDepth\":0})</script>\n"}}