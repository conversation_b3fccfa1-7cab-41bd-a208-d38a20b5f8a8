{"__meta": {"id": "X656d017f8423cb0b693f15a4bbfa53fa", "datetime": "2025-06-17 15:10:24", "utime": **********.515751, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750173023.779782, "end": **********.515776, "duration": 0.7359938621520996, "duration_str": "736ms", "measures": [{"label": "Booting", "start": 1750173023.779782, "relative_start": 0, "end": **********.409394, "relative_end": **********.409394, "duration": 0.6296119689941406, "duration_str": "630ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.409411, "relative_start": 0.6296288967132568, "end": **********.515778, "relative_end": 2.1457672119140625e-06, "duration": 0.10636711120605469, "duration_str": "106ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46370192, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1696\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1696-1706</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.025609999999999997, "accumulated_duration_str": "25.61ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.460172, "duration": 0.02498, "duration_str": "24.98ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 97.54}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.5016992, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 97.54, "width_percent": 2.46}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1650508009 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1650508009\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1725225967 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1725225967\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1650791999 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1650791999\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1606919503 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InArWFJ3amdVbGUzQlliVmwzOEFlYnc9PSIsInZhbHVlIjoiQjFNaEMyVjlycHI4NVhvYlloc1MzMExJWll4REFHODY1WUdXL0JoWHFxZ2U2R1VsaGpBVHlick1QRzZrYi92UWg5YjhhREdWT1NNWHM2ZWJBSmQvZHF2TGl2ZTVwL0w3S0UwZGNLYUNGaE91cEFaV3ZjemVsVU15eG9XUzRZNThnS0lVMzMra1BIblFONTRndTFyWVJFU0Y5ajV2cFpTSmNVeWYrbDlDVXFYTFdMOTBMc3p6Z0tGSnJ0aDJJVUNkMTNrUVRVaHQ2T3dsalRsTTQybUF5SGdDWDF5YlQ5ejVueEZFSU1qSC9lbmR5ckdpUTV1STQwZDhKNVpCc3dvNG92ZngveXZFdkdaZ0JVdEVLVEN4UFZuRDhGTkR2V05sTHZ5b1RpZGdTUGU0N29id3loR1lnT3Z5NGw5OUJzWmQwUEZOMmZ3NEl1UWRaeDlYYVRoRXJUZVpoYk9rS3B0ekZBa21ZUFVITDNaZGhUNFROdStHT2NreHBodzlHN1Q4RDR2MXhFRkpwemZCUmxya1ZjT1ZEUWMyeGxiNU1NRk94cUJNa1N0SUhBZGtYWW8xZGxGUUxtd3J2Mi9pYWVla0ZJV0ZDMjd3aTZGSjkvTDlObEZCaVh5bTFBWC9ldndKNzZxV2NaWTJtcGd4ODAzaEFtKy9JdXJXK0xLdUVQVDQiLCJtYWMiOiI5OGZhZTU0Yzk1YzBhOWVmMjI5YTVhYjBkNDNiNzg1ODUwYzUxZTM0ZThjYTcyOWM0NTRhMGExYTMxODc5ZjA0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ikc1ZW5WOVNHZ01NKzk3SWdNQVU0bGc9PSIsInZhbHVlIjoiWE5XMjZicXhRSkJrMUxuZW04Nm5XTUs3ZzIvS3paZU41UVpNNGRYTXg4TVI5U2hwTVFEdHVNQWNwUXRnbHdoVldLWkptSWxxU2tlcjhvU0dKTGRJeEFCdnZUU1h5Ty80K1E1UGxQSUdxVWxVMTRvVS9nT1VHeGQ5NTB3NGdwZXZNa1YySDVhcEg5VnM5TzZRMUpCVU56K3hXQlZpeDMvMGhoQzZDeU9GNGRnZlNjdkZYalAwRDlNUnN3QzkxSkRya1BSWTVZZWdCdjZPU1NZdU11NEcxK2JQc0p0L1lCbE5JUkMzOWV5UC9SME04TG1oNit6eE93SEJuUGswQktQNEREdVVYWFp3SUYybXpvdjZCS3VyNS9YU1Y3ckQzUVB3MXZWLzc5OEJ0bG5aazVlZytIV3E3MU1HcFFZM2wzYWx2VGNnYjBqaEtXQXk2UlhvQlBHWVVFOGNVMjNVYWc2OEZlUiswMkJiOTJWNGlySmhCUCsrSC9kdXhwMFhkSStUbkJ1ZXZQK0QwNmFiZjJrR3UrZndUUnY2TEpMTFpKR3JwKytoRWV0UURMMVUwdExwbU51UEhqdStxTUU2YmY4V2RWb0FVNFlkeU1wdUhlWTNJclhqV2IvWGZEY2cvemtIRXFxd0RsS25oQWxCVkt0bnhPeHRMUFI4SWYxaDgwaEYiLCJtYWMiOiJkMjMzNDBjOWNhMDFhZTAzNDRiYjlhZGIwZGExMmMzMWVkMDJkNjM5YmIyODcyY2NiZWQ2OTZhNDUzOGRkNDA3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1606919503\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-742179025 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-742179025\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1917790687 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:10:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkFIZjJvODVqZm5QWVZFbk9yMEkvTWc9PSIsInZhbHVlIjoidFBydkRzUXoxMlF5V05hWWJZU1Bjak9XVWR0ZWJPb2cvWUZicGk4SlVoOVJBMDVwdXg1S2ovM1h6M0dadEo1dVdlYU1VN2VtK3ZiQko5TVdYWFM2SitNVUNNT1F6VXNPM0RwZ3BpajdiQ25UT2RqeHJyUGF1Z1JZSkpNd3ArQmVZc0EwcjlkY05RZy9zYlQxVzQwbHdSOG5JVVg3QlpXSlg4S1BBT1ZEQkhUdUJYQ1gzbFJaSWN2OU9mTzhaTXR5dUxzY2JnaGplZHlMRDhaOERtK0UrZUJnSVA3OE5CWmx5SFNwVzJiRnkyUXNhZ2RabTRJaXZGNHB5MVMxTEZCM3ZTcytsM1RKV3pKSXJNWmxRLzlBcEhSRHB2ZVViaGs0K3pxNEVxdnlTdnl1dnJDM3o1bkNVY0xjYUUrdys5WGxRZVltNGVHcFdGU1h5bk9GU2FEVFRJdERRQ0lKWFZMWWJBVXhKcWdsZk1HK2hKbkVsT1BORjJ1QUdTeDZ0Wmw5UEpQVXlaTm4vaUR4L29tODZzQjBVMjh5dm10ZGlPNTcwNjRPd0VKMWc5L2p0N1QxNjZrYjVEaTE2V2poWHhrdHhsYStYN1VtbG9uWVJ5amxoc0cvYnBUZHk3U3B4NjdtdXlUNGVjK25RUG43U0crOUkzc2FsdFBLNVo3cFAvbjEiLCJtYWMiOiI0M2Q4ZDJjMDZiOWU5YWEyN2IwOTc1NmM0ZTE0YzYzM2IyNzY2YzZmOGNhNDU4MWMzZDQxMjI4ZWExZjNiMGQ3IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:10:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik1XaWdId1ZJK1lOSGVPWGhRYUkvSGc9PSIsInZhbHVlIjoiMEkvSHhiWW16RjdtcmpxRnR2WjcvMFUvOTV0RTlud1A2K2FrcStCb1YxWUJuclNQRFNLdFFoUXZTTWNBSHJyb2U1b1Arc2FlMXB2Y1FLMWJ5cUVzV1dQaWVvS0ljbFR3cjBxUHMzaE1ZSXI5anFyeVhva09PdjFKbUVyanRTb3NUbGxTQURkdHI3cGZMeGQxT0g2MTlLeXk0YTdNY2FUbTB6SHcrOUY4WjJSUm54Sm0zMUdFSS9XN2c5MDRHUDBwb095ckplaW5MVkNLRlppNmtuVWNJemtmcVNYalRvczZrWjByRGJkdnRYWSt5Z285dXBrMi9Rdm5sRjJkVUR2Zk5jSE1ZZFhrNU4xbUNLS09Rbmd4Lzc0c0dTV0srK1poTTIzNjJoWE5OSmMvbUlaSDdkMHIwekVHK1MwY0VxS083NmRJMXRRWnl3TlpxMkh4dHljS0YwR0o3UXVMOEdFWVNsSHdJNXV2bGpyRlcvUzlXQWZuQlpuUXdneE1aM3hwSXUrWkQvZ0dOeGY2ZHdTdmRMdE9lUnRUMDZ1ZU5WVGRRYUQzUkF4UEpsLyt1L2Mvdk5XYkdCeTFYYXFXekk3YUtJcnpONlFZRVM2YnZlQ1BWWFdaZWZBZFQ0bEhIL1d0SFh6aGtwVDR2MEo2SFZ5My9ybWJlNldjMG1LNUhwSU8iLCJtYWMiOiI4ZjllNzIwNTQ5NDg1NjM5MjBhMDc2ZWEzYTE3OTRiZjE3MDhlYThjZDJiNWIxMjMyMDBjOTZjMzRlMzIyZjVmIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:10:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkFIZjJvODVqZm5QWVZFbk9yMEkvTWc9PSIsInZhbHVlIjoidFBydkRzUXoxMlF5V05hWWJZU1Bjak9XVWR0ZWJPb2cvWUZicGk4SlVoOVJBMDVwdXg1S2ovM1h6M0dadEo1dVdlYU1VN2VtK3ZiQko5TVdYWFM2SitNVUNNT1F6VXNPM0RwZ3BpajdiQ25UT2RqeHJyUGF1Z1JZSkpNd3ArQmVZc0EwcjlkY05RZy9zYlQxVzQwbHdSOG5JVVg3QlpXSlg4S1BBT1ZEQkhUdUJYQ1gzbFJaSWN2OU9mTzhaTXR5dUxzY2JnaGplZHlMRDhaOERtK0UrZUJnSVA3OE5CWmx5SFNwVzJiRnkyUXNhZ2RabTRJaXZGNHB5MVMxTEZCM3ZTcytsM1RKV3pKSXJNWmxRLzlBcEhSRHB2ZVViaGs0K3pxNEVxdnlTdnl1dnJDM3o1bkNVY0xjYUUrdys5WGxRZVltNGVHcFdGU1h5bk9GU2FEVFRJdERRQ0lKWFZMWWJBVXhKcWdsZk1HK2hKbkVsT1BORjJ1QUdTeDZ0Wmw5UEpQVXlaTm4vaUR4L29tODZzQjBVMjh5dm10ZGlPNTcwNjRPd0VKMWc5L2p0N1QxNjZrYjVEaTE2V2poWHhrdHhsYStYN1VtbG9uWVJ5amxoc0cvYnBUZHk3U3B4NjdtdXlUNGVjK25RUG43U0crOUkzc2FsdFBLNVo3cFAvbjEiLCJtYWMiOiI0M2Q4ZDJjMDZiOWU5YWEyN2IwOTc1NmM0ZTE0YzYzM2IyNzY2YzZmOGNhNDU4MWMzZDQxMjI4ZWExZjNiMGQ3IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:10:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik1XaWdId1ZJK1lOSGVPWGhRYUkvSGc9PSIsInZhbHVlIjoiMEkvSHhiWW16RjdtcmpxRnR2WjcvMFUvOTV0RTlud1A2K2FrcStCb1YxWUJuclNQRFNLdFFoUXZTTWNBSHJyb2U1b1Arc2FlMXB2Y1FLMWJ5cUVzV1dQaWVvS0ljbFR3cjBxUHMzaE1ZSXI5anFyeVhva09PdjFKbUVyanRTb3NUbGxTQURkdHI3cGZMeGQxT0g2MTlLeXk0YTdNY2FUbTB6SHcrOUY4WjJSUm54Sm0zMUdFSS9XN2c5MDRHUDBwb095ckplaW5MVkNLRlppNmtuVWNJemtmcVNYalRvczZrWjByRGJkdnRYWSt5Z285dXBrMi9Rdm5sRjJkVUR2Zk5jSE1ZZFhrNU4xbUNLS09Rbmd4Lzc0c0dTV0srK1poTTIzNjJoWE5OSmMvbUlaSDdkMHIwekVHK1MwY0VxS083NmRJMXRRWnl3TlpxMkh4dHljS0YwR0o3UXVMOEdFWVNsSHdJNXV2bGpyRlcvUzlXQWZuQlpuUXdneE1aM3hwSXUrWkQvZ0dOeGY2ZHdTdmRMdE9lUnRUMDZ1ZU5WVGRRYUQzUkF4UEpsLyt1L2Mvdk5XYkdCeTFYYXFXekk3YUtJcnpONlFZRVM2YnZlQ1BWWFdaZWZBZFQ0bEhIL1d0SFh6aGtwVDR2MEo2SFZ5My9ybWJlNldjMG1LNUhwSU8iLCJtYWMiOiI4ZjllNzIwNTQ5NDg1NjM5MjBhMDc2ZWEzYTE3OTRiZjE3MDhlYThjZDJiNWIxMjMyMDBjOTZjMzRlMzIyZjVmIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:10:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1917790687\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1741661339 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1741661339\", {\"maxDepth\":0})</script>\n"}}