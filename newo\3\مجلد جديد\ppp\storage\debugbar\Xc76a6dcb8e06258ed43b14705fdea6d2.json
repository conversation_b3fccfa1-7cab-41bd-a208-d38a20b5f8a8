{"__meta": {"id": "Xc76a6dcb8e06258ed43b14705fdea6d2", "datetime": "2025-06-17 14:01:58", "utime": **********.959736, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.346953, "end": **********.959761, "duration": 0.6128079891204834, "duration_str": "613ms", "measures": [{"label": "Booting", "start": **********.346953, "relative_start": 0, "end": **********.837646, "relative_end": **********.837646, "duration": 0.4906930923461914, "duration_str": "491ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.837656, "relative_start": 0.49070310592651367, "end": **********.959763, "relative_end": 2.1457672119140625e-06, "duration": 0.12210702896118164, "duration_str": "122ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49205496, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.020890000000000002, "accumulated_duration_str": "20.89ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8826962, "duration": 0.016800000000000002, "duration_str": "16.8ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 80.421}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.911586, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 80.421, "width_percent": 3.016}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.933319, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 83.437, "width_percent": 4.26}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.936872, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 87.697, "width_percent": 3.973}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.9441268, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 91.671, "width_percent": 5.218}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.948899, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 96.888, "width_percent": 3.112}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1131448246 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1131448246\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.942656, "xdebug_link": null}]}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1359353617 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1359353617\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-592907962 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-592907962\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-294780753 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-294780753\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2008186266 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImxVMjVaVlFvV29abGsremRHODVTbHc9PSIsInZhbHVlIjoiZWZJL1BiQ1lSdURQNnQ5QVo0UnBMTkMxbmZIT3VLZzJWbkJQT0xZVk5PenRod3EweWVIZTNNckpoMVRzU3QwUWh3UVlFZ01aT0puME1obzRSMjcrOVJYVGNyVTU4Zm9ILzBCUncrdi9YK1VFWURwVGsrTjRmRkR6YU5BaHFvTjBIQmZUN0VMaDFFK1FJUXB1bGk0UDhHOGNpcUVBa2hnNitaVExWWmUwWDQ1a2djZ3RDaUdGTG9aTEt2SlhZR0p4WlRtdkdxNXlkSGhKajY3b1lpTVBhNzR3NGo2Um9JNUNkNWZCMGVjVGVXVHNxYnZrTWNRVVZVbDB3RllCaXhkaUlFZ2xrMTdPQkVyb2poTy9kVllYYjBaaXpPSWhGT3hhN2NtT2lNaStJcmZiUkRMb3g0aDVJTXlEZjMrTGZCOWFXNUdRNWwxQ3lWRUhwWmZSVHpZaEhmMkpjM3lFMkh4ZTBMMDdycXcwNnlWRVpBRUR5L2R2ZmNKN2ZmdjBZRE5aQ2xjM0lCZzhxQ25MOWk4My9zdzZ5L1NtVTExaVpIMHZQaitLQ1RlMTJyNTE2Skk1aERFUmlqWW5wZEw0Sy9ITXhlajRqZlFHR2M1SUFMWHNjREhkaGQ2bGh4RmZqaWdMSUhFUjRKUkhsTmVxTzZ1b25rblhoYTZBTHptbUpzTlYiLCJtYWMiOiI0MzY5MDMyM2MzYmE3MDE4OTMyOTZlNjE5OWVhNTYyZWQxNjdlMDFmMDgzYTQxMjNjZjg2ZWZlMzcwNTkxYmU0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkFYVHladjg1d3BLK2NSMFZlc29TQkE9PSIsInZhbHVlIjoiYjBOYUREZnZ2M1phbnUxUFpGSldVVDNSMmE5dVlFU0o3V2drZ0kyNGVHdjZkZEoyYkhjaFd1RiswbDNGcXVqdC8wVnpJWTJpUTZRQmdLMWJMSFB2QkRKTEpoYW5BWmZaem42RVJ3cDZQd1hkdWQ0OU9xdURSd1pscTFGYTN0aDFQTlJXNksrY2FjTHJmSzkrWk45ejlIKy9mbTVPSU5FQTY3VTZkRDJScTIvdis4dzVxM1ZqN2JPdGNnN3RjbGV3ODlkTDVtS2VkeEZRMEFwRnI3eHhacUY5d2tPbDVQVkZrMjlvWVR6Zk1HRXZNMVY0bnpjWThIOEt1VFVnN0tGNGpnejlZQUdzMXo4bFJicVVDSzJaQUJSK3M0ekxIQnlGZytORjZJYjRKZmNyQ1Q4Uml1STRzYkV6bWxHZWNHUTVjaFhVZjJ6V1ZTS3BKZnFIY0NFZC9iTXdvV29HblpqOCtJUmVOdmhnT0lGMEpaYW01dHc4UzRsMUEvRm5jeGdmQ3NTVHBzV0ZleWw5M0VPZ0RidHJCNEtTeHZFa0pVdTd3dHVwYVBSenN0Z0puWkRYS3ErcnlENUVFVjdhSG5DTWY1QUNMKzVxclJEalQvMHRvTEhLdnBqcmk1Q0orYlJkMXF5UW9Uc3l0MGJ3YVViMTgxcEQyM1BHZk42TTNYN0ciLCJtYWMiOiJkZDY5Zjk4MzdiODMyYzk3OWNkYmRhZGNjNzdhMDQ2OWE4NjQ4OGRiZGQ4NTRhOTMxMmZkYzkxNGVkNWQxYmMwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2008186266\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-786716939 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-786716939\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2087984560 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:01:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkZVcVlBTVgzUHNNRmwrTTQ5bnhnRGc9PSIsInZhbHVlIjoiVUtUUUdnbVp1TWppa1E4NkVNcXlKeWJIRHh2UFRJeVNrZlRXbm5QdTR6YTBlRHJ0ZXhRV2ZnTHNpQ2hTUGxGQUNRYjdzcitNNEdSVVYvTk1XNjYwTm94dG1nQ1Q1aE9VL0FoTTFOekw0ZEhCUWV2N1RFVUZLeWk2U1NTSnVma0ZZM2p1Z2k5cW1GK3MvQlllZ2F0elZVQS9MR0doWGtVRncwaGhQSDZkTEZLZGdiZDM2V0hNUUNlakkxVTg2SVNXRFYrL2pDc0dPQVd3QUt6VUc1R0JPM2w5dFpXV2ZyT2pPVzFBSDQzRzRlUDBoWTNEQ1c0MDh5aHZnV3h2NlBhaFR1RUFlc05ZT2FpeW9ZanRrcW1xN2N1dUxPRG1RTXg0aXp4T0dJWGtoQ1NIejBCL0FrZUxxOEw4M3VIY2xhTnpGa09sMEEyVFAvWTEzRXhyNDhZR1UrZTVXK0RWdEhKT2h3eXY2WWNtOS9Ec2FjV2hoc29WMjdVMnJIdEZLKzV4Um9nWi9Rc2pUbzlRdWUyQS9xMjlaWDRWVVFyb2RUUDBvdDQ0eFVCSkRrbzdWL0g5MlNhUGZ4RlQwYTZIN05ka0xkTzFZbVFlNDNQTENkQ1hmTVM4RlpPYUZWL3k0RTU1ZzM4UHRVb3kzckZNSmM4L0lGQmFseCs2T2dhcDlkdnIiLCJtYWMiOiI0NzczN2E3NTY0MTNhZjE2ZTMwZjBhZGEyZGNiMWM4NjY3MzMzMjA0NTkzYWI5M2Q3ODc3MDIyODA0M2JkOTliIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:01:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjJ0WGM3azYrWjlFaEhjRVVId0thREE9PSIsInZhbHVlIjoiN1RzdnhkaXZlMy9KaDEwWnVEL1NIeXByNjhkbS9ZdlpsMlcxTmJxNTFrY0ZaVDc3Y2liSXV0NWR3YVpPdXRxK0VxSi80YndLcFR5TFFNOFZUblhVcUthZ3A3TzV5R0prY0F4Y3ZyNUlPRGdOMzZ2a2RwbFRNeW1uT0k5TEZ1Y0RBR3JOMWlsYm9yRHF6aGd5LzM1ZE43eE5xak5TSlRGYzUwTjhNd3J1MkdPaWpEemJzZW5DWU9tSDdhQ2FCbXJUMHdrSGRvY1g3bjEydUs4cWRjeGpjQ1ZDRTlxT3pGdG94VGVOc1NGdTNUU1Vzb3hEeUFEdzlpZ2xOOHI5NUdTYVA3QlVjRXR0NjhrK0FvWTI3RGE1ZjA4bHMxV1cyandzd0Nvd25mc0Q5TERBaFlvSzgrc3JyZ0REaHRkSG5xK3QzdVhBR1VxdG1iRWx4WFcwRSt6Sm90dWFkOFBxKzlwM2pYN0FUMFgybmRNNkxic3ZjOFcxK2FlUk1OZ2p4eGJOeWM4Yk8wb2xlQ3k2V29HalAvK1JzZFpKbmRaMXlaTWZHalRJQ0hlWkVOZ1I2c2N0M2JnazZWRzdvTzE3WHkwbzhTMDFGNUtOc28vOUVnNVg4cTRIN0Z1YUVlbXdKNkxPSzU5MlkyTStiMEUxRDQva1BsUFlOb0dvbFI1b3ovUHgiLCJtYWMiOiI1NWEzMmE4MTBjMGU1MjhmZjRlOTY4NDRkNTQ3MzdiMjY4MmE5N2E2NGUyMGIxMzQ0OGQ1ODA1ODU3NjI1ZjJiIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:01:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkZVcVlBTVgzUHNNRmwrTTQ5bnhnRGc9PSIsInZhbHVlIjoiVUtUUUdnbVp1TWppa1E4NkVNcXlKeWJIRHh2UFRJeVNrZlRXbm5QdTR6YTBlRHJ0ZXhRV2ZnTHNpQ2hTUGxGQUNRYjdzcitNNEdSVVYvTk1XNjYwTm94dG1nQ1Q1aE9VL0FoTTFOekw0ZEhCUWV2N1RFVUZLeWk2U1NTSnVma0ZZM2p1Z2k5cW1GK3MvQlllZ2F0elZVQS9MR0doWGtVRncwaGhQSDZkTEZLZGdiZDM2V0hNUUNlakkxVTg2SVNXRFYrL2pDc0dPQVd3QUt6VUc1R0JPM2w5dFpXV2ZyT2pPVzFBSDQzRzRlUDBoWTNEQ1c0MDh5aHZnV3h2NlBhaFR1RUFlc05ZT2FpeW9ZanRrcW1xN2N1dUxPRG1RTXg0aXp4T0dJWGtoQ1NIejBCL0FrZUxxOEw4M3VIY2xhTnpGa09sMEEyVFAvWTEzRXhyNDhZR1UrZTVXK0RWdEhKT2h3eXY2WWNtOS9Ec2FjV2hoc29WMjdVMnJIdEZLKzV4Um9nWi9Rc2pUbzlRdWUyQS9xMjlaWDRWVVFyb2RUUDBvdDQ0eFVCSkRrbzdWL0g5MlNhUGZ4RlQwYTZIN05ka0xkTzFZbVFlNDNQTENkQ1hmTVM4RlpPYUZWL3k0RTU1ZzM4UHRVb3kzckZNSmM4L0lGQmFseCs2T2dhcDlkdnIiLCJtYWMiOiI0NzczN2E3NTY0MTNhZjE2ZTMwZjBhZGEyZGNiMWM4NjY3MzMzMjA0NTkzYWI5M2Q3ODc3MDIyODA0M2JkOTliIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:01:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjJ0WGM3azYrWjlFaEhjRVVId0thREE9PSIsInZhbHVlIjoiN1RzdnhkaXZlMy9KaDEwWnVEL1NIeXByNjhkbS9ZdlpsMlcxTmJxNTFrY0ZaVDc3Y2liSXV0NWR3YVpPdXRxK0VxSi80YndLcFR5TFFNOFZUblhVcUthZ3A3TzV5R0prY0F4Y3ZyNUlPRGdOMzZ2a2RwbFRNeW1uT0k5TEZ1Y0RBR3JOMWlsYm9yRHF6aGd5LzM1ZE43eE5xak5TSlRGYzUwTjhNd3J1MkdPaWpEemJzZW5DWU9tSDdhQ2FCbXJUMHdrSGRvY1g3bjEydUs4cWRjeGpjQ1ZDRTlxT3pGdG94VGVOc1NGdTNUU1Vzb3hEeUFEdzlpZ2xOOHI5NUdTYVA3QlVjRXR0NjhrK0FvWTI3RGE1ZjA4bHMxV1cyandzd0Nvd25mc0Q5TERBaFlvSzgrc3JyZ0REaHRkSG5xK3QzdVhBR1VxdG1iRWx4WFcwRSt6Sm90dWFkOFBxKzlwM2pYN0FUMFgybmRNNkxic3ZjOFcxK2FlUk1OZ2p4eGJOeWM4Yk8wb2xlQ3k2V29HalAvK1JzZFpKbmRaMXlaTWZHalRJQ0hlWkVOZ1I2c2N0M2JnazZWRzdvTzE3WHkwbzhTMDFGNUtOc28vOUVnNVg4cTRIN0Z1YUVlbXdKNkxPSzU5MlkyTStiMEUxRDQva1BsUFlOb0dvbFI1b3ovUHgiLCJtYWMiOiI1NWEzMmE4MTBjMGU1MjhmZjRlOTY4NDRkNTQ3MzdiMjY4MmE5N2E2NGUyMGIxMzQ0OGQ1ODA1ODU3NjI1ZjJiIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:01:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2087984560\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2146564016 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2146564016\", {\"maxDepth\":0})</script>\n"}}