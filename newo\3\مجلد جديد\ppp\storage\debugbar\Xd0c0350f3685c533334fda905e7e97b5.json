{"__meta": {"id": "Xd0c0350f3685c533334fda905e7e97b5", "datetime": "2025-06-17 15:10:23", "utime": **********.0691, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.447869, "end": **********.069122, "duration": 0.6212530136108398, "duration_str": "621ms", "measures": [{"label": "Booting", "start": **********.447869, "relative_start": 0, "end": **********.942944, "relative_end": **********.942944, "duration": 0.495074987411499, "duration_str": "495ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.942956, "relative_start": 0.4950869083404541, "end": **********.069125, "relative_end": 2.86102294921875e-06, "duration": 0.12616896629333496, "duration_str": "126ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49247608, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.02104, "accumulated_duration_str": "21.04ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.989573, "duration": 0.01261, "duration_str": "12.61ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 59.933}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.013551, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 59.933, "width_percent": 7.082}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.037678, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 67.015, "width_percent": 5.608}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.041624, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 72.624, "width_percent": 3.232}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.048792, "duration": 0.0025099999999999996, "duration_str": "2.51ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 75.856, "width_percent": 11.93}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.055495, "duration": 0.00257, "duration_str": "2.57ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 87.785, "width_percent": 12.215}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1038022645 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1038022645\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.047111, "xdebug_link": null}]}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-955558709 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-955558709\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-87728728 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-87728728\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-549133197 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-549133197\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1046293077 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkxPVE9xR2dBRi9neE43dzZuYzlRVFE9PSIsInZhbHVlIjoiQzlrdFI3Q0RoaW9leHh4MUFodlIvRnlXejltcm54ckNLdHpkb0R5SEZkMzE3VU1ZaUdZbUpjdE90MzlFM0IwVC8rc0RDQi9DWWhRSnY2T0VtSUY1aUtZckpCb0tjRytzU0VTNDU0Qk0wcFBsdDkzdkpodFFWZGRQdjdINkNyVkc4TEkydjRjTTUyVmtUNjRycndhK2cwOFZtWVo4Zk9NNm16OXZjUW1GUm9jcXgrL3h1ODRoUVJ3WGdkQ09pTzNPemZ3M01GOWtHbHN2eXQ2ODVqK2E3MWlBUVkzV2pGdjllMmFBWUdPMkxxSmlXbEpCRXRYc0tabGk4RDhkTmRDckVUeFZtaStaZ2VUN01MNVBNTE5ZY1c2THRWdDNsbUdGam9mR21BTDlhMTlOUnN4a0M4ZzdETi9XTDBIcGZOZ2FRbjJwK0g3d29wSlFGNjFkbWROZTJRSi92cXk0ekx3MFlEVWN0UjYydlRaUDdpeVRuUlc2TDlZeEFKbGFGL205VUNaRGhpZDB1TFRWd1NRbklKQ0FLemR4elZ3UlZST1VjWnkxM0trWnovck52WVQxNlZnVDVtZVZTVmozR0xIMnk2eUxJTkY0UVFBdTZLUlJhQStPaHBhOXI0ZnpCN3BHNlhNYkpOc1FBRDVUMlJpWElBR2tsQnF6TXo3ZUZjSjIiLCJtYWMiOiI3ZTY2MzIwODU1OWIzZWUyZjYwNTZkMTM2ZThhYzFjNWNiOTljNmQ0ZGRmODg3MzlkYzUxYjc3NGRkZjU0ZGFlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlRzUENBZUtscnJET2tuOGZVdTB3S1E9PSIsInZhbHVlIjoiYWVSVkZsajVWVDU2b2tLRURVWVJGYWlhUHZDMFkxSTloMmVBTWRyRFUrc2JOVDVPQlZIcWJkWjdiOE1qSStwSERFSlJyZTF0RHpzc0NrdWoyYmdaQXlpSThubFF3UG05TW5vVkNsV1l1TVJHeE5tQVlyUGNZZ2Y2OUlqNHpNY2s3UTN4UkZwNXZMUHBTYnArdGxoN20xODRtVWFDRHBiWFNoZHRrZExMQnc5TVc3VVlTaWh6ei92VGlsczNJdkR1cHR4N2VEMU1wQStXalp6VU5rRWFpODd5a2FZTGgyejZpK0FrUzh1QnZuQitmcjVQWmhPR202c1FBSXhUelpIWjJZaXZnYnVuZVUwVkdCMkpXN0RRSEgvc2NBS2hhUUxYQytmalFNUkNxYXJpY1pXbGVMOTZ6Zmg4bHhXbklNUXFZQ1d0ZDMvcHZaMGxBTFN5MUVLaUhocWdjSDFiNjlwTFFHbHQ5Q21BL1Nsdy9UWERrMDk3VkVnRm9JYmFtOTRpMUZXdEp5eWhwVkxLL3ltRGtHM0dzOVNPT2x1V3k2YUVyQ2JoZWpqWjFoZ1BxZXVOYWRxQ29tU3FOd2xDMUNha29xcDVvaEpkL3M5U1BvUkFCN0xLdWdqRGhTb3BTTVh3RW5TMExLRjZ3UnV6UFcvUmpMRXRHYkVqQjg2L3YySG4iLCJtYWMiOiI2ZDM4ZTFjMjhmMWVkN2E0N2Y2ZjQ5ZGIzZmUzMTA2YWQyOGVlMDM2ZTViMjE4MzQzZjhmNzJkN2Q2ZGE4MGJjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1046293077\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2118684359 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:10:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InArWFJ3amdVbGUzQlliVmwzOEFlYnc9PSIsInZhbHVlIjoiQjFNaEMyVjlycHI4NVhvYlloc1MzMExJWll4REFHODY1WUdXL0JoWHFxZ2U2R1VsaGpBVHlick1QRzZrYi92UWg5YjhhREdWT1NNWHM2ZWJBSmQvZHF2TGl2ZTVwL0w3S0UwZGNLYUNGaE91cEFaV3ZjemVsVU15eG9XUzRZNThnS0lVMzMra1BIblFONTRndTFyWVJFU0Y5ajV2cFpTSmNVeWYrbDlDVXFYTFdMOTBMc3p6Z0tGSnJ0aDJJVUNkMTNrUVRVaHQ2T3dsalRsTTQybUF5SGdDWDF5YlQ5ejVueEZFSU1qSC9lbmR5ckdpUTV1STQwZDhKNVpCc3dvNG92ZngveXZFdkdaZ0JVdEVLVEN4UFZuRDhGTkR2V05sTHZ5b1RpZGdTUGU0N29id3loR1lnT3Z5NGw5OUJzWmQwUEZOMmZ3NEl1UWRaeDlYYVRoRXJUZVpoYk9rS3B0ekZBa21ZUFVITDNaZGhUNFROdStHT2NreHBodzlHN1Q4RDR2MXhFRkpwemZCUmxya1ZjT1ZEUWMyeGxiNU1NRk94cUJNa1N0SUhBZGtYWW8xZGxGUUxtd3J2Mi9pYWVla0ZJV0ZDMjd3aTZGSjkvTDlObEZCaVh5bTFBWC9ldndKNzZxV2NaWTJtcGd4ODAzaEFtKy9JdXJXK0xLdUVQVDQiLCJtYWMiOiI5OGZhZTU0Yzk1YzBhOWVmMjI5YTVhYjBkNDNiNzg1ODUwYzUxZTM0ZThjYTcyOWM0NTRhMGExYTMxODc5ZjA0IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:10:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ikc1ZW5WOVNHZ01NKzk3SWdNQVU0bGc9PSIsInZhbHVlIjoiWE5XMjZicXhRSkJrMUxuZW04Nm5XTUs3ZzIvS3paZU41UVpNNGRYTXg4TVI5U2hwTVFEdHVNQWNwUXRnbHdoVldLWkptSWxxU2tlcjhvU0dKTGRJeEFCdnZUU1h5Ty80K1E1UGxQSUdxVWxVMTRvVS9nT1VHeGQ5NTB3NGdwZXZNa1YySDVhcEg5VnM5TzZRMUpCVU56K3hXQlZpeDMvMGhoQzZDeU9GNGRnZlNjdkZYalAwRDlNUnN3QzkxSkRya1BSWTVZZWdCdjZPU1NZdU11NEcxK2JQc0p0L1lCbE5JUkMzOWV5UC9SME04TG1oNit6eE93SEJuUGswQktQNEREdVVYWFp3SUYybXpvdjZCS3VyNS9YU1Y3ckQzUVB3MXZWLzc5OEJ0bG5aazVlZytIV3E3MU1HcFFZM2wzYWx2VGNnYjBqaEtXQXk2UlhvQlBHWVVFOGNVMjNVYWc2OEZlUiswMkJiOTJWNGlySmhCUCsrSC9kdXhwMFhkSStUbkJ1ZXZQK0QwNmFiZjJrR3UrZndUUnY2TEpMTFpKR3JwKytoRWV0UURMMVUwdExwbU51UEhqdStxTUU2YmY4V2RWb0FVNFlkeU1wdUhlWTNJclhqV2IvWGZEY2cvemtIRXFxd0RsS25oQWxCVkt0bnhPeHRMUFI4SWYxaDgwaEYiLCJtYWMiOiJkMjMzNDBjOWNhMDFhZTAzNDRiYjlhZGIwZGExMmMzMWVkMDJkNjM5YmIyODcyY2NiZWQ2OTZhNDUzOGRkNDA3IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:10:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InArWFJ3amdVbGUzQlliVmwzOEFlYnc9PSIsInZhbHVlIjoiQjFNaEMyVjlycHI4NVhvYlloc1MzMExJWll4REFHODY1WUdXL0JoWHFxZ2U2R1VsaGpBVHlick1QRzZrYi92UWg5YjhhREdWT1NNWHM2ZWJBSmQvZHF2TGl2ZTVwL0w3S0UwZGNLYUNGaE91cEFaV3ZjemVsVU15eG9XUzRZNThnS0lVMzMra1BIblFONTRndTFyWVJFU0Y5ajV2cFpTSmNVeWYrbDlDVXFYTFdMOTBMc3p6Z0tGSnJ0aDJJVUNkMTNrUVRVaHQ2T3dsalRsTTQybUF5SGdDWDF5YlQ5ejVueEZFSU1qSC9lbmR5ckdpUTV1STQwZDhKNVpCc3dvNG92ZngveXZFdkdaZ0JVdEVLVEN4UFZuRDhGTkR2V05sTHZ5b1RpZGdTUGU0N29id3loR1lnT3Z5NGw5OUJzWmQwUEZOMmZ3NEl1UWRaeDlYYVRoRXJUZVpoYk9rS3B0ekZBa21ZUFVITDNaZGhUNFROdStHT2NreHBodzlHN1Q4RDR2MXhFRkpwemZCUmxya1ZjT1ZEUWMyeGxiNU1NRk94cUJNa1N0SUhBZGtYWW8xZGxGUUxtd3J2Mi9pYWVla0ZJV0ZDMjd3aTZGSjkvTDlObEZCaVh5bTFBWC9ldndKNzZxV2NaWTJtcGd4ODAzaEFtKy9JdXJXK0xLdUVQVDQiLCJtYWMiOiI5OGZhZTU0Yzk1YzBhOWVmMjI5YTVhYjBkNDNiNzg1ODUwYzUxZTM0ZThjYTcyOWM0NTRhMGExYTMxODc5ZjA0IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:10:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ikc1ZW5WOVNHZ01NKzk3SWdNQVU0bGc9PSIsInZhbHVlIjoiWE5XMjZicXhRSkJrMUxuZW04Nm5XTUs3ZzIvS3paZU41UVpNNGRYTXg4TVI5U2hwTVFEdHVNQWNwUXRnbHdoVldLWkptSWxxU2tlcjhvU0dKTGRJeEFCdnZUU1h5Ty80K1E1UGxQSUdxVWxVMTRvVS9nT1VHeGQ5NTB3NGdwZXZNa1YySDVhcEg5VnM5TzZRMUpCVU56K3hXQlZpeDMvMGhoQzZDeU9GNGRnZlNjdkZYalAwRDlNUnN3QzkxSkRya1BSWTVZZWdCdjZPU1NZdU11NEcxK2JQc0p0L1lCbE5JUkMzOWV5UC9SME04TG1oNit6eE93SEJuUGswQktQNEREdVVYWFp3SUYybXpvdjZCS3VyNS9YU1Y3ckQzUVB3MXZWLzc5OEJ0bG5aazVlZytIV3E3MU1HcFFZM2wzYWx2VGNnYjBqaEtXQXk2UlhvQlBHWVVFOGNVMjNVYWc2OEZlUiswMkJiOTJWNGlySmhCUCsrSC9kdXhwMFhkSStUbkJ1ZXZQK0QwNmFiZjJrR3UrZndUUnY2TEpMTFpKR3JwKytoRWV0UURMMVUwdExwbU51UEhqdStxTUU2YmY4V2RWb0FVNFlkeU1wdUhlWTNJclhqV2IvWGZEY2cvemtIRXFxd0RsS25oQWxCVkt0bnhPeHRMUFI4SWYxaDgwaEYiLCJtYWMiOiJkMjMzNDBjOWNhMDFhZTAzNDRiYjlhZGIwZGExMmMzMWVkMDJkNjM5YmIyODcyY2NiZWQ2OTZhNDUzOGRkNDA3IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:10:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2118684359\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-843576344 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-843576344\", {\"maxDepth\":0})</script>\n"}}