{"__meta": {"id": "X2a3eff84b954288f29c84541b36e365d", "datetime": "2025-06-17 14:00:28", "utime": **********.446507, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750168827.580469, "end": **********.446531, "duration": 0.8660621643066406, "duration_str": "866ms", "measures": [{"label": "Booting", "start": 1750168827.580469, "relative_start": 0, "end": **********.342041, "relative_end": **********.342041, "duration": 0.7615721225738525, "duration_str": "762ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.342062, "relative_start": 0.7615931034088135, "end": **********.446534, "relative_end": 2.86102294921875e-06, "duration": 0.10447192192077637, "duration_str": "104ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46326352, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1663\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1663-1673</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01363, "accumulated_duration_str": "13.63ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.396617, "duration": 0.01202, "duration_str": "12.02ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 88.188}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4290829, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 88.188, "width_percent": 11.812}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-660609339 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-660609339\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1947956626 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1947956626\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-194685565 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-194685565\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1405886351 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168503247%7C8%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Iko0Mk5rWlZrV0RrSUhhNTZYazVmU2c9PSIsInZhbHVlIjoibk9aOUo4RG5saS9kUHI4djFIUG1NeEpJSmc4Y2dDelppeS9xOXlrbkhmcUFDYTJqb1o4ZzA5VThBbTB0allDdEtScjI4U0ttZEZPQ0hkRUg4M0RXSm9NNTlsdlUvaGl5MDRIQXBrVnRlZDJqY1JEdGJmc3ZweWRSTG1FTEhaM0JyZzlScnovb1lvemtCVWlaa05NTjJSaDRjU09sY0ZhR2U5M0p2ZVlYS0kwWVpjcnZDSlFKS2haSng3eEF6UzJCeDErSXhXTWNUN1ZzQjJCbXUxT0FXVGVUQU9uNFA3eVQ0S2J2c3VLZHBMejlva05yd2FmcWthOWhueTR2N1JiMEpCSjN4aURIdXJMdUJkSVpNSnovdENoZ3JxV09ZYWpQVWtzdGlQWFlzekRrc1ZlMFFKOWMyU2pKSXVqQitiMkFqR21XVzRlVmF6QUVadmswNUlRb3ZMTk9FYmlkbHFwK3pRWTIzMll4bUMrN1ltMGZ1UXFqVWRZL3N1aDgzMjVrV1k2TEJsUVZTckxabUFITHRsN1BhMWFrVnpxMFo5ZmlsS0JGdHJNUk1rTmZaZTVGdkJIZU11dUtLejhRNkhhMGx4d1lOTmNLZkwvY3o3dHp3WVpXTHkyeXMvY2ZseC9Oc3VvZVJsU3FUK0lzU21ZaHlaQ1RBYlBlU1k0SXUvQ3MiLCJtYWMiOiI1M2E1Y2U3MTBlMDZjZTU3MGEwOWFjNmY0MzViNmRlZGNiODlmZmIxZmIyMTRlMmU0YTg5ZGFlYjY5MTBkZjVlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlJGUkMya1NTM1hWVXlPSG0vTXplQ3c9PSIsInZhbHVlIjoiaTg4amhMNzFyY3lCaUJDRytyTHhEWlhDZnNwZHpZUmRzVDZ6UTUybDgweVowUDRkWHAyR2NHRlo5VmR0VGFYWFFPTDNTNm1RTjB2a2pzbVA1VkM0QndaazdVMWpYY1hIdjRweHZrdTlocUNPbm5jdXkzYmp0dU1MVmZpajRPTnpHcGhhdFpqOVd3SEE1T3VmT0NLU0FYRmEwdUtCbUh3ZFA0aDh5bTVKZzRrb1NZR3luOENVSE9hREU1TFFPNlVpMllQbm84S2NpbkJFSEtpbkpGeW5KaVY4M05zaGR0UmtJM1FmVi8yL2VSMDBETTFkZW9maldLS3pqamhBVWxVa1ZIZFVodW4rRGlZbW5zOU95dzZXd3FOSUJPTVR0UDdudmg1ZnBnWVpIZzh1Z0Rvajg5bm85WWVpUXJ6K3BZZ3JOY09acmpKWXVvWGFib0NrRUZzZ2ZvaU53QlVOTnd2MzRxdUdFclRoRDlPaUZMOUZjQTRCTS8vcHpOVUFWNzJuRHVHVFpOa1puUG1icVlDZTdRbmtkekYwU1pnWU54UUF1NXVkeVhhKzd3d2N1MnNDZnQzOUk2SVZaQnNYektVS3JFcGt6Yk9kb2xSN1lvQml6M1NyMkJobTdRV3J2N3Z1V2F5YTEwOUo4ZDUyYldydHEvUC92MG9qNWg2RzRGYUkiLCJtYWMiOiI0ZmNjMTNmNWRlMmMzZjNkODlhMjM4MjgwOTliZjlkZmEyYTA3OTc5YjVjMjRhYjVmNDQwN2IxY2M2NDMxMGYyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1405886351\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1778937308 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1778937308\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-357147119 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:00:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjQvdGFXTzhBdFpBOEFMcjBwd0hwYnc9PSIsInZhbHVlIjoidkU0WmhLYUYxdElHRWlaMFZMd1NGa3hXR3RCb1FRS1c2NTkwM3RJUXRoSU5LdEZwN1c1WW5OakNyRXQrdlA3ekx6dUlwMUlheGp2dDB1WGlyU2NFL1VYMW5mbVA1Q1RnQnIreUlRWFg5L3JLZDVDbUEwUGRvR3RqbXh0Nlg2RXFCejM0akY2VzNuV2Y4bE1Jd25uaEdwc0pXdzhpVVdzTlg4UVkzNWN5cHhOdXhRR3lnN2FsNlVPam5heTVsVll6Yk1JR1N0em52SUpPY25JZ3pNajBINTdMd05SMktJQTdHVEpNTWVRZGgzZmZTZnllbllzTVE2R29tUktrWjQ4SzFReGNFamkraW5TTWs4MFJLVDZDWkVKMWh1eEtBR0diWEViNDdDOWR5RTI5ME15R0lySU0zMDdQaEZ6ZTNzYmMvMEd6MkJvQmI1eFBpS2xpcFd3QlBFOVZDT3QxM2gvUWhwZjVqbU5mb2pTTlM2a2x6OTVpU3JxL2FzUzErbEh4aG5PakJwRTRsdVdPbk1XRmRFamd6TWQ0cjlmQXhrNFFFMHBPS2tTV0NQNWhEYjVERlF4R3NTYThzTjM5QzRYTXZSVFp2aG00UmxqZ001ZHJIeU44SzVOSEJLKzh3MXF5WHNYZGxGaUxPdkJVSDZWUTlaeWIxS2taaXV5ZzVKclgiLCJtYWMiOiJhMGY4YTU1M2FmZTk4MTQxMTU1N2NhNGNhZmYxMmJkZjM0NDIxZmJhOTY3YTZkZmY1OTZlODJkZGEwZjM3ZGZhIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:00:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlkwU3oxcnVaY2VWNzRNb3hYejQ4WXc9PSIsInZhbHVlIjoiZ0x2QjBWeHMwenRlckd3cEZkTWk0a1JFVU9zV01TTjlvU2hvM3IvenhMYmt1cWZuODhEY0ZlYTJQUE8xdzF3Y08wREdSUEN6Q21YcTk2SndGakFINFdUakZEMVVmT2pscGUwaHN3YVVYTEtxc1NLZWZkZHJDdkFESnlBZkRiL2RDYW5sd0ZHSGtmY1MyaHNNQUNpWFBDTklObUs2TG1ZT052N3Z0dW5TSjFYdGhoSkVlc3dxVThzWGlkZFhNN2FLeDdlbVFuU0dhd3I2bzRPbFN5dU4vUHhBWldDNnQzdnV5OUVPOXo1aDUvZjhzTXFEdU40Y2dkU3FXMFpLdXlFZE5GaGpuRlM0ajFnck94UXN2Sjcvd2dtN0xCTGFJQjJldFI4VjJjTjdPSWRtZTdNa2o3UW9JL2RVdVJrN3grNlUyQWQ0c3dSODE5R1I3WmNHeTQrdGZVSmlIeTErbkQyWlZvSDlWTVFDTTlqa1I5dGxnWnAwTFRRL0s4ODhTeXBXZ29Hck50ZWx4STk2d1F3V0dnZTUxU0lNSWlLVWg4UU8ybUdMSnFkcEovd1prVlRzZVN5REh5a1VLazRsWjNmeTk4d2g5M1JtWCsrYkYza3VMRHNhREcxSGpBNllEVUpSa0NPS281RENCVmhHUkZVeGw1Q2lHaUxQbDlDZXczSHciLCJtYWMiOiJhZmJhMDhiZjgxMGY2YTAwYjRlMWYyYTkyMTUxMWFiZTY2MDlhZGY3MGUxNGYxMjA1ZjAwZmRmNzg4OWM5ZTcyIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:00:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjQvdGFXTzhBdFpBOEFMcjBwd0hwYnc9PSIsInZhbHVlIjoidkU0WmhLYUYxdElHRWlaMFZMd1NGa3hXR3RCb1FRS1c2NTkwM3RJUXRoSU5LdEZwN1c1WW5OakNyRXQrdlA3ekx6dUlwMUlheGp2dDB1WGlyU2NFL1VYMW5mbVA1Q1RnQnIreUlRWFg5L3JLZDVDbUEwUGRvR3RqbXh0Nlg2RXFCejM0akY2VzNuV2Y4bE1Jd25uaEdwc0pXdzhpVVdzTlg4UVkzNWN5cHhOdXhRR3lnN2FsNlVPam5heTVsVll6Yk1JR1N0em52SUpPY25JZ3pNajBINTdMd05SMktJQTdHVEpNTWVRZGgzZmZTZnllbllzTVE2R29tUktrWjQ4SzFReGNFamkraW5TTWs4MFJLVDZDWkVKMWh1eEtBR0diWEViNDdDOWR5RTI5ME15R0lySU0zMDdQaEZ6ZTNzYmMvMEd6MkJvQmI1eFBpS2xpcFd3QlBFOVZDT3QxM2gvUWhwZjVqbU5mb2pTTlM2a2x6OTVpU3JxL2FzUzErbEh4aG5PakJwRTRsdVdPbk1XRmRFamd6TWQ0cjlmQXhrNFFFMHBPS2tTV0NQNWhEYjVERlF4R3NTYThzTjM5QzRYTXZSVFp2aG00UmxqZ001ZHJIeU44SzVOSEJLKzh3MXF5WHNYZGxGaUxPdkJVSDZWUTlaeWIxS2taaXV5ZzVKclgiLCJtYWMiOiJhMGY4YTU1M2FmZTk4MTQxMTU1N2NhNGNhZmYxMmJkZjM0NDIxZmJhOTY3YTZkZmY1OTZlODJkZGEwZjM3ZGZhIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:00:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlkwU3oxcnVaY2VWNzRNb3hYejQ4WXc9PSIsInZhbHVlIjoiZ0x2QjBWeHMwenRlckd3cEZkTWk0a1JFVU9zV01TTjlvU2hvM3IvenhMYmt1cWZuODhEY0ZlYTJQUE8xdzF3Y08wREdSUEN6Q21YcTk2SndGakFINFdUakZEMVVmT2pscGUwaHN3YVVYTEtxc1NLZWZkZHJDdkFESnlBZkRiL2RDYW5sd0ZHSGtmY1MyaHNNQUNpWFBDTklObUs2TG1ZT052N3Z0dW5TSjFYdGhoSkVlc3dxVThzWGlkZFhNN2FLeDdlbVFuU0dhd3I2bzRPbFN5dU4vUHhBWldDNnQzdnV5OUVPOXo1aDUvZjhzTXFEdU40Y2dkU3FXMFpLdXlFZE5GaGpuRlM0ajFnck94UXN2Sjcvd2dtN0xCTGFJQjJldFI4VjJjTjdPSWRtZTdNa2o3UW9JL2RVdVJrN3grNlUyQWQ0c3dSODE5R1I3WmNHeTQrdGZVSmlIeTErbkQyWlZvSDlWTVFDTTlqa1I5dGxnWnAwTFRRL0s4ODhTeXBXZ29Hck50ZWx4STk2d1F3V0dnZTUxU0lNSWlLVWg4UU8ybUdMSnFkcEovd1prVlRzZVN5REh5a1VLazRsWjNmeTk4d2g5M1JtWCsrYkYza3VMRHNhREcxSGpBNllEVUpSa0NPS281RENCVmhHUkZVeGw1Q2lHaUxQbDlDZXczSHciLCJtYWMiOiJhZmJhMDhiZjgxMGY2YTAwYjRlMWYyYTkyMTUxMWFiZTY2MDlhZGY3MGUxNGYxMjA1ZjAwZmRmNzg4OWM5ZTcyIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:00:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-357147119\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-47933238 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-47933238\", {\"maxDepth\":0})</script>\n"}}