{"__meta": {"id": "X9a60fad6e8676e4256ccc4ec5816089e", "datetime": "2025-06-17 15:05:53", "utime": **********.69461, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.089087, "end": **********.694631, "duration": 0.6055440902709961, "duration_str": "606ms", "measures": [{"label": "Booting", "start": **********.089087, "relative_start": 0, "end": **********.570197, "relative_end": **********.570197, "duration": 0.4811100959777832, "duration_str": "481ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.570209, "relative_start": 0.4811220169067383, "end": **********.694633, "relative_end": 1.9073486328125e-06, "duration": 0.12442398071289062, "duration_str": "124ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49205496, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.02391, "accumulated_duration_str": "23.91ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6135662, "duration": 0.01627, "duration_str": "16.27ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 68.047}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.641961, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 68.047, "width_percent": 5.563}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.664844, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 73.609, "width_percent": 5.144}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.668714, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 78.754, "width_percent": 3.513}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.675708, "duration": 0.00256, "duration_str": "2.56ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 82.267, "width_percent": 10.707}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.682595, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 92.974, "width_percent": 7.026}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1268459782 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1268459782\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.674254, "xdebug_link": null}]}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-137760797 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-137760797\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-21529274 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-21529274\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-830746486 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-830746486\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1206648413 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik5TdGhJYkQ3eTlnSEpXYm4yNzRyNFE9PSIsInZhbHVlIjoiUFBBWFd4VTNoQjVqOC94WmtKT0x4UlovMGlJVm8zWDlkQ2FqamwvOWRReWdxT21BblJLVUM5TEkzb2Q4YXc3QTlVNWdSWm05Q0dOZ1BLcWMrSWxORTVFV0VuNm40ZFJYeExER3dsMm9TWU9YY29MdlJyUUMrYkxibXlWaE43RnVuRHNRZlRpbzJGVFJTUnRLLzRjYWNhdGI4cU1GWFFGZkVoVEZ2TDY3ZzBWYXh6by9FWnZrMWxheWh0NlkyVWV4Q1ozM3BSaWtoeDhLbkNaQjFiOFpWSVFQOWp6R0xGTmNsTXl3QXVQR3NVR0h4ZXV2RDM3Szc3NTVJODNzckV1SmFhWG9mNXR5N2VBZzRMQko5YVJYaDlEdXpJS0RETEl0bitPTlg1aWEvYlN5TjkzQ3RUeFk0SDZNKytzQjg2SDhzQk42UnQweGJ3U0wwOVdTUGUxRDhlVUdhUklDOUY3c0lGT0pSblJZcWNmZFJNQXlHK2J3THdRd3BNTzVlRkRrZzhmNWZNUEM5ZG41VldacGU0NlcvSHNGdUZrdFFDUCtiUVo0MnI5THM3dHRtMXY4UGhVZ1FwQjkvM2huSW9hcGV3dWVUODgra1pmMDNQa1pNT1BBYWdrcFdYSVBvSUd1MlZ4c2xaSEsyeGJFZ1RyV2IzNmVNMzVNM1p1Tkhodk0iLCJtYWMiOiJlYmVjNTRkNjlkNjY2ZGZjZGI0ZWIzZDYwYTM2NTdiZDIzMGY5OTJlNzIyMWVlNmZjZDZjNzZhZTI3NTM2ZTdiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IitYRWI3dzdoZUdtTXVQT0RXMjJrekE9PSIsInZhbHVlIjoiSXBPT0VId1M2NWkxdjROeHJPL29pcDVXZC85U1A4ajFzekV3TFlsb0QyZUJTVTVocVl2MTdyZjdyU25oaGU3UU5rdmtsbmxHMXc2UjJoaHJkL28yOWtlTjNmN3ZCcjNXWW1GUXRLMFY2dENqNVlybzBhUHY0citCSi9McEU5WGE2UWNsTUJnSk9qdEZoak1sZTJFTjVFMi9xbFRBcE9RSjIxdzNvQ1hNSC94cWdxdjBheHVnbnYrdDkwSGRWZ05CT2RQWjVyLzJWT3p1N1J5RDBFREFkMW4zSzVxOUZYZFBUc2x1cEI2eVQ0MW8xWWRtTWcxektCQ1BWNUl4VENES21IU3hUM3FmdUhjM29uek5FdVlFd1J3Zml5NVYwTHJQb015dnNxQlZmVm5TYVVsdnVlSzBIUW15TG5zcTR6Tzg3RUJDSnZqTmRWOXZ4U3pFVTBaSG9Va0YwN0x1Zm1mN2R0WExkdG5GcGR1QStZSm9yNENUSnBGeXpxckFjWmV6SlZrOTU2U2FZTUh6Q202b1Y1akk5NVJ3ZW1tYWEwcE43UTFlYUF1anRMOHYwYW9EMUFhYXJDTGx3YWI2ZEp6OWV6bU1GaW4wUzdrUnA3SWxYYjJvWjJZeU92WFB1RGdYSDNNOVM5UUpOTjVHVWVrZVJROHA0REF6dEtMcjJVMjgiLCJtYWMiOiIwYWZiOWMyNTQ1OWQ1ZDJhMzc1MjkwYTMxOGRjYjc5ZjE1ZDdkYWMzYjZjMDY2YjQzYzUzNmVmZmE2YzViYjY3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1206648413\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1433771433 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1433771433\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-236287774 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:05:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InRPOVpweXljWlBwc2t3OU1objlxWEE9PSIsInZhbHVlIjoiTGdNSzJvUkM3MWh4THNvMUhnOFR2eHRuc2FXMzh5eGM5Y1lJdlZzd3IwNm41L3U1N1ZOV3oxMHNEUVBXaEoxMnJvNitjTWU0TGZ1Y1VGQjhVL0Z2YlNZa0Jjb2tuMXdsbEgrV1d3cUgycFVZY2lkRFJ5QmlaendJdVlvYkJNQlNpZzFtdUl3U0UwY2M0RkM0TURQVER0WXE5TjR3NmVteUI0MHV4d1pSODQ2eVZoZ0lqWXdqejhQZ0k1NmpHZjZnQWZCRmVEY3M5MnRtS3hLTFlGbDBvWjF4bFhkcUtwcjk0dWQwMGpnc01IMlJKZWpBR3dQMGhqbXgxeDZZMFp6S3prZDNWQ1hMTkxiL2tvRnMzK01XUkRlMEJiUEhGNGg3Y3JRYVA4Uk9iQzE2MmFBUUtkRXRKMDIzcU9oUUp1ZytaWUVrTjJIUjNWVUdIQzNabVpTYlRsZkMxV3dDWUJKWWorTm85ZC93SEF6azB3VFpzS09WenYvNFhMS283L3BLUVltL1ZVTlFSUzYza2poMnRNRTNLR3FhV0N1bjJhNkVKNSttdlpQQ2twSEhNazJWUUVXZE44bTVsYzh1WVM3cVVHcDFUdmhvYUpRYnZYcWVPOXFIaWluOG9LOXhSZktxdUd1S0tzQW56NGt1emRiamlHUXFOalU5emdCM2hFZXkiLCJtYWMiOiIwYWMzYTJhMGY5NGY1NjIwYzQ3MWEzZjNmYTcwNTI3YWYzNTdjZWVhZjhiZTIxNTZjNzI1ZTcyMGM4YmIyMjlkIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:05:53 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IklYVVFmSG9ib1dGNG1BZll4eEpXRlE9PSIsInZhbHVlIjoiSVBDa3o1REdGT3BlaFRPcU1DaXpJUWp4MFhNZW1sMUhOUTFrYXlldDM5TmR5blBuVWhXTzZNRFZZa0pBU3V0V0lkQ3BaZ0ZpRW02eXpYM25LRC90SnZCaEVwK0lJZmdmOFBUYkhsdXkvMkJVRWpkR0RuZGdodWlFQVV2Nk9tazR0NjhXRGN3RHhsU2xJZTlJNmJteTJoQjBwcElybDBmeG15eXR5YTNmWWljYlFTQVhnUWdOMi9QRm1OLzJaUnBrUWJiSisrNklqdk8vWEgxRnA1ZUNoRk5sRzJLTHREY241MzdKTWJhMEJVeGNMdlBhb2VrenFvdHNFOGdpNTVGODJPR1pNanE0SGwzRnR6Tiszam5veUliMjRXS2k0aXVHaSt6N3VmMzA5S0dxdXg5SXdVTlRQUDZQeTFCT1pkRFJWQmRBM3BSdHVRZXo2RDVIa2xSNjFrWHduTU12eHJsSGFUcHVPekJoLzFBS3RLU2kwWXhLem5reDA1c3VFQnNSOU1Vb0p4UTBsa080OHB1cW5FTTBKa1ZKL1hDUXkyckllLzk0WUxPQnlybXVnNHVrQU1vRTJEYmVHV2ZUOW5HYzV3U0pXb0xoUEpoNXBYaWlORFFGMEpJTG5MbVMybmFyMDNJdDJEdHk3QU1YR24zSitpSDl2RTNkUG9IR3AzdEgiLCJtYWMiOiJlNWNiN2Q5ZGU5NjEzMGNjZTQ2ZmY2MzdlNWU4ZTMzZDFiZTVkMmUzYjRhMDEzZjg2ZWFhYTQzYzg5ZGVjNjYwIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:05:53 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InRPOVpweXljWlBwc2t3OU1objlxWEE9PSIsInZhbHVlIjoiTGdNSzJvUkM3MWh4THNvMUhnOFR2eHRuc2FXMzh5eGM5Y1lJdlZzd3IwNm41L3U1N1ZOV3oxMHNEUVBXaEoxMnJvNitjTWU0TGZ1Y1VGQjhVL0Z2YlNZa0Jjb2tuMXdsbEgrV1d3cUgycFVZY2lkRFJ5QmlaendJdVlvYkJNQlNpZzFtdUl3U0UwY2M0RkM0TURQVER0WXE5TjR3NmVteUI0MHV4d1pSODQ2eVZoZ0lqWXdqejhQZ0k1NmpHZjZnQWZCRmVEY3M5MnRtS3hLTFlGbDBvWjF4bFhkcUtwcjk0dWQwMGpnc01IMlJKZWpBR3dQMGhqbXgxeDZZMFp6S3prZDNWQ1hMTkxiL2tvRnMzK01XUkRlMEJiUEhGNGg3Y3JRYVA4Uk9iQzE2MmFBUUtkRXRKMDIzcU9oUUp1ZytaWUVrTjJIUjNWVUdIQzNabVpTYlRsZkMxV3dDWUJKWWorTm85ZC93SEF6azB3VFpzS09WenYvNFhMS283L3BLUVltL1ZVTlFSUzYza2poMnRNRTNLR3FhV0N1bjJhNkVKNSttdlpQQ2twSEhNazJWUUVXZE44bTVsYzh1WVM3cVVHcDFUdmhvYUpRYnZYcWVPOXFIaWluOG9LOXhSZktxdUd1S0tzQW56NGt1emRiamlHUXFOalU5emdCM2hFZXkiLCJtYWMiOiIwYWMzYTJhMGY5NGY1NjIwYzQ3MWEzZjNmYTcwNTI3YWYzNTdjZWVhZjhiZTIxNTZjNzI1ZTcyMGM4YmIyMjlkIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:05:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IklYVVFmSG9ib1dGNG1BZll4eEpXRlE9PSIsInZhbHVlIjoiSVBDa3o1REdGT3BlaFRPcU1DaXpJUWp4MFhNZW1sMUhOUTFrYXlldDM5TmR5blBuVWhXTzZNRFZZa0pBU3V0V0lkQ3BaZ0ZpRW02eXpYM25LRC90SnZCaEVwK0lJZmdmOFBUYkhsdXkvMkJVRWpkR0RuZGdodWlFQVV2Nk9tazR0NjhXRGN3RHhsU2xJZTlJNmJteTJoQjBwcElybDBmeG15eXR5YTNmWWljYlFTQVhnUWdOMi9QRm1OLzJaUnBrUWJiSisrNklqdk8vWEgxRnA1ZUNoRk5sRzJLTHREY241MzdKTWJhMEJVeGNMdlBhb2VrenFvdHNFOGdpNTVGODJPR1pNanE0SGwzRnR6Tiszam5veUliMjRXS2k0aXVHaSt6N3VmMzA5S0dxdXg5SXdVTlRQUDZQeTFCT1pkRFJWQmRBM3BSdHVRZXo2RDVIa2xSNjFrWHduTU12eHJsSGFUcHVPekJoLzFBS3RLU2kwWXhLem5reDA1c3VFQnNSOU1Vb0p4UTBsa080OHB1cW5FTTBKa1ZKL1hDUXkyckllLzk0WUxPQnlybXVnNHVrQU1vRTJEYmVHV2ZUOW5HYzV3U0pXb0xoUEpoNXBYaWlORFFGMEpJTG5MbVMybmFyMDNJdDJEdHk3QU1YR24zSitpSDl2RTNkUG9IR3AzdEgiLCJtYWMiOiJlNWNiN2Q5ZGU5NjEzMGNjZTQ2ZmY2MzdlNWU4ZTMzZDFiZTVkMmUzYjRhMDEzZjg2ZWFhYTQzYzg5ZGVjNjYwIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:05:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-236287774\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1041290861 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1041290861\", {\"maxDepth\":0})</script>\n"}}