{"__meta": {"id": "X132469b09b9f6941751386db65d0f25a", "datetime": "2025-06-17 14:57:02", "utime": **********.446853, "method": "DELETE", "uri": "/remove-from-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 3, "messages": [{"message": "[14:57:02] LOG.info: removeFromCart called {\n    \"id\": \"7\",\n    \"session_key\": \"pos\",\n    \"request_data\": {\n        \"id\": \"7\",\n        \"session_key\": \"pos\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.407458, "xdebug_link": null, "collector": "log"}, {"message": "[14:57:02] LOG.info: Cart before removal {\n    \"cart\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.438218, "xdebug_link": null, "collector": "log"}, {"message": "[14:57:02] LOG.warning: Product not found in cart {\n    \"id\": \"7\",\n    \"cart_keys\": []\n}", "message_html": null, "is_string": false, "label": "warning", "time": **********.4384, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1750172221.851679, "end": **********.44688, "duration": 0.5952010154724121, "duration_str": "595ms", "measures": [{"label": "Booting", "start": 1750172221.851679, "relative_start": 0, "end": **********.33533, "relative_end": **********.33533, "duration": 0.48365092277526855, "duration_str": "484ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.335343, "relative_start": 0.48366379737854004, "end": **********.446882, "relative_end": 1.9073486328125e-06, "duration": 0.11153912544250488, "duration_str": "112ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48632760, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "DELETE remove-from-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@removeFromCart", "namespace": null, "prefix": "", "where": [], "as": "remove-from-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1675\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1675-1748</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.019569999999999997, "accumulated_duration_str": "19.57ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3753538, "duration": 0.01551, "duration_str": "15.51ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 79.254}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4025888, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 79.254, "width_percent": 5.621}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.425231, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 84.875, "width_percent": 7.614}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.429739, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 92.489, "width_percent": 7.511}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 16,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-129812357 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-129812357\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.437638, "xdebug_link": null}]}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/remove-from-cart", "status_code": "<pre class=sf-dump id=sf-dump-1979850729 data-indent-pad=\"  \"><span class=sf-dump-num>404</span>\n</pre><script>Sfdump(\"sf-dump-1979850729\", {\"maxDepth\":0})</script>\n", "status_text": "Not Found", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1074666463 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1074666463\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-51211688 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>7</span>\"\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-51211688\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2057212772 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">20</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im9XZUxTRU5YekdVem4xRFIwQXNINEE9PSIsInZhbHVlIjoibzhuMnRLOXl3dmw4MEt5UG1yV0JHY3ozRUY3dzBKWHpXcUJvbGF1SGd0aTFDOGZzRlVFZzlDVktGM3N4cjlxMFV0UXVWQVFFeU85Qm9yTHFCcHN1TXFPRkEyQzU3cnVyUTlRTDhqY0hBaHFOUmc2MkFsdUYwVDNEVTVlM1VqRC9PdElxVkFndi9qdDluRmRzZWRvNlpXaGJjTWZDQkY0V1JERHJPY3VjeExwdDZqUWVoOE5vYlVqcWFsU1NUYkVPaGFKc2FKQXU3NkJNVXNxUU5FVkxQZm15MzYwUTk5NDYvSmlMcWxuVFQrVlV2QTBWSDBsNTRNeWpmcDhjT2U0V0RoNjB6MUY4NVlscS92VVRYQXVCRFNmZTM4dy9CZEkyeEFtakJ6dlVHbU5OTyszd2VRUUxPcGZxU1UyQno4Yld6aE1ZZ1RHU093WWZzZGxxbTZiTGdlUmdUYlJnclNIc3JUbmkvQ1NROWphT3JOVy9PS1pUSXZsREtjTUVJWDFITEd1TDZYWFFPNmtVODRRbXFiV2pvUVgwNWJCc04zUDZENU5IemRQdmx3RzlMNFIwR1RSRWo5SlhCS2dSQ3FlTSsralBMeFdwK0FmbFUyU0dxc284ZjFOL29vZkpHaU1nM2J1clNCU0ZjTFV3emNqd2lSeVd6VzhCQ0RZQXNtWUgiLCJtYWMiOiI5MzA0Y2ViNzVkMmFkY2RjZTRmNmI2ZmQ4NTYzYjY1NGVhOWY0MWVhZWFiNDU2YTdkNWQ3ZDU1YzAzMjBlNGIwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IklEWUpTakNVNVdvRkJtd283TStoSUE9PSIsInZhbHVlIjoia0RiVm0xbGh4Snl2dTlZaWVqR2pVT3pWUHZ4cVV4SXZ5bitBNzNPQmR6b3E5RlVITWFSa2JlMHhTZk80WXhTNzNWNlU1QTZGUjRDbTFFL1p1dzIxR1hYYStvQm1BMmtoU1I1Qzd4aVhSek9PejFsQlY5cGNPaC9mQStwOVM0STgrTWhjRDVyZnRFYnVhUitBd1hxZkd5TnFtd1kybzZ6RDFvTXE0cHovNksvT0pEK01Db2I0VldndEQvc3l0RnJDUXBHNFZCVDRxcHR6T2F3ZVc3bzB6bmg5Z3dnU0FoWmd6S0FpT2JCWlMzazFqbVpKbjdwQWJQUHhkUklJK3RQQjFDOEd2NVYwaGFpTngraEtZQ2N4aHFmVHl6OExTeWFaYjZyL2ZzZTFFVzdsMUV4bkF0aDFoNDhDdWFtV2FsbEZjUFdoSUovNGtYRmdwbk5TdmRtZE80eVJ1VDBSUmZNY09CajJUS2kveExYSm5rSVREZERaTHpXTVBMajEycmVMeWNLcmRIcDBjZzI1MHc0WFFWbVlIaUhqSkhnK2pWZlpDS0pid3RYTWw3Q0lmUW1iOUw3dWZldjhPajdKd2dzb2hZZ0p3YzZXTWVZbmJCazNxWWpxRGIraHB0TTR0dS9QY2M1eWQwZENvYlovRjBPODZDVm8ycUxSdFRwVVBTeGEiLCJtYWMiOiIwOGExYjEwMjY0ODYzYzIzNzc4NTFjNzlhZjhlYjMxNmQ4NmU0MWU2MWU3NGRjNjhmMWJmYTlhMWI3NTVmM2EwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2057212772\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-34978436 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-34978436\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1200736524 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:57:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ing4dTBrc045THltb3lCQ2ZsK2FBbXc9PSIsInZhbHVlIjoiQzhWVFRVR3FjUVdBWGt2aUZMRFBXR25XaTZTNk1rSFpvR1piYVk3eDFrUGxrQXZ1WnVybXNKMmFBZzA4WTQvTllGMUMxdENVbndrQWpSVC9UREN0OW5FMStXQmVhQjFmT1ZGSzZ3Q2xqd0EzZ2NjVVMxSjREb0RoTE9wNHFjdlEzVStVdG5lZklBczVhdTFHSEhDMU1WQUo1cXAyV1lxRGJMSWUxeFJiM2ZuVVFiTmtOWDBpK2ptTXl1Ym5PbDh0NzJ4QklJc1pUNXBsU2VUdXRoS1A4Tnl4OCtlZWdvQWp6OGZDb2xUV2UzZ2dHS0xuSGV4cXE4N0tyKzJVc0lORjFnM1pUaHpvYTVXK3J3SlpxWVB4K1Q1WW84YjQzUjFnT0dvMjM2dms0RHV1Q0g3M0cxMVQ2ek15aDFCdFQ1T3BQTUh4T1BkZmZ4UnZnYVlha0UvVjZnZVVCbWhqczBTSk9vNWF2QmZzTGQ2NkVPQlFSV3dZMTF0bmZMVWs5alZ1ckxtSHhRUWZpWjhhWlRjMVFQT2JBcG84bnVaT29CdVp0bnl6MEJJS28rUmtYRjVjeHVHcWowL1hWb2R3aW00bTY4R0hja081UEpTZHlvcE8rSmlvUjBUcHRseUluMlRlVDhLQ1cxWjJ2RWU0aGxQMHZUdDFXbFJWWnErdkxqb0siLCJtYWMiOiI2NDZhZTViMzRlNDAwNjU5MGQwNDNiZTcyMmVjYmQ4ZmY0MTIwODNjZThlN2ZkM2JjNGYyMDI3YmI2NmVhYjMyIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:57:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InRLS3daNWtnSS9oUkpJRHkrOThvWkE9PSIsInZhbHVlIjoiTUNXMW4xRzFJZmlzcWZzLzlBSWUvKzJCT2dxTDdnSHR5NHI1dlN4Tm1vWUsweEFJaHh6TTRCNCtzZXdvS3hpaEFpZ1k3NnlYaGVIeHFzd2s2WGlhcEppa0t3UVhUbHFxVlRNcWZZMDFiM1FtUExicTBrUG16UVZuSDM0SXE2cDY0TFFmNXh4QUNMTmhpMU9mMVNKbzlnQ3Y5Z0pTRmxGbVdzcjNjOEJIYXFZYWRkOENTOUduaDhrUHNJQXVXOUNrRm1NWFVEaW80RldJUTFVMFBwRnpyNStLZU5JaUhaNm51WDBWOUpxc2xuY3RSSjRVWkxqRWhZQmkwY3grWnNOSmhicTd3SFM1ajRESTExU0pLWk5SN3BwVGpMTWxZdnVFUUtOc0lYMk9qM1JCeWc2YWkyb056bWdtcDgrYjUvanJoR29lcmVLa2lkanVkSFVhbS92ekxORTBNNFpsRzB6OTlUZllUdm1ISXRXZmxQdkI5eTBmK0wrOEQ0TkVSTVpxbnhHNTZGcDNKQXRPaGl2M25kcDIxREk2d29qNkZEQ09iakc1RjU2OThoR0VJWFpRTngzUzloOUdRL1YxbW9VZEFCK3BBUjEzVlJhbUQweEVrbCtpUDhIWTdiZi8xRUl3RWlqN3djVzk3eGxTQzhoZ2lTVlROTGhmQ0RkSHk3VVkiLCJtYWMiOiI4M2E2YWNlMTVjZmMyMjI4NTVjYWEwY2NiOTgyMjU3NjY2ODRmM2YzMzA4Mjg2NDVlY2IzMjY3ZDY5MDlhZWM5IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:57:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ing4dTBrc045THltb3lCQ2ZsK2FBbXc9PSIsInZhbHVlIjoiQzhWVFRVR3FjUVdBWGt2aUZMRFBXR25XaTZTNk1rSFpvR1piYVk3eDFrUGxrQXZ1WnVybXNKMmFBZzA4WTQvTllGMUMxdENVbndrQWpSVC9UREN0OW5FMStXQmVhQjFmT1ZGSzZ3Q2xqd0EzZ2NjVVMxSjREb0RoTE9wNHFjdlEzVStVdG5lZklBczVhdTFHSEhDMU1WQUo1cXAyV1lxRGJMSWUxeFJiM2ZuVVFiTmtOWDBpK2ptTXl1Ym5PbDh0NzJ4QklJc1pUNXBsU2VUdXRoS1A4Tnl4OCtlZWdvQWp6OGZDb2xUV2UzZ2dHS0xuSGV4cXE4N0tyKzJVc0lORjFnM1pUaHpvYTVXK3J3SlpxWVB4K1Q1WW84YjQzUjFnT0dvMjM2dms0RHV1Q0g3M0cxMVQ2ek15aDFCdFQ1T3BQTUh4T1BkZmZ4UnZnYVlha0UvVjZnZVVCbWhqczBTSk9vNWF2QmZzTGQ2NkVPQlFSV3dZMTF0bmZMVWs5alZ1ckxtSHhRUWZpWjhhWlRjMVFQT2JBcG84bnVaT29CdVp0bnl6MEJJS28rUmtYRjVjeHVHcWowL1hWb2R3aW00bTY4R0hja081UEpTZHlvcE8rSmlvUjBUcHRseUluMlRlVDhLQ1cxWjJ2RWU0aGxQMHZUdDFXbFJWWnErdkxqb0siLCJtYWMiOiI2NDZhZTViMzRlNDAwNjU5MGQwNDNiZTcyMmVjYmQ4ZmY0MTIwODNjZThlN2ZkM2JjNGYyMDI3YmI2NmVhYjMyIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:57:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InRLS3daNWtnSS9oUkpJRHkrOThvWkE9PSIsInZhbHVlIjoiTUNXMW4xRzFJZmlzcWZzLzlBSWUvKzJCT2dxTDdnSHR5NHI1dlN4Tm1vWUsweEFJaHh6TTRCNCtzZXdvS3hpaEFpZ1k3NnlYaGVIeHFzd2s2WGlhcEppa0t3UVhUbHFxVlRNcWZZMDFiM1FtUExicTBrUG16UVZuSDM0SXE2cDY0TFFmNXh4QUNMTmhpMU9mMVNKbzlnQ3Y5Z0pTRmxGbVdzcjNjOEJIYXFZYWRkOENTOUduaDhrUHNJQXVXOUNrRm1NWFVEaW80RldJUTFVMFBwRnpyNStLZU5JaUhaNm51WDBWOUpxc2xuY3RSSjRVWkxqRWhZQmkwY3grWnNOSmhicTd3SFM1ajRESTExU0pLWk5SN3BwVGpMTWxZdnVFUUtOc0lYMk9qM1JCeWc2YWkyb056bWdtcDgrYjUvanJoR29lcmVLa2lkanVkSFVhbS92ekxORTBNNFpsRzB6OTlUZllUdm1ISXRXZmxQdkI5eTBmK0wrOEQ0TkVSTVpxbnhHNTZGcDNKQXRPaGl2M25kcDIxREk2d29qNkZEQ09iakc1RjU2OThoR0VJWFpRTngzUzloOUdRL1YxbW9VZEFCK3BBUjEzVlJhbUQweEVrbCtpUDhIWTdiZi8xRUl3RWlqN3djVzk3eGxTQzhoZ2lTVlROTGhmQ0RkSHk3VVkiLCJtYWMiOiI4M2E2YWNlMTVjZmMyMjI4NTVjYWEwY2NiOTgyMjU3NjY2ODRmM2YzMzA4Mjg2NDVlY2IzMjY3ZDY5MDlhZWM5IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:57:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1200736524\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1617162282 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1617162282\", {\"maxDepth\":0})</script>\n"}}