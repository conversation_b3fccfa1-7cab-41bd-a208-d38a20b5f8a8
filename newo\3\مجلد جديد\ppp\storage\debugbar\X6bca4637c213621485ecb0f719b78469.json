{"__meta": {"id": "X6bca4637c213621485ecb0f719b78469", "datetime": "2025-06-17 14:56:36", "utime": **********.705205, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750172195.987795, "end": **********.70523, "duration": 0.7174348831176758, "duration_str": "717ms", "measures": [{"label": "Booting", "start": 1750172195.987795, "relative_start": 0, "end": **********.595521, "relative_end": **********.595521, "duration": 0.6077258586883545, "duration_str": "608ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.595538, "relative_start": 0.6077427864074707, "end": **********.705233, "relative_end": 3.0994415283203125e-06, "duration": 0.1096951961517334, "duration_str": "110ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46326352, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1663\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1663-1673</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02391, "accumulated_duration_str": "23.91ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6508, "duration": 0.02316, "duration_str": "23.16ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 96.863}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.690892, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 96.863, "width_percent": 3.137}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1137858206 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1137858206\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-661383211 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-661383211\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-291106648 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-291106648\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1ti4td2%7C1750168869469%7C9%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ill1ZDc1NmFNb0JDWUw4TlVkNm9MR2c9PSIsInZhbHVlIjoiK2hQazdPRk83SklNbWNLQndYaElOd1dSY2UrMVVtcmlhU2pUZ1J4dUpuemRrV2JnWFFXQVo5Vk9vVk1HSEEvUkZZekcyd2N4RXFUYVQxSGdkRXFrOWFlRHJkU2hNOGplbHVSWWhUUmkvUi9jREtRL3Y0WXFCbDFYNHJ1ZUJ2STJIcHdjMnJLTEVRbzJkcDdkR3l2bC9vNWRhd3d3TWZab2hhY0UrMVliSUlPZlQ4RjEwYllsNTh6Z0REWnR5MzRHSHRxZWJUcWZQQmhoQkVlcmYzdW5KUFRheGFRUGxHNy9oUDlVM1djWkFJVVNEZ2FZZDROcG13VExwTXMvMk9kQ0huelpqWWlXMGMwWTVTdlAwb3VFV004N05nVDg3c29maThSQmM5MGpyMTkwZVk0dXJqRTRkSlRzanRGY0dKOVNVUklFNHR4ZlowNVhiQllvWEszQjdMczNhNi91ZVBmbzJFMGxtVkRaWHJpa0RtRDhlZUZ0WFFRb0Zmay9DNWpmQmUxTUVZemt1Umd3aUlDYXU0MU5CNy9RQjVmd3RQOVVpWldKWmYycU1hSlRuSHR3WUFVY3JKOExsMEI2NGxBczNodDgyT3dBUWZHdk1mb2lRY2VNOXB1Ym1laVFuekJuU3BxWFZoaWRESHZMdTRGQU9MMUxpKzBzUXc1ZWV3Y24iLCJtYWMiOiIyZGZhMDZmMDk1NjczY2M3YTg3OThhNGU4Zjk2ZmMyNWE0MWIxZGU5MTIzOWUwN2VhODExYzMwMGI1MjVmNTY1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IndlUm1aQ3Z3SE9YQUJ2dWQ5eUx1Z3c9PSIsInZhbHVlIjoiM25mWHM5dVVyNnBBcm9WblQzQ0EzTCtHRGl2UTA2UXlMVUQ4V2R4eDBnd2lIK3UyS0ROMzZNblY2djVEMUppckNFMVM4VzE1VFh0N1lWU081MEREYzRKemVLK01uVm45QnI2STkrRm1vS2NEeTlFUGlTSDEyc2p2alZyaUhnaERYOW9HcjVaL0hSQzZKU2I3Uzllbmhza3NaR3dpOThLakZDQ25oSm1jY2NkUWRCMlJSMzJNNXV0Q3pmbFl4Z2hKcXJKZTg4ZXBrTmdFamoybWQ3VjA0cUY3bi9PcnNnSmpLYkd5T0tWK1VoV2VRQ2NFUG9OMGlwSFlwWnZEcXN5TFRqa2hkSndaWFY5SHUzaVBwWEY1OVJmZ21pemxqTThsR2d2eStjM3N3WTdUeDVyVktNUG1DKzBEMFkzc1Uxd0JUZ2lFeExiaUlyRml2dzdmcHdyYlc1OHdYU2l6YWMwcnJxV0k1cXpuam0yVHpzcXozdHVBOXFPeG9vd0VHOG41UXJ0MjhIS242b05hZkxKeHMrUUdyb2RCTm5LelRvZ1hwalFKN01IRGJmOS90ZXducVR5bWNnYjVNeW1wSWoxcXQvWXJ1b251dkZNNFJSNEQrd3Jpakg1NG1nZHd1SFlQNThSOG43SFVRdUxQd1FnNEZJcE9HeGZkaC9URkNzdmgiLCJtYWMiOiI2NTE5NzgyM2VmYmZiZDM3MGMzMTFiZGRiODlkZDNlYTFjMzczM2IxMGFhNjAyZTU3ZGM1NDg2MDQ0ZDA4OTcwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-105338354 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-105338354\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1971203742 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 14:56:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IldTWUtQVDgzK0VXUnVwQzhrVXRacVE9PSIsInZhbHVlIjoiQzhicHN2VW1DZmJLcHM0Y0sxeWtCR2lVa0pnY2g5Qk5PM3Y4emtkMnpIOXF6WTZldVFteGc4NVBBMytHay9LOHNwKzRmUS9nWFBFNU1TdnhaeWZ6NHNETkIrNm1UMVl4aXFjbENmTitGdUV0bDBwZ2xkemYwSjJtbWtFTC82N3dua2tiWjNSS3FCdFdsQ3JnTWZUVVFIckxOZVNRR2d4czlqWDVkZUpQRlRsblg4cmF2N0VUbjJIQjlDSWNWTTVPSU1Cd3U3UjAzYm9RVFMzSjNjUVBYWnJXNGVWbkxEV1hialFlcUtCN3RscmtYTmRERzVjMTIwRW5reWw2NlhVRmlWVlR6SG1adTFiU2JTdmxGSkVjNTJYdjVPWmo4YXZxZHdMVE40d21OMGplSSsrZSs2WURoUjFDL2tHTk9PR3BnbjA3NEg3N3o3RzM0VDNiT3lqV0hzc0VwdTYydUc2MG1zU3FmNnd6UDcxL0FNQ2twN2I5N1Y3bm1Mcmttcnc4T0FuUWxhcTE5WHRNYVhpcmlMN2MyYTVsQlRIdFR1VXdneWVYbTY1SXlCUURKQTg3TnVwN0h5cjhXOEhzR1BmN2VNR3pmN0p6T2Y5OGRaYzBXQ01odjhyRmp5S011SWVyek5taDZJdmNrTlBDY3NVcVJsNHp0amo3aytpQThjUUMiLCJtYWMiOiI3ZmM0ZDU2OTQwYzJlNmEzMTMwODE1ZjRjOTFhZGNhMTgwYTdkNmE4ZGQ1YTM0YzEzZWM2YjE2MTVlYzAwMWU4IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:56:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Iis2Sks1N2tzWGlyZlRReFI5bzZMNGc9PSIsInZhbHVlIjoiZTB0MnNWWEFtaUxDbTd2R0pHVk0vS2o3VUtMaFRXczZ0UkdqR1VGYVExb2dLeEo5bSt6V1VOWE5SMFBoWjB3MktiUXFyb1ZES29XWFZJVHFBaXpBb2w4OGphc25YKzBJcWxwbUJJcE4zVFl5VmlER3Q4UkdaUkpMbEpwbWcrUVl3YkFkM1JwS05hcERQa0gwdnBNUmpwWkRkWEJXVkhrUU9qVkR4dSsxWGhYMVBkZEZVNi9Velh2VFdLNkh0eWNJc3hvaDc1a3A3SG56Z1V5Ry82eld4aUlVVHF0SnlrNE5EMUJ0VWU1M0lpeWVXTmloendaNm42b0ptUDRQYjljNlJBZm5jZzBxTThBQjBQbnY2elduYzRjaWwxd1R3L1lrZkxZZUZiVU5MUTlHYnFzaElIZGlGOVNrVjBuc1NvNmdhNmpRdjFiSXVPNkxtc0VSOVZiZnpuN21lMGxQN2gydDl0cGRkUHBFdGNzYUNUZloxNndKSmtoczNPam1FR2Y5aWFpdUg0TWxYeFZ1VUZaNW90YjVsUDVoTkVGd01Zd3FyQXluR2szWFBNQjBudUE3MlIvQnNmVURIc2o5SW83UldxbHhSOHFTdzdPbE10UVRveVVGdHZLZm15cDRCbmY4QVBwc2E4S1ZwY3JiY20ra0tpSmdoSmVqN0c0TnpTcWsiLCJtYWMiOiJlYjViYzlhOTYxNTFmMzk2OWJiNjM2ZjNhZjVhYTFlMTRiZjE3ZDI3OTE2ZDliYzlkYzk0ZTZlOTU5ODMwMmI5IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 16:56:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IldTWUtQVDgzK0VXUnVwQzhrVXRacVE9PSIsInZhbHVlIjoiQzhicHN2VW1DZmJLcHM0Y0sxeWtCR2lVa0pnY2g5Qk5PM3Y4emtkMnpIOXF6WTZldVFteGc4NVBBMytHay9LOHNwKzRmUS9nWFBFNU1TdnhaeWZ6NHNETkIrNm1UMVl4aXFjbENmTitGdUV0bDBwZ2xkemYwSjJtbWtFTC82N3dua2tiWjNSS3FCdFdsQ3JnTWZUVVFIckxOZVNRR2d4czlqWDVkZUpQRlRsblg4cmF2N0VUbjJIQjlDSWNWTTVPSU1Cd3U3UjAzYm9RVFMzSjNjUVBYWnJXNGVWbkxEV1hialFlcUtCN3RscmtYTmRERzVjMTIwRW5reWw2NlhVRmlWVlR6SG1adTFiU2JTdmxGSkVjNTJYdjVPWmo4YXZxZHdMVE40d21OMGplSSsrZSs2WURoUjFDL2tHTk9PR3BnbjA3NEg3N3o3RzM0VDNiT3lqV0hzc0VwdTYydUc2MG1zU3FmNnd6UDcxL0FNQ2twN2I5N1Y3bm1Mcmttcnc4T0FuUWxhcTE5WHRNYVhpcmlMN2MyYTVsQlRIdFR1VXdneWVYbTY1SXlCUURKQTg3TnVwN0h5cjhXOEhzR1BmN2VNR3pmN0p6T2Y5OGRaYzBXQ01odjhyRmp5S011SWVyek5taDZJdmNrTlBDY3NVcVJsNHp0amo3aytpQThjUUMiLCJtYWMiOiI3ZmM0ZDU2OTQwYzJlNmEzMTMwODE1ZjRjOTFhZGNhMTgwYTdkNmE4ZGQ1YTM0YzEzZWM2YjE2MTVlYzAwMWU4IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:56:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Iis2Sks1N2tzWGlyZlRReFI5bzZMNGc9PSIsInZhbHVlIjoiZTB0MnNWWEFtaUxDbTd2R0pHVk0vS2o3VUtMaFRXczZ0UkdqR1VGYVExb2dLeEo5bSt6V1VOWE5SMFBoWjB3MktiUXFyb1ZES29XWFZJVHFBaXpBb2w4OGphc25YKzBJcWxwbUJJcE4zVFl5VmlER3Q4UkdaUkpMbEpwbWcrUVl3YkFkM1JwS05hcERQa0gwdnBNUmpwWkRkWEJXVkhrUU9qVkR4dSsxWGhYMVBkZEZVNi9Velh2VFdLNkh0eWNJc3hvaDc1a3A3SG56Z1V5Ry82eld4aUlVVHF0SnlrNE5EMUJ0VWU1M0lpeWVXTmloendaNm42b0ptUDRQYjljNlJBZm5jZzBxTThBQjBQbnY2elduYzRjaWwxd1R3L1lrZkxZZUZiVU5MUTlHYnFzaElIZGlGOVNrVjBuc1NvNmdhNmpRdjFiSXVPNkxtc0VSOVZiZnpuN21lMGxQN2gydDl0cGRkUHBFdGNzYUNUZloxNndKSmtoczNPam1FR2Y5aWFpdUg0TWxYeFZ1VUZaNW90YjVsUDVoTkVGd01Zd3FyQXluR2szWFBNQjBudUE3MlIvQnNmVURIc2o5SW83UldxbHhSOHFTdzdPbE10UVRveVVGdHZLZm15cDRCbmY4QVBwc2E4S1ZwY3JiY20ra0tpSmdoSmVqN0c0TnpTcWsiLCJtYWMiOiJlYjViYzlhOTYxNTFmMzk2OWJiNjM2ZjNhZjVhYTFlMTRiZjE3ZDI3OTE2ZDliYzlkYzk0ZTZlOTU5ODMwMmI5IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 16:56:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1971203742\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1388537363 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1388537363\", {\"maxDepth\":0})</script>\n"}}